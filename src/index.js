import {NavigationContainer, useNavigationContainerRef} from '@react-navigation/native';
import React, {useEffect, useRef, useState} from 'react';
import 'react-native-gesture-handler';
import './config/StatusbarConfig';
import {AuthProvider, useAuth} from './context/auth';
import {ServiceProvider, useService} from './context/service';
import {TrainingProvider} from './context/training';
import Routes from './routes';

import {RegisterProvider} from './context/register';

import OneSignal from 'react-native-onesignal';

import {ONESIGNAL_APP_ID} from '@env';
import {ClienteProvider} from './context/cliente';

import {Alert, Platform, Text} from 'react-native';
import {WoxAiProvider} from './context/woxAi';
import { ContaProvider } from "./context/conta";
import AlertConfirmarPresenca from './components/AlertConfirmarPresenca';
import AlertConfirmarPresencaResultado from './components/AlertConfirmarPresencaResultado';
import AlertConfirmarPresencaSairFile from './components/AlertConfirmarPresencaSairFile';
import GetLocation from 'react-native-get-location';
import api from './services/api';
import { BankProvider } from './context/bank';

Text.defaultProps = {
  ...Text.defaultProps,
  maxFontSizeMultiplier: 1.3,
};

const App = () => {
  const navigationRef = useNavigationContainerRef();
  const routeNameRef = useRef();
  const [routeInfo, setRouteInfo] = useState({
    current: null,
    previous: null,
  });

  useEffect(() => {
    initOneSignal();
  }, []);

  const initOneSignal = async () => {
    OneSignal.setAppId(ONESIGNAL_APP_ID);
    OneSignal.setLogLevel(6, 0);
    OneSignal.setRequiresUserPrivacyConsent(false);
    if(Platform.OS !== 'ios'){
      OneSignal.clearOneSignalNotifications();
    }
  };

  const linking = {
    prefixes: ['curycorretor://'],
    config: {
      screens: {
        AuthLogin: {
          path: 'login',
        },
        PrivateMain: {
          path: 'home/:area?',
        },
        RegisterDisapproved: {
          path: 'reprovado',
        },
        RhList: {
          path: 'rh',
        },
        TrainingList: {
          path: 'academia',
        },
        AboutTrainingStart: {
          path: 'treinamento/:id',
        },
        PrivateNotification: {
          path: 'notificacao/:id',
        },
        ContasEmPreenchimento: {
          path: 'conta/:id'
        },
        LeadsList: {
          path: 'leads'
        },
        LeadsContent: {
          path: 'lead/:id'
        },
        RepasseHome: {
          path: 'repasse'
        },
        CustomerList: {
          path: 'clientes'
        },
        PrivateShifts: {
          path: 'plantoes'
        },
        SchedulingList: {
          path: 'atendimentos'
        },
        ContasMain: {
          path: 'ficha'
        },
        // IAList: {
        //   path: 'woxai'
        // },
        PaySearch: {
          path: 'pagadoria'
        },
        FilesList: {
          path: 'arquivos'
        },
        BankInitAccount: {
          path: 'cury-bank'
        }
      },
    },
  };

  const ConfirmarPresencaAlerts = () => {
    const [loading, setLoading] = useState(false);

    const { getUser, confirmarPresenca, triggerConfirmarPresenca } = useAuth();
    const { queue, exit } = useService();

    const [success, setSuccess] = useState(false);
    const [error, setError] = useState('');

    const [showModalConfirmarPresenca, setShowModalConfirmarPresenca] = useState(false);
    const [showModalSairFila, setShowModalSairFila] = useState(false);

    useEffect(() => {
      if(confirmarPresenca && !queue.atendimento_ativo && !queue.atendimento_disponivel){
        setShowModalConfirmarPresenca(true);
      } else {
        setShowModalConfirmarPresenca(false);
      }
    }, [confirmarPresenca, queue.atendimento_ativo, queue.atendimento_disponive, triggerConfirmarPresenca]);

    const sairFila = async () => {
      setLoading(true);

      const res = await exit();

      if(res){
        getUser();
        setShowModalSairFila(false);
        setLoading(false);
      } else {
        Alert.alert('Não foi possível sair da fila', 'Por favor, tente novamente');
        setLoading(false);
      }
    }
    
    const enviarConfirmacaoPresenca = async () => {
      setLoading(true);
      
      const geolocation = await GetLocation.getCurrentPosition({
          enableHighAccuracy: true,
          timeout: 15000,
        }).then(location => {
          return location;
        }).catch(error => {
          console.error(error);
          return false;
        });

      if(geolocation) {
        let data = new FormData();
        data.append('latitude', geolocation.latitude);
        data.append('longitude', geolocation.longitude);

        api.post('/checagem/confirmar', data).then(res => {
          setSuccess(true);
          setError('');
          setShowModalConfirmarPresenca(false);
          getUser();
        }).catch(err => {
          setSuccess(false);
          setError(err?.response?.data?.message ?? 'Não foi possível verificar');
          setShowModalConfirmarPresenca(false);
        }).then(() => setLoading(false));

      } else {
        Alert.alert(
          'Erro na localização',
          'Não foi possível obter sua localização',
        );
        setLoading(false);
      }
    }

    return (
      <>
        {showModalConfirmarPresenca && (
          <AlertConfirmarPresenca
            close={() => setShowModalConfirmarPresenca(false)}
            loading={loading}
            actionPositive={() => {
              enviarConfirmacaoPresenca();
            }}
            actionNegative={() => {
              setShowModalConfirmarPresenca(false);
              setShowModalSairFila(true);
            }}
          />
        )}
        {(success || error !== '') && (
          <AlertConfirmarPresencaResultado
            close={() => {
              if(!success){
                setShowModalConfirmarPresenca(true);
              }
              setSuccess(false);
              setError('');
            }}
            nomePlantao={queue.nome}
            success={success}
            error={error}
          />
        )}
        {showModalSairFila && (
          <AlertConfirmarPresencaSairFile
            close={() => {
              setShowModalSairFila(false);
              setShowModalConfirmarPresenca(true);
            }}
            action={sairFila}
            loading={loading}
          />
        )}
      </>
    );
  };

  return (
    <AuthProvider routeInfo={routeInfo}>
      <ContaProvider>
        <ServiceProvider navigationRef={navigationRef}>
          <RegisterProvider>
            <ClienteProvider>
              <TrainingProvider>
                <WoxAiProvider>
                  <BankProvider>
                    <NavigationContainer 
                      linking={linking}
                      ref={navigationRef}
                      onReady={() => {
                        const current = navigationRef.getCurrentRoute()?.name;
                        routeNameRef.current = current;
                        setRouteInfo({
                          current,
                          previous: null,
                        });
                      }}
                      onStateChange={() => {
                        const current = navigationRef.getCurrentRoute()?.name;
                        setRouteInfo(prev => ({
                          current,
                          previous: prev.current,
                        }));
                        routeNameRef.current = current;
                      }}
                    >
                      <Routes />
                      <ConfirmarPresencaAlerts />
                    </NavigationContainer>
                  </BankProvider>
                </WoxAiProvider>
              </TrainingProvider>
            </ClienteProvider>
          </RegisterProvider>
        </ServiceProvider>
      </ContaProvider>
    </AuthProvider>
  );
};

export default App;
