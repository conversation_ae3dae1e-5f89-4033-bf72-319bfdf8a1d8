import React from 'react';
import Svg, { Path } from 'react-native-svg';

import { getSizesByWidth } from './calculator';
  
const Place2 = (props) => {
    const originalWidth = 33;
    const originalHeight = 33;
    const newWidth = props?.style?.width ? props.style.width : 45;

    const sizes = getSizesByWidth(originalWidth, originalHeight, newWidth);

    return (
        <Svg style={[{width: sizes.width, height: sizes.height}, props.style]} viewBox="0 0 33 33" fill="none" xmlns="http://www.w3.org/2000/svg">
			<Path d="M28.875 13.75C28.875 23.375 16.5 31.625 16.5 31.625C16.5 31.625 4.125 23.375 4.125 13.75C4.125 10.4679 5.42879 7.32032 7.74955 4.99955C10.0703 2.67879 13.2179 1.375 16.5 1.375C19.7821 1.375 22.9297 2.67879 25.2504 4.99955C27.5712 7.32032 28.875 10.4679 28.875 13.75Z" stroke="#00467F" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
			<Path d="M16.5 17.875C18.7782 17.875 20.625 16.0282 20.625 13.75C20.625 11.4718 18.7782 9.625 16.5 9.625C14.2218 9.625 12.375 11.4718 12.375 13.75C12.375 16.0282 14.2218 17.875 16.5 17.875Z" stroke="#00467F" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
		</Svg>

    );
}

export default Place2;