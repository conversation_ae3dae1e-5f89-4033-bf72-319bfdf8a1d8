import React from 'react';
import Svg, { Path } from 'react-native-svg';

import { getSizesByWidth } from './calculator';
  
const Badge = (props) => {
    const originalWidth = 26;
    const originalHeight = 45;
    const newWidth = props?.style?.width ? props.style.width : 32;

    const sizes = getSizesByWidth(originalWidth, originalHeight, newWidth);

    return (
        <Svg style={[{width: sizes.width, height: sizes.height}, props.style]} viewBox="0 0 26 45" fill="none" xmlns="http://www.w3.org/2000/svg">
			<Path d="M24.0502 7.32396H16.2503V3.18025C16.2503 2.41081 15.9251 1.64096 15.2753 1.10818C14.6254 0.575402 13.8454 0.220215 13.0001 0.220215C11.1803 0.220215 9.74998 1.52243 9.74998 3.18025V7.32396H1.95C0.844849 7.32396 0 8.0934 0 9.0999V42.2511C0 43.2576 0.844849 44.027 1.95 44.027H24.0496C25.1547 44.027 25.9996 43.2576 25.9996 42.2511L26 9.0999C26 8.0934 25.1547 7.32396 24.05 7.32396H24.0502ZM9.74953 10.4612V10.5797V10.7573C9.74953 10.8164 9.81438 10.8758 9.81438 10.9349C9.81438 10.994 9.87924 11.0534 9.87924 11.0534C9.87924 11.1125 9.94409 11.172 9.94409 11.231C9.94409 11.2901 10.0089 11.2901 10.0089 11.3496C10.0738 11.4086 10.0738 11.4681 10.1391 11.4681C10.204 11.5272 10.204 11.5272 10.2692 11.5866C10.3341 11.6457 10.3994 11.6457 10.3994 11.7052C10.4642 11.7052 10.4642 11.7642 10.5295 11.7642C10.5944 11.8233 10.6597 11.8233 10.7245 11.8827C10.7894 11.8827 10.7894 11.9418 10.8547 11.9418C10.9195 11.9418 10.9848 12.0009 11.1145 12.0009C11.1794 12.0009 11.1794 12.0009 11.2447 12.0599C11.3748 12.0599 11.5045 12.119 11.6347 12.119H14.2998C14.43 12.119 14.5597 12.119 14.6898 12.0599C14.7547 12.0599 14.7547 12.0599 14.82 12.0009C14.8848 12.0009 14.9501 11.9418 15.0798 11.9418C15.1447 11.9418 15.1447 11.8827 15.21 11.8827C15.2748 11.8237 15.3401 11.8237 15.405 11.7642C15.4698 11.7642 15.4698 11.7052 15.5351 11.7052C15.6 11.6461 15.6653 11.6461 15.6653 11.5866L15.7954 11.4681C15.8603 11.409 15.8603 11.3496 15.9256 11.3496C15.9256 11.2905 15.9904 11.2905 15.9904 11.231C16.0553 11.172 16.0553 11.1125 16.0553 11.0534C16.0553 10.9944 16.1201 10.9349 16.1201 10.9349C16.1201 10.8758 16.185 10.8164 16.185 10.7573V10.5797V10.4612C16.575 10.6388 16.8348 11.053 16.8348 11.4677C16.8348 11.7638 16.7047 12.0595 16.4448 12.2966C16.1846 12.5332 15.9247 12.6518 15.5996 12.6518H10.3994C9.68427 12.6518 9.09927 12.119 9.09927 11.4677C9.09927 11.1716 9.22942 10.8758 9.48927 10.6388C9.55456 10.5797 9.6847 10.4612 9.74956 10.4612H9.74953ZM11.0497 3.17984C11.0497 2.17334 11.8945 1.4039 12.9997 1.4039C13.5198 1.4039 14.0395 1.5815 14.3647 1.93668C14.7547 2.29187 14.9497 2.70612 14.9497 3.17984V10.2836C14.9497 10.6388 14.6898 10.8754 14.2998 10.8754H12.0247C11.5045 10.8754 11.0497 10.4612 11.0497 9.98746V3.17984ZM24.6996 42.2505C24.6996 42.6056 24.4398 42.8423 24.0498 42.8423L1.9491 42.8427C1.55911 42.8427 1.29925 42.606 1.29925 42.2509V9.09969C1.29925 8.7445 1.55911 8.50784 1.9491 8.50784H9.74908V9.15916C9.29423 9.27769 8.90423 9.45528 8.57909 9.751C8.05937 10.2243 7.79909 10.8162 7.79909 11.4675C7.79909 12.7697 8.96909 13.8353 10.3989 13.8353H15.5991C16.3142 13.8353 16.9641 13.5986 17.4189 13.1249C17.9391 12.6512 18.1989 12.0593 18.1989 11.4675C18.1989 10.3428 17.3541 9.45488 16.2489 9.15875L16.2494 8.50744H24.0493C24.4393 8.50744 24.6992 8.7441 24.6992 9.09928L24.6996 42.2505Z" fill="#00467F"/>
			<Path d="M9.09992 30.4111C8.70992 30.4111 8.45007 30.6478 8.45007 31.003C8.45007 31.3582 8.70992 31.5948 9.09992 31.5948H16.8999C17.2899 31.5948 17.5498 31.3582 17.5498 31.003C17.5498 30.6478 17.2899 30.4111 16.8999 30.4111H9.09992Z" fill="#00467F"/>
			<Path d="M18.8499 33.9634H7.14991C6.75991 33.9634 6.50006 34.2 6.50006 34.5552C6.50006 34.9104 6.75991 35.1471 7.14991 35.1471H18.8499C19.2399 35.1471 19.4997 34.9104 19.4997 34.5552C19.4997 34.2 19.2399 33.9634 18.8499 33.9634Z" fill="#00467F"/>
			<Path d="M8.45022 28.0424H17.5503C17.9403 28.0424 18.2002 27.8058 18.2002 27.4506V26.2665C18.2002 24.6091 17.0951 23.1289 15.4702 22.478C16.3151 21.8267 16.9001 20.8206 16.9001 19.755C16.9001 17.8015 15.1451 16.2031 13.0001 16.2031C10.8551 16.2031 9.10007 17.8015 9.10007 19.755C9.10007 20.8206 9.62022 21.8271 10.5299 22.478C8.90507 23.1293 7.79993 24.6091 7.79993 26.2665V27.4506C7.79993 27.8058 8.06021 28.0424 8.45021 28.0424H8.45022ZM13.0001 17.3868C14.4299 17.3868 15.5999 18.4524 15.5999 19.7546C15.5999 21.0568 14.4299 22.1224 13.0001 22.1224C11.5702 22.1224 10.4002 21.0568 10.4002 19.7546C10.4002 18.4524 11.5702 17.3868 13.0001 17.3868ZM9.10007 26.2665C9.10007 24.6091 10.5299 23.3065 12.3502 23.3065H13.6504C15.4702 23.3065 16.9005 24.6087 16.9005 26.2665V26.8583L9.10007 26.8587V26.2665Z" fill="#00467F"/>
		</Svg>

    );
}

export default Badge;