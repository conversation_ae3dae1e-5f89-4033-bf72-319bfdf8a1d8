import React from 'react';
import Svg, { Path } from 'react-native-svg';

import { getSizesByWidth } from './calculator';
  
const Pix = (props) => {
    const originalWidth = 24;
    const originalHeight = 24;
    const newWidth = props?.style?.width ? props.style.width : 26;

    const sizes = getSizesByWidth(originalWidth, originalHeight, newWidth);

    return (
    <Svg style={[{width: sizes.width, height: sizes.height}, props.style]} viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
      <Path d="M12 0.0234375C11.004 0.0234375 10.0085 0.401533 9.25124 1.15875L1.1578 9.25125C-0.356636 10.7657 -0.356636 13.2353 1.1578 14.7497L9.25124 22.8431C10.7653 24.3572 13.2347 24.3572 14.7487 22.8431L22.8422 14.7506C24.3566 13.2362 24.3566 10.7666 22.8422 9.25219L14.7497 1.15875C13.9925 0.401533 12.996 0.0234375 12 0.0234375ZM12 0.976875C12.7483 0.976875 13.4971 1.26368 14.0709 1.8375L18.4734 6.24094H17.7937C17.0301 6.24094 16.2976 6.54425 15.7575 7.08375V7.08469L12.509 10.3331C12.2241 10.6181 11.7755 10.6184 11.49 10.3331L8.24155 7.08469C7.7022 6.54476 6.96989 6.24094 6.20624 6.24094H5.52561L9.92999 1.8375C10.5038 1.26368 11.2517 0.976875 12 0.976875ZM4.56561 7.20094H6.20624C6.71554 7.20094 7.20359 7.40291 7.5628 7.7625V7.76344L10.8112 11.0119C11.4636 11.6636 12.5358 11.6639 13.1878 11.0119L16.4362 7.76344C16.7966 7.4034 17.2844 7.20094 17.7937 7.20094H19.4334L22.1634 9.93094C23.3111 11.0786 23.3111 12.9242 22.1634 14.0719L19.4344 16.8009H17.7937C17.2844 16.8009 16.7966 16.5985 16.4362 16.2384L13.1878 12.99C12.8618 12.664 12.4309 12.5016 12 12.5016C11.569 12.5016 11.1374 12.6642 10.8112 12.99L7.5628 16.2384V16.2394C7.20359 16.5989 6.71554 16.8009 6.20624 16.8009H4.56655L1.83655 14.0709C0.688902 12.9233 0.688902 11.0776 1.83655 9.93L4.56561 7.20094ZM12 13.455C12.1834 13.455 12.3666 13.5263 12.509 13.6687L15.7575 16.9172V16.9181C16.2976 17.4576 17.0301 17.7609 17.7937 17.7609H18.4744L14.07 22.1644C12.9229 23.3114 11.077 23.3114 9.92999 22.1644L5.52655 17.7609H6.20624C6.96989 17.7609 7.7022 17.4571 8.24155 16.9172L11.49 13.6687C11.6328 13.5261 11.8165 13.455 12 13.455Z" fill="white"/>
    </Svg>    
    );
}

export default Pix;