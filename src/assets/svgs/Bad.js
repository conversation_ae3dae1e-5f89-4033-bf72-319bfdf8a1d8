import React from 'react';
import Svg, { Path } from 'react-native-svg';

import { getSizesByWidth } from './calculator';
  
const Bad = (props) => {
    const originalWidth = 50;
    const originalHeight = 50;
    const newWidth = props?.style?.width ? props.style.width : 75;

    const sizes = getSizesByWidth(originalWidth, originalHeight, newWidth);

    return (
        <Svg style={[{width: sizes.width, height: sizes.height}, props.style]} viewBox="0 0 50 50" fill="none" xmlns="http://www.w3.org/2000/svg">
			<Path d="M31.4283 28.7699L21.3024 32.3949C20.7703 32.588 20.3227 32.9617 20.038 33.451C19.7532 33.9402 19.6492 34.5139 19.7441 35.072C19.839 35.6301 20.1269 36.1371 20.5574 36.5047C20.9879 36.8723 21.5339 37.077 22.0999 37.0833C22.3802 37.0829 22.6582 37.0339 22.9216 36.9383L33.0716 33.3133C33.3784 33.212 33.6618 33.0502 33.9051 32.8375C34.1483 32.6248 34.3464 32.3655 34.4877 32.0749C34.6289 31.7843 34.7105 31.4683 34.7275 31.1457C34.7445 30.823 34.6967 30.5002 34.5868 30.1963C34.4769 29.8925 34.3072 29.6138 34.0877 29.3767C33.8682 29.1395 33.6034 28.9488 33.3089 28.8159C33.0144 28.6829 32.6962 28.6103 32.3732 28.6024C32.0502 28.5945 31.7289 28.6515 31.4283 28.7699ZM21.7616 21.5199C22.2117 21.0671 22.4643 20.4546 22.4643 19.8162C22.4643 19.1777 22.2117 18.5652 21.7616 18.1124C20.3786 16.8001 18.5448 16.0686 16.6383 16.0686C14.7318 16.0686 12.8979 16.8001 11.5149 18.1124C11.2619 18.3291 11.0565 18.5957 10.9114 18.8955C10.7664 19.1953 10.6849 19.5219 10.672 19.8547C10.6591 20.1875 10.7152 20.5194 10.8367 20.8295C10.9582 21.1397 11.1425 21.4213 11.378 21.6568C11.6135 21.8923 11.8952 22.0766 12.2053 22.1981C12.5154 22.3196 12.8473 22.3757 13.1801 22.3628C13.513 22.35 13.8395 22.2685 14.1394 22.1234C14.4392 21.9784 14.7058 21.7729 14.9224 21.5199C15.1471 21.2934 15.4144 21.1136 15.7089 20.9909C16.0034 20.8682 16.3192 20.8051 16.6383 20.8051C16.9573 20.8051 17.2732 20.8682 17.5677 20.9909C17.8622 21.1136 18.1294 21.2934 18.3541 21.5199C18.8069 21.97 19.4194 22.2227 20.0578 22.2227C20.6963 22.2227 21.3088 21.97 21.7616 21.5199ZM38.6783 18.1124C37.2953 16.8001 35.4614 16.0686 33.5549 16.0686C31.6484 16.0686 29.8146 16.8001 28.4316 18.1124C28.0357 18.5747 27.8288 19.1694 27.8523 19.7776C27.8758 20.3858 28.1279 20.9628 28.5583 21.3932C28.9887 21.8236 29.5657 22.0757 30.1739 22.0992C30.7821 22.1227 31.3768 21.9158 31.8391 21.5199C32.0638 21.2934 32.331 21.1136 32.6255 20.9909C32.92 20.8682 33.2359 20.8051 33.5549 20.8051C33.874 20.8051 34.1898 20.8682 34.4843 20.9909C34.7788 21.1136 35.0461 21.2934 35.2708 21.5199C35.7236 21.97 36.3361 22.2227 36.9745 22.2227C37.613 22.2227 38.2255 21.97 38.6783 21.5199C38.9023 21.2885 39.0777 21.0146 39.1944 20.7144C39.311 20.4141 39.3665 20.0936 39.3575 19.7717C39.3484 19.4497 39.2752 19.1328 39.1419 18.8395C39.0087 18.5463 38.8182 18.2826 38.5816 18.0641L38.6783 18.1124ZM24.9999 0.833252C20.2202 0.833252 15.5478 2.2506 11.5736 4.90607C7.59946 7.56154 4.50196 11.3359 2.67284 15.7517C0.843723 20.1676 0.365142 25.0267 1.29762 29.7146C2.23009 34.4025 4.53175 38.7086 7.91152 42.0883C11.2913 45.4681 15.5974 47.7698 20.2852 48.7022C24.9731 49.6347 29.8322 49.1561 34.2481 47.327C38.664 45.4979 42.4383 42.4004 45.0938 38.4262C47.7492 34.452 49.1666 29.7796 49.1666 24.9999C49.1666 21.8263 48.5415 18.6838 47.327 15.7517C46.1125 12.8197 44.3324 10.1556 42.0883 7.9115C39.8443 5.66742 37.1801 3.88732 34.2481 2.67283C31.3161 1.45834 28.1735 0.833252 24.9999 0.833252ZM24.9999 44.3333C21.1762 44.3333 17.4383 43.1994 14.2589 41.075C11.0796 38.9506 8.60155 35.9312 7.13826 32.3985C5.67496 28.8658 5.2921 24.9785 6.03808 21.2282C6.78406 17.4779 8.62538 14.033 11.3292 11.3292C14.033 8.62537 17.4779 6.78405 21.2282 6.03807C24.9785 5.29209 28.8658 5.67495 32.3985 7.13825C35.9312 8.60154 38.9506 11.0795 41.075 14.2589C43.1994 17.4382 44.3333 21.1761 44.3333 24.9999C44.3333 30.1274 42.2964 35.0449 38.6707 38.6707C35.045 42.2964 30.1275 44.3333 24.9999 44.3333Z" fill="white"/>
		</Svg>

    );
}

export default Bad;