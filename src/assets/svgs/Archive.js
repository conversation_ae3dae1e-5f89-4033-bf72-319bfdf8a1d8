import React from 'react';
import Svg, { Path } from 'react-native-svg';

import { getSizesByWidth } from './calculator';
  
const Archive = (props) => {
    const originalWidth = 31;
    const originalHeight = 31;
    const newWidth = props?.style?.width ? props.style.width : 42;

    const sizes = getSizesByWidth(originalWidth, originalHeight, newWidth);

    return (
        <Svg style={[{width: sizes.width, height: sizes.height}, props.style]} viewBox="0 0 31 31" fill="none" xmlns="http://www.w3.org/2000/svg">
            <Path d="M16.7917 2.58325H7.75C7.06486 2.58325 6.40778 2.85542 5.92331 3.33989C5.43884 3.82436 5.16667 4.48144 5.16667 5.16659V25.8333C5.16667 26.5184 5.43884 27.1755 5.92331 27.6599C6.40778 28.1444 7.06486 28.4166 7.75 28.4166H23.25C23.9351 28.4166 24.5922 28.1444 25.0767 27.6599C25.5612 27.1755 25.8333 26.5184 25.8333 25.8333V11.6249L16.7917 2.58325Z" stroke="#00467F" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
            <Path d="M16.7917 2.58325V11.6249H25.8333" stroke="#00467F" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
        </Svg>
        
    );
}

export default Archive;