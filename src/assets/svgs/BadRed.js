import React from 'react';
import Svg, { Path } from 'react-native-svg';

import { getSizesByWidth } from './calculator';
  
const BadRed = (props) => {
    const originalWidth = 50;
    const originalHeight = 50;
    const newWidth = props?.style?.width ? props.style.width : 85;

    const sizes = getSizesByWidth(originalWidth, originalHeight, newWidth);

    return (
        <Svg style={[{width: sizes.width, height: sizes.height}, props.style]} viewBox="0 0 60 60" fill="none" xmlns="http://www.w3.org/2000/svg">
			<Path d="M30 60C13.4304 60 0 46.5696 0 30C0 13.4304 13.4304 0 30 0C46.5696 0 60 13.4304 60 30C60 46.5696 46.5696 60 30 60ZM30 57.6C45.2421 57.6 57.6 45.2422 57.6 30C57.6 14.7578 45.2422 2.4 30 2.4C14.7578 2.4 2.4 14.7578 2.4 30C2.4 45.2422 14.7578 57.6 30 57.6ZM19.2 26.4C20.5248 26.4 21.6 25.3248 21.6 24C21.6 22.6752 20.5248 21.6 19.2 21.6C17.8752 21.6 16.8 22.6752 16.8 24C16.8 25.3248 17.8752 26.4 19.2 26.4ZM40.8 26.4C42.1248 26.4 43.2 25.3248 43.2 24C43.2 22.6752 42.1248 21.6 40.8 21.6C39.4752 21.6 38.4 22.6752 38.4 24C38.4 25.3248 39.4752 26.4 40.8 26.4ZM29.993 43.2C22.8 43.2 19.2 45.6 19.2 45.6C19.2 45.6 21.6 38.4 30 38.4C38.4 38.4 40.8 45.6 40.8 45.6C40.8 45.6 37.1856 43.2 29.993 43.2ZM39.8931 14.3205L38.5517 16.3101L46.5102 21.679L47.8516 19.6893L39.8931 14.3205ZM12.1491 19.6893L13.4905 21.679L21.449 16.3101L20.1076 14.3205L12.1491 19.6893Z" fill="#FF6542"/>
		</Svg>
    );
}

export default BadRed;