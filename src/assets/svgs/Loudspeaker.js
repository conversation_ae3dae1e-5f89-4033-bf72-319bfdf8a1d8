import React from 'react';
import Svg, { Path } from 'react-native-svg';

import { getSizesByWidth } from './calculator';
  
const Loudspeaker = (props) => {
    const originalWidth = 72;
    const originalHeight = 76;
    const newWidth = props?.style?.width ? props.style.width : 72;

    const sizes = getSizesByWidth(originalWidth, originalHeight, newWidth);

    return (
        <Svg width="72" height="76" viewBox="0 0 72 76" fill="none" xmlns="http://www.w3.org/2000/svg">
			<Path d="M51.963 1.18688C51.5665 1.29289 51.1947 1.47601 50.869 1.72578C50.5433 1.97555 50.2701 2.28708 50.0648 2.64253C49.8596 2.99799 49.7265 3.39041 49.673 3.79736C49.6196 4.20431 49.6468 4.61781 49.7533 5.01421L50.2683 6.93616C48.5683 10.7748 46.0337 14.186 42.8491 16.9216C39.6645 19.6572 35.91 21.6483 31.8589 22.75L13.7478 27.6028C11.3476 28.2487 9.30169 29.8204 8.05891 31.973C6.81612 34.1256 6.47798 36.6832 7.11867 39.0848L8.73629 45.1218C9.38222 47.522 10.9539 49.5679 13.1065 50.8107C15.259 52.0535 17.8167 52.3916 20.2183 51.751L21.6786 51.3597L18.9604 70.226C18.8863 70.7378 18.9405 71.26 19.118 71.7457C19.2956 72.2315 19.591 72.6655 19.9777 73.0088C20.3645 73.3522 20.8304 73.5941 21.3337 73.7129C21.837 73.8317 22.362 73.8236 22.8614 73.6894L34.9355 70.4542C35.5265 70.2962 36.058 69.968 36.464 69.5104C36.8699 69.0528 37.1324 68.4859 37.2189 67.8803L40.3001 46.493C44.0879 45.7704 47.9836 45.8319 51.7467 46.6738C55.5098 47.5157 59.0603 49.1201 62.1791 51.3879L62.6942 53.3105C62.9088 54.1111 63.4325 54.7936 64.1503 55.208C64.868 55.6224 65.721 55.7347 66.5216 55.5202C67.3221 55.3057 68.0047 54.782 68.4191 54.0642C68.8335 53.3464 68.9458 52.4934 68.7313 51.6929L55.7903 3.39659C55.6843 3.00007 55.5012 2.62833 55.2514 2.30263C55.0017 1.97692 54.6901 1.70365 54.3347 1.49842C53.9792 1.2932 53.5868 1.16005 53.1799 1.10659C52.7729 1.05314 52.3594 1.08042 51.963 1.18688ZM18.6007 45.7139C17.8001 45.9278 16.9474 45.8153 16.2298 45.401C15.5122 44.9867 14.9883 44.3045 14.7733 43.5042L13.1557 37.4672C12.9418 36.6666 13.0543 35.8139 13.4687 35.0963C13.883 34.3787 14.5652 33.8549 15.3654 33.6398L18.3839 32.831L21.6192 44.9051L18.6007 45.7139ZM31.3281 64.9503L25.8217 66.4257L28.2451 49.6002L33.7515 48.1247L31.3281 64.9503ZM59.881 42.8115C52.6155 39.503 44.4282 38.8138 36.7117 40.8609L27.6562 43.2873L24.4209 31.2133L33.4765 28.7868C41.1827 26.7008 47.9283 22.0098 52.566 15.5114L59.881 42.8115Z" fill="white"/>
		</Svg>



    );
}

export default Loudspeaker;