import React from 'react';
import Svg, { Path } from 'react-native-svg';

import { getSizesByWidth } from './calculator';
  
const GA = (props) => {
    const originalWidth = 36;
    const originalHeight = 35;
    const newWidth = props?.style?.width ? props.style.width : 30;

    const sizes = getSizesByWidth(originalWidth, originalHeight, newWidth);

    return (
        <Svg style={[{width: sizes.width, height: sizes.height}, props.style]} viewBox="0 0 36 35" fill="none" xmlns="http://www.w3.org/2000/svg">
            <Path d="M18.347 14.3182V21.0955H27.6976C27.287 23.275 26.0549 25.1205 24.2069 26.3614L29.8456 30.7682C33.131 27.7138 35.0264 23.2274 35.0264 17.8979C35.0264 16.6571 34.9158 15.4638 34.7105 14.3184L18.347 14.3182Z" fill="white" fill-opacity="0.9"/>
            <Path d="M8.60993 20.8311L7.33817 21.8116L2.83655 25.3433C5.69542 31.0546 11.5549 35.0001 18.3467 35.0001C23.0376 35.0001 26.9705 33.441 29.8453 30.7684L24.2065 26.3615C22.6586 27.4115 20.6842 28.048 18.3467 28.048C13.8293 28.048 9.99129 24.9775 8.61703 20.8412L8.60993 20.8311Z" fill="white" fill-opacity="0.7"/>
            <Path d="M2.83669 9.6571C1.65213 12.0115 0.973022 14.6684 0.973022 17.5001C0.973022 20.3319 1.65213 22.9887 2.83669 25.3432C2.83669 25.359 8.61773 20.825 8.61773 20.825C8.27024 19.775 8.06485 18.6615 8.06485 17.4999C8.06485 16.3384 8.27024 15.2249 8.61773 14.1749L2.83669 9.6571Z" fill="white"/>
            <Path d="M18.347 6.96838C20.9058 6.96838 23.1803 7.85926 24.9967 9.57745L29.972 4.56618C26.9552 1.73443 23.0382 0.000244141 18.347 0.000244141C11.5553 0.000244141 5.69542 3.92976 2.83655 9.65701L8.61741 14.1752C9.99149 10.0388 13.8297 6.96838 18.347 6.96838Z" fill="white" fill-opacity="0.4"/>
        </Svg>

    );
}

export default GA;