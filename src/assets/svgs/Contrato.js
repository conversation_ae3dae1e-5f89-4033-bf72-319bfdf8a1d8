import React from 'react';
import Svg, { Path } from 'react-native-svg';

import { getSizesByWidth } from './calculator';
  
const Contrato = (props) => {
    const originalWidth = 31;
    const originalHeight = 31;
    const newWidth = props?.style?.width ? props.style.width : 40;

    const sizes = getSizesByWidth(originalWidth, originalHeight, newWidth);

    return (
        <Svg style={[{width: sizes.width, height: sizes.height}, props.style]} viewBox="0 0 31 31" fill="none" xmlns="http://www.w3.org/2000/svg">
            <Path d="M0.536616 0.000328562C0.394174 -0.000148638 0.257217 0.0561644 0.156524 0.15686C0.0558376 0.257792 -0.000473437 0.39452 2.99882e-06 0.536977V30.4676C0.00119598 30.7628 0.241464 31.0012 0.536616 31H22.6384C22.9318 30.9988 23.1695 30.7611 23.1707 30.4676V19.4613L25.6948 16.9348C25.7132 16.9188 25.7306 16.9014 25.7468 16.8828C25.7654 16.8666 25.7829 16.8492 25.7991 16.8306L30.2125 12.4209C31.2625 11.3708 31.2625 9.65557 30.2125 8.60521C29.6875 8.08001 28.9944 7.81611 28.3032 7.81611C27.6122 7.81611 26.9222 8.08025 26.397 8.60521L23.1707 11.8301V7.08955C23.1702 7.02847 23.1592 6.9681 23.1382 6.91107C23.1206 6.86096 23.0958 6.81371 23.0643 6.771L23.0631 6.77005C23.0571 6.76193 23.0509 6.75382 23.0442 6.74595C23.0433 6.74451 23.0423 6.74308 23.0414 6.74189C23.0323 6.73139 23.023 6.72113 23.0132 6.71159L16.464 0.157723C16.3638 0.0567911 16.2271 0 16.0849 0L0.536616 0.000328562ZM1.06893 1.06932H15.5483V7.08985C15.5485 7.23182 15.6051 7.36784 15.7058 7.46782C15.8065 7.5678 15.9427 7.62387 16.0847 7.62316H22.1017V12.8982L21.9607 13.0392C21.9507 13.0483 21.9409 13.0576 21.9316 13.0674L15.6506 19.3531C15.5945 19.4087 15.5513 19.476 15.5243 19.5504L13.8124 24.3244C13.7432 24.5186 13.7921 24.7355 13.9382 24.8813C14.0839 25.0273 14.3006 25.0762 14.495 25.007L19.2687 23.2907C19.3427 23.2649 19.4099 23.2234 19.466 23.1687L22.1018 20.5306V29.931H1.06899L1.06893 1.06932ZM16.6172 1.82715L18.9827 4.18969L21.3439 6.55315H16.6172V1.82715ZM28.3034 8.87581C28.7183 8.87581 29.1337 9.03687 29.4558 9.35924C30.1003 10.0038 30.1003 11.0188 29.4558 11.6633L28.3608 12.7583L26.0569 10.4543L27.1509 9.35924C27.473 9.03712 27.8884 8.87581 28.3034 8.87581ZM5.01785 10.9136C4.72748 10.935 4.50748 11.1846 4.52252 11.4753C4.53755 11.7659 4.78211 11.9914 5.07296 11.9826H18.0962C18.24 11.9859 18.3789 11.931 18.4817 11.8303C18.5846 11.7299 18.6426 11.5919 18.6426 11.4481C18.6426 11.3044 18.5846 11.1665 18.4817 11.066C18.3789 10.9653 18.24 10.9105 18.0962 10.9136H5.07296C5.05459 10.9126 5.03622 10.9126 5.01785 10.9136ZM25.3043 11.208L27.606 13.514L25.0433 16.0788C25.0254 16.0944 25.0087 16.1113 24.9931 16.1292C24.9741 16.1457 24.9562 16.1636 24.9397 16.1824L18.7943 22.3282L15.2002 23.6186L16.4904 20.0244L22.6724 13.8413L22.6727 13.8411C22.6784 13.8361 22.6839 13.8306 22.6894 13.8253L25.3043 11.208ZM5.01968 15.2847C5.01897 15.285 5.01825 15.2852 5.01777 15.2857C4.7274 15.3072 4.50741 15.5565 4.52244 15.8471C4.53747 16.138 4.78204 16.3633 5.07289 16.3547H15.1611C15.305 16.3578 15.4441 16.3029 15.5469 16.2024C15.6498 16.1018 15.7077 15.9641 15.7077 15.8202C15.7077 15.6763 15.6498 15.5386 15.5469 15.4379C15.4441 15.3375 15.305 15.2826 15.1611 15.2857H5.07289C5.05523 15.2845 5.03758 15.284 5.01968 15.2847ZM5.01777 19.6566C4.72263 19.6724 4.49595 19.9241 4.51123 20.2193C4.5265 20.5144 4.77774 20.7416 5.07289 20.7266H12.5377C12.6796 20.7268 12.8159 20.6705 12.9166 20.5703C13.017 20.4698 13.0736 20.3336 13.0736 20.1916C13.0736 20.0496 13.017 19.9134 12.9166 19.8132C12.8159 19.7127 12.6796 19.6564 12.5377 19.6566H5.07289C5.05452 19.6557 5.03615 19.6557 5.01777 19.6566ZM11.2395 23.7009C11.1002 23.699 10.9659 23.7513 10.8647 23.8469L9.17256 25.3901L7.48353 23.8469C7.27929 23.6603 6.96649 23.6603 6.76222 23.8469L4.7141 25.7177C4.59933 25.8103 4.52775 25.9458 4.5163 26.0928C4.50484 26.2398 4.55471 26.3851 4.65397 26.4941C4.75299 26.6032 4.89305 26.6664 5.04026 26.6688C5.18772 26.6712 5.32968 26.6127 5.43228 26.507L7.12442 24.9641L8.81345 26.507C9.01793 26.6943 9.33145 26.6943 9.53592 26.507L11.5829 24.6363C11.7482 24.4905 11.8072 24.2581 11.7311 24.051C11.6552 23.8441 11.4598 23.705 11.2395 23.7009Z" fill="#828282"/>
        </Svg>

    );
}

export default Contrato;