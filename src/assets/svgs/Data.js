import React from 'react';
import Svg, { Path } from 'react-native-svg';

import { getSizesByWidth } from './calculator';
  
const Data = (props) => {
    const originalWidth = 29;
    const originalHeight = 35;
    const newWidth = props?.style?.width ? props.style.width : 39;
    const color = props?.style?.color ? props.style.color : "#00467F";

    const sizes = getSizesByWidth(originalWidth, originalHeight, newWidth);

    return (
      <Svg style={[{width: sizes.width, height: sizes.height}, props.style]} viewBox="0 0 29 35" fill="none" xmlns="http://www.w3.org/2000/svg">
			  <Path d="M3.05209 0C2.72522 0.00134707 2.46028 0.269677 2.45922 0.601039V4.94896L0.613885 4.94923C0.593423 4.94815 0.57296 4.94815 0.552232 4.94923C0.229086 4.97347 -0.0159331 5.255 0.000809244 5.58314C0.0175509 5.91154 0.28994 6.16586 0.613882 6.15616H2.45921V12.9134H0.613882C0.455765 12.9131 0.304022 12.9767 0.192146 13.0899C0.0802691 13.2033 0.0172865 13.3571 0.0172865 13.5174C0.0172865 13.6777 0.0802677 13.8315 0.192146 13.9449C0.304022 14.0581 0.455765 14.1217 0.613882 14.1214H2.45921V20.8786H0.613882C0.290471 20.8859 0.0316336 21.154 0.0316336 21.4821C0.0316336 21.8102 0.290471 22.0783 0.613882 22.0856H2.45894V28.8438H0.613882C0.290471 28.8511 0.0316336 29.1192 0.0316336 29.4473C0.0316336 29.7754 0.290471 30.0435 0.613882 30.0508H2.45894V34.3987C2.46001 34.7301 2.72522 34.9987 3.05208 35H28.4071C28.734 34.9987 28.9987 34.7303 29 34.399V0.601412C28.9987 0.270043 28.734 0.00171819 28.4071 0.000373325L3.05209 0ZM3.64976 1.20693H27.8088V33.7927H3.64976V30.0504H5.49135C5.65133 30.0542 5.80625 29.9919 5.92079 29.8785C6.03533 29.7648 6.0999 29.6094 6.0999 29.4469C6.0999 29.2845 6.03533 29.129 5.92079 29.0154C5.80625 28.9019 5.65133 28.84 5.49135 28.8435H3.64976V22.0852H5.49135C5.65133 22.0887 5.80625 22.0267 5.92079 21.9133C6.03533 21.7996 6.0999 21.6442 6.0999 21.4817C6.0999 21.3193 6.03533 21.1638 5.92079 21.0502C5.80625 20.9367 5.65133 20.8748 5.49135 20.8783H3.64976V14.121H5.49135C5.64946 14.1213 5.80121 14.0577 5.91308 13.9446C6.02496 13.8312 6.08794 13.6773 6.08794 13.517C6.08794 13.3567 6.02496 13.2029 5.91308 13.0895C5.80121 12.9763 5.64946 12.9128 5.49135 12.913H3.64976V6.1558H5.49135C5.65133 6.1593 5.80625 6.09733 5.92079 5.98391C6.03533 5.87023 6.0999 5.71478 6.0999 5.55233C6.0999 5.38988 6.03533 5.23443 5.92079 5.12074C5.80625 5.00733 5.65133 4.94509 5.49135 4.94886H3.64976V1.20693ZM15.7293 9.49269C13.4758 9.49269 11.6379 11.3548 11.6379 13.6391C11.6379 15.0398 12.3314 16.2809 13.3854 17.0336C10.8906 17.9927 9.11623 20.4297 9.11623 23.297V24.9058C9.11756 25.2375 9.38224 25.5058 9.7091 25.5072H21.7492C22.0763 25.5058 22.341 25.2375 22.3423 24.9058V23.297C22.3423 20.4303 20.5688 17.9929 18.0742 17.0336C19.1276 16.2809 19.8204 15.0395 19.8204 13.6391C19.8204 11.3545 17.9828 9.49269 15.7293 9.49269ZM15.7293 10.6996C17.3392 10.6996 18.6347 12.007 18.6347 13.6391C18.6347 15.2711 17.3392 16.5845 15.7293 16.5845C14.1194 16.5845 12.8298 15.2711 12.8298 13.6391C12.8298 12.007 14.1194 10.6996 15.7293 10.6996ZM15.7293 17.8005C18.7455 17.8005 21.1563 20.2389 21.1563 23.2966V24.2996L10.3068 24.2999V23.2969C10.3068 20.2391 12.7131 17.8005 15.7293 17.8005Z" fill={color} />
		  </Svg>
    );
}

export default Data;