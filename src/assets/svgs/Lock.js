import React from 'react';
import Svg, { Path } from 'react-native-svg';

import { getSizesByWidth } from './calculator';
  
const Lock = (props) => {
    const originalWidth = 52;
    const originalHeight = 66;
    const newWidth = props?.style?.width ? props.style.width : 52;
    const color = props?.style?.color ? props.style.color : "white";

    const sizes = getSizesByWidth(originalWidth, originalHeight, newWidth);

    return (
        <Svg style={[{width: sizes.width, height: sizes.height}, props.style]} viewBox="0 0 52 66" fill="none" xmlns="http://www.w3.org/2000/svg">
            <Path d="M26 36.25C25.138 36.25 24.3114 36.5924 23.7019 37.2019C23.0924 37.8114 22.75 38.638 22.75 39.5V49.25C22.75 50.112 23.0924 50.9386 23.7019 51.5481C24.3114 52.1576 25.138 52.5 26 52.5C26.862 52.5 27.6886 52.1576 28.2981 51.5481C28.9076 50.9386 29.25 50.112 29.25 49.25V39.5C29.25 38.638 28.9076 37.8114 28.2981 37.2019C27.6886 36.5924 26.862 36.25 26 36.25ZM42.25 23.25V16.75C42.25 12.4402 40.538 8.30698 37.4905 5.25951C34.443 2.21205 30.3098 0.5 26 0.5C21.6902 0.5 17.557 2.21205 14.5095 5.25951C11.462 8.30698 9.75 12.4402 9.75 16.75V23.25C7.16414 23.25 4.68419 24.2772 2.85571 26.1057C1.02723 27.9342 0 30.4141 0 33V55.75C0 58.3359 1.02723 60.8158 2.85571 62.6443C4.68419 64.4728 7.16414 65.5 9.75 65.5H42.25C44.8359 65.5 47.3158 64.4728 49.1443 62.6443C50.9728 60.8158 52 58.3359 52 55.75V33C52 30.4141 50.9728 27.9342 49.1443 26.1057C47.3158 24.2772 44.8359 23.25 42.25 23.25ZM16.25 16.75C16.25 14.1641 17.2772 11.6842 19.1057 9.85571C20.9342 8.02723 23.4141 7 26 7C28.5859 7 31.0658 8.02723 32.8943 9.85571C34.7228 11.6842 35.75 14.1641 35.75 16.75V23.25H16.25V16.75ZM45.5 55.75C45.5 56.612 45.1576 57.4386 44.5481 58.0481C43.9386 58.6576 43.112 59 42.25 59H9.75C8.88805 59 8.0614 58.6576 7.4519 58.0481C6.84241 57.4386 6.5 56.612 6.5 55.75V33C6.5 32.138 6.84241 31.3114 7.4519 30.7019C8.0614 30.0924 8.88805 29.75 9.75 29.75H42.25C43.112 29.75 43.9386 30.0924 44.5481 30.7019C45.1576 31.3114 45.5 32.138 45.5 33V55.75Z" fill={color}/>
        </Svg>
        
    );
}

export default Lock;