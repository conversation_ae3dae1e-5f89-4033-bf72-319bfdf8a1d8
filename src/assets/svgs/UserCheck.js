import React from 'react';
import Svg, { Path } from 'react-native-svg';

import { getSizesByWidth } from './calculator';
  
const UsersCheck = (props) => {
    const originalWidth = 43;
    const originalHeight = 39;
    const newWidth = props?.style?.width ? props.style.width : 45;

    const sizes = getSizesByWidth(originalWidth, originalHeight, newWidth);

    return (
        <Svg style={[{width: sizes.width, height: sizes.height}, props.style]} viewBox="0 0 43 39" fill="none" xmlns="http://www.w3.org/2000/svg">
            <Path d="M33.1666 37.125V33.2083C33.1666 31.1308 32.3414 29.1384 30.8723 27.6693C29.4033 26.2003 27.4108 25.375 25.3333 25.375H9.66665C7.58912 25.375 5.59668 26.2003 4.12764 27.6693C2.65861 29.1384 1.83331 31.1308 1.83331 33.2083V37.125" stroke="#00467F" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
            <Path d="M17.5 17.5417C21.8263 17.5417 25.3334 14.0346 25.3334 9.70833C25.3334 5.3821 21.8263 1.875 17.5 1.875C13.1738 1.875 9.66669 5.3821 9.66669 9.70833C9.66669 14.0346 13.1738 17.5417 17.5 17.5417Z" stroke="#00467F" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
            <Path fillRule="evenodd" clipRule="evenodd" d="M33.7414 14C38.5695 14 42.4829 17.9137 42.4829 22.7414C42.4829 27.5695 38.5691 31.4829 33.7414 31.4829C28.9134 31.4829 25 27.5691 25 22.7414C25 17.9134 28.9137 14 33.7414 14ZM29.4139 22.8556C29.0911 22.4987 29.119 21.9479 29.4754 21.6255C29.8323 21.3026 30.3832 21.3305 30.7055 21.6869L32.883 24.0929L37.5883 19.2326C37.9243 18.8872 38.4764 18.8795 38.8218 19.2156C39.1672 19.5516 39.1748 20.1037 38.8387 20.4491L33.4876 25.9764L33.4865 25.9753C33.4739 25.9884 33.4602 26.0015 33.4465 26.0136C33.0901 26.3365 32.5392 26.3091 32.2163 25.9522L29.4134 22.8555L29.4139 22.8556Z" fill="#1B9C20"/>
        </Svg>
    );
}

export default UsersCheck;