import React from 'react';
import Svg, { Path } from 'react-native-svg';

import { getSizesByWidth } from './calculator';
  
const EditRed = (props) => {
    const originalWidth = 34;
    const originalHeight = 34;
    const newWidth = props?.style?.width ? props.style.width : 34;
    const color = props?.style?.color ? props.style.color : "#FFFFFF";

    const sizes = getSizesByWidth(originalWidth, originalHeight, newWidth);

    return (
        <Svg style={[{width: sizes.width, height: sizes.height}, props.style]} viewBox="0 0 34 34" fill="none" xmlns="http://www.w3.org/2000/svg">
			<Path d="M15.5833 5.6665H5.66665C4.9152 5.6665 4.19453 5.96501 3.66318 6.49637C3.13182 7.02772 2.83331 7.74839 2.83331 8.49984V28.3332C2.83331 29.0846 3.13182 29.8053 3.66318 30.3366C4.19453 30.868 4.9152 31.1665 5.66665 31.1665H25.5C26.2514 31.1665 26.9721 30.868 27.5034 30.3366C28.0348 29.8053 28.3333 29.0846 28.3333 28.3332V18.4165" stroke="#FF6542" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
			<Path d="M26.2083 3.54182C26.7719 2.97824 27.5363 2.66162 28.3333 2.66162C29.1303 2.66162 29.8947 2.97824 30.4583 3.54182C31.0219 4.10541 31.3385 4.8698 31.3385 5.66682C31.3385 6.46385 31.0219 7.22824 30.4583 7.79183L17 21.2502L11.3333 22.6668L12.75 17.0002L26.2083 3.54182Z" stroke="#FF6542" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
		</Svg>
    );
}

export default EditRed;