import React from 'react';
import Svg, { Path } from 'react-native-svg';

import { getSizesByWidth } from './calculator';
  
const Users3 = (props) => {
    const originalWidth = 35;
    const originalHeight = 35;
    const newWidth = props?.style?.width ? props.style.width : 45;

    const sizes = getSizesByWidth(originalWidth, originalHeight, newWidth);

    return (
        <Svg style={[{width: sizes.width, height: sizes.height}, props.style]} viewBox="0 0 35 35" fill="none" xmlns="http://www.w3.org/2000/svg">
            <Path d="M29.1667 30.625V27.7083C29.1667 26.1612 28.5521 24.6775 27.4582 23.5835C26.3642 22.4896 24.8805 21.875 23.3334 21.875H11.6667C10.1196 21.875 8.63588 22.4896 7.54192 23.5835C6.44796 24.6775 5.83337 26.1612 5.83337 27.7083V30.625" stroke="#00467F" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
            <Path d="M17.5 16.0417C20.7216 16.0417 23.3333 13.43 23.3333 10.2083C23.3333 6.98667 20.7216 4.375 17.5 4.375C14.2783 4.375 11.6666 6.98667 11.6666 10.2083C11.6666 13.43 14.2783 16.0417 17.5 16.0417Z" stroke="#00467F" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
        </Svg>

    );
}

export default Users3;