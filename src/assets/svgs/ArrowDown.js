import React from 'react';
import Svg, { Path } from 'react-native-svg';

import { getSizesByWidth } from './calculator';
  
const ArrowDown = (props) => {
    const originalWidth = 14;
    const originalHeight = 9;
    const newWidth = props?.style?.width ? props.style.width : 14;

    const sizes = getSizesByWidth(originalWidth, originalHeight, newWidth);

    return (
        <Svg style={[{width: sizes.width, height: sizes.height}, props.style]} viewBox="0 0 14 9" fill="none" xmlns="http://www.w3.org/2000/svg">
            <Path d="M13 1.13333L7 7.13333L1 1.13333" stroke="#4EA1CC" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
        </Svg>
    );
}

export default ArrowDown;