import React from 'react';
import Svg, { Path } from 'react-native-svg';

import { getSizesByWidth } from './calculator';
  
const Shifts = (props) => {
    const originalWidth = 53;
    const originalHeight = 43;
    const newWidth = props?.style?.width ? props.style.width : 53;

    const sizes = getSizesByWidth(originalWidth, originalHeight, newWidth);

    return (
        <Svg style={[{width: sizes.width, height: sizes.height}, props.style]} viewBox="0 0 53 43" fill="none" xmlns="http://www.w3.org/2000/svg">
            <Path d="M2.875 21.5H18.625C19.3212 21.5 19.9889 21.2234 20.4812 20.7312C20.9734 20.2389 21.25 19.5712 21.25 18.875V3.125C21.25 2.42881 20.9734 1.76113 20.4812 1.26885C19.9889 0.776562 19.3212 0.5 18.625 0.5H2.875C2.17881 0.5 1.51113 0.776562 1.01884 1.26885C0.526562 1.76113 0.25 2.42881 0.25 3.125V18.875C0.25 19.5712 0.526562 20.2389 1.01884 20.7312C1.51113 21.2234 2.17881 21.5 2.875 21.5ZM5.5 5.75H16V16.25H5.5V5.75ZM29.125 11H50.125C50.8212 11 51.4889 10.7234 51.9812 10.2312C52.4734 9.73887 52.75 9.07119 52.75 8.375C52.75 7.67881 52.4734 7.01113 51.9812 6.51885C51.4889 6.02656 50.8212 5.75 50.125 5.75H29.125C28.4288 5.75 27.7611 6.02656 27.2688 6.51885C26.7766 7.01113 26.5 7.67881 26.5 8.375C26.5 9.07119 26.7766 9.73887 27.2688 10.2312C27.7611 10.7234 28.4288 11 29.125 11ZM29.125 37.25H2.875C2.17881 37.25 1.51113 37.5266 1.01884 38.0188C0.526562 38.5111 0.25 39.1788 0.25 39.875C0.25 40.5712 0.526562 41.2389 1.01884 41.7312C1.51113 42.2234 2.17881 42.5 2.875 42.5H29.125C29.8212 42.5 30.4889 42.2234 30.9812 41.7312C31.4734 41.2389 31.75 40.5712 31.75 39.875C31.75 39.1788 31.4734 38.5111 30.9812 38.0188C30.4889 37.5266 29.8212 37.25 29.125 37.25ZM50.125 26.75H2.875C2.17881 26.75 1.51113 27.0266 1.01884 27.5188C0.526562 28.0111 0.25 28.6788 0.25 29.375C0.25 30.0712 0.526562 30.7389 1.01884 31.2312C1.51113 31.7234 2.17881 32 2.875 32H50.125C50.8212 32 51.4889 31.7234 51.9812 31.2312C52.4734 30.7389 52.75 30.0712 52.75 29.375C52.75 28.6788 52.4734 28.0111 51.9812 27.5188C51.4889 27.0266 50.8212 26.75 50.125 26.75ZM50.125 16.25H29.125C28.4288 16.25 27.7611 16.5266 27.2688 17.0188C26.7766 17.5111 26.5 18.1788 26.5 18.875C26.5 19.5712 26.7766 20.2389 27.2688 20.7312C27.7611 21.2234 28.4288 21.5 29.125 21.5H50.125C50.8212 21.5 51.4889 21.2234 51.9812 20.7312C52.4734 20.2389 52.75 19.5712 52.75 18.875C52.75 18.1788 52.4734 17.5111 51.9812 17.0188C51.4889 16.5266 50.8212 16.25 50.125 16.25Z" fill="#4EA1CC"/>
        </Svg>
    );
}

export default Shifts;