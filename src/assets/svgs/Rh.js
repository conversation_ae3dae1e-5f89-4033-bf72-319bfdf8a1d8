import React from 'react';
import Svg, { Path } from 'react-native-svg';

import { getSizesByWidth } from './calculator';
  
const Rh = (props) => {
    const originalWidth = 31;
    const originalHeight = 36;
    const newWidth = props?.style?.width ? props.style.width : 37;

    const sizes = getSizesByWidth(originalWidth, originalHeight, newWidth);

    return (
        <Svg style={[{width: sizes.width, height: sizes.height}, props.style]} viewBox="0 0 31 36" fill="none" xmlns="http://www.w3.org/2000/svg">
            <Path fillRule="evenodd" clipRule="evenodd" d="M20.9524 35.8235C21.4008 34.8728 21.9031 33.8861 22.5668 32.8995C23.2664 31.877 24.1096 30.8545 25.0244 29.7961C24.558 29.0606 24.1275 28.3431 23.7328 27.6076C22.6027 28.7019 21.5623 29.7782 20.6474 30.8545C19.7684 31.877 19.0508 32.9174 18.4051 33.9041C16.4856 33.5274 14.5841 33.1506 12.6826 32.756C10.7991 32.3793 8.91551 32.0026 7.01401 31.6079C7.65981 32.2896 8.35941 32.9533 9.0949 33.617C11.0323 33.9938 12.9876 34.3525 14.9608 34.7292C16.952 35.106 18.9432 35.4647 20.9524 35.8235ZM16.0551 31.6797C14.2254 31.285 12.3956 30.8904 10.5838 30.4957C8.75406 30.0831 6.92432 29.6885 5.09458 29.2938C4.59229 28.5763 4.12589 27.8587 3.67742 27.1412C5.48923 27.5358 7.28309 27.9484 9.07696 28.361C10.8529 28.7557 12.6109 29.1683 14.3868 29.5808C15.4631 28.5045 16.6112 27.3564 17.8669 26.2443C19.2123 25.0782 20.6295 23.9122 22.1542 22.7283C22.3157 23.5176 22.513 24.3069 22.7283 25.0782C21.3829 26.2263 20.1272 27.3385 18.997 28.4687C17.9028 29.545 16.952 30.6213 16.0551 31.6797ZM12.9158 27.1412C14.2074 25.9931 15.5349 24.8271 17.0058 23.6611C18.5127 22.4592 20.1451 21.2573 21.8493 20.0195C21.8313 19.1944 21.8134 18.3692 21.8493 17.544C19.9837 18.8177 18.1898 20.0734 16.5036 21.2932C14.8891 22.4951 13.3643 23.697 11.9292 24.8809C10.225 24.4683 8.52086 24.0378 6.79875 23.6073C5.04076 23.1947 3.26483 22.7821 1.47097 22.3874C1.75799 23.1588 2.08088 23.9122 2.40378 24.6657C4.19764 25.0782 5.95563 25.4729 7.73156 25.8855C9.47161 26.2981 11.1937 26.7286 12.9158 27.1412ZM11.104 22.2977C9.41779 21.8852 7.71362 21.4546 5.97357 21.042C4.21558 20.6295 2.43966 20.2169 0.645791 19.8043C0.448466 19.0329 0.287019 18.2436 0.179387 17.4364C1.97325 17.849 3.74918 18.2616 5.48923 18.6562C7.22928 19.0688 8.93345 19.4993 10.6197 19.9298C12.3956 18.6741 14.2612 17.4005 16.2345 16.1089C18.2795 14.7815 20.4501 13.454 22.7283 12.1086C22.4951 12.9876 22.2977 13.8486 22.1363 14.7097C20.0554 16.0372 18.0822 17.3108 16.2165 18.6024C14.4227 19.8402 12.7364 21.0959 11.104 22.2977ZM10.3506 17.239C12.3239 15.9475 14.3868 14.6021 16.5933 13.2925C18.8715 11.9113 21.2932 10.5479 23.8225 9.11284C24.2531 8.19797 24.7195 7.26516 25.2576 6.31441C23.8584 7.04989 22.4771 7.78538 21.1497 8.50292C19.8402 9.22047 18.5306 9.93802 17.2749 10.6376C14.8532 12.0189 12.5571 13.4002 10.3865 14.7276C8.71819 14.2971 7.06783 13.8666 5.36366 13.4361C3.67742 13.0055 1.95531 12.575 0.233202 12.1624C0.107631 13.0235 0.0358773 13.8486 0 14.6918C1.75799 15.1043 3.51598 15.5169 5.23809 15.9475C6.97814 16.378 8.66437 16.8085 10.3506 17.239ZM10.8349 11.8933C9.22047 11.4628 7.60599 11.0143 5.97357 10.5838C4.32322 10.1533 2.67286 9.70481 1.00456 9.27429C1.3454 8.37735 1.74005 7.46248 2.22439 6.52967C3.80299 6.97814 5.39953 7.4266 6.97814 7.87507C8.5388 8.32354 10.0995 8.78994 11.6422 9.23841C12.9876 8.52086 14.3689 7.76744 15.786 7.03195C17.2211 6.27853 18.6921 5.52511 20.181 4.77168C21.7416 3.98238 23.3382 3.21102 24.9706 2.42172C26.6389 1.63242 28.3431 0.80724 30.0831 0C29.1324 1.04044 28.2534 2.06295 27.4641 3.06751C25.9034 3.83887 24.3607 4.61023 22.8538 5.3816C21.3649 6.13502 19.9119 6.88844 18.5306 7.62393C17.1673 8.35941 15.8398 9.05902 14.5482 9.77657C13.2925 10.4941 12.0368 11.2117 10.8349 11.8933Z" fill="#2D719F"/>
        </Svg>

    );
}

export default Rh;