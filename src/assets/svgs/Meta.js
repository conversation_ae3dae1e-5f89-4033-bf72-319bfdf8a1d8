import React from 'react';
import Svg, { Path } from 'react-native-svg';

import { getSizesByWidth } from './calculator';
  
const Meta = (props) => {
    const originalWidth = 30;
    const originalHeight = 19;
    const newWidth = props?.style?.width ? props.style.width : 30;

    const sizes = getSizesByWidth(originalWidth, originalHeight, newWidth);

    return (
		<Svg style={[{width: sizes.width, height: sizes.height}, props.style]} viewBox="0 0 30 19" fill="none" xmlns="http://www.w3.org/2000/svg">
			<Path d="M3.56691 12.5301C3.56691 13.6223 3.80665 14.4609 4.12 14.9682C4.53084 15.6327 5.14361 15.9142 5.76832 15.9142C6.57408 15.9142 7.3112 15.7143 8.73173 13.7496C9.86974 12.1749 11.2107 9.96455 12.1129 8.57885L13.6409 6.2312C14.7023 4.60079 15.9308 2.78832 17.3394 1.55979C18.4894 0.557068 19.7298 0 20.9783 0C23.0742 0 25.0707 1.21461 26.5987 3.49262C28.2709 5.98749 29.0826 9.12995 29.0826 12.3729C29.0826 14.3007 28.7026 15.7173 28.056 16.8364C27.4313 17.9187 26.2137 19 24.1655 19V15.9142C25.9193 15.9142 26.357 14.3027 26.357 12.4584C26.357 9.83026 25.7442 6.91361 24.3943 4.82958C23.4363 3.35136 22.1949 2.44812 20.8291 2.44812C19.3518 2.44812 18.1631 3.56225 16.8271 5.5488C16.1169 6.60424 15.3877 7.89047 14.569 9.34183L13.6677 10.9384C11.8573 14.1485 11.3987 14.8797 10.4935 16.0863C8.90681 18.1992 7.55194 19 5.76832 19C3.65246 19 2.3145 18.0838 1.48586 16.7031C0.809424 15.578 0.477173 14.1018 0.477173 12.4196L3.56691 12.5301Z" fill="white"/>
			<Path d="M2.91333 3.71047C4.32987 1.52696 6.37412 0 8.71878 0C10.0766 0 11.4265 0.401885 12.8361 1.55283C14.378 2.8112 16.0213 4.8833 18.0715 8.29832L18.8067 9.52387C20.5813 12.4803 21.591 14.0013 22.1819 14.7185C22.9419 15.6397 23.4741 15.9142 24.1655 15.9142C25.9192 15.9142 26.3569 14.3027 26.3569 12.4584L29.0826 12.3729C29.0826 14.3007 28.7026 15.7173 28.056 16.8364C27.4313 17.9187 26.2137 19 24.1655 19C22.8922 19 21.7641 18.7235 20.5167 17.5466C19.5577 16.6434 18.4366 15.0388 17.5742 13.5964L15.0087 9.311C13.7214 7.16031 12.5407 5.55675 11.8573 4.83058C11.1221 4.04969 10.1771 3.10665 8.66904 3.10665C7.44846 3.10665 6.41192 3.96314 5.54448 5.27325L2.91333 3.71047Z" fill="white"/>
			<Path d="M8.66906 3.10665C7.44848 3.10665 6.41194 3.96314 5.5445 5.27325C4.31796 7.1245 3.56691 9.88199 3.56691 12.5301C3.56691 13.6223 3.80665 14.4609 4.12 14.9682L1.48586 16.7031C0.809424 15.578 0.477173 14.1018 0.477173 12.4196C0.477173 9.36073 1.31675 6.17251 2.91335 3.71047C4.3299 1.52696 6.37414 0 8.7188 0L8.66906 3.10665Z" fill="white"/>
		</Svg>

    );
}

export default Meta;