import React from 'react';
import Svg, { Path } from 'react-native-svg';

import { getSizesByWidth } from './calculator';
  
const Ok = (props) => {
    const originalWidth = 84;
    const originalHeight = 84;
    const newWidth = props?.style?.width ? props.style.width : 84;

    const sizes = getSizesByWidth(originalWidth, originalHeight, newWidth);

    return (
        <Svg style={[{width: sizes.width, height: sizes.height}, props.style]} viewBox="0 0 84 84" fill="none" xmlns="http://www.w3.org/2000/svg">
            <Path d="M80.7499 34.0001C79.5791 32.5945 78.1141 31.463 76.4582 30.6854C74.8023 29.9079 72.9959 29.5033 71.1666 29.5001H52.1666L54.4999 23.5418C55.4705 20.933 55.7937 18.1277 55.4419 15.3665C55.09 12.6053 54.0736 9.97066 52.4799 7.68859C50.8861 5.40652 48.7625 3.54512 46.2913 2.26406C43.8201 0.983009 41.0751 0.320535 38.2916 0.333468C37.4901 0.335141 36.7061 0.567948 36.0335 1.00398C35.361 1.44001 34.8285 2.06076 34.4999 2.7918L22.6249 29.5001H12.8333C9.51805 29.5001 6.33862 30.8171 3.99442 33.1613C1.65021 35.5055 0.333252 38.6849 0.333252 42.0001V71.1668C0.333252 74.482 1.65021 77.6614 3.99442 80.0056C6.33862 82.3498 9.51805 83.6668 12.8333 83.6668H65.8749C68.7991 83.6658 71.6305 82.6397 73.8763 80.7669C76.1221 78.8941 77.6402 76.2932 78.1666 73.4168L83.4583 44.2501C83.7856 42.4475 83.7126 40.5951 83.2446 38.8238C82.7766 37.0525 81.9249 35.4058 80.7499 34.0001ZM21.1666 75.3335H12.8333C11.7282 75.3335 10.6684 74.8945 9.88698 74.1131C9.10557 73.3317 8.66659 72.2719 8.66659 71.1668V42.0001C8.66659 40.8951 9.10557 39.8353 9.88698 39.0539C10.6684 38.2725 11.7282 37.8335 12.8333 37.8335H21.1666V75.3335ZM75.3333 42.7501L70.0416 71.9168C69.8641 72.8875 69.3479 73.7636 68.5849 74.3893C67.8219 75.015 66.8615 75.3496 65.8749 75.3335H29.4999V34.5418L40.8333 9.0418C41.9999 9.38191 43.0833 9.96016 44.0152 10.7401C44.947 11.52 45.7071 12.4846 46.2474 13.573C46.7877 14.6615 47.0965 15.8501 47.1543 17.0639C47.2121 18.2777 47.0177 19.4903 46.5833 20.6251L44.3749 26.5835C43.9044 27.8429 43.7455 29.1974 43.9119 30.5315C44.0783 31.8656 44.565 33.1396 45.3305 34.2449C46.096 35.3501 47.1175 36.2537 48.3079 36.8786C49.4983 37.5035 50.8222 37.8311 52.1666 37.8335H71.1666C71.7787 37.8325 72.3835 37.9664 72.9381 38.2256C73.4926 38.4849 73.9832 38.8631 74.3749 39.3335C74.7763 39.7973 75.0703 40.3441 75.236 40.9347C75.4016 41.5253 75.4349 42.1452 75.3333 42.7501Z" fill="#4EA1CC"/>
        </Svg>
    );
}

export default Ok;