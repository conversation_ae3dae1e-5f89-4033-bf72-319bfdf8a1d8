import React from 'react';
import Svg, { Path } from 'react-native-svg';

import { getSizesByWidth } from './calculator';
  
const Home3 = (props) => {
    const originalWidth = 96;
    const originalHeight = 88;
    const newWidth = props?.style?.width ? props.style.width : 34;

    const sizes = getSizesByWidth(originalWidth, originalHeight, newWidth);

    return (
        <Svg style={[{width: sizes.width, height: sizes.height}, props.style]} viewBox="0 0 96 88" fill="none" xmlns="http://www.w3.org/2000/svg">
            <Path d="M48.41 0.77002L0 45.06L2.7 48.06L11.39 40.06V87.23H39.21V64.06H57.59V87.23H85.36V40.66L93.23 48L96 45.07L48.41 0.77002ZM81.41 83.24H61.59V60.06H35.21V83.23H15.43V36.39L48.43 6.21002L81.43 36.93L81.41 83.24Z" fill="#2D719F"/>
        </Svg>
    );
}

export default Home3;