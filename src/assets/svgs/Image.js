import React from 'react';
import Svg, { Path } from 'react-native-svg';

import { getSizesByWidth } from './calculator';
  
const Image = (props) => {
    const originalWidth = 40;
    const originalHeight = 40;
    const newWidth = props?.style?.width ? props.style.width : 45;
    const color =  props?.style?.color ? props.style.color : '#4EA1CC';

    const sizes = getSizesByWidth(originalWidth, originalHeight, newWidth);

    return (
		<Svg style={[{width: sizes.width, height: sizes.height}, props.style]} viewBox="0 0 40 40" fill="none" xmlns="http://www.w3.org/2000/svg">
			<Path d="M31.6667 5H8.33333C6.49238 5 5 6.49238 5 8.33333V31.6667C5 33.5076 6.49238 35 8.33333 35H31.6667C33.5076 35 35 33.5076 35 31.6667V8.33333C35 6.49238 33.5076 5 31.6667 5Z" stroke={color} strokeWidth="1" strokeLinecap="round" strokeLinejoin="round"/>
			<Path d="M35 25.0001L26.6667 16.6667L8.33334 35.0001" stroke={color} strokeWidth="1" strokeLinecap="round" strokeLinejoin="round"/>
			<Path d="M14.1667 16.6667C15.5474 16.6667 16.6667 15.5475 16.6667 14.1667C16.6667 12.786 15.5474 11.6667 14.1667 11.6667C12.7859 11.6667 11.6667 12.786 11.6667 14.1667C11.6667 15.5475 12.7859 16.6667 14.1667 16.6667Z" stroke={color} strokeWidth="1" strokeLinecap="round" strokeLinejoin="round"/>
		</Svg>

    );
}

export default Image;