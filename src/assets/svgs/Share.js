import React from 'react';
import Svg, { Path } from 'react-native-svg';

import { getSizesByWidth } from './calculator';
  
const Share = (props) => {
    const originalWidth = 18;
    const originalHeight = 18;
    const newWidth = props?.style?.width ? props.style.width : 30;

    const sizes = getSizesByWidth(originalWidth, originalHeight, newWidth);

    return (
        <Svg style={[{width: sizes.width, height: sizes.height}, props.style]} viewBox="0 0 18 18" fill="none" xmlns="http://www.w3.org/2000/svg">
			<Path d="M13.5 16.5C14.7426 16.5 15.75 15.4926 15.75 14.25C15.75 13.0074 14.7426 12 13.5 12C12.2574 12 11.25 13.0074 11.25 14.25C11.25 15.4926 12.2574 16.5 13.5 16.5Z" stroke="white" strokeWidth="1" strokeLinecap="round" strokeLinejoin="round"/>
			<Path d="M4.5 11.25C5.74264 11.25 6.75 10.2426 6.75 9C6.75 7.75736 5.74264 6.75 4.5 6.75C3.25736 6.75 2.25 7.75736 2.25 9C2.25 10.2426 3.25736 11.25 4.5 11.25Z" stroke="white" strokeWidth="1" strokeLinecap="round" strokeLinejoin="round"/>
			<Path d="M6.4425 10.1326L11.565 13.1176" stroke="white" strokeWidth="1" strokeLinecap="round" strokeLinejoin="round"/>
			<Path d="M13.5 6C14.7426 6 15.75 4.99264 15.75 3.75C15.75 2.50736 14.7426 1.5 13.5 1.5C12.2574 1.5 11.25 2.50736 11.25 3.75C11.25 4.99264 12.2574 6 13.5 6Z" stroke="white" strokeWidth="1" strokeLinecap="round" strokeLinejoin="round"/>
			<Path d="M11.5575 4.88257L6.4425 7.86757" stroke="white" strokeWidth="1" strokeLinecap="round" strokeLinejoin="round"/>
		</Svg>
    );
}

export default Share;