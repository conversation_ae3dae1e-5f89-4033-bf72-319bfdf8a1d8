import React from 'react';
import Svg, { Path } from 'react-native-svg';

import { getSizesByWidth } from './calculator';
  
const Checkin = (props) => {
    const originalWidth = 60;
    const originalHeight = 50;
    const newWidth = props?.style?.width ? props.style.width : 60;

    const sizes = getSizesByWidth(originalWidth, originalHeight, newWidth);

    return (
        <Svg style={[{width: sizes.width, height: sizes.height}, props.style]} viewBox="0 0 60 50" fill="none" xmlns="http://www.w3.org/2000/svg">
            <Path d="M56.5833 46.75V41.9167C56.5817 39.7749 55.8688 37.6942 54.5566 36.0015C53.2444 34.3087 51.4071 33.0997 49.3333 32.5642" stroke="#4EA1CC" strokeWidth="5" strokeLinecap="round" strokeLinejoin="round"/>
            <Path d="M42.0834 46.75V41.9167C42.0834 39.3529 41.0649 36.8942 39.2521 35.0813C37.4392 33.2685 34.9804 32.25 32.4167 32.25H13.0834C10.5196 32.25 8.06084 33.2685 6.24799 35.0813C4.43514 36.8942 3.41669 39.3529 3.41669 41.9167V46.75" stroke="#4EA1CC" strokeWidth="5" strokeLinecap="round" strokeLinejoin="round"/>
            <Path d="M39.6667 3.56421C41.746 4.0966 43.589 5.3059 44.9051 7.00146C46.2213 8.69701 46.9356 10.7824 46.9356 12.9288C46.9356 15.0752 46.2213 17.1606 44.9051 18.8561C43.589 20.5517 41.746 21.761 39.6667 22.2934" stroke="#4EA1CC" strokeWidth="5" strokeLinecap="round" strokeLinejoin="round"/>
            <Path d="M22.75 22.5833C28.0887 22.5833 32.4166 18.2554 32.4166 12.9167C32.4166 7.57791 28.0887 3.25 22.75 3.25C17.4112 3.25 13.0833 7.57791 13.0833 12.9167C13.0833 18.2554 17.4112 22.5833 22.75 22.5833Z" stroke="#4EA1CC" strokeWidth="5" strokeLinecap="round" strokeLinejoin="round"/>
        </Svg>
    );
}

export default Checkin;