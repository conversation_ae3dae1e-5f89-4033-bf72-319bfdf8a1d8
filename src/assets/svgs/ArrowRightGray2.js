import React from 'react';
import Svg, { Path } from 'react-native-svg';

import { getSizesByWidth } from './calculator';
  
const ArrowRightGray2 = (props) => {
    const originalWidth = 36;
    const originalHeight = 36;
    const newWidth = props?.style?.width ? props.style.width : 20;

    const sizes = getSizesByWidth(originalWidth, originalHeight, newWidth);

    return (
    <Svg style={[{width: sizes.width, height: sizes.height}, props.style]} viewBox="0 0 9 14" fill="none" xmlns="http://www.w3.org/2000/svg">
      <Path d="M1.59486 1L7.96143 7L1.59487 13" stroke="#90B0C0" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
    </Svg>     
    );
}

export default ArrowRightGray2;