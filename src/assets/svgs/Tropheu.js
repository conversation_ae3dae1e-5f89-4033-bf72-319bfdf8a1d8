import React from 'react';
import Svg, { <PERSON>, G, Line, ClipPath, Rect, Defs} from 'react-native-svg';

import { getSizesByWidth } from './calculator';
  
const Tropheu = (props) => {
    const originalWidth = 32;
    const originalHeight = 33;
    const newWidth = props?.style?.width ? props.style.width : 42;

    const sizes = getSizesByWidth(originalWidth, originalHeight, newWidth);

    return (
        <Svg style={[{width: sizes.width, height: sizes.height}, props.style]} viewBox="0 0 33 32" fill="none" xmlns="http://www.w3.org/2000/svg">
            <Path d="M30.605 3.90009C29.76 3.18516 28.59 2.99016 27.29 3.25002C27.42 2.34009 27.485 1.49502 27.485 0.715016C27.485 0.325016 27.225 0.0649414 26.835 0.0649414L6.16496 0.0651591C5.77496 0.0651591 5.51488 0.325233 5.51488 0.715234C5.51488 1.49523 5.57996 2.34031 5.70988 3.25023C4.40996 2.99016 3.30481 3.25023 2.39488 3.90031C0.119956 5.78523 0.509956 10.2701 3.36988 14.1055C5.31988 16.8355 7.98481 18.4606 10.2601 18.4606C10.6501 18.4606 10.975 18.3955 11.3001 18.3306C12.2101 19.5006 13.2501 20.4756 14.3551 20.9956C13.705 23.4006 12.6001 24.9606 11.4301 24.9606C11.0401 24.9606 10.78 25.2207 10.78 25.6107V25.6758V30.5501C10.78 30.9401 11.0401 31.2002 11.4301 31.2002H21.6353C22.0253 31.2002 22.2853 30.9401 22.2853 30.5501V25.6751V25.6101C22.2853 25.2201 22.0253 24.96 21.6353 24.96C20.5303 24.96 19.3603 23.4 18.7103 20.9949C19.8152 20.475 20.7902 19.5 21.7652 18.33C22.0901 18.3951 22.4153 18.4599 22.8053 18.4599C25.0802 18.4599 27.7453 16.77 29.6954 14.1048C32.4903 10.2053 32.9454 5.72038 30.6054 3.9002L30.605 3.90009ZM4.40984 13.3253C2.06984 10.1402 1.54992 6.24009 3.23984 4.87509C3.62984 4.55016 4.08492 4.42002 4.73477 4.42002C5.05991 4.42002 5.44992 4.42002 5.83992 4.55016C5.83992 4.61523 5.83992 4.68009 5.90499 4.74516C6.55507 8.97009 8.04999 13.1302 10.4551 17.16C8.56992 17.2899 6.16484 15.7299 4.41007 13.325L4.40984 13.3253ZM20.9197 26.3251V29.9002H12.08V26.3251H20.9197ZM19.1647 25.0252H13.7697C14.4847 24.1801 15.0697 23.0101 15.5247 21.4501C16.3047 21.6451 16.8897 21.58 17.4747 21.4501C17.8647 22.945 18.4497 24.1801 19.1647 25.0252ZM20.9847 17.2252C20.9197 17.2252 20.9847 17.2252 20.9847 17.2252C19.6197 19.1101 18.0597 20.2801 16.4997 20.2801C14.9397 20.2801 13.4448 19.1101 12.0798 17.2252V17.1601C9.80488 14.0401 7.85488 8.84028 7.13973 3.96528V3.9002C6.94473 2.99028 6.81481 2.1452 6.81481 1.30013H26.185C26.185 2.1452 26.0551 2.99005 25.9249 3.9002V3.96528C25.2098 8.84028 23.5849 13.1949 20.9848 17.2253L20.9847 17.2252ZM28.5897 13.3252C26.8347 15.7302 24.3648 17.2902 22.5447 17.1601C24.8197 13.1302 26.5098 9.03528 27.0948 4.80991C27.0948 4.74483 27.1599 4.6149 27.1599 4.54983C28.2 4.22491 29.175 4.35483 29.8248 4.87476C31.5147 6.23998 30.9948 10.14 28.5897 13.3249V13.3252Z" fill="#2D719F"/>
        </Svg>
        
    );
}

export default Tropheu;