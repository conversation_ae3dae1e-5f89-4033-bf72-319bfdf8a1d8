import React from 'react';
import Svg, { Path } from 'react-native-svg';

import { getSizesByWidth } from './calculator';
  
const XLS = (props) => {
    const originalWidth = 29;
    const originalHeight = 34;
    const newWidth = props?.style?.width ? props.style.width : 40;
    const color =  props?.style?.color ? props.style.color : '#4EA1CC';

    const sizes = getSizesByWidth(originalWidth, originalHeight, newWidth);

    return (
        <Svg style={[{width: sizes.width, height: sizes.height}, props.style]} viewBox="0 0 29 34" fill="none" xmlns="http://www.w3.org/2000/svg">
            <Path d="M8.84957 24.5685H16.1711V18.2987H8.84957V24.5685ZM16.6964 25.6124H8.32615C8.03534 25.6124 7.80135 25.3792 7.80135 25.0916V17.7752C7.80135 17.4863 8.03509 17.2531 8.32615 17.2531H16.6964C16.9853 17.2531 17.2215 17.4863 17.2215 17.7752V25.0916C17.2215 25.3792 16.9853 25.6124 16.6964 25.6124ZM3.34031 24.5685H21.6797V23.1745H3.34031V24.5685ZM22.2063 25.6124H2.81562C2.52679 25.6124 2.29206 25.3792 2.29206 25.0916V22.652C2.29206 22.3639 2.52679 22.1304 2.81562 22.1304H22.2063C22.4956 22.1304 22.7306 22.3637 22.7306 22.652V25.0916C22.7306 25.3792 22.4956 25.6124 22.2063 25.6124ZM3.34031 22.1304H21.6797V20.7364L3.34031 20.7367V22.1304ZM22.2063 23.1746H2.81562C2.52679 23.1746 2.29206 22.9414 2.29206 22.652V20.2134C2.29206 19.9258 2.52679 19.692 2.81562 19.692H22.2063C22.4956 19.692 22.7306 19.9258 22.7306 20.2134V22.652C22.7306 22.9414 22.4956 23.1746 22.2063 23.1746ZM3.34031 19.6921H21.6797V18.2985H3.34031V19.6921ZM22.2063 20.7367H2.81562C2.52679 20.7367 2.29206 20.5034 2.29206 20.2136V17.7757C2.29206 17.4869 2.52679 17.2536 2.81562 17.2536H22.2063C22.4956 17.2536 22.7306 17.4869 22.7306 17.7757V20.2136C22.7306 20.5034 22.4956 20.7367 22.2063 20.7367ZM22.8884 9.74295C22.8884 9.57993 22.8279 9.44619 22.7152 9.34346C22.6023 9.25066 22.3999 9.14867 22.1028 9.06779C21.6058 8.92437 21.2187 8.74894 20.9634 8.54423C20.7085 8.33877 20.5758 8.07252 20.5758 7.71469C20.5758 7.36582 20.7291 7.07997 21.0252 6.86312C21.3194 6.6393 21.6959 6.52516 22.1552 6.52516C22.6244 6.52516 22.9998 6.6594 23.2946 6.90506C23.5906 7.1517 23.7338 7.45789 23.7234 7.82714L23.7127 7.83781H22.8884C22.8884 7.64377 22.8278 7.48968 22.6951 7.36611C22.5623 7.2547 22.38 7.19217 22.1442 7.19217C21.9107 7.19217 21.7368 7.24328 21.6147 7.33608C21.4926 7.43831 21.4309 7.57231 21.4309 7.72515C21.4309 7.86932 21.4924 7.99239 21.6261 8.0842C21.7574 8.17601 21.9921 8.26781 22.3174 8.37104C22.7867 8.50503 23.1328 8.67897 23.3772 8.8941C23.6219 9.10874 23.7427 9.38416 23.7427 9.73278C23.7427 10.1015 23.6011 10.3886 23.305 10.5936C23.0204 10.8087 22.643 10.9119 22.1756 10.9119C21.7158 10.9119 21.3098 10.7975 20.9632 10.5531C20.627 10.3169 20.4545 9.97869 20.4642 9.54871L20.4751 9.52886H21.2996C21.2996 9.7837 21.3813 9.9693 21.5331 10.0919C21.6865 10.204 21.8991 10.2668 22.1755 10.2668C22.4102 10.2668 22.5834 10.2154 22.7055 10.1219C22.8279 10.0301 22.8884 9.8961 22.8884 9.74301L22.8884 9.74295ZM18.2248 10.1928H20.1088V10.8489H17.3788V6.58744H18.2247L18.2248 10.1928ZM15.0777 8.10402L15.8725 6.58744H16.8598L15.6065 8.70748L16.9311 10.8489H15.9028L15.0889 9.31367L14.2733 10.8489H13.2764L14.5679 8.70748L13.3061 6.58744H14.294L15.0777 8.10402ZM27.6855 5.04627H9.33278C9.24742 5.04627 9.16877 5.08374 9.11145 5.14007C9.05487 5.19639 9.01939 5.27753 9.01939 5.36785V11.7709C9.01939 11.8607 9.05487 11.9431 9.11145 12.0001C9.16901 12.0565 9.24742 12.0917 9.33278 12.0917H27.6862C27.7715 12.0917 27.8514 12.0565 27.9075 12.0001C27.9628 11.9431 27.9988 11.8607 27.9988 11.7709V5.36785C27.9988 5.27753 27.9628 5.19664 27.9075 5.14007C27.8514 5.08399 27.7713 5.04627 27.6859 5.04627H27.6855ZM9.33278 4.0505H27.6862C28.047 4.0505 28.3765 4.19988 28.6139 4.43858C28.8531 4.67902 29 5.0083 29 5.36784V11.7709C29 12.1319 28.8516 12.4622 28.6139 12.6996C28.3764 12.9393 28.0469 13.0887 27.6862 13.0887H9.33278C8.9715 13.0887 8.64198 12.9393 8.40479 12.6996C8.16733 12.4621 8.01919 12.1319 8.01919 11.7709V5.36784C8.01919 5.0083 8.16584 4.67901 8.40479 4.43858C8.64201 4.19988 8.97153 4.0505 9.33278 4.0505ZM24.5075 33.214H0.523561C0.234733 33.214 0 32.9813 0 32.6915V0.522074C0 0.233989 0.234733 0 0.523561 0H24.5075C24.7981 0 25.0316 0.233742 25.0316 0.522074V4.54975H23.984V1.04537H1.0494V32.1705H23.984V12.5905H25.0316V32.6914C25.0316 32.9814 24.7981 33.2139 24.5075 33.2139V33.214Z" fill={color}/>
        </Svg>
    );
}

export default XLS;