import React from 'react';
import Svg, { Path } from 'react-native-svg';

import { getSizesByWidth } from './calculator';
  
const Medal = (props) => {
    const originalWidth = 58;
    const originalHeight = 62;
    const newWidth = props?.style?.width ? props.style.width : 75;

    const sizes = getSizesByWidth(originalWidth, originalHeight, newWidth);

    return (
        <Svg style={[{width: sizes.width, height: sizes.height}, props.style]} viewBox="0 0 58 62" fill="none" xmlns="http://www.w3.org/2000/svg">
            <Path d="M57.0885 47.625L48.5068 32.805C50.2629 29.5798 51.1777 25.964 51.1668 22.2917C51.1668 16.4127 48.8314 10.7745 44.6744 6.61747C40.5173 2.46041 34.8791 0.125 29.0002 0.125C23.1212 0.125 17.483 2.46041 13.3259 6.61747C9.16889 10.7745 6.83348 16.4127 6.83348 22.2917C6.82258 25.964 7.73742 29.5798 9.49348 32.805L0.911817 47.625C0.633344 48.1073 0.487 48.6546 0.48755 49.2115C0.488101 49.7684 0.635525 50.3154 0.91495 50.7971C1.19438 51.2789 1.59592 51.6785 2.07907 51.9555C2.56222 52.2325 3.10988 52.3772 3.66682 52.375H12.7551L17.3785 60.165C17.5343 60.4232 17.7263 60.6578 17.9485 60.8617C18.5355 61.4275 19.3182 61.7451 20.1335 61.7483H20.5768C21.0549 61.6829 21.5117 61.5091 21.9123 61.2402C22.313 60.9713 22.6469 60.6143 22.8885 60.1967L29.0002 49.6833L35.1118 60.2917C35.357 60.7034 35.6924 61.0542 36.0929 61.3175C36.4933 61.5808 36.9483 61.7497 37.4235 61.8117H37.8668C38.6931 61.8166 39.4886 61.4984 40.0835 60.925C40.2965 60.7327 40.4781 60.5084 40.6218 60.26L45.2452 52.47H54.3335C54.8915 52.4722 55.4402 52.327 55.924 52.0489C56.4078 51.7709 56.8095 51.3699 57.0885 50.8867C57.3844 50.3942 57.5407 49.8304 57.5407 49.2558C57.5407 48.6813 57.3844 48.1175 57.0885 47.625ZM20.1018 52.47L17.2835 47.7517C17.006 47.2835 16.6125 46.8948 16.141 46.623C15.6695 46.3513 15.136 46.2056 14.5918 46.2H9.11348L13.6418 38.3467C16.7601 41.3515 20.6902 43.3771 24.9468 44.1733L20.1018 52.47ZM29.0002 38.125C25.8686 38.125 22.8074 37.1964 20.2036 35.4566C17.5998 33.7168 15.5704 31.244 14.3721 28.3508C13.1737 25.4577 12.8601 22.2741 13.4711 19.2027C14.082 16.1314 15.59 13.3101 17.8043 11.0958C20.0186 8.88148 22.8399 7.3735 25.9112 6.76257C28.9826 6.15163 32.1661 6.46519 35.0593 7.66357C37.9525 8.86196 40.4253 10.8914 42.1651 13.4951C43.9049 16.0989 44.8335 19.1601 44.8335 22.2917C44.8335 26.4909 43.1653 30.5182 40.196 33.4875C37.2267 36.4569 33.1994 38.125 29.0002 38.125ZM43.4085 46.2C42.8643 46.2056 42.3308 46.3513 41.8593 46.623C41.3878 46.8948 40.9943 47.2835 40.7168 47.7517L37.8985 52.47L33.0852 44.0783C37.3268 43.2658 41.2424 41.242 44.3585 38.2517L48.8868 46.105L43.4085 46.2Z" fill="white"/>
        </Svg>

    );
}

export default Medal;