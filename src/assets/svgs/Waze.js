import React from 'react';
import Svg, { Path } from 'react-native-svg';

import { getSizesByWidth } from './calculator';
  
const Waze = (props) => {
    const originalWidth = 32;
    const originalHeight = 32;
    const newWidth = props?.style?.width ? props.style.width : 32;

    const sizes = getSizesByWidth(originalWidth, originalHeight, newWidth);

    return (
        <Svg style={[{width: sizes.width, height: sizes.height}, props.style]} viewBox="0 0 32 32" fill="none" xmlns="http://www.w3.org/2000/svg">
            <Path d="M18 1C10.28 1 4 6.832 4.012 14.152C4.122 14.876 4.192 17 3.212 18.138C2.714 18.718 1.99 19 1 19C0.448 19 0 19.448 0 20C0 23.022 2.506 25.088 7.454 26.138C7.524 26.152 7.592 26.16 7.662 26.16C8.124 26.16 8.538 25.838 8.638 25.368C8.752 24.828 8.408 24.298 7.868 24.182C4.602 23.488 2.686 22.384 2.154 20.896C3.208 20.698 4.074 20.21 4.736 19.436C6.51 17.36 6.012 14 6 14C6 7.934 11.382 3 18 3C24.618 3 30 7.934 30 14C30 18.132 27.498 21.886 23.472 23.796C22.972 24.032 22.76 24.63 22.996 25.128C23.234 25.63 23.836 25.832 24.328 25.604C29.06 23.36 32 18.914 32 14C32 6.832 25.718 1 18 1Z" fill="white"/>
            <Path d="M20.194 25.838C20.138 25.288 19.604 24.886 19.098 24.946C18.732 24.982 18.366 25 18 25C15.954 25 14.314 24.944 12.84 24.824C12.298 24.794 11.808 25.19 11.762 25.738C11.718 26.288 12.128 26.772 12.678 26.816C14.206 26.942 15.898 27 18 27C18.436 27 18.87 26.978 19.302 26.934C19.852 26.878 20.25 26.386 20.194 25.838Z" fill="white"/>
            <Path d="M22 23C19.794 23 18 24.794 18 27C18 29.206 19.794 31 22 31C24.206 31 26 29.206 26 27C26 24.794 24.206 23 22 23ZM22 29C20.896 29 20 28.104 20 27C20 25.896 20.896 25 22 25C23.104 25 24 25.896 24 27C24 28.104 23.104 29 22 29Z" fill="white"/>
            <Path d="M10 23C7.794 23 6 24.794 6 27C6 29.206 7.794 31 10 31C12.206 31 14 29.206 14 27C14 24.794 12.206 23 10 23ZM10 29C8.898 29 8 28.104 8 27C8 25.896 8.898 25 10 25C11.102 25 12 25.896 12 27C12 28.104 11.102 29 10 29Z" fill="white"/>
            <Path d="M14.04 10H14.02C13.468 10 13.03 10.448 13.03 11C13.03 11.552 13.488 12 14.04 12C14.592 12 15.04 11.552 15.04 11C15.04 10.448 14.592 10 14.04 10Z" fill="white"/>
            <Path d="M22.04 10H22.02C21.468 10 21.03 10.448 21.03 11C21.03 11.552 21.49 12 22.04 12C22.592 12 23.04 11.552 23.04 11C23.04 10.448 22.592 10 22.04 10Z" fill="white"/>
            <Path d="M23.22 15.024C22.678 14.908 22.148 15.242 22.024 15.782C21.598 17.676 19.944 19 18 19C16.056 19 14.402 17.676 13.976 15.782C13.854 15.242 13.32 14.902 12.78 15.026C12.242 15.146 11.904 15.682 12.024 16.218C12.658 19.036 15.116 21 18 21C20.886 21 23.344 19.034 23.978 16.22C24.098 15.68 23.76 15.146 23.22 15.024Z" fill="white"/>
        </Svg>        
    );
}

export default Waze;