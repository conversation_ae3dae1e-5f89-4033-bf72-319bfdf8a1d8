import React from 'react';
import Svg, { Path } from 'react-native-svg';

import { getSizesByWidth } from './calculator';
  
const StudioC = (props) => {
    const originalWidth = 24;
    const originalHeight = 24;
    const newWidth = props?.style?.width ? props.style.width : 44;

    const sizes = getSizesByWidth(originalWidth, originalHeight, newWidth);

    return (
        <Svg style={[{width: sizes.width, height: sizes.height}, props.style]} viewBox="0 0 24 42" fill="none" xmlns="http://www.w3.org/2000/svg">
            <Path d="M12.8572 41.1429H11.1429V35.1429H12.8572V41.1429Z" fill="#2D719F"/>
            <Path d="M13.9286 41.1429H10.0715C9.71642 41.1429 9.42859 40.951 9.42859 40.7142C9.42859 40.4776 9.71642 40.2857 10.0715 40.2857H13.9286C14.2836 40.2857 14.5714 40.4776 14.5714 40.7142C14.5714 40.951 14.2836 41.1429 13.9286 41.1429Z" fill="#2D719F"/>
            <Path d="M11.9663 36C5.3332 35.9813 0 30.3935 0 23.6105V23.0076C0 22.6089 0.316224 22.2857 0.705918 22.2857C1.09578 22.2857 1.41184 22.6089 1.41184 23.0076V23.6103C1.41184 29.585 6.09612 34.5224 11.9389 34.5563C17.8055 34.5903 22.5882 29.7205 22.5882 23.7294V23.0076C22.5882 22.6089 22.9042 22.2857 23.2941 22.2857C23.6839 22.2857 24 22.6089 24 23.0076V23.7294C24 30.5071 18.5987 36.0187 11.9663 36Z" fill="#2D719F"/>
            <Path d="M19.9781 24.0746C19.9781 28.4389 16.3992 31.9894 11.9999 31.9894C7.60078 31.9894 4.02188 28.4389 4.02188 24.0746V22.6355V9.35393C4.02188 5.27405 7.14999 1.9058 11.1515 1.48419C11.1797 1.48119 11.2076 1.47872 11.2358 1.47624C11.3321 1.46706 11.4291 1.45982 11.5265 1.45416C11.5596 1.45222 11.5927 1.44993 11.6258 1.44834C11.7499 1.44268 11.8746 1.43897 11.9999 1.43897C12.1256 1.43897 12.25 1.44268 12.3741 1.44834C12.4074 1.44993 12.4404 1.45222 12.4735 1.45416C12.5709 1.45982 12.6679 1.46706 12.7644 1.47624C12.7923 1.47872 12.8205 1.48119 12.8484 1.48419C16.8502 1.9058 19.9781 5.27405 19.9781 9.35393V22.6355V24.0746ZM17.4966 1.76114L17.5005 1.75602C16.164 0.801708 14.625 0.222729 13.0024 0.0533447C12.9697 0.0498123 12.9365 0.0469894 12.9034 0.043808C12.7893 0.0332108 12.6747 0.0243797 12.5596 0.0178452C12.5205 0.0155487 12.4813 0.0128975 12.442 0.0111313C12.2951 0.00441742 12.148 3.8147e-06 11.9999 3.8147e-06C11.8521 3.8147e-06 11.7049 0.00441742 11.5582 0.0111313C11.5187 0.0128975 11.4797 0.0155487 11.4403 0.0178452C11.3253 0.0243797 11.2108 0.0332108 11.0965 0.043808C11.0636 0.0469894 11.0305 0.0498123 10.9975 0.0533447C9.37492 0.222729 7.83596 0.801708 6.49944 1.75602L6.50336 1.76114C4.12461 3.46099 2.57141 6.23013 2.57141 9.35393V22.6355V24.0746C2.57141 29.2323 6.80121 33.4286 11.9999 33.4286C17.1988 33.4286 21.4286 29.2323 21.4286 24.0746V22.6355V9.35393C21.4286 6.23013 19.8755 3.46099 17.4966 1.76114Z" fill="#2D719F"/>
            <Path fill-rule="evenodd" clip-rule="evenodd" d="M16.1437 24.0531C16.3822 23.5474 16.6494 23.0227 17.0024 22.4979C17.3745 21.9541 17.8229 21.4103 18.3095 20.8473C18.0614 20.4562 17.8324 20.0745 17.6225 19.6834C17.0215 20.2654 16.4681 20.8378 15.9815 21.4103C15.514 21.9541 15.1324 22.5075 14.7889 23.0322C13.768 22.8318 12.7567 22.6315 11.7454 22.4216C10.7436 22.2212 9.7418 22.0209 8.73047 21.811C9.07394 22.1735 9.44603 22.5265 9.83721 22.8795C10.8676 23.0799 11.9076 23.2707 12.9571 23.4711C14.0161 23.6714 15.0751 23.8623 16.1437 24.0531ZM13.5391 21.8491C12.5659 21.6392 11.5927 21.4293 10.6291 21.2194C9.65593 21 8.68277 20.7901 7.7096 20.5802C7.44246 20.1986 7.19439 19.8169 6.95587 19.4353C7.9195 19.6452 8.87358 19.8646 9.82767 20.0841C10.7722 20.294 11.7072 20.5134 12.6518 20.7329C13.2242 20.1604 13.8348 19.5498 14.5027 18.9583C15.2182 18.3381 15.972 17.718 16.7829 17.0883C16.8688 17.5081 16.9738 17.9278 17.0883 18.3381C16.3727 18.9487 15.7048 19.5403 15.1038 20.1413C14.5218 20.7138 14.0161 21.2862 13.5391 21.8491ZM11.8694 19.4353C12.5564 18.8247 13.2624 18.2045 14.0447 17.5844C14.8462 16.9451 15.7144 16.3059 16.6208 15.6476C16.6112 15.2087 16.6017 14.7698 16.6208 14.3309C15.6285 15.0083 14.6744 15.6762 13.7776 16.325C12.9189 16.9642 12.1079 17.6035 11.3447 18.2332C10.4383 18.0137 9.5319 17.7847 8.61598 17.5558C7.68098 17.3363 6.73643 17.1169 5.78235 16.907C5.935 17.3172 6.10674 17.7179 6.27847 18.1187C7.23256 18.3381 8.16756 18.548 9.1121 18.7674C10.0376 18.9869 10.9535 19.2159 11.8694 19.4353ZM10.9058 16.8593C10.0089 16.6398 9.10256 16.4109 8.1771 16.1914C7.2421 15.972 6.29756 15.7525 5.34347 15.5331C5.23852 15.1228 5.15265 14.703 5.09541 14.2737C6.04949 14.4931 6.99404 14.7126 7.9195 14.9225C8.84496 15.1419 9.75134 15.3709 10.6482 15.5999C11.5927 14.932 12.585 14.2546 13.6345 13.5677C14.7221 12.8617 15.8766 12.1556 17.0883 11.4401C16.9642 11.9076 16.8593 12.3655 16.7734 12.8235C15.6667 13.5295 14.6172 14.2069 13.6249 14.8939C12.6708 15.5522 11.774 16.22 10.9058 16.8593ZM10.5051 14.1688C11.5546 13.4818 12.6518 12.7663 13.8253 12.0698C15.037 11.3351 16.325 10.61 17.6702 9.84675C17.8992 9.36017 18.1473 8.86404 18.4335 8.35838C17.6893 8.74955 16.9547 9.14073 16.2487 9.52236C15.5522 9.904 14.8557 10.2856 14.1878 10.6577C12.8998 11.3924 11.6786 12.127 10.5241 12.833C9.63685 12.6041 8.75909 12.3751 7.85271 12.1461C6.95587 11.9171 6.03995 11.6881 5.12403 11.4687C5.05724 11.9267 5.01908 12.3655 5 12.814C5.935 13.0334 6.87001 13.2528 7.78593 13.4818C8.71139 13.7108 9.60823 13.9398 10.5051 14.1688ZM10.7627 11.3256C9.90399 11.0966 9.04532 10.8581 8.1771 10.6291C7.29934 10.4001 6.42159 10.1616 5.53429 9.93262C5.71556 9.45558 5.92546 8.96899 6.18306 8.47287C7.02266 8.71139 7.87179 8.94991 8.71139 9.18843C9.54144 9.42695 10.3715 9.67502 11.192 9.91354C11.9076 9.5319 12.6422 9.13119 13.3959 8.74001C14.1592 8.3393 14.9416 7.93858 15.7335 7.53787C16.5635 7.11807 17.4126 6.70781 18.2809 6.28801C19.1682 5.86822 20.0745 5.42934 21 5C20.4943 5.55337 20.0268 6.0972 19.607 6.63149C18.777 7.04174 17.9565 7.452 17.155 7.86225C16.3631 8.26297 15.5903 8.66369 14.8557 9.05486C14.1306 9.44603 13.4246 9.81813 12.7376 10.1998C12.0698 10.5814 11.4019 10.963 10.7627 11.3256Z" fill="#2D719F"/>
        </Svg>
    );
}

export default StudioC;