import React from 'react';
import Svg, { Path, Line } from 'react-native-svg';

import { getSizesByWidth } from './calculator';
  
const Less = (props) => {
    const originalWidth = 24;
    const originalHeight = 22;
    const newWidth = props?.style?.width ? props.style.width : 24;

    const sizes = getSizesByWidth(originalWidth, originalHeight, newWidth);

    return (
        <Svg width="12" height="12" viewBox="0 0 12 12" fill="none" xmlns="http://www.w3.org/2000/svg">
            <Line y1="6" x2="12" y2="6" stroke="#90B0C0" strokeWidth="2"/>
        </Svg>        

    );
}

export default Less;