import React from 'react';
import Svg, { Path } from 'react-native-svg';

import { getSizesByWidth } from './calculator';
  
const Camera = (props) => {
    const originalWidth = 32;
    const originalHeight = 26;
    const newWidth = props?.style?.width ? props.style.width : 40;
    const color = props?.style?.color ? props.style.color : "#00467F";

    const sizes = getSizesByWidth(originalWidth, originalHeight, newWidth);

    return (
        <Svg style={[{width: sizes.width, height: sizes.height}, props.style]} viewBox="0 0 32 26" fill="none" xmlns="http://www.w3.org/2000/svg">
            <Path d="M30.6666 22.3333C30.6666 23.0406 30.3856 23.7189 29.8855 24.219C29.3854 24.719 28.7072 25 27.9999 25H3.99992C3.29267 25 2.6144 24.719 2.1143 24.219C1.6142 23.7189 1.33325 23.0406 1.33325 22.3333V7.66667C1.33325 6.95942 1.6142 6.28115 2.1143 5.78105C2.6144 5.28095 3.29267 5 3.99992 5H9.33325L11.9999 1H19.9999L22.6666 5H27.9999C28.7072 5 29.3854 5.28095 29.8855 5.78105C30.3856 6.28115 30.6666 6.95942 30.6666 7.66667V22.3333Z" stroke={color} strokeWidth="1" strokeLinecap="round" strokeLinejoin="round"/>
            <Path d="M16.0001 19.6667C18.9456 19.6667 21.3334 17.2789 21.3334 14.3333C21.3334 11.3878 18.9456 9 16.0001 9C13.0546 9 10.6667 11.3878 10.6667 14.3333C10.6667 17.2789 13.0546 19.6667 16.0001 19.6667Z" stroke={color} strokeWidth="1" strokeLinecap="round" strokeLinejoin="round"/>
        </Svg>
    );
}

export default Camera;