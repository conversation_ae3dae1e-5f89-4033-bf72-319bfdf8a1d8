import React from 'react';
import Svg, { Path } from 'react-native-svg';

import { getSizesByWidth } from './calculator';
  
const Phone = (props) => {
    const originalWidth = 35;
    const originalHeight = 34;
    const newWidth = props?.style?.width ? props.style.width : 43;

    const sizes = getSizesByWidth(originalWidth, originalHeight, newWidth);

    return (
		<Svg style={[{width: sizes.width, height: sizes.height}, props.style]} viewBox="0 0 35 34" fill="none" xmlns="http://www.w3.org/2000/svg">
			<Path fillRule="evenodd" clipRule="evenodd" d="M3.36263 0C1.80278 0 0.529297 1.27348 0.529297 2.83333V26.6333C0.529297 28.1932 1.80278 29.4667 3.36263 29.4667H4.55133C4.7861 30.6888 5.76361 31.7538 7.1003 32.5358C8.63988 33.4364 10.711 34 12.9959 34C15.2808 34 17.3522 33.4364 18.8915 32.5358C20.228 31.7539 21.2054 30.6888 21.4405 29.4667H31.6962C33.256 29.4667 34.5295 28.1932 34.5295 26.6333V2.83333C34.5295 1.27348 33.256 0 31.6962 0H14.6962C13.1363 0 11.8628 1.27348 11.8628 2.83333H9.59616C9.59616 1.27348 8.32268 0 6.76283 0H3.36263ZM3.36263 1.13333H6.76263C7.71431 1.13333 8.46263 1.88166 8.46263 2.83333V26.6333C8.46263 27.585 7.71431 28.3333 6.76263 28.3333H3.36263C2.41095 28.3333 1.66263 27.585 1.66263 26.6333V2.83333C1.66263 1.88166 2.41095 1.13333 3.36263 1.13333ZM14.696 1.13333H31.696C32.6476 1.13333 33.396 1.88166 33.396 2.83333V26.6333C33.396 27.585 32.6476 28.3333 31.696 28.3333H14.696C13.7443 28.3333 12.996 27.585 12.996 26.6333V2.83333C12.996 1.88166 13.7443 1.13333 14.696 1.13333ZM16.396 2.83333C15.464 2.83333 14.696 3.60134 14.696 4.53333V10.2C14.696 11.132 15.464 11.9 16.396 11.9H29.996C30.928 11.9 31.696 11.132 31.696 10.2V4.53333C31.696 3.60134 30.928 2.83333 29.996 2.83333H16.396ZM9.59596 3.96667H11.8626V25.5H9.59596V3.96667ZM16.396 3.96667H29.996C30.3198 3.96667 30.5626 4.20952 30.5626 4.53333V10.2C30.5626 10.5238 30.3198 10.7667 29.996 10.7667H16.396C16.0722 10.7667 15.8293 10.5238 15.8293 10.2V4.53333C15.8293 4.20952 16.0722 3.96667 16.396 3.96667ZM17.5293 14.1667C16.597 14.1667 15.8293 14.9344 15.8293 15.8667C15.8293 16.7989 16.597 17.5667 17.5293 17.5667C18.4615 17.5667 19.2293 16.7989 19.2293 15.8667C19.2293 14.9344 18.4615 14.1667 17.5293 14.1667ZM23.196 14.1667C22.2637 14.1667 21.496 14.9344 21.496 15.8667C21.496 16.7989 22.2637 17.5667 23.196 17.5667C24.1282 17.5667 24.896 16.7989 24.896 15.8667C24.896 14.9344 24.1282 14.1667 23.196 14.1667ZM28.8626 14.1667C27.9304 14.1667 27.1626 14.9344 27.1626 15.8667C27.1626 16.7989 27.9304 17.5667 28.8626 17.5667C29.7949 17.5667 30.5626 16.7989 30.5626 15.8667C30.5626 14.9344 29.7949 14.1667 28.8626 14.1667ZM17.5293 15.3C17.8491 15.3 18.096 15.5469 18.096 15.8667C18.096 16.1864 17.8491 16.4333 17.5293 16.4333C17.2095 16.4333 16.9626 16.1864 16.9626 15.8667C16.9626 15.5469 17.2095 15.3 17.5293 15.3ZM23.196 15.3C23.5157 15.3 23.7626 15.5469 23.7626 15.8667C23.7626 16.1864 23.5157 16.4333 23.196 16.4333C22.8762 16.4333 22.6293 16.1864 22.6293 15.8667C22.6293 15.5469 22.8762 15.3 23.196 15.3ZM28.8626 15.3C29.1824 15.3 29.4293 15.5469 29.4293 15.8667C29.4293 16.1864 29.1824 16.4333 28.8626 16.4333C28.5429 16.4333 28.296 16.1864 28.296 15.8667C28.296 15.5469 28.5429 15.3 28.8626 15.3ZM17.5293 18.7C16.597 18.7 15.8293 19.4678 15.8293 20.4C15.8293 21.3322 16.597 22.1 17.5293 22.1C18.4615 22.1 19.2293 21.3322 19.2293 20.4C19.2293 19.4678 18.4615 18.7 17.5293 18.7ZM23.196 18.7C22.2637 18.7 21.496 19.4678 21.496 20.4C21.496 21.3322 22.2637 22.1 23.196 22.1C24.1282 22.1 24.896 21.3322 24.896 20.4C24.896 19.4678 24.1282 18.7 23.196 18.7ZM28.8626 18.7C27.9304 18.7 27.1626 19.4678 27.1626 20.4C27.1626 21.3322 27.9304 22.1 28.8626 22.1C29.7949 22.1 30.5626 21.3322 30.5626 20.4C30.5626 19.4678 29.7949 18.7 28.8626 18.7ZM17.5293 19.8333C17.8491 19.8333 18.096 20.0802 18.096 20.4C18.096 20.7198 17.8491 20.9667 17.5293 20.9667C17.2095 20.9667 16.9626 20.7198 16.9626 20.4C16.9626 20.0802 17.2095 19.8333 17.5293 19.8333ZM23.196 19.8333C23.5157 19.8333 23.7626 20.0802 23.7626 20.4C23.7626 20.7198 23.5157 20.9667 23.196 20.9667C22.8762 20.9667 22.6293 20.7198 22.6293 20.4C22.6293 20.0802 22.8762 19.8333 23.196 19.8333ZM28.8626 19.8333C29.1824 19.8333 29.4293 20.0802 29.4293 20.4C29.4293 20.7198 29.1824 20.9667 28.8626 20.9667C28.5429 20.9667 28.296 20.7198 28.296 20.4C28.296 20.0802 28.5429 19.8333 28.8626 19.8333ZM17.5293 23.2333C16.597 23.2333 15.8293 24.0011 15.8293 24.9333C15.8293 25.8656 16.597 26.6333 17.5293 26.6333C18.4615 26.6333 19.2293 25.8656 19.2293 24.9333C19.2293 24.0011 18.4615 23.2333 17.5293 23.2333ZM23.196 23.2333C22.2637 23.2333 21.496 24.0011 21.496 24.9333C21.496 25.8656 22.2637 26.6333 23.196 26.6333C24.1282 26.6333 24.896 25.8656 24.896 24.9333C24.896 24.0011 24.1282 23.2333 23.196 23.2333ZM28.8626 23.2333C27.9304 23.2333 27.1626 24.0011 27.1626 24.9333C27.1626 25.8656 27.9304 26.6333 28.8626 26.6333C29.7949 26.6333 30.5626 25.8656 30.5626 24.9333C30.5626 24.0011 29.7949 23.2333 28.8626 23.2333ZM17.5293 24.3667C17.8491 24.3667 18.096 24.6136 18.096 24.9333C18.096 25.2531 17.8491 25.5 17.5293 25.5C17.2095 25.5 16.9626 25.2531 16.9626 24.9333C16.9626 24.6136 17.2095 24.3667 17.5293 24.3667ZM23.196 24.3667C23.5157 24.3667 23.7626 24.6136 23.7626 24.9333C23.7626 25.2531 23.5157 25.5 23.196 25.5C22.8762 25.5 22.6293 25.2531 22.6293 24.9333C22.6293 24.6136 22.8762 24.3667 23.196 24.3667ZM28.8626 24.3667C29.1824 24.3667 29.4293 24.6136 29.4293 24.9333C29.4293 25.2531 29.1824 25.5 28.8626 25.5C28.5429 25.5 28.296 25.2531 28.296 24.9333C28.296 24.6136 28.5429 24.3667 28.8626 24.3667ZM9.59596 26.6333H11.8626C11.8626 28.1932 13.1361 29.4667 14.696 29.4667H20.2685C20.0366 30.1922 19.3733 30.9408 18.3196 31.5572C16.988 32.3364 15.0924 32.8667 12.996 32.8667C10.8996 32.8667 9.00423 32.3364 7.67234 31.5572C6.61866 30.9407 5.95511 30.1922 5.7234 29.4667H6.76263C8.32249 29.4667 9.59596 28.1932 9.59596 26.6333Z" fill="#00467F"/>
		</Svg>

    );
}

export default Phone;