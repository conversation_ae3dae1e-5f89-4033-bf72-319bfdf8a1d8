import React from 'react';
import Svg, { Path } from 'react-native-svg';

import { getSizesByWidth } from './calculator';
  
const Document2 = (props) => {
    const originalWidth = 25;
    const originalHeight = 28;
    const newWidth = props?.style?.width ? props.style.width : 33;

    const sizes = getSizesByWidth(originalWidth, originalHeight, newWidth);

    return (
		<Svg style={[{width: sizes.width, height: sizes.height}, props.style]} viewBox="0 0 25 28" fill="none" xmlns="http://www.w3.org/2000/svg">
			<Path d="M24.3753 3.75001H21.2501V0.625072L20.6252 0H6.87505L6.4312 0.181222L0.181013 6.43141L0 6.87526V23.1251L0.625072 23.7502H3.75V26.8751L4.37508 27.5002H24.3749L25 26.8751L24.9998 4.37508L24.3753 3.75001ZM6.24987 2.13134V6.25008H2.13113L6.24987 2.13134ZM1.24994 22.4998V7.49974H6.87494L7.50002 6.87467V1.24966H19.9999V22.4993L1.24994 22.4998ZM23.75 26.2498H4.99994V23.7499H20.6251L21.2502 23.1248L21.25 4.99999H23.7501L23.75 26.2498Z" fill="white"/>
			<Path d="M7.50024 10.6253H16.2501V11.8752H7.50024V10.6253Z" fill="white"/>
			<Path d="M5 16.8748H16.25V18.1248H5V16.8748Z" fill="white"/>
		</Svg>

    );
}

export default Document2;