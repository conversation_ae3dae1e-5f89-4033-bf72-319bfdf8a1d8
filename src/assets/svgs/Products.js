import React from 'react';
import Svg, { Path } from 'react-native-svg';

import { getSizesByWidth } from './calculator';
  
const Products = (props) => {
    const originalWidth = 52;
    const originalHeight = 52;
    const newWidth = props?.style?.width ? props.style.width : 52;

    const sizes = getSizesByWidth(originalWidth, originalHeight, newWidth);

    return (
        <Svg style={[{width: sizes.width, height: sizes.height}, props.style]} viewBox="0 0 52 52" fill="none" xmlns="http://www.w3.org/2000/svg">
            <Path d="M50.955 21.4792L27.705 0.812486C27.2336 0.398328 26.6275 0.169922 26 0.169922C25.3725 0.169922 24.7664 0.398328 24.295 0.812486L1.04498 21.4792C0.649824 21.8287 0.371165 22.2909 0.246472 22.8035C0.121778 23.3161 0.157027 23.8547 0.347482 24.3467C0.53482 24.8322 0.864419 25.2498 1.29311 25.5448C1.7218 25.8398 2.22959 25.9985 2.74998 26H5.33332V49.25C5.33332 49.9351 5.60549 50.5922 6.08996 51.0767C6.57443 51.5611 7.23151 51.8333 7.91665 51.8333H44.0833C44.7685 51.8333 45.4255 51.5611 45.91 51.0767C46.3945 50.5922 46.6666 49.9351 46.6666 49.25V26H49.25C49.7704 25.9985 50.2782 25.8398 50.7069 25.5448C51.1355 25.2498 51.4651 24.8322 51.6525 24.3467C51.8429 23.8547 51.8782 23.3161 51.7535 22.8035C51.6288 22.2909 51.3501 21.8287 50.955 21.4792ZM28.5833 46.6667H23.4167V38.9167C23.4167 38.2315 23.6888 37.5744 24.1733 37.09C24.6578 36.6055 25.3148 36.3333 26 36.3333C26.6851 36.3333 27.3422 36.6055 27.8267 37.09C28.3111 37.5744 28.5833 38.2315 28.5833 38.9167V46.6667ZM41.5 46.6667H33.75V38.9167C33.75 36.8612 32.9335 34.89 31.4801 33.4366C30.0267 31.9832 28.0554 31.1667 26 31.1667C23.9446 31.1667 21.9733 31.9832 20.5199 33.4366C19.0665 34.89 18.25 36.8612 18.25 38.9167V46.6667H10.5V26H41.5V46.6667ZM9.54415 20.8333L26 6.21165L42.4558 20.8333H9.54415Z" fill="#4EA1CC"/>
        </Svg>

    );
}

export default Products;