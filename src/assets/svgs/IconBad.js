import React from 'react';
import Svg, { Path } from 'react-native-svg';

import { getSizesByWidth } from './calculator';
  
const IconBad = (props) => {
    const originalWidth = 35;
    const originalHeight = 35;
    const newWidth = props?.style?.width ? props.style.width : 45;

    const sizes = getSizesByWidth(originalWidth, originalHeight, newWidth);

    return (
		<Svg style={[{width: sizes.width, height: sizes.height}, props.style]} viewBox="0 0 35 35" fill="none" xmlns="http://www.w3.org/2000/svg">
			<Path d="M17.5 35C7.8344 35 0 27.1656 0 17.5C0 7.8344 7.8344 0 17.5 0C27.1656 0 35 7.8344 35 17.5C35 27.1656 27.1656 35 17.5 35ZM17.5 33.6C26.3912 33.6 33.6 26.3913 33.6 17.5C33.6 8.60872 26.3913 1.4 17.5 1.4C8.60872 1.4 1.4 8.60872 1.4 17.5C1.4 26.3913 8.60872 33.6 17.5 33.6ZM11.2 15.4C11.9728 15.4 12.6 14.7728 12.6 14C12.6 13.2272 11.9728 12.6 11.2 12.6C10.4272 12.6 9.8 13.2272 9.8 14C9.8 14.7728 10.4272 15.4 11.2 15.4ZM23.8 15.4C24.5728 15.4 25.2 14.7728 25.2 14C25.2 13.2272 24.5728 12.6 23.8 12.6C23.0272 12.6 22.4 13.2272 22.4 14C22.4 14.7728 23.0272 15.4 23.8 15.4ZM17.4959 25.2C13.3 25.2 11.2 26.6 11.2 26.6C11.2 26.6 12.6 22.4 17.5 22.4C22.4 22.4 23.8 26.6 23.8 26.6C23.8 26.6 21.6916 25.2 17.4959 25.2ZM23.271 8.3536L22.4885 9.51424L27.1309 12.6461L27.9134 11.4854L23.271 8.3536ZM7.08696 11.4854L7.86946 12.6461L12.5119 9.51424L11.7294 8.3536L7.08696 11.4854Z" fill="#828282"/>
		</Svg>

    );
}

export default IconBad;