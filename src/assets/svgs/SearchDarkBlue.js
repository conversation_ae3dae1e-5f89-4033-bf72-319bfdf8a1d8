import React from 'react';
import Svg, { Path } from 'react-native-svg';

import { getSizesByWidth } from './calculator';
  
const SearchIcon = (props) => {
    const originalWidth = 32;
    const originalHeight = 32;
    const newWidth = props?.style?.width ? props.style.width : 42;

    const sizes = getSizesByWidth(originalWidth, originalHeight, newWidth);

    return (
        <Svg style={[{width: sizes.width, height: sizes.height}, props.style]} viewBox="0 0 32 32" fill="none" xmlns="http://www.w3.org/2000/svg">
            <Path d="M14.6667 25.3333C20.5577 25.3333 25.3333 20.5577 25.3333 14.6667C25.3333 8.77563 20.5577 4 14.6667 4C8.77563 4 4 8.77563 4 14.6667C4 20.5577 8.77563 25.3333 14.6667 25.3333Z" stroke="#828282" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
            <Path d="M28 28L22.2 22.2" stroke="#828282" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
        </Svg>
    );
}

export default SearchIcon;