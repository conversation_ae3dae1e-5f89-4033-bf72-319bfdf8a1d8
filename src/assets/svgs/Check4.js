import React from 'react';
import Svg, { Path } from 'react-native-svg';

import { getSizesByWidth } from './calculator';
  
const Check4 = (props) => {
    const originalWidth = 79;
    const originalHeight = 79;
    const newWidth = props?.style?.width ? props.style.width : 80;

    const sizes = getSizesByWidth(originalWidth, originalHeight, newWidth);

    return (
        <Svg style={[{width: sizes.width, height: sizes.height}, props.style]} viewBox="0 0 79 79" fill="none" xmlns="http://www.w3.org/2000/svg">
            <Path d="M72.4168 36.4717V39.5C72.4128 46.5982 70.1143 53.505 65.8642 59.1902C61.6141 64.8754 55.6401 69.0344 48.8332 71.047C42.0263 73.0596 34.7511 72.8179 28.0928 70.358C21.4344 67.8981 15.7497 63.3517 11.8862 57.397C8.02283 51.4423 6.18781 44.3982 6.65485 37.3154C7.12189 30.2325 9.86597 23.4904 14.4778 18.0945C19.0897 12.6986 25.3222 8.93812 32.246 7.37381C39.1697 5.80949 46.4135 6.52518 52.8973 9.41416" stroke="#60BA64" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
            <Path d="M72.4167 13.1667L39.5 46.1162L29.625 36.2412" stroke="#60BA64" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
        </Svg>

    );
}

export default Check4;