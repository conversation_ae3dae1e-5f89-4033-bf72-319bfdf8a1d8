import React from 'react';
import Svg, { Path } from 'react-native-svg';

import { getSizesByWidth } from './calculator';
  
const Notification = (props) => {
    const originalWidth = 34;
    const originalHeight = 38;
    const newWidth = props?.style?.width ? props.style.width : 34;
    const color = props?.style?.color ? props.style.color : "#4EA1CC";

    const sizes = getSizesByWidth(originalWidth, originalHeight, newWidth);

    return (
        <Svg style={[{width: sizes.width, height: sizes.height}, props.style]} viewBox="0 0 34 38" fill="none" xmlns="http://www.w3.org/2000/svg">
            <Path d="M27 12.3334C27 9.68121 25.9464 7.13767 24.0711 5.26231C22.1957 3.38694 19.6522 2.33337 17 2.33337C14.3478 2.33337 11.8043 3.38694 9.92893 5.26231C8.05357 7.13767 7 9.68121 7 12.3334C7 24 2 27.3334 2 27.3334H32C32 27.3334 27 24 27 12.3334Z" stroke={color} strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
            <Path d="M19.8834 34C19.5903 34.5051 19.1698 34.9244 18.6637 35.2159C18.1577 35.5073 17.584 35.6608 17 35.6608C16.4161 35.6608 15.8423 35.5073 15.3363 35.2159C14.8303 34.9244 14.4097 34.5051 14.1167 34" stroke={color} strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
        </Svg>

    );
}

export default Notification;