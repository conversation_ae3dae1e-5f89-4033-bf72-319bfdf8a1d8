import React from 'react';
import Svg, { Path } from 'react-native-svg';

import { getSizesByWidth } from './calculator';
  
const ArrowRightGray = (props) => {
    const originalWidth = 36;
    const originalHeight = 36;
    const newWidth = props?.style?.width ? props.style.width : 45;

    const sizes = getSizesByWidth(originalWidth, originalHeight, newWidth);

    return (
      <Svg style={[{width: sizes.width, height: sizes.height}, props.style]} viewBox="0 0 36 36" fill="none" xmlns="http://www.w3.org/2000/svg">
		<Path d="M13.5 27L22.5 18L13.5 9" stroke="#BDBDBD" strokeWidth="3" strokeLinecap="round" strokeLinejoin="round"/>
	  </Svg>       
    );
}

export default ArrowRightGray;