import React from 'react';
import Svg, { <PERSON>, ClipPath, G, Defs, Rect } from 'react-native-svg';

import { getSizesByWidth } from './calculator';
  
const Alert5 = (props) => {
    const originalWidth = 19;
    const originalHeight = 19;
    const newWidth = props?.style?.width ? props.style.width : 19;

    const sizes = getSizesByWidth(originalWidth, originalHeight, newWidth);

    return (
        <Svg style={[{width: sizes.width, height: sizes.height}, props.style]} viewBox="0 0 19 19" fill="none" xmlns="http://www.w3.org/2000/svg">
            <G clipPath="url(#clip0_7278_12093)">
                <Path d="M6.22248 1.58337H12.7775L17.4166 6.22254V12.7775L12.7775 17.4167H6.22248L1.58331 12.7775V6.22254L6.22248 1.58337Z" stroke="#FFF" strokeWidth="1.2" strokeLinecap="round" strokeLinejoin="round"/>
                <Path d="M9.5 12.6666H9.50792" stroke="#FFF" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round"/>
                <Path d="M9.5 6.33337V9.50004" stroke="#FFF" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round"/>
            </G>
            <Defs>
                <ClipPath id="clip0_7278_12093">
                    <Rect width="19" height="19" fill="white"/>
                </ClipPath>
            </Defs>
        </Svg>

    );
}

export default Alert5;