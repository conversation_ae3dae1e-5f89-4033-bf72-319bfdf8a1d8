import React from 'react';
import Svg, { Mask, Path, Rect } from 'react-native-svg';

import { getSizesByWidth } from './calculator';
  
const Hub = (props) => {
    const originalWidth = 30;
    const originalHeight = 38;
    const newWidth = props?.style?.width ? props.style.width : 34;

    const sizes = getSizesByWidth(originalWidth, originalHeight, newWidth);

    return (
        <Svg style={[{width: sizes.width, height: sizes.height}, props.style]} viewBox="0 0 30 38" fill="none" xmlns="http://www.w3.org/2000/svg">
            <Path d="M15.0086 14.3921L29.8673 24.3881L15.0086 33.2733L0.768354 24.3881L15.0086 14.3921Z" fill="#2D719F"/>
            <Path d="M15.0086 10.888L29.8673 20.884L15.0086 29.7692L0.768354 20.884L15.0086 10.888Z" fill="white"/>
            <Path d="M15.0086 8.55192L29.8673 18.5479L15.0086 27.4331L0.768354 18.5479L15.0086 8.55192Z" fill="#2D719F"/>
            <Path d="M15.0086 4.93497L29.8673 14.931L15.0086 23.8162L0.768354 14.931L15.0086 4.93497Z" fill="white"/>
            <Path d="M15.011 3.44864L28.0332 12.2092L15.011 19.9963L2.53076 12.2092L15.011 3.44864Z" fill="#2D719F"/>
            <Rect x="14.406" y="20.1909" width="1.5574" height="14.406" fill="white"/>
        </Svg>
    );
}

export default Hub;