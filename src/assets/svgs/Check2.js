import React from 'react';
import Svg, { Path } from 'react-native-svg';

import { getSizesByWidth } from './calculator';
  
const Check2 = (props) => {
    const originalWidth = 37;
    const originalHeight = 37;
    const newWidth = props?.style?.width ? props.style.width : 43;
    const color = props?.style?.color ? props.style.color : "#1B9C20";

    const sizes = getSizesByWidth(originalWidth, originalHeight, newWidth);

    return (
        <Svg style={[{width: sizes.width, height: sizes.height}, props.style]} viewBox="0 0 37 37" fill="none" xmlns="http://www.w3.org/2000/svg">
            <Path d="M33.9167 17.0816V18.5C33.9148 21.8244 32.8383 25.0592 30.8477 27.7219C28.8572 30.3846 26.0592 32.3325 22.8712 33.2751C19.6831 34.2177 16.2758 34.1045 13.1573 32.9524C10.0388 31.8003 7.37634 29.671 5.56689 26.8821C3.75745 24.0932 2.89801 20.7941 3.11675 17.4768C3.33549 14.1595 4.62069 11.0018 6.78068 8.47461C8.94066 5.94743 11.8597 4.18617 15.1025 3.45351C18.3452 2.72086 21.7379 3.05606 24.7746 4.40912" stroke={color} strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
            <Path d="M33.9167 6.16675L18.5 21.5988L13.875 16.9738" stroke={color} strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
        </Svg>

    );
}

export default Check2;