import React from 'react';
import Svg, { Path } from 'react-native-svg';

import { getSizesByWidth } from './calculator';
  
const QrCode = (props) => {
    const originalWidth = 115;
    const originalHeight = 115;
    const newWidth = props?.style?.width ? props.style.width : 115;

    const sizes = getSizesByWidth(originalWidth, originalHeight, newWidth);

    return (
        <Svg style={[{width: sizes.width, height: sizes.height}, props.style]} viewBox="0 0 115 115" fill="none" xmlns="http://www.w3.org/2000/svg">
			<Path d="M36.7998 0H46V4.59958H36.7998V0Z" fill="black"/>
			<Path d="M55.2 0H59.7995V4.59958H68.9997V9.19998H55.2V0Z" fill="black"/>
			<Path d="M73.5998 0H78.2002V50.5999H73.5998V41.4005H69.0002V46.0001H64.3982V41.4005H59.8V36.8001H55.2005V23.0004H50.6001V13.8002H36.8003V9.2006H46.0005V4.60019H50.6001V9.2006H55.2005V13.8002H69.0002V18.4006H59.8V32.2003H64.3982V27.5999H69.0002V36.8001H73.5998V23.0004H69.0002V18.4008H73.5998V0Z" fill="black"/>
			<Path d="M0 0V32.1999H32.1999V0H0ZM27.5999 27.5999H4.59999V4.59999H27.5999V27.5999Z" fill="black"/>
			<Path d="M82.7998 0V32.1999H115V0H82.7998ZM110.4 27.5999H87.3998V4.59999H110.4V27.5999Z" fill="black"/>
			<Path d="M9.19995 9.2002H22.9997V23H9.19995V9.2002Z" fill="black"/>
			<Path d="M91.9998 9.2002H105.8V23H91.9998V9.2002Z" fill="black"/>
			<Path d="M36.7998 18.4004H46V27.5998H41.4004V23.0002H36.8L36.7998 18.4004Z" fill="black"/>
			<Path d="M36.8 27.6001H41.4004V32.2005H46V27.6001H50.5996V36.8003H55.2V41.4007H59.7995V46.0003H55.2V50.5998H50.5996V41.4005H46V36.8001H36.7998L36.8 27.6001Z" fill="black"/>
			<Path d="M23 36.8003H32.2002V41.3999H23V36.8003Z" fill="black"/>
			<Path d="M91.9998 36.8003H96.5993V41.3999H91.9998V36.8003Z" fill="black"/>
			<Path d="M101.2 36.8003H105.799V46.0005H114.999V59.8002H105.799V50.6001H101.2V36.8003Z" fill="black"/>
			<Path d="M110.4 36.8003H114.999V41.3999H110.4V36.8003Z" fill="black"/>
			<Path d="M4.59961 41.3999H13.7998V45.9995H4.59961V41.3999Z" fill="black"/>
			<Path d="M18.4001 41.3999H22.9997V45.9995H18.4001V41.3999Z" fill="black"/>
			<Path d="M0 46H4.59958V50.5996H0V46Z" fill="black"/>
			<Path d="M27.5999 46H32.1994V50.5996H27.5999V46Z" fill="black"/>
			<Path d="M9.19981 50.5996H18.4V59.7998H13.7996V64.4002H9.20001V59.7998H4.59961V55.2002H9.20001L9.19981 50.5996Z" fill="black"/>
			<Path d="M23 50.5996H27.5996V55.1992H23V50.5996Z" fill="black"/>
			<Path d="M68.9998 50.5996H73.5993V55.1992H68.9998V50.5996Z" fill="black"/>
			<Path d="M27.5999 55.1997H32.1994V59.7993H27.5999V55.1997Z" fill="black"/>
			<Path d="M0 59.7998H4.59958V78.2002H0V59.7998Z" fill="black"/>
			<Path d="M18.4001 59.7998H22.9997V64.4018H18.4001V59.7998Z" fill="black"/>
			<Path d="M13.8 64.3999H18.3996V68.9995H13.8V64.3999Z" fill="black"/>
			<Path d="M27.5999 64.3999H32.1994V68.9995H27.5999V64.3999Z" fill="black"/>
			<Path d="M36.7998 64.3999H41.4002V68.9995H45.9998V78.1997H41.4002V82.7992H36.7998V64.3999Z" fill="black"/>
			<Path d="M87.3999 64.3999H96.5984V68.9995H87.3999V64.3999Z" fill="black"/>
			<Path d="M105.8 64.3999H115V68.9995H105.8V64.3999Z" fill="black"/>
			<Path d="M18.4001 69H22.9997V73.5996H18.4001V69Z" fill="black"/>
			<Path d="M9.19995 73.5996H13.7995V78.2017H9.19995V73.5996Z" fill="black"/>
			<Path d="M23 73.5996H32.2002V78.2017H23V73.5996Z" fill="black"/>
			<Path d="M46 78.1997H50.5996V82.7993H46V78.1997Z" fill="black"/>
			<Path d="M115 78.2V73.5996H105.8V69H96.5994V73.5996H78.1998V69H82.7994V64.4004H87.3998V59.8H96.5992V64.4004H101.2V50.6006L96.5992 50.5998V46.0003H91.9996V50.5998H87.4V46.0003H91.9996V41.4007H87.4V36.8011L82.7996 36.8003V50.6001H78.2001V64.3998L68.9999 64.4006V55.2004H64.3978V64.4006H59.7982V50.6009H55.1987V59.801H50.5991V50.6009H36.7993V55.2004H41.3989V64.4006H46.0001V69.0002H50.5997V64.4006H55.1993L55.2001 69.0002H50.6005V78.2004H55.2001V82.8H59.7997V73.5998H64.3992V82.8H68.9996V78.2004H73.5992V82.8H68.9996V87.4004H64.3976V101.2H55.1999V105.8H64.3984L64.3976 110.4H55.1999V115H78.1998V110.4H73.5994V105.8H68.9999V91.9999H73.5994V96.5995H78.1998V101.2H82.7994V96.5995H96.5992V101.2H101.2V96.5995H105.799V91.9999H101.2V82.7998H96.5992V78.2002L115 78.2ZM73.5999 73.5996H69.0003V69H73.5999V73.5996ZM92.0002 91.9999H78.2005V78.2002H92.0002V91.9999Z" fill="black"/>
			<Path d="M50.5999 82.7998H55.1994V92H50.5999V82.7998Z" fill="black"/>
			<Path d="M82.7998 82.7998H87.4018V87.4018H82.7998V82.7998Z" fill="black"/>
			<Path d="M110.4 82.7998H114.999V92H110.4V82.7998Z" fill="black"/>
			<Path d="M0 82.7998V115H32.1999V82.7998H0ZM27.5999 110.4H4.59999V87.3998H27.5999V110.4Z" fill="black"/>
			<Path d="M36.7998 87.3999H46V96.5993H50.5996V101.2H46V105.799H41.4004V91.9995H36.8L36.7998 87.3999Z" fill="black"/>
			<Path d="M9.19995 92H22.9997V105.8H9.19995V92Z" fill="black"/>
			<Path d="M55.2 92H59.7995V96.5996H55.2V92Z" fill="black"/>
			<Path d="M110.4 96.5996H114.999V115H110.4V96.5996Z" fill="black"/>
			<Path d="M73.5999 101.2H78.2019V105.799H73.5999V101.2Z" fill="black"/>
			<Path d="M82.7998 101.2H92V105.799H101.2V114.999H96.5998V110.4H87.4004V114.999H82.8L82.7998 101.2Z" fill="black"/>
			<Path d="M101.2 101.2H105.799V105.799H101.2V101.2Z" fill="black"/>
			<Path d="M50.5999 105.8H55.1994V110.402H50.5999V105.8Z" fill="black"/>
			<Path d="M41.3999 110.4H45.9995V114.999H41.3999V110.4Z" fill="black"/>
		</Svg>

    );
}

export default QrCode;