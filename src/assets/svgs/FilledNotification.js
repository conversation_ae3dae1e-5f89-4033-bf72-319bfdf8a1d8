import React from 'react';
import Svg, { Path } from 'react-native-svg';

import { getSizesByWidth } from './calculator';
  
const FilledNotification = (props) => {
    const originalWidth = 16;
    const originalHeight = 16;
    const newWidth = props?.style?.width ? props.style.width : 16;
    const color = props?.style?.color ? props.style.color : "#FF6542";

    const sizes = getSizesByWidth(originalWidth, originalHeight, newWidth);

    return (
        <Svg style={[{width: sizes.width, height: sizes.height}, props.style]} viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
            <Path d="M12 5.33325C12 4.27239 11.5786 3.25497 10.8284 2.50482C10.0783 1.75468 9.06087 1.33325 8 1.33325C6.93913 1.33325 5.92172 1.75468 5.17157 2.50482C4.42143 3.25497 4 4.27239 4 5.33325C4 9.99992 2 11.3333 2 11.3333H14C14 11.3333 12 9.99992 12 5.33325Z" fill={color} stroke={color} strokeWidth="1.33333" strokeLinecap="round" strokeLinejoin="round"/>
            <Path d="M9.15335 14C9.03614 14.2021 8.86791 14.3698 8.6655 14.4864C8.46309 14.6029 8.2336 14.6643 8.00001 14.6643C7.76643 14.6643 7.53694 14.6029 7.33453 14.4864C7.13212 14.3698 6.96389 14.2021 6.84668 14" stroke={color} strokeWidth="1.33333" strokeLinecap="round" strokeLinejoin="round"/>
        </Svg>


    );
}

export default FilledNotification;