import React from 'react';
import Svg, { <PERSON>, G, Line, ClipPath, Rect, Defs} from 'react-native-svg';

import { getSizesByWidth } from './calculator';
  
const MegaPhoneLight = (props) => {
    const originalWidth = 32;
    const originalHeight = 24;
    const newWidth = props?.style?.width ? props.style.width : 38;

    const sizes = getSizesByWidth(originalWidth, originalHeight, newWidth);

    return (
        <Svg style={[{width: sizes.width, height: sizes.height}, props.style]} viewBox="0 0 32 24" fill="none" xmlns="http://www.w3.org/2000/svg">
            <Path d="M23.6691 8.6308C22.6003 4.4743 20.5082 0 18.4417 0C18.3393 0 18.2373 0.0113293 18.139 0.0335068C17.6886 0.135711 17.3301 0.458247 17.0669 0.981809C17.0389 1.00254 17.0084 1.01845 16.9853 1.04617C15.8285 2.43607 11.6858 7.02676 6.95984 8.10027L3.34889 8.97601C3.33453 8.97914 3.32067 8.98324 3.30657 8.98734C2.20001 9.27323 1.26615 9.92718 0.664934 10.8403C0.0362611 11.7961 -0.155069 12.9254 0.12706 14.0213C0.409208 15.1169 1.1279 16.0399 2.15159 16.6201C3.17068 17.1979 4.37541 17.3696 5.54375 17.1046L6.29245 16.9347H6.29321H6.29347L7.19404 16.73L10.182 22.5002C10.5711 23.2508 11.3734 23.6883 12.2078 23.6883C12.5377 23.6883 12.8727 23.6204 13.1884 23.4757C14.3029 22.9654 14.7684 21.6982 14.2257 20.6503L11.8098 15.9849C16.1235 15.9507 20.5285 17.8348 21.5352 18.2937C21.8955 18.5461 22.2605 18.6859 22.625 18.6859C22.7274 18.6859 22.8295 18.6746 22.9277 18.6522C23.8816 18.4355 24.4298 17.2437 24.5133 15.2051C24.5885 13.3743 24.2889 11.0397 23.6692 8.6305L23.6691 8.6308ZM5.31817 16.2317C4.39711 16.4414 3.44729 16.3059 2.64443 15.8508C1.84137 15.3957 1.27653 14.6706 1.05544 13.8103C0.834085 12.9497 0.98362 12.0624 1.47764 11.3124C1.96421 10.5726 2.7301 10.0476 3.63684 9.83305L3.95516 9.76049L5.60291 16.1666L5.31817 16.2317ZM12.7696 22.668C12.1292 22.9612 11.3528 22.7095 11.0414 22.1071L9.95688 20.0133L12.3136 19.0129L13.3657 21.0443C13.6779 21.6462 13.4101 22.3747 12.7696 22.668L12.7696 22.668ZM11.8953 18.2044L9.53854 19.2048L8.14523 16.5141L9.25199 16.2624C9.74882 16.1499 10.257 16.0754 10.7702 16.032L11.8953 18.2044ZM9.02709 15.3894L6.53241 15.9559L4.88466 9.54909L7.18465 9.02552C11.1588 8.12276 14.6926 4.92394 16.5929 2.90849C16.5759 3.09193 16.5621 3.2814 16.5536 3.48099C16.5144 4.43411 16.579 5.52588 16.7357 6.69184C16.7381 6.70847 16.7334 6.72438 16.7375 6.74125C16.7391 6.7468 16.7434 6.75065 16.745 6.7562C16.8891 7.81058 17.1069 8.92402 17.398 10.0558C17.6832 11.1653 18.0423 12.2971 18.4532 13.3609C18.4555 13.3819 18.4504 13.4021 18.4558 13.4231C18.4717 13.4843 18.5025 13.5364 18.5404 13.5839C19.0121 14.7721 19.5503 15.8619 20.1246 16.7287C17.5535 15.7703 13.0257 14.4806 9.02682 15.3894L9.02709 15.3894ZM17.7577 7.0881C18.4779 7.27444 19.3331 8.19839 19.6953 9.6052C20.0546 11.0033 19.7502 12.185 19.2051 12.6666C18.8924 11.8212 18.5939 10.8823 18.3271 9.84384C18.0847 8.90202 17.8957 7.97544 17.7577 7.0881H17.7577ZM23.5581 15.1703C23.4886 16.8536 23.0787 17.6934 22.7027 17.7788C22.6778 17.7846 22.6519 17.787 22.6247 17.787C22.5401 17.787 22.446 17.7624 22.3439 17.7152C22.3039 17.6679 22.2538 17.6269 22.1931 17.5973C22.1733 17.5876 22.1217 17.5623 22.0445 17.5264C21.3528 16.9903 20.4191 15.6341 19.5519 13.5487C20.6292 12.9268 21.0989 11.2375 20.6246 9.39342C20.1477 7.53971 18.9024 6.23244 17.6318 6.15462C17.5215 5.19978 17.4763 4.30498 17.5087 3.51484C17.5779 1.83157 17.9878 0.992185 18.3636 0.90684C18.3885 0.901054 18.4144 0.898644 18.4416 0.898644C19.3634 0.898644 21.4244 3.7272 22.7397 8.84137C23.3394 11.1724 23.6297 13.4197 23.5582 15.1698L23.5581 15.1703Z" fill="white"/>
            <Path d="M27.3199 8.35444C27.1088 8.35034 26.9187 8.21391 26.8661 8.0119C26.8033 7.77109 26.9603 7.52786 27.2165 7.46856L30.3399 6.74828C30.5958 6.68874 30.8549 6.83675 30.918 7.07756C30.9808 7.31837 30.8239 7.5616 30.5676 7.6209L27.4443 8.34118C27.4027 8.35082 27.3609 8.3554 27.3199 8.35444V8.35444Z" fill="white"/>
            <Path d="M24.9542 3.44474C24.8262 3.43944 24.7008 3.38592 24.611 3.28709C24.4396 3.0981 24.4643 2.81438 24.6654 2.65357L27.4817 0.402567C27.6826 0.242262 27.9847 0.264681 28.1553 0.45367C28.3266 0.642658 28.302 0.926384 28.1009 1.08719L25.2846 3.33819C25.1891 3.41509 25.0709 3.44956 24.9542 3.44474L24.9542 3.44474Z" fill="white"/>
            <Path d="M31.525 14.5341C31.4622 14.5442 31.396 14.5427 31.3293 14.5271L27.8669 13.7232C27.6101 13.6636 27.4544 13.4202 27.5175 13.1796C27.5801 12.9388 27.8387 12.7905 28.0959 12.8513L31.5584 13.6552C31.8151 13.7147 31.9708 13.9582 31.9077 14.1988C31.8613 14.3774 31.7066 14.5046 31.525 14.5341Z" fill="white"/>
        </Svg>
        
    );
}

export default MegaPhoneLight;