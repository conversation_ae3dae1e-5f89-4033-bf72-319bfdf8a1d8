import React from 'react';
import Svg, { Path } from 'react-native-svg';

import { getSizesByWidth } from './calculator';
  
const SalesAcademy = (props) => {
    const originalWidth = 30;
    const originalHeight = 26;
    const newWidth = props?.style?.width ? props.style.width : 30;

    const sizes = getSizesByWidth(originalWidth, originalHeight, newWidth);

    return (
        <Svg style={[{width: sizes.width, height: sizes.height}, props.style]} viewBox="0 0 30 26" fill="none" xmlns="http://www.w3.org/2000/svg">
            <Path fillRule="evenodd" clipRule="evenodd" d="M6.16949 22.6228V19.8305H0V24.678V23.3559C1.82035 23.3559 3.95865 23.1068 6.16949 22.6228Z" fill="white"/>
            <Path fillRule="evenodd" clipRule="evenodd" d="M13.6609 20.0673V14.983H7.49146V22.3072C9.5954 21.7632 11.7206 21.0126 13.6609 20.0673ZM13.6609 23.9389V26H7.49146V25.9386C9.56029 25.4539 11.6658 24.7851 13.6609 23.9389Z" fill="white"/>
            <Path fillRule="evenodd" clipRule="evenodd" d="M21.1526 14.1415C19.6214 16.2053 17.5432 17.9424 14.9832 19.3762V8.37288H21.1526V14.1415ZM21.1526 19.321V26H14.9832V23.3437C15.4309 23.13 15.871 22.9068 16.3016 22.6743C18.1113 21.6974 19.7355 20.5798 21.1526 19.321Z" fill="white"/>
            <Path d="M25.0704 5.28814C24.7798 12.0987 21.1546 17.0497 15.0454 20.3477C10.3433 22.8861 4.32781 24.2373 0 24.2373V26C4.60596 26 10.9079 24.5845 15.8828 21.8988C22.5281 18.3113 26.5407 12.8052 26.8346 5.28814H29.7826L26.0992 0L22.4158 5.28814H25.0704Z" fill="white"/>
        </Svg>

    );
}

export default SalesAcademy;