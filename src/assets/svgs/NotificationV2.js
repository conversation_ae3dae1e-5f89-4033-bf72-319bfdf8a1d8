import React from 'react';
import Svg, { Path } from 'react-native-svg';

import { getSizesByWidth } from './calculator';
  
const NotificationV2 = (props) => {
    const originalWidth = 34;
    const originalHeight = 38;
    const newWidth = props?.style?.width ? props.style.width : 34;

    const sizes = getSizesByWidth(originalWidth, originalHeight, newWidth);

    return (
        <Svg style={[{width: sizes.width, height: sizes.height}, props.style]} viewBox="0 0 41 41" fill="none" xmlns="http://www.w3.org/2000/svg">
            <Path d="M30.75 13.6667C30.75 10.9483 29.6701 8.34115 27.7478 6.4189C25.8256 4.49666 23.2185 3.41675 20.5 3.41675C17.7815 3.41675 15.1744 4.49666 13.2522 6.4189C11.3299 8.34115 10.25 10.9483 10.25 13.6667C10.25 25.6251 5.125 29.0417 5.125 29.0417H35.875C35.875 29.0417 30.75 25.6251 30.75 13.6667Z" stroke="#2D719F" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
            <Path d="M23.4554 35.875C23.1551 36.3928 22.724 36.8225 22.2053 37.1213C21.6866 37.42 21.0986 37.5773 20.5 37.5773C19.9014 37.5773 19.3134 37.42 18.7947 37.1213C18.276 36.8225 17.8449 36.3928 17.5446 35.875" stroke="#2D719F" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
        </Svg>

    );
}

export default NotificationV2;