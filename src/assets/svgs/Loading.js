import React from 'react';
import Svg, { Path } from 'react-native-svg';

import { getSizesByWidth } from './calculator';
  
const Loading = (props) => {
    const originalWidth = 27;
    const originalHeight = 35;
    const newWidth = props?.style?.width ? props.style.width : 34;
    const color = props?.style?.color ?? "#4EA1CC";

    const sizes = getSizesByWidth(originalWidth, originalHeight, newWidth);

    return (
        <Svg style={[{width: sizes.width, height: sizes.height}, props.style]} viewBox="0 0 27 35" fill="none" xmlns="http://www.w3.org/2000/svg">
            <Path d="M1.4259 31.9011H2.16908C2.12789 31.4363 2.12832 30.9958 2.14248 30.6298C2.2107 28.8829 2.69128 27.1575 3.57004 25.502C4.4059 23.9296 5.40997 22.629 6.55522 21.6354C7.06026 21.1972 7.67038 20.7828 8.26081 20.3823C9.73044 19.3855 11.1181 18.444 11.1181 17.1392C11.1181 15.8344 9.73044 14.8928 8.26081 13.8963C7.67038 13.4959 7.06019 13.0814 6.55566 12.6433C5.4104 11.649 4.40597 10.3484 3.5696 8.77568C2.68997 7.12059 2.21025 5.39516 2.14203 3.64822C2.1253 3.2263 2.12358 2.7077 2.1888 2.16306H1.42546C0.638083 2.16306 -1.52588e-05 1.67896 -1.52588e-05 1.08153C-1.52588e-05 0.484101 0.638039 0 1.42546 0L25.5741 0.000325555C26.3615 0.000325555 26.9996 0.484427 26.9996 1.08186C26.9996 1.67925 26.3615 2.16339 25.5741 2.16339H24.8107C24.8764 2.70837 24.8742 3.22667 24.8575 3.64855C24.7893 5.39548 24.3087 7.12092 23.4299 8.77634C22.5945 10.3488 21.59 11.6494 20.4448 12.6436C19.9397 13.0818 19.3296 13.4962 18.7392 13.8966C17.2695 14.8932 15.8819 15.8343 15.8819 17.1395C15.8819 18.4443 17.2695 19.3858 18.7392 20.3827C19.3296 20.7831 19.9398 21.1975 20.4443 21.6357C21.5896 22.6293 22.5936 23.9305 23.4304 25.503C24.31 27.158 24.7897 28.8835 24.858 30.6304C24.8721 30.9963 24.8725 31.4368 24.8313 31.9017H25.5745C26.3619 31.9017 27 32.3858 27 32.9832C27 33.5807 26.3619 34.0648 25.5745 34.0648H1.427C0.639616 34.0648 0.00152016 33.5807 0.00152016 32.9832C0.001091 32.3852 0.639145 31.9011 1.42568 31.9011L1.4259 31.9011ZM23.0129 30.6715C22.9507 29.0903 22.5147 27.5273 21.7175 26.0259C20.9597 24.5989 20.0638 23.4318 19.0546 22.5564C18.6251 22.183 18.0827 21.8151 17.5086 21.4261C15.8811 20.3218 14.0356 19.07 14.0356 17.1389C14.0356 15.2078 15.8803 13.9562 17.5082 12.8519C18.0827 12.4626 18.6247 12.0944 19.0555 11.721C20.0643 10.8456 20.9598 9.67811 21.7179 8.25188C22.5156 6.75038 22.9519 5.18738 23.0133 3.60622C23.0357 3.03324 23.0176 2.57161 22.9584 2.16206H4.04178C3.98214 2.57096 3.96498 3.03291 3.98686 3.60622C4.04908 5.18747 4.48503 6.75047 5.28228 8.25188C6.04004 9.67811 6.93601 10.8459 7.94517 11.7213C8.37469 12.0947 8.91706 12.4626 9.49159 12.8516C11.1191 13.9552 12.9642 15.207 12.9642 17.1386C12.9642 19.0698 11.1195 20.3212 9.49115 21.4258C8.91703 21.8151 8.37511 22.1827 7.94473 22.5561C6.93595 23.4312 6.04044 24.5983 5.28228 26.0252C4.48461 27.5267 4.04827 29.0897 3.98686 30.6708C3.96841 31.1478 3.98 31.544 4.01776 31.9011H22.9816C23.019 31.544 23.0314 31.1485 23.013 30.6715H23.0129Z" fill={color}/>
            <Path d="M7.02914 4.86348C6.9253 4.18079 7.62471 3.58276 8.53008 3.58276H18.4703C19.3757 3.58276 20.075 4.18114 19.9712 4.86348C19.6391 7.0483 18.4381 11.4863 14.1052 13.8286C13.7431 14.0246 13.2569 14.0246 12.8947 13.8286C8.56183 11.4853 7.3612 7.04763 7.02914 4.86348Z" fill={color}/>
        </Svg>

    );
}

export default Loading;