import React from 'react';
import Svg, { Path } from 'react-native-svg';

import { getSizesByWidth } from './calculator';
  
const Like = (props) => {
    const originalWidth = 34;
    const originalHeight = 34;
    const newWidth = props?.style?.width ? props.style.width : 40;

    const sizes = getSizesByWidth(originalWidth, originalHeight, newWidth);

    return (
        <Svg style={[{width: sizes.width, height: sizes.height}, props.style]} viewBox="0 0 34 34" fill="none" xmlns="http://www.w3.org/2000/svg">
            <Path d="M9.08333 32.8334H4.33333C3.49348 32.8334 2.68802 32.4998 2.09416 31.9059C1.50029 31.3121 1.16666 30.5066 1.16666 29.6667V18.5834C1.16666 17.7436 1.50029 16.9381 2.09416 16.3442C2.68802 15.7504 3.49348 15.4167 4.33333 15.4167H9.08333M20.1667 12.2501V5.91675C20.1667 4.65697 19.6662 3.44879 18.7754 2.55799C17.8846 1.66719 16.6764 1.16675 15.4167 1.16675L9.08333 15.4167V32.8334H26.9433C27.707 32.842 28.4481 32.5744 29.03 32.0798C29.6119 31.5851 29.9955 30.8968 30.11 30.1417L32.295 15.8917C32.3639 15.4379 32.3333 14.9745 32.2053 14.5336C32.0773 14.0928 31.855 13.685 31.5538 13.3386C31.2526 12.9922 30.8796 12.7155 30.4608 12.5275C30.042 12.3395 29.5874 12.2449 29.1283 12.2501H20.1667Z" stroke="#4EA1CC" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
        </Svg>

    );
}

export default Like;