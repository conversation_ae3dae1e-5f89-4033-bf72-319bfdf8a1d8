import React from 'react';
import Svg, { Path } from 'react-native-svg';

import { getSizesByWidth } from './calculator';
  
const HomeV2 = (props) => {
    const originalWidth = 34;
    const originalHeight = 38;
    const newWidth = props?.style?.width ? props.style.width : 34;

    const sizes = getSizesByWidth(originalWidth, originalHeight, newWidth);

    return (
        <Svg style={[{width: sizes.width, height: sizes.height}, props.style]} viewBox="0 0 38 38" fill="none" xmlns="http://www.w3.org/2000/svg">
            <Path d="M4.75 14.2501L19 3.16675L33.25 14.2501V31.6668C33.25 32.5066 32.9164 33.3121 32.3225 33.9059C31.7286 34.4998 30.9232 34.8334 30.0833 34.8334H7.91667C7.07681 34.8334 6.27136 34.4998 5.6775 33.9059C5.08363 33.3121 4.75 32.5066 4.75 31.6668V14.2501Z" stroke="#4EA1CC" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
            <Path d="M14.25 34.8333V19H23.75V34.8333" stroke="#4EA1CC" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
        </Svg>
        
    );
}

export default HomeV2;