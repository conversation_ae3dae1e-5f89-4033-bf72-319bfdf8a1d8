import React from 'react';
import Svg, { Path } from 'react-native-svg';

import { getSizesByWidth } from './calculator';
  
const Menu = (props) => {
    const originalWidth = 33;
    const originalHeight = 23;
    const originalColor = '#2D719F';
    
    const newWidth = props?.style?.width ? props.style.width : originalWidth;
    const color = props?.color ?? originalColor;

    const sizes = getSizesByWidth(originalWidth, originalHeight, newWidth);

    return (
        <Svg style={[{width: sizes.width, height: sizes.height}, props.style]} viewBox="0 0 33 23" fill="none" xmlns="http://www.w3.org/2000/svg">
            <Path d="M1.26923 0C0.568254 0 0 0.572061 0 1.27778C0 1.98343 0.568254 2.55556 1.26923 2.55556H31.7308C32.4317 2.55556 33 1.98343 33 1.27778C33 0.572061 32.4317 0 31.7308 0H1.26923ZM1.26923 10.2222C0.568254 10.2222 0 10.7943 0 11.5C0 12.2057 0.568254 12.7778 1.26923 12.7778H31.7308C32.4317 12.7778 33 12.2057 33 11.5C33 10.7943 32.4317 10.2222 31.7308 10.2222H1.26923ZM1.26923 20.4444C0.568254 20.4444 0 21.0165 0 21.7222C0 22.4279 0.568254 23 1.26923 23H31.7308C32.4317 23 33 22.4279 33 21.7222C33 21.0165 32.4317 20.4444 31.7308 20.4444H1.26923Z" fill={color} />
        </Svg>
    );
}

export default Menu;