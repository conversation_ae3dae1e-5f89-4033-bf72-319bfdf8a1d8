import React from 'react';
import Svg, { Path } from 'react-native-svg';

import { getSizesByWidth } from './calculator';
  
const Notification = (props) => {
    const originalWidth = 28;
    const originalHeight = 34;
    const newWidth = props?.style?.width ? props.style.width : 28;

    const sizes = getSizesByWidth(originalWidth, originalHeight, newWidth);

    return (
        <Svg style={[{width: sizes.width, height: sizes.height}, props.style]} viewBox="0 0 28 34" fill="none" xmlns="http://www.w3.org/2000/svg">
            <Path d="M0.666687 17C0.666687 17.4421 0.842282 17.866 1.15484 18.1786C1.4674 18.4911 1.89133 18.6667 2.33335 18.6667H14.9834L11.15 22.4834C10.9938 22.6383 10.8698 22.8226 10.7852 23.0257C10.7006 23.2288 10.657 23.4467 10.657 23.6667C10.657 23.8867 10.7006 24.1046 10.7852 24.3077C10.8698 24.5108 10.9938 24.6951 11.15 24.85C11.305 25.0063 11.4893 25.1302 11.6924 25.2149C11.8955 25.2995 12.1133 25.343 12.3334 25.343C12.5534 25.343 12.7712 25.2995 12.9743 25.2149C13.1774 25.1302 13.3617 25.0063 13.5167 24.85L20.1834 18.1834C20.3351 18.0249 20.454 17.838 20.5334 17.6334C20.7001 17.2276 20.7001 16.7725 20.5334 16.3667C20.454 16.1621 20.3351 15.9752 20.1834 15.8167L13.5167 9.15004C13.3613 8.99464 13.1768 8.87138 12.9738 8.78728C12.7707 8.70317 12.5531 8.65989 12.3334 8.65989C12.1136 8.65989 11.896 8.70317 11.6929 8.78728C11.4899 8.87138 11.3054 8.99464 11.15 9.15004C10.9946 9.30544 10.8714 9.48992 10.7873 9.69296C10.7032 9.896 10.6599 10.1136 10.6599 10.3334C10.6599 10.5531 10.7032 10.7708 10.7873 10.9738C10.8714 11.1768 10.9946 11.3613 11.15 11.5167L14.9834 15.3334H2.33335C1.89133 15.3334 1.4674 15.509 1.15484 15.8215C0.842282 16.1341 0.666687 16.558 0.666687 17ZM22.3334 0.333374H5.66669C4.3406 0.333374 3.06884 0.860158 2.13115 1.79784C1.19347 2.73552 0.666687 4.00729 0.666687 5.33337V10.3334C0.666687 10.7754 0.842282 11.1993 1.15484 11.5119C1.4674 11.8244 1.89133 12 2.33335 12C2.77538 12 3.1993 11.8244 3.51186 11.5119C3.82443 11.1993 4.00002 10.7754 4.00002 10.3334V5.33337C4.00002 4.89135 4.17562 4.46742 4.48818 4.15486C4.80074 3.8423 5.22466 3.66671 5.66669 3.66671H22.3334C22.7754 3.66671 23.1993 3.8423 23.5119 4.15486C23.8244 4.46742 24 4.89135 24 5.33337V28.6667C24 29.1087 23.8244 29.5327 23.5119 29.8452C23.1993 30.1578 22.7754 30.3334 22.3334 30.3334H5.66669C5.22466 30.3334 4.80074 30.1578 4.48818 29.8452C4.17562 29.5327 4.00002 29.1087 4.00002 28.6667V23.6667C4.00002 23.2247 3.82443 22.8008 3.51186 22.4882C3.1993 22.1756 2.77538 22 2.33335 22C1.89133 22 1.4674 22.1756 1.15484 22.4882C0.842282 22.8008 0.666687 23.2247 0.666687 23.6667V28.6667C0.666687 29.9928 1.19347 31.2646 2.13115 32.2022C3.06884 33.1399 4.3406 33.6667 5.66669 33.6667H22.3334C23.6594 33.6667 24.9312 33.1399 25.8689 32.2022C26.8066 31.2646 27.3334 29.9928 27.3334 28.6667V5.33337C27.3334 4.00729 26.8066 2.73552 25.8689 1.79784C24.9312 0.860158 23.6594 0.333374 22.3334 0.333374Z" fill="#4EA1CC"/>
        </Svg>
    );
}

export default Notification;