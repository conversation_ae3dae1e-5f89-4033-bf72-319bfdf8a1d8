import { Dimensions } from 'react-native';

const windowWidth = Dimensions.get('window').width;

export const getSizesByFullWidth = (originalWidth, originalHeight) => {
    let newWidth = Math.ceil(windowWidth);
    let sizes = getSizesByWidth(originalWidth, originalHeight, newWidth);
    return sizes;
}

export const getSizesByWidth = (originalWidth, originalHeight, newWidth) => {
    let ratio = originalWidth / originalHeight;
    let newHeight = Math.ceil(newWidth / ratio);

    const sizes = {
        width: newWidth,
        height: newHeight
    }

    return sizes;
}