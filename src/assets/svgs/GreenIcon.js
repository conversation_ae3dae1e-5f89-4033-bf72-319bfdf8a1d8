import React from 'react';
import Svg, { <PERSON>, <PERSON>, <PERSON>lip<PERSON><PERSON>, Rect, Defs } from 'react-native-svg';

const GreenIcon = (props) => {
    return (
        <Svg
            width={props.style?.width || 10}
            height={props.style?.height || 10}
            viewBox="0 0 10 10"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
            {...props}
        >
            <G clipPath="url(#clip0_2649_3327)">
                <Path
                    d="M8.94678 5.0756V5.4206C8.94632 6.22926 8.68447 7.01611 8.20028 7.66379C7.71609 8.31147 7.03551 8.78528 6.26004 9.01457C5.48456 9.24385 4.65575 9.21632 3.8972 8.93607C3.13866 8.65583 2.49102 8.13789 2.05089 7.4595C1.61075 6.78112 1.4017 5.97863 1.45491 5.17172C1.50811 4.36482 1.82073 3.59673 2.34613 2.98201C2.87153 2.36729 3.58157 1.93887 4.37035 1.76066C5.15912 1.58244 5.98438 1.66398 6.72303 1.9931"
                    stroke="#1B9C20"
                    strokeLinecap="round"
                    strokeLinejoin="round"
                />
                <Path
                    d="M8.94678 2.42041L5.19678 6.17416L4.07178 5.04916"
                    stroke="#1B9C20"
                    strokeLinecap="round"
                    strokeLinejoin="round"
                />
            </G>
            <Defs>
                <ClipPath id="clip0_2649_3327">
                    <Rect
                        width="9"
                        height="9"
                        fill="white"
                        transform="translate(0.696777 0.92041)"
                    />
                </ClipPath>
            </Defs>
        </Svg>
    );
};

export default GreenIcon;