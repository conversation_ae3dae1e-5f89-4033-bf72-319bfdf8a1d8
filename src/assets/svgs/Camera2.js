import React from 'react';
import Svg, { Path } from 'react-native-svg';

import { getSizesByWidth } from './calculator';
  
const Camera2 = (props) => {
    const originalWidth = 69;
    const originalHeight = 69;
    const newWidth = props?.style?.width ? props.style.width : 60;

    const sizes = getSizesByWidth(originalWidth, originalHeight, newWidth);

    return (
        <Svg style={[{width: sizes.width, height: sizes.height}, props.style]} viewBox="0 0 69 69" fill="none" xmlns="http://www.w3.org/2000/svg">
			<Path d="M66.0937 54.1185C66.0937 55.6214 65.4949 57.0627 64.4288 58.1254C63.3628 59.1881 61.917 59.7851 60.4094 59.7851H9.24988C7.74228 59.7851 6.29644 59.1881 5.23041 58.1254C4.16438 57.0627 3.56549 55.6214 3.56549 54.1185V22.9518C3.56549 21.4489 4.16438 20.0076 5.23041 18.9449C6.29644 17.8822 7.74228 17.2852 9.24988 17.2852H20.6187L26.303 8.78516H43.3562L49.0406 17.2852H60.4094C61.917 17.2852 63.3628 17.8822 64.4288 18.9449C65.4949 20.0076 66.0937 21.4489 66.0937 22.9518V54.1185Z" stroke="#00467F" strokeWidth="1.3" strokeLinecap="round" strokeLinejoin="round"/>
			<Path d="M34.8296 48.4518C41.1084 48.4518 46.1984 43.3777 46.1984 37.1185C46.1984 30.8593 41.1084 25.7852 34.8296 25.7852C28.5508 25.7852 23.4608 30.8593 23.4608 37.1185C23.4608 43.3777 28.5508 48.4518 34.8296 48.4518Z" stroke="#00467F" strokeWidth="1.3" strokeLinecap="round" strokeLinejoin="round"/>
		</Svg>
    );
}

export default Camera2;