import React from 'react';
import Svg, { Path } from 'react-native-svg';

import { getSizesByWidth } from './calculator';
  
const UserOut = (props) => {
    const originalWidth = 43;
    const originalHeight = 39;
    const newWidth = props?.style?.width ? props.style.width : 45;

    const sizes = getSizesByWidth(originalWidth, originalHeight, newWidth);

    return (
        <Svg style={[{width: sizes.width, height: sizes.height}, props.style]} viewBox="0 0 43 39" fill="none" xmlns="http://www.w3.org/2000/svg">
            <Path d="M33.1667 37.125V33.2083C33.1667 31.1308 32.3414 29.1384 30.8724 27.6693C29.4033 26.2003 27.4109 25.375 25.3334 25.375H9.66671C7.58918 25.375 5.59674 26.2003 4.1277 27.6693C2.65867 29.1384 1.83337 31.1308 1.83337 33.2083V37.125" stroke="#979797" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
            <Path d="M17.5 17.5417C21.8262 17.5417 25.3333 14.0346 25.3333 9.70833C25.3333 5.3821 21.8262 1.875 17.5 1.875C13.1737 1.875 9.66663 5.3821 9.66663 9.70833C9.66663 14.0346 13.1737 17.5417 17.5 17.5417Z" stroke="#979797" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
            <Path fillRule="evenodd" clipRule="evenodd" d="M34 14C29.0294 14 25 18.0294 25 23C25 27.9706 29.0294 32 34 32C38.9706 32 43 27.9706 43 23C43 18.0294 38.9706 14 34 14V14ZM29.6097 22.3415C29.246 22.3415 28.9512 22.6363 28.9512 23C28.9512 23.3637 29.246 23.6585 29.6097 23.6585H38.3903C38.754 23.6585 39.0488 23.3637 39.0488 23C39.0488 22.6363 38.754 22.3415 38.3903 22.3415H29.6097Z" fill="#FF312E"/>
        </Svg>
    );
}

export default UserOut;