import React from 'react';
import Svg, { Path } from 'react-native-svg';

import { getSizesByWidth } from './calculator';
  
const Calendar = (props) => {
    const originalWidth = 38;
    const originalHeight = 42;
    const newWidth = props?.style?.width ? props.style.width : 52;
    const color = props?.style?.color ? props.style.color : "white";

    const sizes = getSizesByWidth(originalWidth, originalHeight, newWidth);

    return (
        <Svg style={[{width: sizes.width, height: sizes.height}, props.style]} viewBox="0 0 38 42" fill="none" xmlns="http://www.w3.org/2000/svg">
            <Path d="M19.0001 33.8334C19.3627 33.8334 19.7171 33.7259 20.0186 33.5244C20.3201 33.323 20.5551 33.0366 20.6939 32.7016C20.8326 32.3666 20.8689 31.998 20.7982 31.6424C20.7274 31.2867 20.5528 30.9601 20.2964 30.7037C20.04 30.4473 19.7134 30.2727 19.3577 30.2019C19.0021 30.1312 18.6335 30.1675 18.2985 30.3063C17.9635 30.445 17.6772 30.68 17.4757 30.9815C17.2743 31.283 17.1667 31.6374 17.1667 32C17.1667 32.4863 17.3599 32.9526 17.7037 33.2964C18.0475 33.6402 18.5139 33.8334 19.0001 33.8334ZM28.1667 33.8334C28.5293 33.8334 28.8838 33.7259 29.1853 33.5244C29.4868 33.323 29.7218 33.0366 29.8605 32.7016C29.9993 32.3666 30.0356 31.998 29.9649 31.6424C29.8941 31.2867 29.7195 30.9601 29.4631 30.7037C29.2067 30.4473 28.88 30.2727 28.5244 30.2019C28.1688 30.1312 27.8002 30.1675 27.4652 30.3063C27.1302 30.445 26.8438 30.68 26.6424 30.9815C26.4409 31.283 26.3334 31.6374 26.3334 32C26.3334 32.4863 26.5266 32.9526 26.8704 33.2964C27.2142 33.6402 27.6805 33.8334 28.1667 33.8334ZM28.1667 26.5C28.5293 26.5 28.8838 26.3925 29.1853 26.1911C29.4868 25.9896 29.7218 25.7033 29.8605 25.3683C29.9993 25.0333 30.0356 24.6647 29.9649 24.309C29.8941 23.9534 29.7195 23.6267 29.4631 23.3703C29.2067 23.1139 28.88 22.9393 28.5244 22.8686C28.1688 22.7979 27.8002 22.8342 27.4652 22.9729C27.1302 23.1117 26.8438 23.3467 26.6424 23.6482C26.4409 23.9496 26.3334 24.3041 26.3334 24.6667C26.3334 25.1529 26.5266 25.6193 26.8704 25.9631C27.2142 26.3069 27.6805 26.5 28.1667 26.5ZM19.0001 26.5C19.3627 26.5 19.7171 26.3925 20.0186 26.1911C20.3201 25.9896 20.5551 25.7033 20.6939 25.3683C20.8326 25.0333 20.8689 24.6647 20.7982 24.309C20.7274 23.9534 20.5528 23.6267 20.2964 23.3703C20.04 23.1139 19.7134 22.9393 19.3577 22.8686C19.0021 22.7979 18.6335 22.8342 18.2985 22.9729C17.9635 23.1117 17.6772 23.3467 17.4757 23.6482C17.2743 23.9496 17.1667 24.3041 17.1667 24.6667C17.1667 25.1529 17.3599 25.6193 17.7037 25.9631C18.0475 26.3069 18.5139 26.5 19.0001 26.5ZM31.8334 4.50004H30.0001V2.66671C30.0001 2.18048 29.8069 1.71416 29.4631 1.37034C29.1193 1.02653 28.653 0.833374 28.1667 0.833374C27.6805 0.833374 27.2142 1.02653 26.8704 1.37034C26.5266 1.71416 26.3334 2.18048 26.3334 2.66671V4.50004H11.6667V2.66671C11.6667 2.18048 11.4736 1.71416 11.1298 1.37034C10.786 1.02653 10.3196 0.833374 9.83342 0.833374C9.34718 0.833374 8.88087 1.02653 8.53705 1.37034C8.19324 1.71416 8.00008 2.18048 8.00008 2.66671V4.50004H6.16675C4.70806 4.50004 3.30911 5.0795 2.27766 6.11095C1.24621 7.1424 0.666748 8.54135 0.666748 10V35.6667C0.666748 37.1254 1.24621 38.5243 2.27766 39.5558C3.30911 40.5872 4.70806 41.1667 6.16675 41.1667H31.8334C33.2921 41.1667 34.6911 40.5872 35.7225 39.5558C36.754 38.5243 37.3334 37.1254 37.3334 35.6667V10C37.3334 8.54135 36.754 7.1424 35.7225 6.11095C34.6911 5.0795 33.2921 4.50004 31.8334 4.50004ZM33.6667 35.6667C33.6667 36.1529 33.4736 36.6193 33.1298 36.9631C32.786 37.3069 32.3196 37.5 31.8334 37.5H6.16675C5.68052 37.5 5.2142 37.3069 4.87039 36.9631C4.52657 36.6193 4.33341 36.1529 4.33341 35.6667V19.1667H33.6667V35.6667ZM33.6667 15.5H4.33341V10C4.33341 9.51381 4.52657 9.04749 4.87039 8.70368C5.2142 8.35986 5.68052 8.16671 6.16675 8.16671H8.00008V10C8.00008 10.4863 8.19324 10.9526 8.53705 11.2964C8.88087 11.6402 9.34718 11.8334 9.83342 11.8334C10.3196 11.8334 10.786 11.6402 11.1298 11.2964C11.4736 10.9526 11.6667 10.4863 11.6667 10V8.16671H26.3334V10C26.3334 10.4863 26.5266 10.9526 26.8704 11.2964C27.2142 11.6402 27.6805 11.8334 28.1667 11.8334C28.653 11.8334 29.1193 11.6402 29.4631 11.2964C29.8069 10.9526 30.0001 10.4863 30.0001 10V8.16671H31.8334C32.3196 8.16671 32.786 8.35986 33.1298 8.70368C33.4736 9.04749 33.6667 9.51381 33.6667 10V15.5ZM9.83342 26.5C10.196 26.5 10.5505 26.3925 10.852 26.1911C11.1535 25.9896 11.3884 25.7033 11.5272 25.3683C11.666 25.0333 11.7023 24.6647 11.6315 24.309C11.5608 23.9534 11.3862 23.6267 11.1298 23.3703C10.8734 23.1139 10.5467 22.9393 10.1911 22.8686C9.83545 22.7979 9.46683 22.8342 9.13183 22.9729C8.79683 23.1117 8.5105 23.3467 8.30905 23.6482C8.1076 23.9496 8.00008 24.3041 8.00008 24.6667C8.00008 25.1529 8.19324 25.6193 8.53705 25.9631C8.88087 26.3069 9.34718 26.5 9.83342 26.5ZM9.83342 33.8334C10.196 33.8334 10.5505 33.7259 10.852 33.5244C11.1535 33.323 11.3884 33.0366 11.5272 32.7016C11.666 32.3666 11.7023 31.998 11.6315 31.6424C11.5608 31.2867 11.3862 30.9601 11.1298 30.7037C10.8734 30.4473 10.5467 30.2727 10.1911 30.2019C9.83545 30.1312 9.46683 30.1675 9.13183 30.3063C8.79683 30.445 8.5105 30.68 8.30905 30.9815C8.1076 31.283 8.00008 31.6374 8.00008 32C8.00008 32.4863 8.19324 32.9526 8.53705 33.2964C8.88087 33.6402 9.34718 33.8334 9.83342 33.8334Z" fill={color}/>
        </Svg>
        
    );
}

export default Calendar;