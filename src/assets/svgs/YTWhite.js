import React from 'react';
import Svg, { Path } from 'react-native-svg';

import { getSizesByWidth } from './calculator';
  
const YTWhite = (props) => {
    const originalWidth = 32;
    const originalHeight = 32;
    const newWidth = props?.style?.width ? props.style.width : 43;

    const sizes = getSizesByWidth(originalWidth, originalHeight, newWidth);

    return (
        <Svg style={[{width: sizes.width, height: sizes.height}, props.style]} viewBox="0 0 32 32" fill="none" xmlns="http://www.w3.org/2000/svg">
			<Path d="M30.0533 8.55992C29.895 7.92713 29.5724 7.34735 29.1182 6.87913C28.664 6.41091 28.0943 6.07083 27.4667 5.89325C25.1733 5.33325 16 5.33325 16 5.33325C16 5.33325 6.82668 5.33325 4.53334 5.94659C3.90567 6.12416 3.33598 6.46424 2.8818 6.93246C2.42763 7.40068 2.10506 7.98046 1.94668 8.61325C1.52696 10.9407 1.32166 13.3017 1.33334 15.6666C1.31838 18.0493 1.5237 20.4283 1.94668 22.7733C2.12129 23.3864 2.45108 23.9441 2.9042 24.3926C3.35732 24.841 3.91844 25.165 4.53334 25.3333C6.82668 25.9466 16 25.9466 16 25.9466C16 25.9466 25.1733 25.9466 27.4667 25.3333C28.0943 25.1557 28.664 24.8156 29.1182 24.3474C29.5724 23.8792 29.895 23.2994 30.0533 22.6666C30.4698 20.3567 30.6751 18.0137 30.6667 15.6666C30.6816 13.2839 30.4763 10.9048 30.0533 8.55992V8.55992Z" stroke="#FFF" strokeWidth="1" strokeLinecap="round" strokeLinejoin="round"/>
			<Path d="M13 20.0266L20.6667 15.6666L13 11.3066V20.0266Z" stroke="#FFF" strokeWidth="1" strokeLinecap="round" strokeLinejoin="round"/>
		</Svg>

    );
}

export default YTWhite;