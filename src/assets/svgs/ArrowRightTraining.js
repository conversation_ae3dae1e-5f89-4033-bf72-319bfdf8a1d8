import React from 'react';
import Svg, { Path } from 'react-native-svg';

import { getSizesByWidth } from './calculator';
  
const ArrowRight = (props) => {
    const originalWidth = 12;
    const originalHeight = 22;
    const newWidth = props?.style?.width ? props.style.width : 16;

    const sizes = getSizesByWidth(originalWidth, originalHeight, newWidth);

    return (
      <Svg style={[{width: sizes.width, height: sizes.height}, props.style]} viewBox="0 0 12 22" fill="none" xmlns="http://www.w3.org/2000/svg">
		<Path d="M1.5 20L10.5 11L1.5 2" stroke="#BDBDBD" strokeWidth="3" strokeLinecap="round" strokeLinejoin="round"/>
	</Svg>
         
    );
}

export default ArrowRight;