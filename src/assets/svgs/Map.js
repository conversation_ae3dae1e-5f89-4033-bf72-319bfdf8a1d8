import React from 'react';
import Svg, { Path } from 'react-native-svg';

import { getSizesByWidth } from './calculator';
  
const Map = (props) => {
    const originalWidth = 24;
    const originalHeight = 22;
    const newWidth = props?.style?.width ? props.style.width : 24;

    const sizes = getSizesByWidth(originalWidth, originalHeight, newWidth);

    return (
        <Svg style={[{width: sizes.width, height: sizes.height}, props.style]} viewBox="0 0 24 22" fill="none" xmlns="http://www.w3.org/2000/svg">
            <Path d="M1 5V21L8 17L16 21L23 17V1L16 5L8 1L1 5Z" stroke="#828282" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
            <Path d="M16 5V21" stroke="#828282" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
            <Path d="M8 1V17" stroke="#828282" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
        </Svg>
        

    );
}

export default Map;