import React from 'react';
import Svg, { Path } from 'react-native-svg';

import { getSizesByWidth } from './calculator';
  
const Users2 = (props) => {
    const originalWidth = 42;
    const originalHeight = 42;
    const newWidth = props?.style?.width ? props.style.width : 45;

    const sizes = getSizesByWidth(originalWidth, originalHeight, newWidth);

    return (
        <Svg style={[{width: sizes.width, height: sizes.height}, props.style]} viewBox="0 0 36 37" fill="none" xmlns="http://www.w3.org/2000/svg">
			<Path d="M35.5884 17.2951L31.2393 10.0274C30.7775 9.25021 29.9692 8.78362 29.1225 8.66703C30.0462 7.73421 30.6234 6.49068 30.6234 5.09148C30.6234 2.33195 28.3912 0.0390034 25.6201 0.0390034C22.8874 0.0390034 20.6168 2.29315 20.6168 5.09148C20.6168 6.49061 21.194 7.77321 22.1178 8.66703C21.2326 8.74483 20.463 9.24989 20.0009 10.0274L17.9998 13.3308L15.9985 9.98843C15.5367 9.2112 14.7284 8.74462 13.8816 8.62802C14.8054 7.6952 15.3826 6.45167 15.3826 5.05247C15.3826 2.29295 13.1504 0 10.3793 0C7.64659 0 5.37595 2.25415 5.37595 5.05247C5.37595 6.4516 5.95314 7.73421 6.87693 8.62802C5.9918 8.70583 5.22214 9.21088 4.76009 9.98843L0.411312 17.295C-0.28131 18.4997 -0.0890961 20.0154 0.911721 20.9093C1.45052 21.3755 2.10474 21.6478 2.79763 21.6478C3.68276 21.6478 4.52946 21.2202 5.06828 20.4817V33.8512C5.06828 35.3281 6.06886 36.6107 7.493 36.8826C7.68549 36.9214 7.91637 36.9604 8.10885 36.9604C9.0326 36.9604 9.87937 36.5329 10.4182 35.8722C10.8416 36.3775 11.4188 36.766 12.1117 36.8826C12.3042 36.9214 12.535 36.9604 12.7275 36.9604C14.3823 36.9604 15.7678 35.6 15.7678 33.8903L15.7675 20.482C16.0755 20.9095 16.4602 21.2205 16.9222 21.4148C17.2685 21.5701 17.6535 21.6479 18.0382 21.6479C18.2307 21.6479 18.3845 21.6479 18.577 21.6092L17.5378 23.3971C17.0374 24.2521 17.0374 25.3015 17.5378 26.1566C18.0382 27.0116 18.9233 27.5557 19.8855 27.5557H20.2704V33.8907C20.2704 35.3677 21.271 36.6503 22.6952 36.9222C22.8877 36.961 23.1185 37 23.311 37C24.2348 37 25.0815 36.5725 25.6204 35.9118C26.0437 36.4171 26.6209 36.8056 27.3138 36.9222C27.5063 36.961 27.7372 37 27.9297 37C29.5845 37 30.97 35.6396 30.97 33.9298L30.9697 27.5167H31.3547C32.3553 27.5167 33.2406 27.0114 33.7024 26.1176C34.2028 25.2626 34.2028 24.2132 33.7024 23.3581L32.7018 21.6093C32.8556 21.648 33.0481 21.648 33.2406 21.648C33.8178 21.648 34.3566 21.4536 34.857 21.1427C36.0114 20.2879 36.358 18.6166 35.5883 17.2952L35.5884 17.2951ZM22.0796 5.09162C22.0796 3.10959 23.6576 1.51607 25.6203 1.51607C27.5831 1.51607 29.1611 3.10959 29.1611 5.09162C29.1611 7.07365 27.5831 8.66717 25.6203 8.66717C23.6576 8.66717 22.0796 7.03485 22.0796 5.09162ZM6.87734 5.09162C6.87734 3.10959 8.45536 1.51607 10.4181 1.51607C12.3809 1.51607 13.9589 3.10959 13.9589 5.09162C13.9589 7.07365 12.3809 8.66717 10.4181 8.66717C8.45536 8.66717 6.87734 7.03485 6.87734 5.09162ZM33.9719 19.9379C33.741 20.0933 33.4715 20.1711 33.2023 20.1711C32.7405 20.1711 32.2785 19.9379 32.0476 19.5104L29.8537 15.8571C29.8156 15.7795 29.7769 15.7795 29.6999 15.7795C29.5844 15.7795 29.5074 15.8573 29.5074 15.9739V18.9665L32.4709 24.1355C32.9713 24.9906 32.3555 26.0788 31.355 26.0788H30.0078C29.7383 26.0788 29.5074 26.3119 29.5074 26.5841V33.8907C29.5074 34.7845 28.8148 35.4842 27.9294 35.4842C27.8139 35.4842 27.6985 35.4842 27.583 35.4454C26.8517 35.2901 26.313 34.5904 26.313 33.8519L26.3133 26.8167C26.3133 26.3892 25.9669 26.0782 25.5819 26.0782C25.197 26.0782 24.8506 26.4279 24.8506 26.8167V33.9289C24.8506 34.8227 24.158 35.5224 23.2726 35.5224C23.1572 35.5224 23.0417 35.5224 22.9263 35.4837C22.195 35.3283 21.6562 34.6287 21.6562 33.8902V26.5836C21.6562 26.3114 21.4254 26.0783 21.1558 26.0783H19.8087C18.8081 26.0783 18.1922 24.9901 18.6927 24.135L21.6562 18.966V15.9734C21.6562 15.8568 21.5408 15.779 21.4637 15.779C21.3867 15.779 21.3483 15.8178 21.3099 15.8568L19.1547 19.5102C18.9238 19.9377 18.4621 20.1708 18 20.1708C17.8076 20.1708 17.6537 20.1321 17.4613 20.0543C17.1917 19.9377 16.9608 19.7433 16.8454 19.4714L14.6515 15.8181C14.6131 15.7793 14.5364 15.7793 14.4977 15.7793C14.3823 15.7793 14.3052 15.8571 14.3052 15.9737V33.9291C14.3052 34.823 13.6126 35.5227 12.7272 35.5227C12.6118 35.5227 12.4963 35.5227 12.3809 35.4839C11.6496 35.3286 11.1108 34.6289 11.1108 33.8904L11.1111 23.7856C11.1111 23.358 10.7648 23.0471 10.3798 23.0471C9.99479 23.0471 9.64845 23.3968 9.64845 23.7856V33.9292C9.64845 34.8231 8.95583 35.5228 8.07043 35.5228C7.95499 35.5228 7.83955 35.5228 7.72411 35.484C6.9928 35.3286 6.45406 34.629 6.45406 33.8905V15.9739C6.45406 15.8573 6.33862 15.7795 6.26157 15.7795C6.18452 15.7795 6.14613 15.8183 6.10774 15.8573L3.91386 19.5106C3.64433 19.9382 3.22124 20.1713 2.7592 20.1713C2.45127 20.1713 2.10496 20.0547 1.83545 19.8216C1.33504 19.3941 1.29665 18.6168 1.60457 18.0727L5.95363 10.7662C6.18451 10.3387 6.64625 10.1055 7.10829 10.1055H13.5356C13.9973 10.1055 14.4593 10.3387 14.6902 10.7662L18.0001 16.1681L21.2329 10.7659C21.4638 10.3384 21.9255 10.1052 22.3876 10.1052H28.8148C29.2766 10.1052 29.7386 10.3384 29.9695 10.7659L34.3185 18.0336C34.7033 18.6945 34.5878 19.5105 33.972 19.938L33.9719 19.9379Z" fill="#2D719F"/>
		</Svg>

    );
}

export default Users2;