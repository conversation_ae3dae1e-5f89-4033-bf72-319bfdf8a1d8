import React from 'react';
import Svg, { Path } from 'react-native-svg';

import { getSizesByWidth } from './calculator';
  
const Email = (props) => {
    const originalWidth = 34;
    const originalHeight = 23;
    const newWidth = props?.style?.width ? props.style.width : 43;

    const sizes = getSizesByWidth(originalWidth, originalHeight, newWidth);

    return (
		<Svg style={[{width: sizes.width, height: sizes.height}, props.style]} viewBox="0 0 34 23" fill="none" xmlns="http://www.w3.org/2000/svg">
			<Path d="M1.11571 1.20511H32.1967V3.82959L16.6562 16.2988L1.11571 3.82959V1.20511ZM22.8501 12.6241L31.5135 21.5764H1.79821L10.4615 12.6241L16.3836 17.3883L16.4111 17.4104H16.4143C16.5729 17.516 16.7742 17.5089 16.927 17.3891L16.928 17.3883L22.8501 12.6241ZM1.0273 0.148047C0.555036 0.148047 0.170874 0.588089 0.170874 1.10511V21.6764C0.170874 22.1934 0.555037 22.6335 1.0273 22.6335H32.2851C32.7573 22.6335 33.1415 22.1934 33.1415 21.6764V1.10511C33.1415 0.588072 32.7573 0.148047 32.2851 0.148047H1.0273ZM1.11561 20.8532V5.13054L9.67722 12.0026L1.11561 20.8532ZM23.635 12.0028L32.1966 5.13055V20.8533L23.635 12.0028Z" fill="#00467F" stroke="#00467F" strokeWidth="0.2"/>
		</Svg>
    );
}

export default Email;