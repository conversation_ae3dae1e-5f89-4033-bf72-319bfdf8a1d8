import React from 'react';
import Svg, {Path} from 'react-native-svg';

import {getSizesByWidth} from './calculator';

const ArrowRightChatAi = props => {
  const originalWidth = 21;
  const originalHeight = 19;
  const newWidth = props?.style?.width ? props.style.width : 50;
  const color = props?.style?.color ? props.style.color : 'white';

  const sizes = getSizesByWidth(originalWidth, originalHeight, newWidth);

  return (
    <Svg
      style={[{width: sizes.width, height: sizes.height}, props.style]}
      viewBox="0 0 21 19"
      fill="none"
      xmlns="http://www.w3.org/2000/svg">
      <Path
        d="M1.04718 1.02251L19.7869 9.5L1.04718 17.9775L5.024 9.71689L5.12841 9.5L5.024 9.28312L1.04718 1.02251Z"
        stroke={color}
      />
    </Svg>
  );
};

export default ArrowRightChatAi;
