import React from 'react';
import Svg, { Path, Rect } from 'react-native-svg';

import { getSizesByWidth } from './calculator';
  
const Tag = (props) => {
    const originalWidth = 14;
    const originalHeight = 15;
    const newWidth = props?.style?.width ? props.style.width : 20;

    const sizes = getSizesByWidth(originalWidth, originalHeight, newWidth);

    return (
		<Svg style={[{width: sizes.width, height: sizes.height}, props.style]} viewBox="0 0 14 15" fill="none" xmlns="http://www.w3.org/2000/svg">
				<Path d="M3.2 0.42334H12.4V11.6233H3.2V0.42334Z" fill="#FF6542"/>
				<Rect x="0.480438" y="0.736328" width="2.40217" height="2.8826" fill="#BE4E35"/>
				<Path d="M7.34776 11.7462L12.3373 9.36047L12.3316 14.1438L7.34776 11.7462Z" fill="#FF6542"/>
				<Path d="M8.19858 11.7472L3.32315 9.34972L3.31746 14.133L8.19858 11.7472Z" fill="#FF6542"/>
				<Path d="M1.75664 7.0942e-05H11.4195C12.3883 7.0942e-05 13.1765 0.681984 13.1765 1.52045V14.4433C13.1765 14.5821 13.0893 14.7098 12.9487 14.7767C12.8079 14.8433 12.6368 14.8382 12.5014 14.7641L7.90601 12.2334L3.31025 14.764C3.23825 14.8034 3.15675 14.8235 3.07434 14.8235C3.00142 14.8235 2.92897 14.8079 2.86279 14.7767C2.72262 14.7098 2.6353 14.5821 2.6353 14.4433V4.18104H0.439187C0.196536 4.18104 4.76837e-06 4.01096 4.76837e-06 3.80099V1.52051C4.76837e-06 0.681914 0.788331 0 1.75678 0L1.75664 7.0942e-05ZM3.51357 13.751L7.67011 11.4622C7.81411 11.3828 7.99778 11.3828 8.14177 11.4622L12.2979 13.751V1.52041C12.2979 1.10137 11.9042 0.760309 11.4195 0.760309H3.26922C3.42057 0.985004 3.51371 1.24203 3.51371 1.52041L3.51357 13.751ZM0.878274 3.42084H2.63521V1.52041C2.63521 1.10137 2.24151 0.760309 1.75684 0.760309C1.2726 0.760309 0.87847 1.1014 0.87847 1.52041L0.878274 3.42084Z" fill="#FF6542"/>
		</Svg>
    );
}

export default Tag;