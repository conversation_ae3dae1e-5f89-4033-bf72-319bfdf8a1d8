import React from 'react';
import Svg, { Path } from 'react-native-svg';

import { getSizesByWidth } from './calculator';
  
const ArrowRightRegisterButton = (props) => {
    const originalWidth = 9;
    const originalHeight = 14;
    const newWidth = props?.style?.width ? props.style.width : originalWidth;

    const sizes = getSizesByWidth(originalWidth, originalHeight, newWidth);

    return (
        <Svg style={[{width: sizes.width, height: sizes.height}, props.style]} viewBox="0 0 9 14" fill="none" xmlns="http://www.w3.org/2000/svg">
		    <Path d="M1.5 20L10.5 11L1.5 2" d="M1.59486 1L7.96143 7L1.59487 13" stroke="#90B0C0" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
	    </Svg>
    );
}

export default ArrowRightRegisterButton;