import React from 'react';
import Svg, { Path } from 'react-native-svg';

import { getSizesByWidth } from './calculator';
  
const CalendarLight = (props) => {
    const originalWidth = 28;
    const originalHeight = 27;
    const newWidth = props?.style?.width ? props.style.width : 35;
    const color = props?.style?.color ? props.style.color : "white";

    const sizes = getSizesByWidth(originalWidth, originalHeight, newWidth);

    return (
        <Svg style={[{width: sizes.width, height: sizes.height}, props.style]} viewBox="0 0 28 27" fill="none" xmlns="http://www.w3.org/2000/svg">
			<Path fillRule="evenodd" clipRule="evenodd" d="M25.2268 26.9999H2.37056C1.06361 26.9999 0.000213623 25.9369 0.000213623 24.6296V4.33152C0.000213623 3.02478 1.06341 1.96118 2.37056 1.96118H25.2268C26.5336 1.96118 27.5972 3.02438 27.5972 4.33152V24.6296C27.5972 25.9367 26.534 26.9999 25.2268 26.9999ZM2.37056 2.60805C1.42016 2.60805 0.647033 3.38098 0.647033 4.33157V24.6296C0.647033 25.58 1.42016 26.353 2.37056 26.353H25.2268C26.177 26.353 26.95 25.5802 26.95 24.6298V4.33177C26.95 3.38138 26.1772 2.60825 25.2268 2.60825H2.37056V2.60805Z" fill="white"/>
			<Path fillRule="evenodd" clipRule="evenodd" d="M27.2735 7.72494H0.32341C0.144957 7.72494 0 7.57999 0 7.40153C0 7.22308 0.144957 7.07812 0.32341 7.07812H27.2735C27.452 7.07812 27.5969 7.22308 27.5969 7.40153C27.5969 7.57999 27.452 7.72494 27.2735 7.72494Z" fill="white"/>
			<Path fillRule="evenodd" clipRule="evenodd" d="M5.52555 4.56968C5.34709 4.56968 5.20214 4.42472 5.20214 4.24627C5.20214 4.06781 5.34709 3.92286 5.52555 3.92286C6.42878 3.92286 7.16356 3.18807 7.16356 2.28484C7.16356 1.38161 6.42878 0.646623 5.52555 0.646623C4.62231 0.646623 3.88733 1.38141 3.88733 2.28484C3.88733 2.46329 3.74237 2.60825 3.56392 2.60825C3.38547 2.60825 3.24051 2.46329 3.24051 2.28484C3.24051 1.0251 4.26561 0 5.52535 0C6.78508 0 7.81019 1.0251 7.81019 2.28484C7.81057 3.54477 6.78548 4.56968 5.52535 4.56968H5.52555Z" fill="white"/>
			<Path fillRule="evenodd" clipRule="evenodd" d="M13.7985 4.56968C13.62 4.56968 13.4751 4.42472 13.4751 4.24627C13.4751 4.06781 13.62 3.92286 13.7985 3.92286C14.7017 3.92286 15.4365 3.18807 15.4365 2.28484C15.4365 1.38161 14.7017 0.646623 13.7985 0.646623C12.8952 0.646623 12.1602 1.38141 12.1602 2.28484C12.1602 2.46329 12.0153 2.60825 11.8368 2.60825C11.6584 2.60825 11.5134 2.46329 11.5134 2.28484C11.5134 1.0251 12.5385 0 13.7983 0C15.0584 0 16.0833 1.0251 16.0833 2.28484C16.0835 3.54477 15.0584 4.56968 13.7983 4.56968H13.7985Z" fill="white"/>
			<Path fillRule="evenodd" clipRule="evenodd" d="M22.0713 4.56968C21.8929 4.56968 21.7479 4.42472 21.7479 4.24627C21.7479 4.06781 21.8929 3.92286 22.0713 3.92286C22.9746 3.92286 23.7094 3.18807 23.7094 2.28484C23.7094 1.38161 22.9748 0.646623 22.0713 0.646623C21.1679 0.646623 20.4333 1.38141 20.4333 2.28484C20.4333 2.46329 20.2884 2.60825 20.1099 2.60825C19.9315 2.60825 19.7865 2.46329 19.7865 2.28484C19.7865 1.0251 20.8116 0 22.0715 0C23.3317 0 24.3566 1.0251 24.3566 2.28484C24.3564 3.54477 23.3313 4.56968 22.0713 4.56968Z" fill="white"/>
			<Path fillRule="evenodd" clipRule="evenodd" d="M6.95425 13.9512H3.37227C3.19382 13.9512 3.04886 13.8063 3.04886 13.6278V10.0458C3.04886 9.86737 3.19382 9.72241 3.37227 9.72241H6.95425C7.1327 9.72241 7.27766 9.86737 7.27766 10.0458V13.6278C7.27766 13.8066 7.1327 13.9512 6.95425 13.9512ZM3.6957 13.3044H6.63086V10.3692H3.6957V13.3044Z" fill="white"/>
			<Path fillRule="evenodd" clipRule="evenodd" d="M12.7113 13.9512H9.12935C8.9509 13.9512 8.80594 13.8063 8.80594 13.6278V10.0458C8.80594 9.86737 8.9509 9.72241 9.12935 9.72241H12.7113C12.8898 9.72241 13.0347 9.86737 13.0347 10.0458V13.6278C13.0347 13.8066 12.8902 13.9512 12.7113 13.9512ZM9.45278 13.3044H12.3879V10.3692H9.45278V13.3044Z" fill="white"/>
			<Path fillRule="evenodd" clipRule="evenodd" d="M18.4679 13.9512H14.8859C14.7075 13.9512 14.5625 13.8063 14.5625 13.6278V10.0458C14.5625 9.86737 14.7075 9.72241 14.8859 9.72241H18.4679C18.6463 9.72241 18.7913 9.86737 18.7913 10.0458V13.6278C18.7915 13.8066 18.6463 13.9512 18.4679 13.9512ZM15.2095 13.3044H18.1445V10.3692H15.2095V13.3044Z" fill="white"/>
			<Path fillRule="evenodd" clipRule="evenodd" d="M24.2244 13.9512H20.6425C20.464 13.9512 20.3191 13.8063 20.3191 13.6278V10.0458C20.3191 9.86737 20.464 9.72241 20.6425 9.72241H24.2244C24.4029 9.72241 24.5479 9.86737 24.5479 10.0458V13.6278C24.5482 13.8066 24.4029 13.9512 24.2244 13.9512ZM20.9659 13.3044H23.9009V10.3692H20.9659V13.3044Z" fill="white"/>
			<Path fillRule="evenodd" clipRule="evenodd" d="M6.95425 19.1533H3.37227C3.19382 19.1533 3.04886 19.0084 3.04886 18.8299V15.2477C3.04886 15.0693 3.19382 14.9243 3.37227 14.9243H6.95425C7.1327 14.9243 7.27766 15.0693 7.27766 15.2477V18.8299C7.27766 19.0084 7.1327 19.1533 6.95425 19.1533ZM3.6957 18.5065H6.63086V15.5712H3.6957V18.5065Z" fill="white"/>
			<Path fillRule="evenodd" clipRule="evenodd" d="M12.7113 19.1533H9.12935C8.9509 19.1533 8.80594 19.0084 8.80594 18.8299V15.2477C8.80594 15.0693 8.9509 14.9243 9.12935 14.9243H12.7113C12.8898 14.9243 13.0347 15.0693 13.0347 15.2477V18.8299C13.0347 19.0084 12.8902 19.1533 12.7113 19.1533ZM9.45278 18.5065H12.3879V15.5712H9.45278V18.5065Z" fill="white"/>
			<Path fillRule="evenodd" clipRule="evenodd" d="M18.4679 19.1533H14.8859C14.7075 19.1533 14.5625 19.0084 14.5625 18.8299V15.2477C14.5625 15.0693 14.7075 14.9243 14.8859 14.9243H18.4679C18.6463 14.9243 18.7913 15.0693 18.7913 15.2477V18.8299C18.7915 19.0084 18.6463 19.1533 18.4679 19.1533ZM15.2095 18.5065H18.1445V15.5712H15.2095V18.5065Z" fill="white"/>
			<Path fillRule="evenodd" clipRule="evenodd" d="M24.2244 19.1533H20.6425C20.464 19.1533 20.3191 19.0084 20.3191 18.8299V15.2477C20.3191 15.0693 20.464 14.9243 20.6425 14.9243H24.2244C24.4029 14.9243 24.5479 15.0693 24.5479 15.2477V18.8299C24.5482 19.0084 24.4029 19.1533 24.2244 19.1533ZM20.9659 18.5065H23.9009V15.5712H20.9659V18.5065Z" fill="white"/>
			<Path fillRule="evenodd" clipRule="evenodd" d="M6.95425 24.356H3.37227C3.19382 24.356 3.04886 24.2111 3.04886 24.0326V20.4509C3.04886 20.2724 3.19382 20.1274 3.37227 20.1274H6.95425C7.1327 20.1274 7.27766 20.2724 7.27766 20.4509V24.0326C7.27766 24.2111 7.1327 24.356 6.95425 24.356ZM3.6957 23.7092H6.63086V20.7741H3.6957V23.7092Z" fill="white"/>
			<Path fillRule="evenodd" clipRule="evenodd" d="M12.7113 24.356H9.12935C8.9509 24.356 8.80594 24.2111 8.80594 24.0326V20.4509C8.80594 20.2724 8.9509 20.1274 9.12935 20.1274H12.7113C12.8898 20.1274 13.0347 20.2724 13.0347 20.4509V24.0326C13.0347 24.2111 12.8902 24.356 12.7113 24.356ZM9.45278 23.7092H12.3879V20.7741H9.45278V23.7092Z" fill="white"/>
			<Path fillRule="evenodd" clipRule="evenodd" d="M18.4679 24.356H14.8859C14.7075 24.356 14.5625 24.2111 14.5625 24.0326V20.4509C14.5625 20.2724 14.7075 20.1274 14.8859 20.1274H18.4679C18.6463 20.1274 18.7913 20.2724 18.7913 20.4509V24.0326C18.7915 24.2111 18.6463 24.356 18.4679 24.356ZM15.2095 23.7092H18.1445V20.7741H15.2095V23.7092Z" fill="white"/>
			<Path fillRule="evenodd" clipRule="evenodd" d="M24.2244 24.356H20.6425C20.464 24.356 20.3191 24.2111 20.3191 24.0326V20.4509C20.3191 20.2724 20.464 20.1274 20.6425 20.1274H24.2244C24.4029 20.1274 24.5479 20.2724 24.5479 20.4509V24.0326C24.5482 24.2111 24.4029 24.356 24.2244 24.356ZM20.9659 23.7092H23.9009V20.7741H20.9659V23.7092Z" fill="white"/>
		</Svg>
        
    );
}

export default CalendarLight;