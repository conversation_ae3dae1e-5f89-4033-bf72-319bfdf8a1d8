import React from 'react';
import Svg, { Path } from 'react-native-svg';

import { getSizesByWidth } from './calculator';
  
const Password = (props) => {
    const originalWidth = 38;
    const originalHeight = 44;
    const newWidth = props?.style?.width ? props.style.width : 40;
    const color = props?.style?.color ? props.style.color : "#00467F";

    const sizes = getSizesByWidth(originalWidth, originalHeight, newWidth);

    return (
		<Svg style={[{width: sizes.width, height: sizes.height}, props.style]} viewBox="0 0 38 44" fill="none" xmlns="http://www.w3.org/2000/svg">
			<Path d="M28.605 27.4046V21.7416C28.606 21.0007 28.3211 20.2877 27.8103 19.7512C27.2991 19.2146 26.6012 18.8955 25.861 18.8603V15.7732C25.861 13.3222 24.5535 11.0574 22.4309 9.8321C20.3082 8.60675 17.6933 8.60675 15.5707 9.8321C13.448 11.0574 12.1406 13.3223 12.1406 15.7732V18.8603C11.4016 18.8955 10.7045 19.2137 10.1937 19.7487C9.68281 20.2838 9.39743 20.9949 9.39648 21.7348V27.4046C9.39648 28.8672 9.97715 30.2699 11.0105 31.3048C12.0441 32.3396 13.4461 32.9218 14.9086 32.9236H23.093C24.5553 32.9218 25.9574 32.3396 26.9911 31.3048C28.0244 30.2699 28.605 28.8672 28.605 27.4046ZM19.0008 10.2846C20.4564 10.2846 21.8523 10.8629 22.8814 11.8922C23.9107 12.9212 24.4889 14.3172 24.4889 15.7728V18.8599H13.5126V15.7728C13.5126 14.3172 14.0908 12.9212 15.1202 11.8922C16.1492 10.8629 17.5451 10.2846 19.0008 10.2846ZM10.7685 27.4046V21.7416C10.7685 21.3413 10.9275 20.9575 11.2105 20.6743C11.4937 20.3913 11.8775 20.2323 12.2777 20.2323H25.7237C26.124 20.2323 26.5078 20.3913 26.791 20.6743C27.074 20.9576 27.233 21.3413 27.233 21.7416V27.4046C27.233 28.5034 26.7968 29.5569 26.0208 30.3345C25.2444 31.1121 24.1915 31.5498 23.0929 31.5516H14.9085C13.8099 31.5497 12.7571 31.1121 11.9807 30.3345C11.2046 29.5569 10.7685 28.5034 10.7685 27.4046H10.7685Z" fill={color} />
			<Path d="M19.0007 21.769C18.2522 21.7721 17.5509 22.1344 17.1148 22.743C16.679 23.3512 16.5613 24.1319 16.799 24.8418C17.0367 25.5514 17.6002 26.1042 18.3147 26.3274V28.3855C18.3147 28.7646 18.6219 29.0715 19.0007 29.0715C19.3795 29.0715 19.6867 28.7646 19.6867 28.3855V26.3274C20.4012 26.1042 20.9648 25.5514 21.2024 24.8418C21.44 24.1319 21.3224 23.3512 20.8866 22.743C20.4505 22.1344 19.7492 21.7721 19.0007 21.769H19.0007ZM19.0007 25.0586V25.0583C18.6118 25.0598 18.2602 24.8264 18.1107 24.4672C17.9609 24.1079 18.043 23.6939 18.318 23.4189C18.5934 23.1438 19.0071 23.0618 19.3664 23.2112C19.7256 23.361 19.959 23.7123 19.9577 24.1015C19.9577 24.3551 19.857 24.5985 19.6775 24.778C19.498 24.9575 19.2546 25.0582 19.0007 25.0582L19.0007 25.0586Z" fill={color} />
			<Path d="M38 21.9469C38.0055 17.9496 36.748 14.0533 34.4073 10.813C32.0666 7.57306 28.762 5.15513 24.9653 3.9047C24.6054 3.78618 24.2174 3.98218 24.0992 4.34203C23.9806 4.70188 24.1766 5.08961 24.5365 5.20814C28.3705 6.47818 31.6524 9.02626 33.8327 12.426C36.0129 15.8258 36.9605 19.8712 36.5158 23.8853C36.0711 27.8997 34.2618 31.6397 31.39 34.4798C28.5182 37.3198 24.7582 39.0877 20.739 39.488L22.4301 37.7935C22.5695 37.6677 22.6513 37.49 22.6562 37.3026C22.6611 37.1149 22.5888 36.9333 22.4565 36.8C22.3239 36.6671 22.1426 36.5942 21.9548 36.5985C21.7671 36.6028 21.5892 36.684 21.463 36.8227L18.5165 39.783C18.4831 39.8133 18.4543 39.8479 18.4307 39.8859C18.4188 39.9015 18.4096 39.919 18.4031 39.9374C18.3888 39.9591 18.3759 39.9821 18.3655 40.006L18.345 40.0746C18.3327 40.0905 18.3226 40.108 18.314 40.126C18.2966 40.2142 18.2966 40.3052 18.314 40.3937C18.318 40.4145 18.3238 40.4354 18.3312 40.4553L18.3517 40.5239L18.3894 40.5891C18.3958 40.6075 18.4053 40.6249 18.4169 40.6405C18.4414 40.6791 18.4702 40.7147 18.5027 40.7468L21.4559 43.6967C21.723 43.9659 22.1576 43.9674 22.4268 43.7001C22.6957 43.433 22.6972 42.9984 22.4302 42.7295L20.5779 40.8739C25.3246 40.4742 29.7486 38.3078 32.9749 34.8029C36.201 31.2981 37.9942 26.7102 37.9997 21.9465L38 21.9469Z" fill={color} />
			<Path d="M13.4373 38.6788C9.60636 37.4042 6.32875 34.8537 4.15215 31.4533C1.97586 28.0526 1.0326 24.0084 1.48004 19.9956C1.92718 15.9833 3.73811 12.2457 6.60991 9.40813C9.4817 6.57036 13.2404 4.80411 17.2577 4.40481L15.5703 6.09932C15.4309 6.22519 15.3491 6.40282 15.3442 6.59025C15.3393 6.77798 15.4116 6.9596 15.5439 7.09282C15.6765 7.22574 15.8578 7.29863 16.0456 7.29434C16.2333 7.29005 16.4113 7.2089 16.5374 7.07016L19.4839 4.10984C19.5173 4.07952 19.5461 4.04492 19.5697 4.00694C19.5816 3.99132 19.5908 3.97387 19.5973 3.95549C19.6117 3.93375 19.6245 3.91078 19.6349 3.88689L19.6555 3.81829C19.6677 3.80236 19.6778 3.7849 19.6864 3.76683C19.7038 3.67863 19.7038 3.58767 19.6864 3.49916C19.6879 3.47864 19.6879 3.45812 19.6864 3.4376L19.6659 3.369L19.6282 3.30377C19.6218 3.28539 19.6123 3.26794 19.6006 3.25232C19.5761 3.21373 19.5473 3.1782 19.5149 3.14604L16.5307 0.196157C16.264 -0.0653856 15.837 -0.0653856 15.5703 0.196157C15.3042 0.463524 15.3042 0.89596 15.5703 1.16333L17.4226 3.01895C13.0605 3.38217 8.95824 5.24025 5.80804 8.27927C2.65784 11.3183 0.653718 15.352 0.134303 19.6978C-0.385112 24.0439 0.611757 28.4357 2.9571 32.1316C5.30211 35.8276 8.85138 38.6001 13.0051 39.9812C13.075 40.0039 13.1478 40.0155 13.2214 40.0155C13.5588 40.0165 13.8467 39.7718 13.9006 39.4389C13.9542 39.1056 13.7579 38.7831 13.4376 38.6778L13.4373 38.6788Z" fill={color} />
		</Svg>
    );
}

export default Password;