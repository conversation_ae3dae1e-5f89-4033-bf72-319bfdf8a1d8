import React from 'react';
import Svg, { <PERSON>, G, Line, ClipPath, Rect, Defs} from 'react-native-svg';

import { getSizesByWidth } from './calculator';
  
const MegaPhone = (props) => {
    const originalWidth = 67;
    const originalHeight = 60;
    const newWidth = props?.style?.width ? props.style.width : 34;

    const sizes = getSizesByWidth(originalWidth, originalHeight, newWidth);

    return (
        <Svg style={[{width: sizes.width, height: sizes.height}, props.style]} viewBox="0 0 67 60" fill="none" xmlns="http://www.w3.org/2000/svg">
            <G clipPath="url(#clip0)">
                <Path d="M37.9177 8.53298C37.6751 8.59785 37.4476 8.70991 37.2483 8.86275C37.049 9.0156 36.8818 9.20623 36.7562 9.42375C36.6306 9.64127 36.5491 9.88141 36.5164 10.1304C36.4837 10.3795 36.5004 10.6325 36.5655 10.8751L36.8807 12.0512C35.8404 14.4002 34.2894 16.4877 32.3406 18.1617C30.3918 19.8357 28.0943 21.0542 25.6152 21.7283L14.5323 24.698C13.0635 25.0932 11.8115 26.055 11.051 27.3723C10.2905 28.6895 10.0836 30.2546 10.4757 31.7243L11.4656 35.4186C11.8608 36.8873 12.8226 38.1393 14.1398 38.8998C15.4571 39.6603 17.0222 39.8673 18.4918 39.4752L19.3855 39.2358L17.7221 50.7809C17.6768 51.0941 17.7099 51.4136 17.8186 51.7108C17.9272 52.0081 18.108 52.2737 18.3447 52.4838C18.5813 52.6939 18.8664 52.8419 19.1744 52.9146C19.4824 52.9873 19.8037 52.9824 20.1093 52.9002L27.4979 50.9205C27.8596 50.8238 28.1848 50.623 28.4332 50.3429C28.6817 50.0629 28.8423 49.716 28.8952 49.3454L30.7808 36.2576C33.0987 35.8154 35.4826 35.8531 37.7854 36.3683C40.0882 36.8835 42.2609 37.8653 44.1694 39.253L44.4846 40.4296C44.6159 40.9195 44.9364 41.3371 45.3756 41.5907C45.8149 41.8443 46.3368 41.913 46.8267 41.7818C47.3166 41.6505 47.7343 41.33 47.9879 40.8908C48.2415 40.4515 48.3102 39.9296 48.1789 39.4397L40.2598 9.88519C40.195 9.64254 40.0829 9.41506 39.9301 9.21575C39.7772 9.01644 39.5866 8.84921 39.3691 8.72362C39.1516 8.59804 38.9114 8.51656 38.6624 8.48385C38.4134 8.45113 38.1603 8.46783 37.9177 8.53298ZM17.502 35.7809C17.0121 35.9118 16.4903 35.8429 16.0511 35.5894C15.612 35.3359 15.2914 34.9184 15.1599 34.4287L14.17 30.7344C14.0391 30.2445 14.1079 29.7227 14.3615 29.2835C14.615 28.8444 15.0325 28.5238 15.5222 28.3923L17.3693 27.8973L19.3491 35.286L17.502 35.7809ZM25.2904 47.5524L21.9208 48.4553L23.4038 38.1591L26.7734 37.2562L25.2904 47.5524ZM42.7631 34.0048C38.317 31.9802 33.3069 31.5584 28.5849 32.8111L23.0434 34.296L21.0636 26.9074L26.6051 25.4225C31.3208 24.146 35.4487 21.2753 38.2868 17.2988L42.7631 34.0048Z" fill="white"/>
            </G>
            <Line x1="51.1735" y1="23.0211" x2="58.5405" y2="21.0471" stroke="white" strokeWidth="4" strokeLinecap="round"/>
            <Line x1="48.7989" y1="15.9399" x2="51.6943" y2="14.2683" stroke="white" strokeWidth="4" strokeLinecap="round"/>
            <Line x1="2" y1="-2" x2="5.34329" y2="-2" transform="matrix(-1 0 0 1 58.2534 32.131)" stroke="white" strokeWidth="4" strokeLinecap="round"/>
            <Defs>
                <ClipPath id="clip0">
                    <Rect width="45.8956" height="45.8956" fill="white" transform="translate(0 14.7295) rotate(-15)"/>
                </ClipPath>
            </Defs>
        </Svg>
        
    );
}

export default MegaPhone;