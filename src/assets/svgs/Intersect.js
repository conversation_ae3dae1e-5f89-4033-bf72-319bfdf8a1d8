import React, { Component } from 'react';
import Svg, { Path, Defs, G, Rect, ClipPath } from 'react-native-svg';

import { getSizesByFullWidth } from './calculator';

export const Intersect = (props) => {
    const originalWidth = 360;
    const originalHeight = 57;

    const sizes = getSizesByFullWidth(originalWidth, originalHeight);
    
    return (
        <Svg style={[{width: sizes.width, height: sizes.height}, props.style]} viewBox="0 0 360 57" fill="none" xmlns="http://www.w3.org/2000/svg">
            <G clip-path="url(#clip0)">
                <Path fill-rule="evenodd" clipRule="evenodd" d="M0 46.0016V1.99423e-05L356.177 0C357.51 -7.46184e-08 358.785 0.0430987 360 0.124969V16.8764L86.4855 56.509C81.9677 57.1637 77.0323 57.1637 72.5146 56.509L0 46.0016Z" fill="#00467F"/>
            </G>
            <Defs>
                <ClipPath id="clip0">
                    <Rect width="360" height="57" fill="white"/>
                </ClipPath>
            </Defs>
        </Svg>
        
    );
}

export default Intersect;
