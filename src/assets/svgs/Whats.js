import React from 'react';
import Svg, { Path } from 'react-native-svg';

import { getSizesByWidth } from './calculator';
  
const Whats = (props) => {
    const originalWidth = 28;
    const originalHeight = 28;
    const newWidth = props?.style?.width ? props.style.width : 42;

    const sizes = getSizesByWidth(originalWidth, originalHeight, newWidth);

    return (
        <Svg style={[{width: sizes.width, height: sizes.height}, props.style]} viewBox="0 0 28 28" fill="none" xmlns="http://www.w3.org/2000/svg">
            <Path d="M14 0.199951C6.38572 0.199951 0.2 6.38567 0.2 13.9999C0.2 16.4474 0.871212 18.7315 1.99062 20.7253L0.222265 27.0382C0.194014 27.1392 0.192738 27.2459 0.218563 27.3476C0.244389 27.4492 0.296414 27.5424 0.369452 27.6177C0.442489 27.6929 0.533987 27.7478 0.634823 27.7767C0.735659 27.8056 0.842309 27.8075 0.94414 27.7824L7.52656 26.1511C9.46078 27.1837 11.6556 27.7999 14 27.7999C21.6143 27.7999 27.8 21.6142 27.8 13.9999C27.8 6.38567 21.6143 0.199951 14 0.199951ZM14 1.39995C20.9657 1.39995 26.6 7.03423 26.6 13.9999C26.6 20.9657 20.9657 26.5999 14 26.5999C11.7668 26.5999 9.67527 26.0177 7.85703 25.0003C7.72405 24.926 7.56778 24.9051 7.41992 24.9417L1.65547 26.3703L3.20117 20.8554C3.22329 20.7775 3.2294 20.6959 3.21912 20.6155C3.20885 20.5352 3.18242 20.4577 3.14141 20.3878C2.03641 18.5141 1.4 16.334 1.4 13.9999C1.4 7.03423 7.03428 1.39995 14 1.39995ZM8.98555 6.79995C8.60092 6.79995 8.05163 6.94304 7.60039 7.42925C7.32936 7.72128 6.2 8.82166 6.2 10.7562C6.2 12.7731 7.59883 14.5135 7.76797 14.7371H7.76914V14.7382C7.7531 14.7171 7.98382 15.0513 8.2918 15.4519C8.59977 15.8525 9.03044 16.3787 9.57148 16.9484C10.6536 18.0877 12.175 19.4044 14.0633 20.2097C14.9329 20.58 15.6189 20.8036 16.1387 20.9667C17.1019 21.2692 17.9792 21.2232 18.6383 21.1261C19.1316 21.0535 19.6741 20.8166 20.2109 20.4769C20.7477 20.1372 21.2739 19.7148 21.5059 19.073C21.6721 18.6129 21.7566 18.1877 21.7871 17.8378C21.8024 17.6629 21.8043 17.5085 21.793 17.3656C21.7816 17.2226 21.7937 17.1131 21.6605 16.8945C21.3812 16.4358 21.0648 16.4239 20.7348 16.2605C20.5514 16.1697 20.0292 15.9147 19.5055 15.6652C18.9823 15.4159 18.5293 15.1951 18.2504 15.0957C18.0741 15.0322 17.8589 14.941 17.5484 14.9761C17.2379 15.0113 16.9313 15.2353 16.7527 15.4999C16.5835 15.7508 15.9022 16.555 15.6945 16.7914C15.6918 16.7897 15.7098 16.798 15.6277 16.7574C15.3709 16.6302 15.0567 16.5221 14.5918 16.2769C14.1269 16.0317 13.5454 15.6695 12.909 15.1085V15.1074C11.9618 14.2735 11.2984 13.2266 11.0891 12.8749C11.1032 12.8582 11.0874 12.8785 11.1172 12.8492L11.1184 12.848C11.3323 12.6373 11.5218 12.3857 11.682 12.2011C11.9092 11.9395 12.0094 11.7088 12.118 11.4933C12.3344 11.0638 12.2139 10.5912 12.0887 10.3425V10.3414C12.0973 10.3586 12.0209 10.19 11.9387 9.99565C11.8562 9.80071 11.751 9.54797 11.6387 9.27847C11.414 8.73945 11.1632 8.1349 11.0141 7.78081V7.77964C10.8384 7.36256 10.6006 7.06209 10.2898 6.91714C9.97905 6.77218 9.70446 6.81339 9.69336 6.81284H9.69219C9.4704 6.80261 9.22702 6.79995 8.98555 6.79995ZM8.98555 7.99995C9.21687 7.99995 9.44493 8.00277 9.63594 8.01167C9.83243 8.02146 9.82021 8.02226 9.78242 8.00464C9.74403 7.98673 9.79612 7.98092 9.90781 8.24604C10.0539 8.59276 10.3059 9.1995 10.5312 9.74018C10.6439 10.0105 10.7494 10.2642 10.8336 10.4632C10.9178 10.6622 10.9634 10.7733 11.0164 10.8792V10.8804L11.0176 10.8816C11.0695 10.984 11.0649 10.9184 11.0469 10.9542C10.9203 11.2055 10.9031 11.2672 10.775 11.4148C10.58 11.6395 10.3811 11.89 10.2758 11.9937C10.1837 12.0842 10.0175 12.2252 9.91367 12.5011C9.80973 12.7775 9.85824 13.1567 10.025 13.4398C10.247 13.8167 10.9786 15.0075 12.1156 16.0085C12.8317 16.6398 13.4987 17.0575 14.0316 17.3386C14.5645 17.6197 14.9986 17.7839 15.0957 17.832C15.3263 17.9461 15.5784 18.0347 15.8715 17.9996C16.1646 17.9644 16.4173 17.7865 16.5781 17.6046L16.5793 17.6035C16.7934 17.3608 17.4295 16.6344 17.7348 16.1867C17.7477 16.1912 17.7435 16.1877 17.8449 16.2242V16.2253H17.8461C17.8924 16.2418 18.4726 16.5015 18.9898 16.748C19.5071 16.9945 20.0321 17.2507 20.2027 17.3351C20.4489 17.457 20.5653 17.5363 20.5953 17.5367C20.5973 17.5895 20.5994 17.6467 20.5918 17.7335C20.5708 17.9745 20.5063 18.3081 20.3773 18.6652C20.3141 18.84 19.985 19.2005 19.5699 19.4632C19.1548 19.7259 18.6496 19.9115 18.4625 19.939C17.8999 20.0219 17.232 20.0522 16.4984 19.8218C15.9898 19.6622 15.3556 19.4555 14.5344 19.1058C12.8689 18.3955 11.4534 17.1869 10.4422 16.1222C9.9366 15.5899 9.53154 15.0944 9.24336 14.7195C8.95572 14.3453 8.83012 14.1506 8.72539 14.0128L8.72422 14.0117C8.53832 13.7658 7.4 12.1825 7.4 10.7562C7.4 9.24674 8.1011 8.65481 8.48047 8.24604C8.67963 8.03145 8.89737 7.99995 8.98555 7.99995Z" fill="#2D719F"/>
        </Svg>

    );
}

export default Whats;