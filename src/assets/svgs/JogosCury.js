import React from 'react';
import Svg, { Path } from 'react-native-svg';

import { getSizesByWidth } from './calculator';
  
const StudioC = (props) => {
    const originalWidth = 46;
    const originalHeight = 45;
    const newWidth = props?.style?.width ? props.style.width : 46;

    const sizes = getSizesByWidth(originalWidth, originalHeight, newWidth);

    return (
        <Svg style={[{width: sizes.width, height: sizes.height}, props.style]} viewBox="0 0 46 45" fill="none" xmlns="http://www.w3.org/2000/svg">
            <Path d="M12.9454 3.58759C11.8894 5.00999 10.1924 5.51599 10.1924 5.51599C10.1924 5.51599 10.2094 3.71974 11.2664 2.29926C12.324 0.878784 14.0178 0.371948 14.0178 0.371948C14.0178 0.371948 13.9993 2.16887 12.9454 3.58759Z" fill="#2D719F"/>
            <Path d="M11.3964 7.87913C9.66341 8.12937 8.10962 7.27343 8.10962 7.27343C8.10962 7.27343 9.36734 6.00919 11.101 5.7587C12.8356 5.50846 14.388 6.36573 14.388 6.36573C14.388 6.36573 13.13 7.62788 11.3964 7.87913Z" fill="#2D719F"/>
            <Path d="M7.92744 11.5701C6.39339 12.4319 4.64062 12.1915 4.64062 12.1915C4.64062 12.1915 5.37721 10.5566 6.90967 9.69481C8.44114 8.8352 10.1922 9.07557 10.1922 9.07557C10.1922 9.07557 9.45782 10.7097 7.92744 11.5701Z" fill="#2D719F"/>
            <Path d="M5.73602 17.0511C4.65402 18.4514 2.9491 18.9237 2.9491 18.9237C2.9491 18.9237 2.99819 17.1286 4.08095 15.7281C5.16186 14.3264 6.86578 13.8544 6.86578 13.8544C6.86578 13.8544 6.81668 15.6513 5.73602 17.0511Z" fill="#2D719F"/>
            <Path d="M5.9259 22.8367C5.47377 24.5591 4.09042 25.6772 4.09042 25.6772C4.09042 25.6772 3.4399 24.004 3.89087 22.2853C4.34301 20.5644 5.72635 19.4464 5.72635 19.4464C5.72635 19.4464 6.37645 21.1163 5.9259 22.8367Z" fill="#2D719F"/>
            <Path d="M7.90053 27.6045C8.21416 29.3561 7.433 30.9682 7.433 30.9682C7.433 30.9682 6.14492 29.7382 5.83153 27.9866C5.5174 26.2339 6.30057 24.6218 6.30057 24.6218C6.30057 24.6218 7.58898 25.8507 7.90053 27.6045Z" fill="#2D719F"/>
            <Path d="M11.1359 30.6855C11.9128 32.2821 11.5987 34.0502 11.5987 34.0502C11.5987 34.0502 10.0258 33.232 9.24814 31.6347C8.47367 30.0386 8.78539 28.2704 8.78539 28.2704C8.78539 28.2704 10.3606 29.0877 11.1359 30.6855Z" fill="#2D719F"/>
            <Path d="M15.6888 32.9825C16.94 34.2296 17.2149 36.0029 17.2149 36.0029C17.2149 36.0029 15.4664 35.7588 14.2157 34.5118C12.9665 33.2664 12.6864 31.492 12.6864 31.492C12.6864 31.492 14.4376 31.7376 15.6888 32.9825Z" fill="#2D719F"/>
            <Path d="M4.35707 12.3949C4.39078 14.1741 3.36607 15.6379 3.36607 15.6379C3.36607 15.6379 2.28624 14.2147 2.25203 12.4347C2.21833 10.6541 3.24404 9.18954 3.24404 9.18954C3.24404 9.18954 4.32312 10.6122 4.35707 12.3949Z" fill="#2D719F"/>
            <Path d="M2.55267 19.6833C3.45845 21.2065 3.29318 22.9939 3.29318 22.9939C3.29318 22.9939 1.65801 22.3136 0.751147 20.7915C-0.154131 19.2654 0.0119703 17.476 0.0119703 17.476C0.0119703 17.476 1.64689 18.159 2.55267 19.6833Z" fill="#2D719F"/>
            <Path d="M3.72903 25.9621C5.14984 27.0033 5.68604 28.7151 5.68604 28.7151C5.68604 28.7151 3.91763 28.7406 2.49849 27.6974C1.07802 26.6562 0.541992 24.9437 0.541992 24.9437C0.541992 24.9437 2.30805 24.919 3.72903 25.9621Z" fill="#2D719F"/>
            <Path d="M7.44838 31.5158C9.14009 31.9776 10.2391 33.3832 10.2391 33.3832C10.2391 33.3832 8.59512 34.0438 6.90583 33.5853C5.21229 33.1265 4.11389 31.7185 4.11389 31.7185C4.11389 31.7185 5.75667 31.0574 7.44838 31.5158Z" fill="#2D719F"/>
            <Path d="M11.1831 34.4752C12.9291 34.3621 14.412 35.3391 14.412 35.3391C14.412 35.3391 13.0636 36.4986 11.3157 36.6127C9.5699 36.7257 8.08435 35.7494 8.08435 35.7494C8.08435 35.7494 9.43566 34.5888 11.1831 34.4752Z" fill="#2D719F"/>
            <Path d="M16.0692 36.6705C17.7384 36.1324 19.4086 36.7155 19.4086 36.7155C19.4086 36.7155 18.3749 38.1712 16.707 38.7114C15.0393 39.2509 13.3667 38.6689 13.3667 38.6689C13.3667 38.6689 14.4012 37.2107 16.0692 36.6705Z" fill="#2D719F"/>
            <Path d="M7.80678 7.15351C7.25963 8.84598 5.81582 9.88206 5.81582 9.88206C5.81582 9.88206 5.26073 8.17605 5.80595 6.48601C6.35385 4.79322 7.79666 3.75638 7.79666 3.75638C7.79666 3.75638 8.35384 5.46314 7.80678 7.15351Z" fill="#2D719F"/>
            <Path d="M21.0198 34.929C19.5889 33.9 17.8229 33.9409 17.8229 33.9409C17.8229 33.9409 18.3718 35.6469 19.8032 36.6759C21.2322 37.7037 22.9998 37.6639 22.9998 37.6639C22.9998 37.6639 22.4496 35.9573 21.0198 34.929Z" fill="#2D719F"/>
            <Path d="M33.054 3.58759C34.1105 5.00999 35.8069 5.51599 35.8069 5.51599C35.8069 5.51599 35.79 3.71974 34.7335 2.29926C33.6754 0.878784 31.9811 0.371948 31.9811 0.371948C31.9811 0.371948 32.0001 2.16887 33.054 3.58759Z" fill="#2D719F"/>
            <Path d="M34.6025 7.87913C36.3366 8.12937 37.8888 7.27343 37.8888 7.27343C37.8888 7.27343 36.6318 6.00919 34.8992 5.7587C33.164 5.50846 31.6108 6.36573 31.6108 6.36573C31.6108 6.36573 32.8694 7.62788 34.6025 7.87913Z" fill="#2D719F"/>
            <Path d="M38.0718 11.5701C39.6059 12.4319 41.3575 12.1915 41.3575 12.1915C41.3575 12.1915 40.6215 10.5566 39.0891 9.69481C37.5576 8.8352 35.807 9.07557 35.807 9.07557C35.807 9.07557 36.5409 10.7097 38.0718 11.5701Z" fill="#2D719F"/>
            <Path d="M40.2634 17.0511C41.3454 18.4514 43.0498 18.9237 43.0498 18.9237C43.0498 18.9237 43.0009 17.1286 41.9174 15.7281C40.837 14.3264 39.1337 13.8544 39.1337 13.8544C39.1337 13.8544 39.1819 15.6513 40.2634 17.0511Z" fill="#2D719F"/>
            <Path d="M40.0734 22.8367C40.5255 24.5591 41.91 25.6772 41.91 25.6772C41.91 25.6772 42.5584 24.004 42.1079 22.2853C41.6568 20.5644 40.273 19.4464 40.273 19.4464C40.273 19.4464 39.6218 21.1163 40.0734 22.8367Z" fill="#2D719F"/>
            <Path d="M38.0978 27.6045C37.7858 29.3561 38.5663 30.9682 38.5663 30.9682C38.5663 30.9682 39.8543 29.7382 40.1684 27.9866C40.4814 26.2339 39.6993 24.6218 39.6993 24.6218C39.6993 24.6218 38.4109 25.8507 38.0978 27.6045Z" fill="#2D719F"/>
            <Path d="M34.8636 30.6855C34.0867 32.2821 34.4004 34.0502 34.4004 34.0502C34.4004 34.0502 35.9737 33.232 36.7506 31.6347C37.5259 30.0386 37.2139 28.2704 37.2139 28.2704C37.2139 28.2704 35.6388 29.0877 34.8636 30.6855Z" fill="#2D719F"/>
            <Path d="M30.3107 32.9825C29.06 34.2296 28.7845 36.0029 28.7845 36.0029C28.7845 36.0029 30.5335 35.7588 31.7837 34.5118C33.0323 33.2664 33.313 31.492 33.313 31.492C33.313 31.492 31.5609 31.7376 30.3107 32.9825Z" fill="#2D719F"/>
            <Path d="M41.6414 12.3949C41.6085 14.1741 42.6327 15.6379 42.6327 15.6379C42.6327 15.6379 43.7131 14.2147 43.747 12.4347C43.7804 10.6541 42.7542 9.18954 42.7542 9.18954C42.7542 9.18954 41.6764 10.6122 41.6414 12.3949Z" fill="#2D719F"/>
            <Path d="M43.4468 19.6833C42.5405 21.2065 42.7061 22.9939 42.7061 22.9939C42.7061 22.9939 44.3416 22.3136 45.2484 20.7915C46.1541 19.2654 45.9881 17.476 45.9881 17.476C45.9881 17.476 44.3521 18.159 43.4468 19.6833Z" fill="#2D719F"/>
            <Path d="M42.2697 25.9621C40.8497 27.0033 40.3137 28.7151 40.3137 28.7151C40.3137 28.7151 42.0813 28.7406 43.5013 27.6974C44.9207 26.6562 45.4567 24.9437 45.4567 24.9437C45.4567 24.9437 43.6908 24.919 42.2697 25.9621Z" fill="#2D719F"/>
            <Path d="M38.5506 31.5158C36.8588 31.9776 35.7599 33.3832 35.7599 33.3832C35.7599 33.3832 37.4043 34.0438 39.0934 33.5853C40.7872 33.1265 41.8851 31.7185 41.8851 31.7185C41.8851 31.7185 40.2427 31.0574 38.5506 31.5158Z" fill="#2D719F"/>
            <Path d="M34.8164 34.4752C33.07 34.3621 31.5879 35.3391 31.5879 35.3391C31.5879 35.3391 32.9358 36.4986 34.6837 36.6127C36.4295 36.7257 37.9154 35.7494 37.9154 35.7494C37.9154 35.7494 36.5638 34.5888 34.8164 34.4752Z" fill="#2D719F"/>
            <Path d="M29.9301 36.6705C28.2602 36.1324 26.5902 36.7155 26.5902 36.7155C26.5902 36.7155 27.6235 38.1712 29.2927 38.7114C30.9595 39.2509 32.6321 38.6689 32.6321 38.6689C32.6321 38.6689 31.5985 37.2107 29.9301 36.6705Z" fill="#2D719F"/>
            <Path d="M38.1928 7.15351C38.7399 8.84598 40.1838 9.88206 40.1838 9.88206C40.1838 9.88206 40.7394 8.17605 40.1928 6.48601C39.6452 4.79322 38.2035 3.75638 38.2035 3.75638C38.2035 3.75638 37.6463 5.46314 38.1928 7.15351Z" fill="#2D719F"/>
            <Path d="M24.9793 34.929C26.4098 33.9 28.1759 33.9409 28.1759 33.9409C28.1759 33.9409 27.6272 35.6469 26.1955 36.6759C24.7671 37.7037 23 37.6638 23 37.6638C23 37.6638 23.5497 35.9573 24.9793 34.929Z" fill="#2D719F"/>
            <Path d="M16.9893 44.6608C16.941 44.6608 16.8928 44.6471 16.8498 44.6207C16.7219 44.5431 16.68 44.3734 16.7569 44.2422C16.8063 44.1601 17.9886 42.1892 20.5489 40.3866C23.0833 38.602 24.8254 37.8475 24.8986 37.8167C25.0355 37.7583 25.1958 37.8241 25.253 37.9631C25.3108 38.1028 25.2467 38.2651 25.1082 38.3239C25.0902 38.3314 23.3412 39.0902 20.8572 40.8392C18.3992 42.57 17.2329 44.508 17.2212 44.5272C17.1708 44.6127 17.0809 44.6608 16.9893 44.6608Z" fill="#2D719F"/>
            <Path d="M29.0096 44.6608C28.9189 44.6608 28.8292 44.6127 28.7778 44.5272C28.7672 44.508 27.5997 42.5701 25.1418 40.8392C22.6584 39.0902 20.9085 38.3314 20.8908 38.3239C20.7533 38.2651 20.6876 38.1027 20.7459 37.9631C20.8043 37.8241 20.9636 37.7578 21.101 37.8167C21.1747 37.8474 22.9163 38.602 25.4506 40.3866C28.0109 42.1892 29.1933 44.16 29.2431 44.2422C29.3196 44.3734 29.2777 44.5432 29.1504 44.6207C29.1061 44.6471 29.0589 44.6608 29.0096 44.6608Z" fill="#2D719F"/>
            <Path d="M20.9779 24.4616L20.7148 26.3812C20.6766 26.5311 20.753 26.6825 20.8647 26.7957C20.9779 26.8707 21.166 26.9089 21.2792 26.8339L23.0107 25.9682L24.7422 26.8339C24.8171 26.8721 24.8553 26.8721 24.9303 26.8721C25.0053 26.8721 25.1184 26.8339 25.1934 26.7972C25.3066 26.7222 25.3815 26.5708 25.3433 26.3827L25.0802 24.4631L26.4354 23.0711C26.5486 22.958 26.5853 22.808 26.5486 22.6566C26.5104 22.5067 26.3605 22.3935 26.2105 22.3553L24.2909 22.0173L23.4237 20.3226C23.3488 20.1726 23.1974 20.0962 23.0475 20.0962C22.8975 20.0962 22.7461 20.1712 22.6712 20.3226L21.7687 22.0158L19.8491 22.3539C19.6992 22.3921 19.586 22.5038 19.5111 22.6552C19.4728 22.8051 19.5111 22.9565 19.6242 23.0697L20.9779 24.4616ZM22.1067 22.8433C22.2199 22.8051 22.3331 22.7301 22.408 22.617L23.0474 21.4132L23.6868 22.617C23.7617 22.7301 23.8749 22.8051 23.9881 22.8433L25.305 23.0697L24.3644 24.0486C24.2894 24.1235 24.2144 24.2749 24.2512 24.3866L24.4393 25.7036L23.2355 25.101C23.1224 25.026 22.9724 25.026 22.8593 25.101L21.6555 25.7036L21.8436 24.3866C21.8436 24.2735 21.8054 24.1235 21.7305 24.0486L20.7898 23.0682L22.1067 22.8433Z" fill="#2D719F"/>
            <Path d="M15.3338 23.4825C15.3338 27.7348 18.7953 31.1962 23.0475 31.1962C27.2997 31.1962 30.7611 27.7347 30.7611 23.4825C30.7611 20.246 28.7665 17.4621 25.9444 16.3333L26.4706 15.8821L28.9913 13.6627C29.0663 13.5877 29.1412 13.4745 29.1412 13.3613L29.1427 1.50868C29.1427 1.28233 28.9546 1.09418 28.7282 1.09418H17.4031C17.1767 1.09418 16.9886 1.28231 16.9886 1.50868V13.323C16.9886 13.4361 17.0268 13.5493 17.1385 13.6243L19.6593 15.8437L20.1854 16.2949C17.3281 17.462 15.3334 20.2458 15.3334 23.4825H15.3338ZM26.5837 1.9216H28.2769V13.135L26.5837 14.6401V1.9216ZM19.5109 14.6401L17.8176 13.135V1.9216H19.5109V14.6401ZM20.3751 1.9216H25.7562V15.3926L25.0419 16.032C24.3643 15.8821 23.7249 15.7689 23.0473 15.7689C22.3698 15.7689 21.7304 15.8821 21.091 16.032L20.3752 15.3926L20.3751 1.9216ZM23.0473 16.5963C26.8484 16.5963 29.9335 19.6815 29.9335 23.4825C29.9335 27.2836 26.8483 30.3687 23.0473 30.3687C19.2462 30.3687 16.1611 27.2836 16.1611 23.4825C16.1611 19.6815 19.2462 16.5963 23.0473 16.5963Z" fill="#2D719F"/>
            <Path d="M23.0475 29.5413C26.3958 29.5413 29.1063 26.8324 29.1063 23.4825C29.1063 20.1343 26.3974 17.4238 23.0475 17.4238C19.6993 17.4238 16.9888 20.1326 16.9888 23.4825C16.9888 26.8308 19.6991 29.5413 23.0475 29.5413ZM23.0475 18.2531C25.9446 18.2531 28.277 20.5857 28.277 23.4825C28.277 26.3794 25.9444 28.712 23.0475 28.712C20.1507 28.712 17.8181 26.3794 17.8181 23.4825C17.8181 20.5857 20.1507 18.2531 23.0475 18.2531Z" fill="#2D719F"/>
        </Svg>
    );
}

export default StudioC;