import React from 'react';
import Svg, { Path } from 'react-native-svg';

import { getSizesByWidth } from './calculator';
  
const Training = (props) => {
    const originalWidth = 48;
    const originalHeight = 52;
    const newWidth = props?.style?.width ? props.style.width : 48;

    const sizes = getSizesByWidth(originalWidth, originalHeight, newWidth);

    return (
        <Svg width="48" height="52" viewBox="0 0 48 52" fill="none" xmlns="http://www.w3.org/2000/svg">
            <Path d="M45.1317 21.3241C43.2735 20.9879 41.3884 20.8236 39.5 20.8333C38.415 20.8333 37.3559 20.8333 36.2967 21.04C34.9781 19.2804 33.295 17.8264 31.3625 16.7775C33.0225 14.9646 33.944 12.5963 33.9459 10.1383C33.9459 7.49364 32.8953 4.95731 31.0252 3.08726C29.1552 1.21721 26.6189 0.166626 23.9742 0.166626C21.3296 0.166626 18.7932 1.21721 16.9232 3.08726C15.0531 4.95731 14.0025 7.49364 14.0025 10.1383C14.0044 12.5963 14.926 14.9646 16.5859 16.7775C14.6649 17.8319 12.9845 19.2747 11.6517 21.0141C10.6442 20.8333 9.58504 20.8333 8.50004 20.8333C6.61013 20.8406 4.72493 21.0222 2.86837 21.3758C2.26656 21.4858 1.72334 21.8059 1.33542 22.279C0.9475 22.752 0.740061 23.3474 0.750039 23.9591V45.2716C0.749422 45.6511 0.832434 46.0261 0.993172 46.3699C1.15391 46.7137 1.38842 47.0179 1.68004 47.2608C1.96981 47.5051 2.30981 47.6827 2.6759 47.7809C3.04199 47.8791 3.42521 47.8956 3.79837 47.8291C5.34427 47.4924 6.91845 47.3022 8.50004 47.2608C13.5042 47.2553 18.3998 48.7195 22.5792 51.4716L22.915 51.6008C23.2577 51.7495 23.6265 51.8285 24 51.8333C24.2466 51.8302 24.491 51.7866 24.7234 51.7041H24.9042L25.24 51.575C29.4592 48.7522 34.4237 47.2503 39.5 47.2608C41.0784 47.2681 42.6525 47.4238 44.2017 47.7258C44.5749 47.7923 44.9581 47.7758 45.3242 47.6776C45.6903 47.5794 46.0303 47.4018 46.32 47.1575C46.6117 46.9146 46.8462 46.6104 47.0069 46.2666C47.1676 45.9228 47.2507 45.5478 47.25 45.1683V23.8558C47.2478 23.2529 47.0348 22.6698 46.648 22.2075C46.2611 21.7451 45.7247 21.4326 45.1317 21.3241ZM24 5.33329C25.2102 5.42221 26.3419 5.96568 27.1679 6.85458C27.9939 7.74347 28.453 8.91196 28.453 10.1254C28.453 11.3388 27.9939 12.5073 27.1679 13.3962C26.3419 14.2851 25.2102 14.8285 24 14.9175C22.7899 14.8285 21.6582 14.2851 20.8322 13.3962C20.0062 12.5073 19.5471 11.3388 19.5471 10.1254C19.5471 8.91196 20.0062 7.74347 20.8322 6.85458C21.6582 5.96568 22.7899 5.42221 24 5.33329ZM21.4167 44.9358C17.3678 43.0642 12.9606 42.0947 8.50004 42.0941C7.64754 42.0941 6.79504 42.0941 5.91671 42.2233V26C8.08299 25.7576 10.2717 25.8011 12.4267 26.1291H12.7109C15.7892 26.6949 18.739 27.8153 21.4167 29.4358V44.9358ZM24 24.9666C22.8513 24.304 21.6604 23.7171 20.435 23.21H20.28C19.4275 22.8741 18.575 22.5383 17.6967 22.28C19.4903 20.8523 21.7078 20.0617 24 20.0325C26.2862 20.047 28.503 20.8192 30.3034 22.2283C28.1104 22.913 25.9971 23.831 24 24.9666ZM42.0834 42.2233C36.7789 41.6903 31.4283 42.5821 26.5834 44.8066V29.3066C29.2659 27.7277 32.2176 26.6591 35.2892 26.155H35.8059C37.8828 25.8315 39.993 25.7794 42.0834 26V42.2233Z" fill="#4EA1CC"/>
        </Svg>
    );
}

export default Training;