import React from 'react';
import Svg, { Path } from 'react-native-svg';

import { getSizesByWidth } from './calculator';
  
const ArrowLeft = (props) => {
    const originalWidth = 14;
    const originalHeight = 24;
    const newWidth = props?.style?.width ? props.style.width : 50;
    const color = props?.style?.color ? props.style.color : '#00467F';

    const sizes = getSizesByWidth(originalWidth, originalHeight, newWidth);

    return (
      <Svg style={[{width: sizes.width, height: sizes.height}, props.style]} viewBox="0 0 14 24" fill="none" xmlns="http://www.w3.org/2000/svg">
        <Path d="M12 22L2 12L12 2" stroke={color} strokeWidth="4" strokeLinecap="round" strokeLinejoin="round"/>
      </Svg>
         
    );
}

export default ArrowLeft;