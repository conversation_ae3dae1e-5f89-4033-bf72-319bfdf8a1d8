import React from 'react';
import Svg, { Path } from 'react-native-svg';

import { getSizesByWidth } from './calculator';
  
const CheckBox = (props) => {
    const originalWidth = 15;
    const originalHeight = 11;
    const newWidth = props?.style?.width ? props.style.width : 11;
    
    const sizes = getSizesByWidth(originalWidth, originalHeight, newWidth);

    return (
        <Svg style={[{width: sizes.width, height: sizes.height}, props.style]} viewBox="0 0 15 11" fill="none" xmlns="http://www.w3.org/2000/svg">
            <Path d="M14.6564 0.318324C14.5572 0.217458 14.4391 0.137398 14.3091 0.0827634C14.179 0.0281285 14.0395 0 13.8986 0C13.7577 0 13.6182 0.0281285 13.4881 0.0827634C13.3581 0.137398 13.24 0.217458 13.1408 0.318324L5.18924 8.34643L1.84852 4.9673C1.7455 4.86697 1.62389 4.78807 1.49063 4.73512C1.35737 4.68217 1.21507 4.6562 1.07185 4.6587C0.928639 4.6612 0.787316 4.69211 0.655951 4.74968C0.524587 4.80724 0.405754 4.89034 0.306239 4.99421C0.206723 5.09808 0.128473 5.2207 0.075957 5.35506C0.0234405 5.48943 -0.00231454 5.6329 0.000163213 5.7773C0.00264097 5.9217 0.0333028 6.06419 0.0903976 6.19664C0.147492 6.32909 0.229902 6.44891 0.332922 6.54925L4.43144 10.6817C4.53066 10.7825 4.64871 10.8626 4.77877 10.9172C4.90884 10.9719 5.04834 11 5.18924 11C5.33014 11 5.46965 10.9719 5.59971 10.9172C5.72977 10.8626 5.84782 10.7825 5.94704 10.6817L14.6564 1.90027C14.7647 1.7995 14.8512 1.67719 14.9103 1.54106C14.9695 1.40492 15 1.25792 15 1.1093C15 0.960678 14.9695 0.81367 14.9103 0.677537C14.8512 0.541404 14.7647 0.419098 14.6564 0.318324Z" fill="white"/>
        </Svg>

    );
}

export default CheckBox;