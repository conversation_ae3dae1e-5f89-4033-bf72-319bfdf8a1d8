import React from 'react';
import Svg, { Path } from 'react-native-svg';

import { getSizesByWidth } from './calculator';
  
const Trash = (props) => {
    const originalWidth = 20;
    const originalHeight = 22;
    const newWidth = props?.style?.width ? props.style.width : 34;
    const color = props?.style?.color ? props.style.color : "#00467F";

    const sizes = getSizesByWidth(originalWidth, originalHeight, newWidth);

    return (
        <Svg style={[{width: sizes.width, height: sizes.height}, props.style]} viewBox="0 0 20 22" fill="none" xmlns="http://www.w3.org/2000/svg">
            <Path d="M15.9398 21.2929H4.13043L2.56522 3.29291H17.513L15.9398 21.2929ZM4.84999 20.5103H15.2117L16.6126 4.07552H3.38646L4.84999 20.5103Z" fill="#90B0C0" stroke="#90B0C0" stroke-width="0.5"/>
            <Path d="M18.6087 4.07552H1.3913C1.17521 4.07552 1 3.9003 1 3.68421C1 3.46812 1.17521 3.29291 1.3913 3.29291H18.6087C18.8248 3.29291 19 3.46812 19 3.68421C19 3.9003 18.8248 4.07552 18.6087 4.07552Z" fill="#90B0C0" stroke="#90B0C0" stroke-width="0.5"/>
            <Path d="M13.4281 4.07556H6.55676V2.95652C6.55676 2.43754 6.7629 1.93985 7.12972 1.57296C7.49674 1.20611 7.99444 1 8.51328 1H11.4715C11.9904 1 12.4881 1.20613 12.855 1.57296C13.2219 1.93981 13.428 2.4375 13.428 2.95652L13.4281 4.07556ZM7.33937 3.29295H12.6455V2.9565C12.6435 2.64573 12.5192 2.34839 12.2994 2.12864C12.0796 1.90888 11.7823 1.7845 11.4715 1.78258H8.51336C8.20207 1.78258 7.90333 1.90626 7.68326 2.12637C7.46315 2.34648 7.33947 2.64503 7.33947 2.95647L7.33937 3.29295Z" fill="#90B0C0" stroke="#90B0C0" stroke-width="0.5"/>
            <Path d="M9.99192 6.4469C10.208 6.4469 10.3832 6.66299 10.3832 6.8382V17.7715C10.3832 17.9876 10.208 18.1628 9.99192 18.1628C9.77583 18.1628 9.60062 17.9467 9.60062 17.7715V6.8382C9.60062 6.62211 9.77583 6.4469 9.99192 6.4469Z" fill="#90B0C0" stroke="#90B0C0" stroke-width="0.5"/>
            <Path d="M6.94807 6.4469C7.16416 6.4469 7.33937 6.66299 7.33937 6.8382V17.7715C7.33937 17.9876 7.16416 18.1628 6.94807 18.1628C6.73198 18.1628 6.55676 17.9467 6.55676 17.7715V6.8382C6.55676 6.62211 6.73198 6.4469 6.94807 6.4469Z" fill="#90B0C0" stroke="#90B0C0" stroke-width="0.5"/>
            <Path d="M13.0366 6.4469C13.2527 6.4469 13.4279 6.66299 13.4279 6.8382V17.7715C13.4279 17.9876 13.2527 18.1628 13.0366 18.1628C12.8205 18.1628 12.6453 17.9467 12.6453 17.7715V6.8382C12.6453 6.62211 12.8205 6.4469 13.0366 6.4469Z" fill="#90B0C0" stroke="#90B0C0" stroke-width="0.5"/>
        </Svg>
    );
}

export default Trash;