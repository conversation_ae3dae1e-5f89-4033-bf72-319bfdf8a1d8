import React from 'react';
import Svg, { Path } from 'react-native-svg';

import { getSizesByWidth } from './calculator';
  
const Disapproved = (props) => {
    const originalWidth = 80;
    const originalHeight = 80;
    const newWidth = props?.style?.width ? props.style.width : 80;

    const sizes = getSizesByWidth(originalWidth, originalHeight, newWidth);

    return (
        <Svg style={[{width: sizes.width, height: sizes.height}, props.style]} viewBox="0 0 80 80" fill="none" xmlns="http://www.w3.org/2000/svg">
            <Path d="M40 20C38.9391 20 37.9217 20.4214 37.1716 21.1716C36.4214 21.9217 36 22.9391 36 24V40C36 41.0609 36.4214 42.0783 37.1716 42.8284C37.9217 43.5786 38.9391 44 40 44C41.0609 44 42.0783 43.5786 42.8284 42.8284C43.5786 42.0783 44 41.0609 44 40V24C44 22.9391 43.5786 21.9217 42.8284 21.1716C42.0783 20.4214 41.0609 20 40 20ZM40 52C39.2089 52 38.4355 52.2346 37.7777 52.6741C37.1199 53.1136 36.6072 53.7384 36.3045 54.4693C36.0017 55.2002 35.9225 56.0044 36.0769 56.7804C36.2312 57.5563 36.6122 58.269 37.1716 58.8284C37.731 59.3878 38.4437 59.7688 39.2196 59.9231C39.9956 60.0775 40.7998 59.9983 41.5307 59.6955C42.2616 59.3928 42.8864 58.8801 43.3259 58.2223C43.7654 57.5645 44 56.7911 44 56C44 54.9391 43.5786 53.9217 42.8284 53.1716C42.0783 52.4214 41.0609 52 40 52ZM78.84 22.24L57.76 1.16C56.9935 0.429065 55.9791 0.014734 54.92 0H25.08C24.0209 0.014734 23.0065 0.429065 22.24 1.16L1.16 22.24C0.429065 23.0065 0.014734 24.0209 0 25.08V54.92C0.014734 55.9791 0.429065 56.9935 1.16 57.76L22.24 78.84C23.0065 79.5709 24.0209 79.9853 25.08 80H54.92C55.9791 79.9853 56.9935 79.5709 57.76 78.84L78.84 57.76C79.5709 56.9935 79.9853 55.9791 80 54.92V25.08C79.9853 24.0209 79.5709 23.0065 78.84 22.24ZM72 53.24L53.24 72H26.76L8 53.24V26.76L26.76 8H53.24L72 26.76V53.24Z" fill="#FF6542"/>
        </Svg>
    );
}

export default Disapproved;