import React from 'react';
import Svg, { Path } from 'react-native-svg';

import { getSizesByWidth } from './calculator';
  
const Info = (props) => {
    const originalWidth = 44;
    const originalHeight = 44;
    const newWidth = props?.style?.width ? props.style.width : 44;

    const sizes = getSizesByWidth(originalWidth, originalHeight, newWidth);

    return (
        <Svg width="44" height="44" viewBox="0 0 44 44" fill="none" xmlns="http://www.w3.org/2000/svg">
            <Path d="M22 18.3334C21.5138 18.3334 21.0475 18.5265 20.7037 18.8703C20.3598 19.2141 20.1667 19.6805 20.1667 20.1667V23.8334C20.1667 24.3196 20.3598 24.7859 20.7037 25.1297C21.0475 25.4735 21.5138 25.6667 22 25.6667C22.4863 25.6667 22.9526 25.4735 23.2964 25.1297C23.6402 24.7859 23.8334 24.3196 23.8334 23.8334V20.1667C23.8334 19.6805 23.6402 19.2141 23.2964 18.8703C22.9526 18.5265 22.4863 18.3334 22 18.3334ZM37.51 17.7467C37.2669 15.0601 36.3306 12.4827 34.7927 10.2665C33.2548 8.05035 31.1679 6.27135 28.7363 5.10356C26.3046 3.93578 23.6115 3.41921 20.9203 3.60439C18.2291 3.78958 15.6321 4.67017 13.3834 6.16003C11.4569 7.44821 9.83962 9.14707 8.64776 11.1346C7.45589 13.1221 6.719 15.3489 6.49003 17.655C6.26541 19.9461 6.55359 22.2584 7.33367 24.4242C8.11375 26.59 9.36615 28.555 11 30.1767L20.7167 39.9117C20.8871 40.0835 21.0899 40.2199 21.3133 40.313C21.5367 40.4061 21.7763 40.454 22.0184 40.454C22.2604 40.454 22.5 40.4061 22.7234 40.313C22.9468 40.2199 23.1496 40.0835 23.32 39.9117L33 30.1767C34.6339 28.555 35.8863 26.59 36.6664 24.4242C37.4465 22.2584 37.7346 19.9461 37.51 17.655V17.7467ZM30.4334 27.5917L22 36.025L13.5667 27.5917C12.3239 26.3488 11.3718 24.846 10.7791 23.1913C10.1863 21.5365 9.96746 19.7711 10.1384 18.0217C10.3104 16.2454 10.8749 14.5295 11.7912 12.9981C12.7076 11.4667 13.9527 10.158 15.4367 9.16669C17.3818 7.87463 19.6649 7.1854 22 7.1854C24.3351 7.1854 26.6183 7.87463 28.5634 9.16669C30.0428 10.1542 31.2853 11.457 32.2013 12.9817C33.1174 14.5065 33.6845 16.2151 33.8617 17.985C34.0382 19.7403 33.8221 21.5129 33.2292 23.1744C32.6362 24.8359 31.6812 26.3447 30.4334 27.5917ZM23.6867 13.8784C23.6501 13.7657 23.5943 13.6603 23.5217 13.5667L23.3017 13.2917C23.0828 13.0839 22.816 12.9333 22.525 12.8533C22.234 12.7734 21.9277 12.7665 21.6334 12.8334H21.3034L20.9734 12.9984L20.6984 13.2367L20.4784 13.5117C20.4058 13.6053 20.35 13.7107 20.3134 13.8234C20.2575 13.9325 20.2204 14.0503 20.2034 14.1717C20.173 14.3348 20.1607 14.5008 20.1667 14.6667C20.1603 14.9131 20.2105 15.1577 20.3134 15.3817C20.4068 15.5981 20.537 15.7966 20.6984 15.9684C20.8729 16.1397 21.0781 16.2765 21.3034 16.3717C21.7522 16.5422 22.2479 16.5422 22.6967 16.3717C22.9195 16.272 23.124 16.1357 23.3017 15.9684C23.463 15.7966 23.5933 15.5981 23.6867 15.3817C23.783 15.1556 23.8329 14.9124 23.8334 14.6667C23.8423 14.5508 23.8423 14.4343 23.8334 14.3184C23.8163 14.1629 23.7663 14.0129 23.6867 13.8784Z" fill="#FF312E"/>
        </Svg>

    );
}

export default Info;