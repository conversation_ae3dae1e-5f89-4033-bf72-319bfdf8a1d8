import React from 'react';
import Svg, { Path } from 'react-native-svg';

import { getSizesByWidth } from './calculator';
  
const Alert = (props) => {
    const originalWidth = 64;
    const originalHeight = 64;
    const newWidth = props?.style?.width ? props.style.width : 64;

    const sizes = getSizesByWidth(originalWidth, originalHeight, newWidth);

    return (
        <Svg width="64" height="64" viewBox="0 0 64 64" fill="none" xmlns="http://www.w3.org/2000/svg">
			<Path d="M31.9999 16.1667C31.1601 16.1667 30.3546 16.5003 29.7607 17.0942C29.1669 17.6881 28.8333 18.4935 28.8333 19.3334V32C28.8333 32.8399 29.1669 33.6454 29.7607 34.2392C30.3546 34.8331 31.1601 35.1667 31.9999 35.1667C32.8398 35.1667 33.6452 34.8331 34.2391 34.2392C34.833 33.6454 35.1666 32.8399 35.1666 32V19.3334C35.1666 18.4935 34.833 17.6881 34.2391 17.0942C33.6452 16.5003 32.8398 16.1667 31.9999 16.1667ZM31.9999 41.5C31.3736 41.5 30.7614 41.6858 30.2406 42.0337C29.7199 42.3817 29.314 42.8762 29.0743 43.4549C28.8346 44.0335 28.7719 44.6702 28.8941 45.2845C29.0163 45.8988 29.3179 46.463 29.7607 46.9059C30.2036 47.3488 30.7679 47.6503 31.3821 47.7725C31.9964 47.8947 32.6331 47.832 33.2118 47.5923C33.7904 47.3527 34.285 46.9468 34.6329 46.426C34.9809 45.9053 35.1666 45.293 35.1666 44.6667C35.1666 43.8269 34.833 43.0214 34.2391 42.4275C33.6452 41.8337 32.8398 41.5 31.9999 41.5ZM62.7483 17.94L46.0599 1.25171C45.4531 0.67305 44.65 0.345038 43.8116 0.333374H20.1883C19.3498 0.345038 18.5468 0.67305 17.9399 1.25171L1.25159 17.94C0.672928 18.5469 0.344916 19.3499 0.333252 20.1884V43.8117C0.344916 44.6501 0.672928 45.4532 1.25159 46.06L17.9399 62.7484C18.5468 63.327 19.3498 63.655 20.1883 63.6667H43.8116C44.65 63.655 45.4531 63.327 46.0599 62.7484L62.7483 46.06C63.3269 45.4532 63.6549 44.6501 63.6666 43.8117V20.1884C63.6549 19.3499 63.3269 18.5469 62.7483 17.94ZM57.3333 42.4817L42.4816 57.3334H21.5183L6.66659 42.4817V21.5184L21.5183 6.66671H42.4816L57.3333 21.5184V42.4817Z" fill="white"/>
		</Svg>

    );
}

export default Alert;