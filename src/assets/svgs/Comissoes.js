import React from 'react';
import Svg, { Path } from 'react-native-svg';

import { getSizesByWidth } from './calculator';
  
const Comissoes = (props) => {
    const originalWidth = 19;
    const originalHeight = 22;
    const newWidth = props?.style?.width ? props.style.width : 26;

    const sizes = getSizesByWidth(originalWidth, originalHeight, newWidth);

    return (
    <Svg style={[{width: sizes.width, height: sizes.height}, props.style]} viewBox="0 0 19 22" fill="none" xmlns="http://www.w3.org/2000/svg">
      <Path d="M18.5252 3.75003H16.1501V1.37508L15.6752 0.900024H5.22504L4.88771 1.03775L0.13757 5.7879L0 6.12522V18.4751L0.475055 18.9502H2.85V21.3251L3.32506 21.8002H18.5249L19 21.3251L18.9998 4.22508L18.5252 3.75003ZM4.7499 2.51984V5.65009H1.61966L4.7499 2.51984ZM0.949951 17.9998V6.59983H5.22496L5.70001 6.12477V1.84977H15.1999V17.9995L0.949951 17.9998ZM18.05 20.8498H3.79996V18.95H15.6751L16.1502 18.4749L16.15 4.70002H18.0501L18.05 20.8498Z" fill="white"/>
      <Path d="M5.70021 8.97522H12.3501V9.92517H5.70021V8.97522Z" fill="white"/>
      <Path d="M3.80003 13.725H12.35V14.6749H3.80003V13.725Z" fill="white"/>
    </Svg>       
    );
}

export default Comissoes;