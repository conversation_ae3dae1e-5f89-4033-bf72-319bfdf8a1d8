import React from 'react';
import Svg, { Path } from 'react-native-svg';

import { getSizesByWidth } from './calculator';
  
const ArrowDown2 = (props) => {
    const originalWidth = 14;
    const originalHeight = 9;
    const newWidth = props?.style?.width ? props.style.width : 14;

    const sizes = getSizesByWidth(originalWidth, originalHeight, newWidth);

    return (
        <Svg width="16" height="10" viewBox="0 0 16 10" fill="none" xmlns="http://www.w3.org/2000/svg">
			<Path d="M14.12 0L8 6.10667L1.88 0L0 1.88L8 9.88L16 1.88L14.12 0Z" fill="#90B0C0"/>
		</Svg>
    );
}

export default ArrowDown2;