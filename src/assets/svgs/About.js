import React from 'react';
import Svg, { Path } from 'react-native-svg';

import { getSizesByWidth } from './calculator';
  
const About = (props) => {
    const originalWidth = 40;
    const originalHeight = 37;
    const newWidth = props?.style?.width ? props.style.width : 45;

    const sizes = getSizesByWidth(originalWidth, originalHeight, newWidth);

    return (
        <Svg style={[{width: sizes.width, height: sizes.height}, props.style]} viewBox="0 0 40 37" fill="none" xmlns="http://www.w3.org/2000/svg">
            <Path d="M39.3321 35.3473H28.3567V5.19722C28.3567 5.00072 28.2154 4.8265 28.0077 4.7678L12.1203 0.267788C11.9707 0.225726 11.8054 0.249896 11.6769 0.334335C11.5487 0.419088 11.4734 0.55375 11.4734 0.697196V35.3473H0.498011C0.222956 35.3473 0 35.5488 0 35.7974C0 36.046 0.22261 36.2476 0.498011 36.2476H39.3318C39.6068 36.2476 39.8298 36.046 39.8298 35.7974C39.8294 35.5488 39.6068 35.3473 39.3314 35.3473L39.3321 35.3473ZM22.0658 9.915V15.8112H17.7657V9.915H22.0658ZM17.7657 9.01507V2.80964L22.0658 4.02753V9.01499L17.7657 9.01507ZM27.3612 15.8112H23.0612V9.915H27.3612V15.8112ZM16.77 15.8112H12.4699V9.915H16.77V15.8112ZM12.4699 16.7111H16.77V22.6074H12.4699V16.7111ZM17.7656 16.7111H22.0657V22.6074H17.7656V16.7111ZM23.0614 16.7111H27.3614V22.6074H23.0614V16.7111ZM27.3614 9.01507H23.0614V4.30943L27.3614 5.52732V9.01507ZM16.7702 2.5275V9.01507H12.4701V1.30953L16.7702 2.5275ZM12.4701 23.508H27.3617L27.362 35.3479H23.6222V29.2397C23.6222 28.9911 23.3995 28.7896 23.1241 28.7896L16.7084 28.7899C16.4333 28.7899 16.2104 28.9914 16.2104 29.2401V35.3479H12.4705L12.4701 23.508ZM22.6257 35.3479H17.2053V29.6899H22.6254L22.6257 35.3479Z" fill="#00467F"/>
        </Svg>

    );
}

export default About;