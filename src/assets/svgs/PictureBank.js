import React from 'react';
import Svg, { Path } from 'react-native-svg';

import { getSizesByWidth } from './calculator';
  
const PictureBank = (props) => {
    const originalWidth = 43;
    const originalHeight = 40;
    const newWidth = props?.style?.width ? props.style.width : 43;

    const sizes = getSizesByWidth(originalWidth, originalHeight, newWidth);

    return (
    <Svg style={[{width: sizes.width, height: sizes.height}, props.style]} viewBox="0 0 43 40" fill="none" xmlns="http://www.w3.org/2000/svg">
      <Path d="M40.3878 0.103149H1.98085C0.94883 0.103149 0.109253 0.895705 0.109253 1.86984V38.1301C0.109253 39.1042 0.94883 39.8968 1.98085 39.8968H40.3878C41.4196 39.8968 42.2591 39.1042 42.2591 38.1301V1.86984C42.2591 0.895705 41.4196 0.103149 40.3878 0.103149ZM40.3878 38.4995H1.98085C1.76496 38.4995 1.58935 38.3338 1.58935 38.1301V33.7477L15.4326 20.6782L28.882 33.376C29.0266 33.5124 29.2159 33.5806 29.4054 33.5806C29.5947 33.5806 29.7842 33.5124 29.9287 33.376C30.2176 33.1031 30.2176 32.6608 29.9287 32.3879L15.9559 19.1962C15.8114 19.0597 15.622 18.9915 15.4326 18.9916C15.2432 18.9915 15.0538 19.0597 14.9093 19.1962L1.58935 31.7714V1.86984C1.58935 1.66622 1.76496 1.50053 1.98085 1.50053H40.3878C40.6035 1.50053 40.779 1.66622 40.779 1.86984V31.486L27.7615 19.1962C27.7073 19.145 27.6468 19.1035 27.5824 19.0715C27.3032 18.9329 26.9497 18.9745 26.7148 19.1962L23.8076 21.9411C23.5186 22.2139 23.5186 22.6562 23.8077 22.9292C23.9522 23.0655 24.1416 23.1338 24.3309 23.1338C24.5202 23.1338 24.7097 23.0655 24.8542 22.9291L27.2382 20.6783L40.6876 33.376C40.7163 33.403 40.7472 33.4267 40.779 33.4484V38.1301C40.779 38.3338 40.6035 38.4995 40.3878 38.4995Z" fill="#2D719F"/>
      <Path d="M25.6301 12.4481C25.6301 10.2781 23.7602 8.51254 21.4617 8.51254C19.1632 8.51254 17.2932 10.2781 17.2932 12.4481C17.2932 14.6181 19.1632 16.3835 21.4617 16.3835C23.7602 16.3835 25.6301 14.6181 25.6301 12.4481ZM21.4617 14.9862C19.9793 14.9862 18.7733 13.8476 18.7733 12.4481C18.7733 11.0485 19.9793 9.90992 21.4617 9.90992C22.944 9.90992 24.15 11.0485 24.15 12.4481C24.15 13.8476 22.9441 14.9862 21.4617 14.9862Z" fill="#2D719F"/>
    </Svg>    
    );
}

export default PictureBank;