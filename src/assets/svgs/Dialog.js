import React from 'react';
import Svg, { Path } from 'react-native-svg';

import { getSizesByWidth } from './calculator';
  
const Dialog = (props) => {
    const originalWidth = 71;
    const originalHeight = 71;
    const newWidth = props?.style?.width ? props.style.width : 71;

    const sizes = getSizesByWidth(originalWidth, originalHeight, newWidth);

    return (
        <Svg width="71" height="71" viewBox="0 0 71 71" fill="none" xmlns="http://www.w3.org/2000/svg">
            <Path d="M65.276 60.3654C67.4433 57.4602 68.9306 54.1054 69.6282 50.5486C70.3257 46.9918 70.2157 43.3238 69.3061 39.8152C68.3966 36.3066 66.7109 33.047 64.3733 30.277C62.0357 27.507 59.1059 25.2972 55.8002 23.8108C55.0801 19.6681 53.4253 15.744 50.9613 12.3366C48.4974 8.92933 45.2893 6.12845 41.5807 4.14683C37.872 2.1652 33.7605 1.05498 29.5585 0.900513C25.3565 0.746044 21.1746 1.55139 17.3305 3.25537C13.4864 4.95935 10.0812 7.51712 7.37379 10.7343C4.66635 13.9516 2.72785 17.7436 1.70558 21.8222C0.683306 25.9009 0.60416 30.159 1.47416 34.2728C2.34415 38.3867 4.1404 42.2481 6.72641 45.5637L1.91932 50.3362C1.43945 50.8226 1.11438 51.4401 0.98513 52.111C0.855878 52.7819 0.928236 53.476 1.19307 54.1058C1.45252 54.7374 1.8931 55.278 2.4593 55.6596C3.0255 56.0411 3.69198 56.2466 4.37474 56.25H24.0527C26.0123 60.3813 29.1019 63.8732 32.9639 66.3213C36.8259 68.7695 41.3022 70.0738 45.8747 70.0833H66.6247C67.3075 70.0799 67.974 69.8745 68.5402 69.4929C69.1064 69.1113 69.547 68.5707 69.8064 67.9391C70.0712 67.3093 70.1436 66.6152 70.0143 65.9443C69.8851 65.2734 69.56 64.6559 69.0801 64.1696L65.276 60.3654ZM21.6664 45.875C21.6712 47.0331 21.7636 48.1892 21.9431 49.3333H12.7093L13.9197 48.1575C14.2439 47.836 14.5012 47.4535 14.6767 47.0321C14.8523 46.6106 14.9427 46.1586 14.9427 45.7021C14.9427 45.2455 14.8523 44.7935 14.6767 44.3721C14.5012 43.9506 14.2439 43.5681 13.9197 43.2466C11.9815 41.3297 10.445 39.0456 9.39988 36.5279C8.35479 34.0101 7.82213 31.3093 7.83307 28.5833C7.83307 23.0801 10.0192 17.8022 13.9106 13.9108C17.802 10.0195 23.0798 7.8333 28.5831 7.8333C32.8772 7.8075 37.0715 9.12805 40.5763 11.6093C44.0811 14.0906 46.7202 17.6079 48.1227 21.6666C47.3618 21.6666 46.6356 21.6666 45.8747 21.6666C39.4543 21.6666 33.2968 24.2172 28.7569 28.7571C24.2169 33.297 21.6664 39.4545 21.6664 45.875ZM58.1172 63.1666L58.2902 63.3396H45.8747C41.8746 63.3324 38.0007 61.9386 34.9129 59.3957C31.8252 56.8527 29.7145 53.3178 28.9407 49.3933C28.1668 45.4687 28.7775 41.3973 30.6687 37.8725C32.56 34.3477 35.6148 31.5876 39.3127 30.0624C43.0107 28.5373 47.1231 28.3414 50.9493 29.5082C54.7754 30.6749 58.0788 33.1321 60.2965 36.4612C62.5142 39.7903 63.5091 43.7853 63.1118 47.7656C62.7144 51.746 60.9493 55.4654 58.1172 58.2904C57.4656 58.9289 57.0927 59.799 57.0797 60.7112C57.0816 61.1688 57.1743 61.6214 57.3524 62.0429C57.5305 62.4644 57.7905 62.8463 58.1172 63.1666Z" fill="white"/>
        </Svg>

    );
}

export default Dialog;