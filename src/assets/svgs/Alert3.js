import React from 'react';
import Svg, { Path } from 'react-native-svg';

import { getSizesByWidth } from './calculator';
  
const Alert3 = (props) => {
    const originalWidth = 38;
    const originalHeight = 38;
    const newWidth = props?.style?.width ? props.style.width : 43;
    const color = props?.style?.color ? props.style.color : '#FFF';

    const sizes = getSizesByWidth(originalWidth, originalHeight, newWidth);

    return (
        <Svg style={[{width: sizes.width, height: sizes.height}, props.style]} viewBox="0 0 38 38" fill="none" xmlns="http://www.w3.org/2000/svg">
			<Path d="M12.445 3.16675H25.555L34.8333 12.4451V25.5551L25.555 34.8334H12.445L3.16666 25.5551V12.4451L12.445 3.16675Z" stroke={color} strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
			<Path d="M19 25.3333H19.0158" stroke={color} strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
			<Path d="M19 12.6667V19.0001" stroke={color} strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
		</Svg>

    );
}

export default Alert3;