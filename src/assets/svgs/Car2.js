import React from 'react';
import Svg, { Path } from 'react-native-svg';

import { getSizesByWidth } from './calculator';
  
const Car2 = (props) => {
    const originalWidth = 49;
    const originalHeight = 29;
    const newWidth = props?.style?.width ? props.style.width : 52;

    const sizes = getSizesByWidth(originalWidth, originalHeight, newWidth);

    return (
        <Svg style={[{width: sizes.width, height: sizes.height}, props.style]} viewBox="0 0 49 29" fill="none" xmlns="http://www.w3.org/2000/svg">
            <Path d="M29.1534 3.8225C28.4524 3.17385 27.5438 2.81738 26.5957 2.81738H11.9997C10.94 2.81738 9.92803 3.27122 9.2032 4.04971L4.15968 9.56211C4.00048 9.74051 3.95244 9.99169 4.04817 10.2186C4.1439 10.4373 4.35885 10.5832 4.59801 10.5832H34.9459C35.1928 10.5832 35.4162 10.4291 35.5035 10.194C35.5911 9.95885 35.5273 9.69125 35.352 9.52106L29.1534 3.8225ZM21.5687 9.37561H5.97638L10.0794 4.8925C10.5735 4.34947 11.2745 4.04118 11.9996 4.04118H21.5681L21.5687 9.37561ZM22.7638 9.37561V4.04159H26.5961C27.2413 4.04159 27.8707 4.28493 28.349 4.73054L33.4002 9.3756L22.7638 9.37561Z" fill="#00467F"/>
            <Path d="M9.78523 19.6799C8.13614 19.6799 6.79767 21.0418 6.79767 22.7198C6.79767 24.3899 8.13614 25.7517 9.78523 25.7517C11.4343 25.7517 12.7728 24.3899 12.7728 22.7198C12.7728 21.0339 11.4343 19.6799 9.78523 19.6799ZM9.78523 24.5441C8.79739 24.5359 7.99265 23.717 7.99265 22.7201C7.99265 21.715 8.79742 20.8962 9.78523 20.888C10.7731 20.8962 11.5778 21.715 11.5778 22.7201C11.5778 23.717 10.773 24.5276 9.78523 24.5441Z" fill="#00467F"/>
            <Path d="M37.8298 19.6799C36.1885 19.6799 34.8423 21.0418 34.8423 22.7198C34.8423 24.3899 36.1888 25.7517 37.8298 25.7596C39.4789 25.7514 40.8174 24.3895 40.8174 22.7198C40.8174 21.0339 39.4789 19.6799 37.8298 19.6799ZM37.8298 24.5441C36.842 24.5359 36.0373 23.717 36.0373 22.7201C36.0373 21.715 36.842 20.8962 37.8298 20.888C38.8177 20.8962 39.6224 21.715 39.6224 22.7201C39.6224 23.717 38.8177 24.5276 37.8298 24.5441Z" fill="#00467F"/>
            <Path d="M48.9279 14.3456C48.9199 14.2889 48.9041 14.2404 48.888 14.1837C48.8403 13.9892 48.7845 13.8026 48.7126 13.6242C48.7046 13.5917 48.6969 13.5593 48.6807 13.535C48.6727 13.519 48.665 13.5108 48.6569 13.5025C48.2505 12.5702 47.5014 11.8164 46.5375 11.4435L38.0285 8.20091C37.6301 8.04677 37.2714 7.81985 36.953 7.52799L30.3399 1.44787C29.4956 0.669709 28.404 0.247932 27.2647 0.247932L11.2264 0.247559C9.95944 0.247559 8.74051 0.782757 7.87193 1.72285L1.01197 9.2133C0.358703 9.92652 0 10.8589 0 11.84V21.098C0 22.3221 0.980141 23.3272 2.1909 23.3272H4.03925C4.34187 26.2783 6.7961 28.5967 9.78376 28.5967C12.7714 28.5967 15.2256 26.2782 15.5283 23.3272H32.0849C32.3875 26.2783 34.8495 28.5967 37.8294 28.5967C40.817 28.5967 43.2713 26.2782 43.5739 23.3272L46.8091 23.3276C48.0202 23.3276 49 22.3225 49 21.0984V15.0507C49.0004 14.8074 48.9685 14.5723 48.9285 14.3454L48.9279 14.3456ZM1.19515 15.2048H2.80471V19.0232H1.19515V15.2048ZM9.7842 27.3809C7.25844 27.3727 5.20285 25.2811 5.20285 22.7194C5.20285 20.1495 7.25844 18.0579 9.7842 18.0501C12.3019 18.0583 14.3575 20.1499 14.3655 22.7194C14.3575 25.2811 12.3019 27.3649 9.7842 27.3809ZM37.8297 27.3809C35.312 27.3727 33.2564 25.2811 33.2483 22.7194C33.2564 20.1495 35.312 18.0579 37.8297 18.0579C40.3554 18.0579 42.411 20.1495 42.411 22.7194C42.4106 25.2811 40.355 27.3649 37.8297 27.3809ZM47.8048 21.0981C47.8048 21.6494 47.3588 22.1033 46.809 22.1114H43.5742C43.2716 19.1525 40.8173 16.8419 37.8297 16.8419C34.8498 16.8419 32.3878 19.1526 32.0852 22.1114H15.5285C15.2259 19.1525 12.7717 16.8419 9.78401 16.8419C6.79636 16.8419 4.34215 19.1526 4.03951 22.1114H2.19116C1.64132 22.1032 1.19534 21.6493 1.19534 21.0981V20.239H3.40239C3.56158 20.239 3.71307 20.174 3.82458 20.0606C3.93609 19.9471 3.99991 19.793 3.99991 19.631V14.5967C3.99991 14.4347 3.93609 14.2806 3.82458 14.1671C3.71307 14.0537 3.56158 13.9887 3.40239 13.9887H1.19496V11.8405C1.19496 11.1758 1.44182 10.5354 1.88822 10.0491L8.74817 2.55863C9.38567 1.86145 10.2858 1.46436 11.2259 1.46436H27.2727C28.1171 1.46436 28.9218 1.78048 29.5436 2.34814L36.1567 8.43648C36.587 8.82575 37.0811 9.13366 37.6228 9.34453L46.1241 12.5871C46.5463 12.7491 46.9051 13.0249 47.1919 13.3652C45.8057 13.8355 44.8018 15.1489 44.8018 16.697C44.8018 18.4806 46.1164 19.9477 47.8055 20.191L47.8048 21.0981ZM47.8048 18.9664C46.7613 18.7394 46.0041 17.7907 45.9961 16.6964C45.9961 15.6264 46.7132 14.7102 47.7252 14.4508C47.7252 14.4668 47.7332 14.475 47.7332 14.4914C47.7732 14.6698 47.7971 14.8561 47.7971 15.0509L47.7967 18.9663L47.8048 18.9664Z" fill="#00467F"/>
            <Path d="M25.6799 13.1943H23.6083C23.2737 13.1943 23.0107 13.4619 23.0107 13.8023C23.0107 14.1349 23.2737 14.4103 23.6083 14.4103H25.6799C26.0067 14.4103 26.2774 14.1345 26.2774 13.8023C26.2774 13.4619 26.0064 13.1943 25.6799 13.1943Z" fill="#00467F"/>
            <Path d="M10.8209 13.1943H8.74925C8.41473 13.1943 8.15173 13.4619 8.15173 13.8023C8.15173 14.1349 8.41473 14.4021 8.74925 14.4021H10.8209C11.1477 14.4021 11.4184 14.1345 11.4184 13.8023C11.4184 13.4619 11.1474 13.1943 10.8209 13.1943Z" fill="#00467F"/>
        </Svg>
        
    );
}

export default Car2;