import React from 'react';
import Svg, { Path } from 'react-native-svg';

import { getSizesByWidth } from './calculator';
  
const Plantoes = (props) => {
    const originalWidth = 37;
    const originalHeight = 37;
    const newWidth = props?.style?.width ? props.style.width : 45;
    const color = props?.style?.color ? props.style.color : "#2D719F";

    const sizes = getSizesByWidth(originalWidth, originalHeight, newWidth);

    return (
        <Svg style={[{width: sizes.width, height: sizes.height}, props.style]} viewBox="0 0 37 37" fill="none" xmlns="http://www.w3.org/2000/svg">
            <Path d="M9.31837 24.7101H13.5269C13.7824 24.7101 13.9895 24.503 13.9895 24.2475V21.5298C13.9895 20.1145 12.8384 18.9631 11.4228 18.9631C10.0071 18.9631 8.85608 20.1145 8.85608 21.5298V24.2475C8.85608 24.503 9.06287 24.7101 9.31839 24.7101H9.31837ZM9.781 21.5298C9.781 20.6245 10.5175 19.888 11.4228 19.888C12.3281 19.888 13.0646 20.6245 13.0646 21.5298V23.7852H9.781V21.5298Z" fill={color}/>
            <Path d="M16.3961 24.7101H20.6046C20.8601 24.7101 21.0673 24.503 21.0673 24.2475V21.5298C21.0673 20.1145 19.9161 18.9631 18.5006 18.9631C17.085 18.9631 15.9338 20.1145 15.9338 21.5298V24.2475C15.9338 24.503 16.1406 24.7101 16.3961 24.7101H16.3961ZM16.8588 21.5298C16.8588 20.6245 17.5953 19.888 18.5006 19.888C19.4058 19.888 20.1423 20.6245 20.1423 21.5298V23.7852H16.8588V21.5298Z" fill={color}/>
            <Path d="M23.4733 24.7101H27.6818C27.9373 24.7101 28.1444 24.503 28.1444 24.2475V21.5298C28.1444 20.1145 26.9933 18.9631 25.5777 18.9631C24.162 18.9631 23.011 20.1145 23.011 21.5298V24.2475C23.011 24.503 23.2178 24.7101 23.4733 24.7101H23.4733ZM23.9359 21.5298C23.9359 20.6245 24.6724 19.888 25.5777 19.888C26.483 19.888 27.2195 20.6245 27.2195 21.5298V23.7852H23.9359V21.5298Z" fill={color}/>
            <Path d="M9.31837 16.9384H13.5269C13.7824 16.9384 13.9895 16.7313 13.9895 16.4758V13.7581C13.9895 12.3428 12.8381 11.1914 11.4228 11.1914C10.0071 11.1914 8.85608 12.3428 8.85608 13.7581V16.4758C8.85608 16.7313 9.06287 16.9384 9.31839 16.9384H9.31837ZM9.781 13.7581C9.781 12.8528 10.5175 12.1163 11.4228 12.1163C12.3281 12.1163 13.0646 12.8528 13.0646 13.7581V16.0135H9.781V13.7581Z" fill={color}/>
            <Path d="M16.3961 16.9384H20.6046C20.8601 16.9384 21.0673 16.7313 21.0673 16.4758V13.7581C21.0673 12.3428 19.9159 11.1914 18.5006 11.1914C17.0849 11.1914 15.9338 12.3428 15.9338 13.7581V16.4758C15.9338 16.7313 16.1406 16.9384 16.3961 16.9384H16.3961ZM16.8588 13.7581C16.8588 12.8528 17.5953 12.1163 18.5006 12.1163C19.4058 12.1163 20.1423 12.8528 20.1423 13.7581V16.0135H16.8588V13.7581Z" fill={color}/>
            <Path d="M23.4733 16.9384H27.6818C27.9373 16.9384 28.1444 16.7313 28.1444 16.4758V13.7581C28.1444 12.3428 26.993 11.1914 25.5777 11.1914C24.162 11.1914 23.011 12.3428 23.011 13.7581V16.4758C23.011 16.7313 23.2178 16.9384 23.4733 16.9384H23.4733ZM23.9359 13.7581C23.9359 12.8528 24.6724 12.1163 25.5777 12.1163C26.483 12.1163 27.2195 12.8528 27.2195 13.7581V16.0135H23.9359V13.7581Z" fill={color}/>
            <Path d="M36.538 36.0752H35.0227V31.6048C36.1476 31.3876 37.0006 30.3985 37.0006 29.2113C37.0006 27.8657 35.9056 26.7714 34.5603 26.7714C33.2147 26.7714 32.1204 27.8657 32.1204 29.2113C32.1204 30.3985 32.973 31.3877 34.0977 31.6048V36.0752H31.5687L31.569 8.70511H33.3437C33.5021 8.70511 33.6499 8.62381 33.7347 8.4896C33.8192 8.35539 33.8296 8.1873 33.7615 8.04375L30.0614 0.263576C29.9843 0.102915 29.8217 0 29.6433 0H7.35735C7.17894 0 7.01635 0.102915 6.93957 0.263898L3.23948 8.04407C3.17141 8.18763 3.18173 8.35571 3.26626 8.48992C3.3511 8.62413 3.49886 8.70543 3.65727 8.70543H5.43195L5.43163 36.0747H2.90265V31.6043C4.02727 31.3871 4.87993 30.398 4.87993 29.2108C4.87993 27.8652 3.78562 26.7709 2.43999 26.7709C1.09502 26.7709 4.97785e-05 27.8652 4.97785e-05 29.2108C4.97785e-05 30.398 0.853033 31.3872 1.97798 31.6043V36.0747H0.462634C0.207118 36.0747 0 36.2819 0 36.5374C0 36.7929 0.206796 37 0.462634 37H36.5377C36.7932 37 37.0003 36.7929 37.0003 36.5374C37 36.2819 36.7932 36.0747 36.5374 36.0747L36.538 36.0752ZM0.925452 29.2113C0.925452 28.3761 1.60521 27.6963 2.4408 27.6963C3.27603 27.6963 3.95582 28.376 3.95582 29.2113C3.95582 30.0465 3.27607 30.7263 2.4408 30.7263C1.60525 30.7263 0.925452 30.0466 0.925452 29.2113ZM4.38967 7.78019L7.64972 0.924935H29.3509L32.611 7.77986L4.38967 7.78019ZM9.99575 36.0753H6.35728V31.2281H9.99575V36.0753ZM14.5937 36.0753H10.9207V31.2281H14.5937V36.0753ZM21.4825 36.0753H15.5186V28.109H21.4825V36.0753ZM26.051 36.0753H22.4074V31.2281H26.051V36.0753ZM30.6437 36.0753H26.9759V31.2281H30.6437V36.0753ZM30.6437 30.3031H22.4073V27.6463C22.4073 27.3908 22.2005 27.1837 21.9447 27.1837H15.0562C14.8007 27.1837 14.5936 27.3908 14.5936 27.6463V30.3031H6.35718V8.70511H30.6442L30.6437 30.3031ZM33.0452 29.2113C33.0452 28.3761 33.725 27.6963 34.5602 27.6963C35.3958 27.6963 36.0756 28.376 36.0756 29.2113C36.0756 30.0465 35.3958 30.7263 34.5602 30.7263C33.725 30.7263 33.0452 30.0466 33.0452 29.2113Z" fill={color}/>
        </Svg>
    );
}

export default Plantoes;