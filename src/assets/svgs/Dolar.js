import React from 'react';
import Svg, { Path } from 'react-native-svg';

import { getSizesByWidth } from './calculator';
  
const Dolar = (props) => {
    const originalWidth = 32;
    const originalHeight = 32;
    const newWidth = props?.style?.width ? props.style.width : 41;

    const sizes = getSizesByWidth(originalWidth, originalHeight, newWidth);

    return (
        <Svg style={[{width: sizes.width, height: sizes.height}, props.style]} viewBox="0 0 32 32" fill="none" xmlns="http://www.w3.org/2000/svg">
            <Path d="M16 0C7.16343 0 0 7.16343 0 16C0 24.8366 7.16343 32 16 32C24.8366 32 32 24.8366 32 16C32 7.16343 24.8366 0 16 0ZM16 30.5806C7.94686 30.5806 1.41943 24.0526 1.41943 16C1.41943 7.94743 7.94743 1.41943 16 1.41943C24.0526 1.41943 30.5806 7.94743 30.5806 16C30.5806 24.0526 24.0531 30.5806 16 30.5806Z" fill="#2D719F"/>
            <Path d="M17.1457 14.6533C15.4171 14.0304 14.7134 13.5881 14.7134 12.9449C14.7134 12.3824 15.1355 11.7993 16.4419 11.7993C17.889 11.7993 18.8337 12.2614 19.3359 12.5027L19.9389 10.2313C19.2355 9.91007 18.3313 9.6083 17.0045 9.5683V7.79956H15.0348V9.70893C12.8841 10.1109 11.6379 11.4978 11.6379 13.2866C11.6379 15.2364 13.1051 16.241 15.2556 16.9647C16.7632 17.4672 17.4067 17.9497 17.4067 18.7132C17.4067 19.4969 16.6228 19.9592 15.457 19.9592C14.1505 19.9592 12.9648 19.5371 12.1206 19.0748L11.5174 21.4265C12.2813 21.8486 13.5875 22.2303 14.9344 22.2908V24.2002H16.9241V22.1301C19.2355 21.7484 20.4815 20.2207 20.4815 18.4318C20.4819 16.603 19.5373 15.4974 17.1458 14.6533L17.1457 14.6533Z" fill="#2D719F"/>
        </Svg>

    );
}

export default Dolar;