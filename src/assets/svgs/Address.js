import React from 'react';
import Svg, { Path } from 'react-native-svg';

import { getSizesByWidth } from './calculator';
  
const Address = (props) => {
    const originalWidth = 36;
    const originalHeight = 36;
    const newWidth = props?.style?.width ? props.style.width : 45;

    const sizes = getSizesByWidth(originalWidth, originalHeight, newWidth);

    return (
        <Svg style={[{width: sizes.width, height: sizes.height}, props.style]} viewBox="0 0 36 36" fill="none" xmlns="http://www.w3.org/2000/svg">
			<Path d="M14.1943 0C6.36203 0 0 6.35677 0 14.1832C0 18.223 3.37409 23.36 6.7376 27.702C10.1011 32.0439 13.5179 35.509 13.7868 35.7806L13.7865 35.7809C13.8982 35.9127 14.0601 35.9919 14.2328 35.9994C14.4057 36.0071 14.5737 35.9423 14.6968 35.8207C14.8198 35.6992 14.8863 35.5319 14.8808 35.3593C14.875 35.1865 14.7976 35.0239 14.6668 34.9107C14.4182 34.6594 11.0286 31.2146 7.71827 26.9413C4.40825 22.668 1.2415 17.4865 1.2415 14.1832C1.2415 7.02782 7.03282 1.24082 14.194 1.24082C21.355 1.24082 27.1466 7.02761 27.1466 14.1832C27.1466 14.7222 27.0673 15.3162 26.9173 15.9463L26.9176 15.9461C26.8397 16.2801 27.0471 16.6138 27.3811 16.6922C27.7151 16.7703 28.0493 16.5634 28.128 16.2296C28.2957 15.5248 28.3888 14.8412 28.3888 14.1832C28.3888 6.35707 22.0271 1.41996e-05 14.1946 1.41996e-05L14.1943 0ZM14.1943 6.78665C10.8773 6.78665 8.17704 9.48453 8.17704 12.7992C8.17704 16.1136 10.877 18.8128 14.1943 18.8128C17.5113 18.8128 20.2126 16.1136 20.2126 12.7992C20.2126 9.48481 17.5113 6.78665 14.1943 6.78665ZM14.1943 8.02743C16.8402 8.02743 18.9708 10.155 18.9708 12.7992C18.9708 15.443 16.8402 17.5756 14.1943 17.5756C11.5484 17.5756 9.41879 15.443 9.41879 12.7992C9.41879 10.1553 11.5481 8.02743 14.1943 8.02743ZM24.7094 17.4928L24.7092 17.4931C24.5609 17.4986 24.4192 17.5571 24.3103 17.6579L13.6717 27.6784C13.4222 27.9138 13.4109 28.3066 13.6462 28.5558C13.8818 28.8051 14.2748 28.8165 14.5243 28.5813L16.6101 26.6171V35.3636C16.6115 35.7062 16.8903 35.9829 17.2334 35.9815H32.2437H32.2435C32.5847 35.9801 32.8607 35.7042 32.8621 35.3636V26.6136L34.9529 28.5814H34.9526C35.2021 28.8165 35.5951 28.8052 35.8307 28.5559C36.066 28.3066 36.0546 27.9139 35.8052 27.6785L25.163 17.658C25.0402 17.5441 24.8767 17.4849 24.7093 17.4932L24.7094 17.4928ZM24.736 18.9637L31.6203 25.4438V34.7397L27.7057 34.7403V28.3971C27.7044 28.0545 27.4252 27.7778 27.0824 27.7792H22.3942H22.3945C22.3729 27.7781 22.3515 27.7781 22.3302 27.7792C22.0126 27.8107 21.7709 28.078 21.7711 28.3971V34.7403H17.8519V25.4479L24.736 18.9637ZM23.0129 29.0197H26.464V34.7401H23.0129V29.0197Z" fill="#00467F"/>
		</Svg>
    );
}

export default Address;