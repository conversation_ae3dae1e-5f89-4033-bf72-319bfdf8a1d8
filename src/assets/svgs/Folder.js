import React from 'react';
import Svg, { Path } from 'react-native-svg';

import { getSizesByWidth } from './calculator';
  
const Folder = (props) => {
    const originalWidth = 54;
    const originalHeight = 53;
    const newWidth = props?.style?.width ? props.style.width : 40;
    const color = props?.style?.color ? props.style.color : "#4EA1CC";

    const sizes = getSizesByWidth(originalWidth, originalHeight, newWidth);

    return (
		<Svg style={[{width: sizes.width, height: sizes.height}, props.style]} viewBox="0 0 54 53" fill="none" xmlns="http://www.w3.org/2000/svg">
			<Path d="M44.77 38.665C44.77 39.7444 44.3412 40.7796 43.5779 41.5429C42.8147 42.3062 41.7794 42.735 40.7 42.735H8.14001C7.06058 42.735 6.02536 42.3062 5.26208 41.5429C4.49881 40.7796 4.07001 39.7444 4.07001 38.665V10.175C4.07001 9.09555 4.49881 8.06033 5.26208 7.29706C6.02536 6.53378 7.06058 6.10498 8.14001 6.10498H18.315L22.385 12.21H40.7C41.7794 12.21 42.8147 12.6388 43.5779 13.4021C44.3412 14.1653 44.77 15.2005 44.77 16.28V38.665Z" stroke={color} strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
			<Path d="M49.21 42.217C49.21 43.2964 48.7812 44.3316 48.0179 45.0949C47.2547 45.8582 46.2194 46.287 45.14 46.287H12.58C11.5006 46.287 10.4654 45.8582 9.70209 45.0949C8.93881 44.3316 8.51001 43.2964 8.51001 42.217V13.727C8.51001 12.6476 8.93881 11.6123 9.70209 10.8491C10.4654 10.0858 11.5006 9.65698 12.58 9.65698H22.755L26.825 15.762H45.14C46.2194 15.762 47.2547 16.1908 48.0179 16.9541C48.7812 17.7173 49.21 18.7526 49.21 19.832V42.217Z" fill="#90B0C0" stroke={color} strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
		</Svg>
    );
}

export default Folder;