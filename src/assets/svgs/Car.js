import React from 'react';
import Svg, { Path } from 'react-native-svg';

import { getSizesByWidth } from './calculator';
  
const Map = (props) => {
    const originalWidth = 39;
    const originalHeight = 29;
    const newWidth = props?.style?.width ? props.style.width : 39;
    const color = props?.style?.color ? props.style.color : "#00467F";

    const sizes = getSizesByWidth(originalWidth, originalHeight, newWidth);

    return (
        <Svg style={[{width: sizes.width, height: sizes.height}, props.style]} viewBox="0 0 39 29" fill="none" xmlns="http://www.w3.org/2000/svg">
            <Path d="M32.625 9.81252H32.025L29.6813 3.96252C29.2627 2.92238 28.5428 2.03106 27.614 1.40297C26.6852 0.774878 25.59 0.438679 24.4688 0.437523H13.2187C11.9162 0.433795 10.6527 0.882256 9.64391 1.70636C8.63515 2.53046 7.94367 3.67912 7.6875 4.95627L6.7125 9.81252H6.375C4.88316 9.81252 3.45242 10.4052 2.39752 11.46C1.34263 12.5149 0.75 13.9457 0.75 15.4375V21.0625C0.75 21.5598 0.947544 22.0367 1.29917 22.3883C1.65081 22.74 2.12772 22.9375 2.625 22.9375H4.5C4.5 24.4294 5.09263 25.8601 6.14752 26.915C7.20242 27.9699 8.63316 28.5625 10.125 28.5625C11.6168 28.5625 13.0476 27.9699 14.1025 26.915C15.1574 25.8601 15.75 24.4294 15.75 22.9375H23.25C23.25 24.4294 23.8426 25.8601 24.8975 26.915C25.9524 27.9699 27.3832 28.5625 28.875 28.5625C30.3668 28.5625 31.7976 27.9699 32.8525 26.915C33.9074 25.8601 34.5 24.4294 34.5 22.9375H36.375C36.8723 22.9375 37.3492 22.74 37.7008 22.3883C38.0525 22.0367 38.25 21.5598 38.25 21.0625V15.4375C38.25 13.9457 37.6574 12.5149 36.6025 11.46C35.5476 10.4052 34.1168 9.81252 32.625 9.81252ZM21.375 4.18752H24.4688C24.8411 4.19089 25.204 4.30504 25.5112 4.51541C25.8184 4.72578 26.056 5.02285 26.1937 5.36877L27.975 9.81252H21.375V4.18752ZM11.3625 5.68752C11.4502 5.25763 11.6858 4.87209 12.0284 4.59801C12.371 4.32393 12.7989 4.17868 13.2375 4.18752H17.625V9.81252H10.5375L11.3625 5.68752ZM10.125 24.8125C9.75416 24.8125 9.39165 24.7026 9.08331 24.4965C8.77496 24.2905 8.53464 23.9977 8.39273 23.6551C8.25081 23.3124 8.21368 22.9354 8.28603 22.5717C8.35837 22.208 8.53695 21.8739 8.79918 21.6117C9.0614 21.3495 9.39549 21.1709 9.75921 21.0985C10.1229 21.0262 10.4999 21.0633 10.8425 21.2052C11.1851 21.3472 11.478 21.5875 11.684 21.8958C11.89 22.2042 12 22.5667 12 22.9375C12 23.4348 11.8025 23.9117 11.4508 24.2633C11.0992 24.615 10.6223 24.8125 10.125 24.8125ZM28.875 24.8125C28.5042 24.8125 28.1416 24.7026 27.8333 24.4965C27.525 24.2905 27.2846 23.9977 27.1427 23.6551C27.0008 23.3124 26.9637 22.9354 27.036 22.5717C27.1084 22.208 27.2869 21.8739 27.5492 21.6117C27.8114 21.3495 28.1455 21.1709 28.5092 21.0985C28.8729 21.0262 29.2499 21.0633 29.5925 21.2052C29.9351 21.3472 30.228 21.5875 30.434 21.8958C30.64 22.2042 30.75 22.5667 30.75 22.9375C30.75 23.4348 30.5525 23.9117 30.2008 24.2633C29.8492 24.615 29.3723 24.8125 28.875 24.8125ZM34.5 19.1875H33.0375C32.5103 18.6075 31.8677 18.144 31.1508 17.8269C30.434 17.5098 29.6588 17.3459 28.875 17.3459C28.0912 17.3459 27.316 17.5098 26.5992 17.8269C25.8823 18.144 25.2397 18.6075 24.7125 19.1875H14.2875C13.7603 18.6075 13.1177 18.144 12.4008 17.8269C11.684 17.5098 10.9088 17.3459 10.125 17.3459C9.34116 17.3459 8.56598 17.5098 7.84916 17.8269C7.13234 18.144 6.48972 18.6075 5.9625 19.1875H4.5V15.4375C4.5 14.9402 4.69754 14.4633 5.04918 14.1117C5.40081 13.7601 5.87772 13.5625 6.375 13.5625H32.625C33.1223 13.5625 33.5992 13.7601 33.9508 14.1117C34.3025 14.4633 34.5 14.9402 34.5 15.4375V19.1875Z" fill={color} />
        </Svg>
    );
}

export default Map;