import React from 'react';
import Svg, { Path } from 'react-native-svg';

import { getSizesByWidth } from './calculator';
  
const Wait = (props) => {
    const originalWidth = 65;
    const originalHeight = 89;
    const newWidth = props?.style?.width ? props.style.width : 84;

    const sizes = getSizesByWidth(originalWidth, originalHeight, newWidth);

    return (
        <Svg style={[{width: sizes.width, height: sizes.height}, props.style]} viewBox="0 0 65 89" fill="none" xmlns="http://www.w3.org/2000/svg">
            <Path d="M60.1753 80.1328H56.217V73.5342C56.2046 71.269 55.8036 69.0228 55.0312 66.8932C54.9879 66.777 54.9425 66.6693 54.8891 66.5586C54.248 64.8107 53.3624 63.1625 52.2586 61.6631L46.7174 54.2722C45.1799 52.2133 44.3469 49.7137 44.342 47.1441V43.1469C44.351 39.9998 45.6004 36.983 47.819 34.7509L50.4205 32.1494C53.4432 29.1104 55.3849 25.1633 55.9476 20.9141C55.9497 20.8591 55.9793 20.8132 55.9793 20.7576L55.9681 20.7032C56.1058 19.8604 56.1889 19.0096 56.217 18.1561V8.88279H60.1753C61.2252 8.88279 62.232 8.46575 62.9743 7.72342C63.7166 6.98109 64.1337 5.97427 64.1337 4.92446C64.1337 3.87464 63.7166 2.86783 62.9743 2.12549C62.232 1.38316 61.2252 0.966125 60.1753 0.966125H4.75867C3.70885 0.966125 2.70203 1.38316 1.9597 2.12549C1.21737 2.86783 0.800333 3.87464 0.800333 4.92446C0.800333 5.97427 1.21737 6.98109 1.9597 7.72342C2.70203 8.46575 3.70885 8.88279 4.75867 8.88279H8.717V18.1561C8.74512 19.0096 8.82824 19.8604 8.96586 20.7032L8.9547 20.7578C8.9547 20.8132 8.98415 20.8593 8.98636 20.9143C9.54907 25.1635 11.4908 29.1106 14.5134 32.1496L17.1149 34.7511C19.3335 36.9832 20.5829 39.9998 20.592 43.1469V47.144C20.5868 49.7133 19.7546 52.2126 18.2185 54.2721L12.6734 61.663C11.5699 63.163 10.6846 64.8118 10.0436 66.5601C9.99091 66.6698 9.94571 66.7764 9.90296 66.8914C9.13051 69.0215 8.72936 71.2684 8.71696 73.5342V80.1328H4.75863C3.70881 80.1328 2.70199 80.5498 1.95966 81.2922C1.21733 82.0345 0.800293 83.0413 0.800293 84.0911C0.800293 85.1409 1.21733 86.1478 1.95966 86.8901C2.70199 87.6324 3.70881 88.0495 4.75863 88.0495H60.1753C61.2251 88.0495 62.2319 87.6324 62.9743 86.8901C63.7166 86.1478 64.1336 85.1409 64.1336 84.0911C64.1336 83.0413 63.7166 82.0345 62.9743 81.2922C62.2319 80.5498 61.2251 80.1328 60.1753 80.1328ZM16.6336 16.7995V8.88279H48.3003V16.7995H16.6336ZM20.1107 26.5523C19.553 25.9919 19.0531 25.3768 18.6188 24.7161H46.3151C45.8808 25.3768 45.381 25.9919 44.8232 26.5523L42.2217 29.1538C39.1341 32.2233 37.1542 36.232 36.5934 40.5495H28.3403C27.7797 36.2318 25.7999 32.223 22.7122 29.1534L20.1107 26.5523ZM24.5522 59.019C26.8426 55.9501 28.2007 52.2865 28.4642 48.4661H36.4697C36.733 52.2868 38.0919 55.9507 40.3836 59.019L44.3424 64.2995H20.5905L24.5522 59.019ZM48.3003 80.1328H16.6336V73.5342C16.6446 73.0932 16.6806 72.6531 16.7414 72.2161H48.1926C48.2533 72.6531 48.2893 73.0932 48.3003 73.5342V80.1328Z" fill="#00467F"/>
        </Svg>
        
    );
}

export default Wait;