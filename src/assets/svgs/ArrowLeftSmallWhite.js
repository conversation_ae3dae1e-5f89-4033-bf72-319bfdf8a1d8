import React from 'react';
import Svg, { Path } from 'react-native-svg';

import { getSizesByWidth } from './calculator';
  
const ArrowLeftSmallWhite = (props) => {
    const originalWidth = 8;
    const originalHeight = 14;
    const newWidth = props?.style?.width ? props.style.width : 12;

    const sizes = getSizesByWidth(originalWidth, originalHeight, newWidth);

    return (
     	<Svg width="8" height="14" viewBox="0 0 8 14" fill="none" xmlns="http://www.w3.org/2000/svg">
			<Path d="M6.75 12.5L1.25 7L6.75 1.5" stroke="#FFF" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
		</Svg>

         
    );
}

export default ArrowLeftSmallWhite;