import React from 'react';
import Svg, { Path } from 'react-native-svg';

import { getSizesByWidth } from './calculator';
  
const Document = (props) => {
    const originalWidth = 40;
    const originalHeight = 25;
    const newWidth = props?.style?.width ? props.style.width : 45;

    const sizes = getSizesByWidth(originalWidth, originalHeight, newWidth);

    return (
		<Svg style={[{width: sizes.width, height: sizes.height}, props.style]} viewBox="0 0 40 25" fill="none" xmlns="http://www.w3.org/2000/svg">
			<Path d="M39.6514 0H0.30746C0.137651 0 0 0.137651 0 0.307461V24.6925C0 24.8623 0.137651 25 0.30746 25H39.6514C39.8212 25 39.9589 24.8623 39.9589 24.6925V0.307461C39.9589 0.137972 39.8212 0.000321609 39.6514 0.000321609V0ZM39.344 24.3851H0.61502V0.61502H39.344V24.3851Z" fill="#00467F"/>
			<Path d="M10.0687 13.9185C11.8611 13.9185 13.3192 12.1306 13.3192 9.93312C13.3192 7.73559 11.861 5.94775 10.0687 5.94775C8.27637 5.94775 6.81827 7.73559 6.81827 9.93312C6.81827 12.1306 8.27645 13.9185 10.0687 13.9185ZM10.0687 6.56228C11.5221 6.56228 12.7043 8.07447 12.7043 9.93303C12.7043 11.7916 11.5221 13.3035 10.0687 13.3035C8.61541 13.3035 7.43312 11.7913 7.43312 9.93303C7.43312 8.07414 8.61533 6.56228 10.0687 6.56228Z" fill="#00467F"/>
			<Path d="M2.94456 20.8918H17.1954C17.3652 20.8918 17.5029 20.7541 17.5029 20.5843V19.1815L17.5025 4.41439C17.5025 4.24458 17.3649 4.10693 17.1951 4.10693H2.94506C2.77526 4.10693 2.6376 4.24458 2.6376 4.41439V20.5844C2.6376 20.7542 2.77526 20.8918 2.94506 20.8918L2.94456 20.8918ZM16.8875 20.2768H3.25166V19.1814C3.25166 17.3235 4.9398 15.7466 7.09393 15.7466C12.7038 15.8434 13.409 15.5093 14.7444 16.1026C16.0462 16.6748 16.8876 17.8834 16.8876 19.1811L16.8875 20.2768ZM3.25166 4.72187H16.8875V17.1416C16.4488 16.4605 15.7969 15.8945 14.9929 15.541C13.4751 14.8666 12.6329 15.2397 7.09385 15.1326C5.56008 15.1326 4.08198 15.8491 3.25158 17.1398L3.25166 4.72187Z" fill="#00467F"/>
			<Path d="M20.7537 4.72283H36.9419C37.1117 4.72283 37.2493 4.58518 37.2493 4.41537C37.2493 4.24556 37.1117 4.10791 36.9419 4.10791H20.7537C20.5839 4.10791 20.4463 4.24556 20.4463 4.41537C20.4463 4.58518 20.5839 4.72283 20.7537 4.72283Z" fill="#00467F"/>
			<Path d="M20.7537 7.95574H36.9419C37.1117 7.95574 37.2493 7.81809 37.2493 7.64828C37.2493 7.47847 37.1117 7.34082 36.9419 7.34082L20.7537 7.34114C20.5839 7.34114 20.4463 7.47879 20.4463 7.6486C20.4463 7.81809 20.5839 7.95574 20.7537 7.95574V7.95574Z" fill="#00467F"/>
			<Path d="M20.7537 11.1901H36.9419C37.1117 11.1901 37.2493 11.0525 37.2493 10.8827C37.2493 10.7128 37.1117 10.5752 36.9419 10.5752H20.7537C20.5839 10.5752 20.4463 10.7128 20.4463 10.8827C20.4463 11.0525 20.5839 11.1901 20.7537 11.1901Z" fill="#00467F"/>
			<Path d="M20.7537 14.4245H36.9419C37.1117 14.4245 37.2493 14.2868 37.2493 14.117C37.2493 13.9472 37.1117 13.8096 36.9419 13.8096H20.7537C20.5839 13.8096 20.4463 13.9472 20.4463 14.117C20.4463 14.2868 20.5839 14.4245 20.7537 14.4245Z" fill="#00467F"/>
			<Path d="M20.7537 17.6579H36.9419C37.1117 17.6579 37.2493 17.5202 37.2493 17.3504C37.2493 17.1806 37.1117 17.043 36.9419 17.043L20.7537 17.0433C20.5839 17.0433 20.4463 17.1809 20.4463 17.3507C20.4463 17.5202 20.5839 17.6579 20.7537 17.6579Z" fill="#00467F"/>
			<Path d="M20.7537 20.8918H36.9419C37.1117 20.8918 37.2493 20.7541 37.2493 20.5843C37.2493 20.4145 37.1117 20.2769 36.9419 20.2769H20.7537C20.5839 20.2769 20.4463 20.4145 20.4463 20.5843C20.4463 20.7541 20.5839 20.8918 20.7537 20.8918Z" fill="#00467F"/>
		</Svg>

    );
}

export default Document;