import React from 'react';
import Svg, { Path } from 'react-native-svg';

import { getSizesByWidth } from './calculator';
  
const Download = (props) => {
    const originalWidth = 25;
    const originalHeight = 25;
    const newWidth = props?.style?.width ? props.style.width : 25;

    const sizes = getSizesByWidth(originalWidth, originalHeight, newWidth);

    return (
        <Svg style={[{width: sizes.width, height: sizes.height}, props.style]} viewBox="0 0 25 25" fill="none" xmlns="http://www.w3.org/2000/svg">
            <Path d="M19.7916 20.8333H5.20829C4.93203 20.8333 4.66707 20.943 4.47172 21.1383C4.27637 21.3337 4.16663 21.5987 4.16663 21.8749C4.16663 22.1512 4.27637 22.4161 4.47172 22.6115C4.66707 22.8068 4.93203 22.9166 5.20829 22.9166H19.7916C20.0679 22.9166 20.3328 22.8068 20.5282 22.6115C20.7235 22.4161 20.8333 22.1512 20.8333 21.8749C20.8333 21.5987 20.7235 21.3337 20.5282 21.1383C20.3328 20.943 20.0679 20.8333 19.7916 20.8333ZM11.7604 18.4478C11.8594 18.5427 11.9763 18.617 12.1041 18.6666C12.2288 18.7217 12.3636 18.7502 12.5 18.7502C12.6363 18.7502 12.7711 18.7217 12.8958 18.6666C13.0237 18.617 13.1405 18.5427 13.2395 18.4478L17.4062 14.2812C17.6024 14.085 17.7126 13.819 17.7126 13.5416C17.7126 13.2642 17.6024 12.9982 17.4062 12.802C17.2101 12.6059 16.944 12.4957 16.6666 12.4957C16.3892 12.4957 16.1232 12.6059 15.927 12.802L13.5416 15.1978V3.12492C13.5416 2.84865 13.4319 2.5837 13.2365 2.38835C13.0412 2.193 12.7762 2.08325 12.5 2.08325C12.2237 2.08325 11.9587 2.193 11.7634 2.38835C11.568 2.5837 11.4583 2.84865 11.4583 3.12492V15.1978L9.07288 12.802C8.97575 12.7049 8.86045 12.6278 8.73355 12.5753C8.60665 12.5227 8.47065 12.4957 8.33329 12.4957C8.19594 12.4957 8.05993 12.5227 7.93303 12.5753C7.80613 12.6278 7.69083 12.7049 7.59371 12.802C7.49659 12.8991 7.41954 13.0144 7.36698 13.1413C7.31442 13.2682 7.28736 13.4042 7.28736 13.5416C7.28736 13.6789 7.31442 13.8149 7.36698 13.9418C7.41954 14.0687 7.49659 14.184 7.59371 14.2812L11.7604 18.4478Z" fill="white"/>
        </Svg>
    );
}

export default Download;