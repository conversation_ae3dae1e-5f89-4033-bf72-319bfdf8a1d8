import React from 'react';
import Svg, { Path } from 'react-native-svg';

import { getSizesByWidth } from './calculator';
  
const Smile = (props) => {
    const originalWidth = 24;
    const originalHeight = 24;
    const newWidth = props?.style?.width ? props.style.width : 24;

    const sizes = getSizesByWidth(originalWidth, originalHeight, newWidth);

    return ( 
        <Svg style={[{width: sizes.width, height: sizes.height}, props.style]} viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
            <Path d="M12 22C17.5228 22 22 17.5228 22 12C22 6.47715 17.5228 2 12 2C6.47715 2 2 6.47715 2 12C2 17.5228 6.47715 22 12 22Z" stroke="#00467F" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
            <Path d="M8 14C8 14 9.5 16 12 16C14.5 16 16 14 16 14" stroke="#00467F" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
            <Path d="M15 9H15.01" stroke="#00467F" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
            <Path d="M9 9H9.01" stroke="#00467F" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
        </Svg>
            
    );
}

export default Smile;