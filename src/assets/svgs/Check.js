import React from 'react';
import Svg, { Path } from 'react-native-svg';

import { getSizesByWidth } from './calculator';
  
const Checkin = (props) => {
    const originalWidth = 71;
    const originalHeight = 71;
    const newWidth = props?.style?.width ? props.style.width : 71;

    const sizes = getSizesByWidth(originalWidth, originalHeight, newWidth);

    return (
        <Svg width="71" height="71" viewBox="0 0 71 71" fill="none" xmlns="http://www.w3.org/2000/svg">
            <Path d="M46.8781 24.6062L30.5202 40.9987L24.1222 34.5662C23.471 33.915 22.5878 33.5491 21.6668 33.5491C20.7459 33.5491 19.8626 33.915 19.2114 34.5662C18.5602 35.2174 18.1943 36.1007 18.1943 37.0216C18.1943 37.4776 18.2842 37.9292 18.4587 38.3505C18.6332 38.7718 18.8889 39.1546 19.2114 39.477L28.0647 48.3304C28.3862 48.6545 28.7687 48.9118 29.1902 49.0874C29.6116 49.263 30.0636 49.3533 30.5202 49.3533C30.9767 49.3533 31.4287 49.263 31.8501 49.0874C32.2716 48.9118 32.6541 48.6545 32.9756 48.3304L51.7889 29.517C52.1114 29.1946 52.3671 28.8118 52.5416 28.3905C52.7162 27.9692 52.806 27.5176 52.806 27.0616C52.806 26.6056 52.7162 26.1541 52.5416 25.7328C52.3671 25.3115 52.1114 24.9287 51.7889 24.6062C51.4664 24.2838 51.0836 24.028 50.6623 23.8535C50.241 23.679 49.7895 23.5891 49.3335 23.5891C48.8775 23.5891 48.4259 23.679 48.0046 23.8535C47.5833 24.028 47.2005 24.2838 46.8781 24.6062ZM35.5002 0.916626C30.9586 0.916626 26.4615 1.81115 22.2657 3.54913C18.0698 5.2871 14.2574 7.83449 11.046 11.0458C4.5604 17.5315 0.916814 26.3279 0.916814 35.5C0.88658 43.4857 3.65164 51.2303 8.73265 57.3912L1.81598 64.3079C1.33611 64.7942 1.01104 65.4118 0.881788 66.0826C0.752536 66.7535 0.824893 67.4477 1.08973 68.0775C1.37697 68.6997 1.84262 69.2226 2.42758 69.5797C3.01253 69.9369 3.6904 70.1121 4.37515 70.0833H35.5002C44.6722 70.0833 53.4686 66.4397 59.9543 59.9541C66.4399 53.4684 70.0835 44.672 70.0835 35.5C70.0835 26.3279 66.4399 17.5315 59.9543 11.0458C53.4686 4.56022 44.6722 0.916626 35.5002 0.916626ZM35.5002 63.1666H12.7097L15.926 59.9504C16.5701 59.3024 16.9316 58.4259 16.9316 57.5123C16.9316 56.5986 16.5701 55.7221 15.926 55.0741C11.3976 50.5508 8.57763 44.5972 7.94654 38.2279C7.31545 31.8585 8.91228 25.4674 12.465 20.1433C16.0177 14.8193 21.3064 10.8917 27.4302 9.02978C33.5539 7.16787 40.1338 7.48678 46.0488 9.9322C51.9638 12.3776 56.8479 16.7982 59.8691 22.4409C62.8902 28.0836 63.8615 34.5992 62.6174 40.8777C61.3733 47.1562 57.9908 52.8091 53.0461 56.8733C48.1015 60.9375 41.9007 63.1616 35.5002 63.1666Z" fill="white"/>
        </Svg>

    );
}

export default Checkin;