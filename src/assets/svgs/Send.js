import React from 'react';
import Svg, { Path } from 'react-native-svg';

import { getSizesByWidth } from './calculator';
  
const Send = (props) => {
    const originalWidth = 60;
    const originalHeight = 44;
    const newWidth = props?.style?.width ? props.style.width : 40;
    const color = props?.style?.color ? props.style.color : '#FFF';

    const sizes = getSizesByWidth(originalWidth, originalHeight, newWidth);

    return (
        <Svg style={[{width: sizes.width, height: sizes.height}, props.style]} viewBox="0 0 60 44" fill="none" xmlns="http://www.w3.org/2000/svg">
            <Path d="M59.53 0.750025C59 0.110025 58.13 -0.149975 57.33 0.100025L1.45 17.17C0.589998 17.44 0.0199975 18.18 -2.47138e-06 19.06C-0.0200025 19.94 0.509997 20.71 1.36 21.01L10.15 24.14L13.6 42.35C13.78 43.29 14.55 43.95 15.52 44C15.56 44 15.6 44 15.63 44C16.1 44 16.53 43.85 16.88 43.58L31.3 32.15L40.69 35.45C41.66 35.79 42.74 35.37 43.21 34.47L59.77 2.95002C60.15 2.23002 60.06 1.38002 59.53 0.750025ZM15.57 41.97L12.14 23.9L43.76 11.05L20.53 27.17V27.18C20.16 27.44 19.88 27.82 19.75 28.25L15.68 41.99L15.67 42C15.64 42 15.59 42 15.57 41.97ZM18.42 39.81L21.75 28.8L29.07 31.37L18.42 39.81ZM57.99 2.03002L41.35 33.57L23.74 27.38L44.88 12.72C45.76 12.13 46.04 11.03 45.55 10.1C45.04 9.15003 43.94 8.76002 42.94 9.17002L10.85 22.27C10.83 22.26 10.81 22.25 10.78 22.24L2.02 19.09L58 2.02003C58 2.02003 58 2.02002 57.99 2.03002Z" fill={color} />
        </Svg>

    );
}

export default Send;