import React from 'react';
import Svg, { Path } from 'react-native-svg';

import { getSizesByWidth } from './calculator';
  
const CreditoCury = (props) => {
    const originalWidth = 44;
    const originalHeight = 44;
    const newWidth = props?.style?.width ? props.style.width : originalWidth;

    const sizes = getSizesByWidth(originalWidth, originalHeight, newWidth);

    return (
        <Svg style={[{width: sizes.width, height: sizes.height}, props.style]} viewBox="0 0 44 44" fill="none" xmlns="http://www.w3.org/2000/svg">
            <Path fillRule="evenodd" clipRule="evenodd" d="M30.4266 24.8703C21.4971 24.8703 16.9318 32.9889 23.2052 37.7935C26.2548 40.1294 30.8122 40.6876 34.6073 39.3951C34.7796 39.337 34.9613 39.3576 35.1112 39.44L38.5204 41.1615L37.4359 38.539C37.3253 38.2711 37.4153 37.9714 37.6344 37.8047C43.902 33.0205 39.3899 24.8703 30.4266 24.8703ZM25.2226 31.7321C24.3965 31.7321 24.3965 30.477 25.2226 30.477H35.6301C36.4563 30.477 36.4563 31.7321 35.6301 31.7321H25.2226ZM25.2226 35.4972C24.3965 35.4972 24.3965 34.2422 25.2226 34.2422H30.5221C31.3482 34.2422 31.3482 35.4972 30.5221 35.4972H25.2226ZM19.0768 33.0939C17.3235 32.8822 15.6189 32.5057 13.9649 31.9587L4.5951 36.6886C4.08185 36.9471 3.50117 36.4282 3.74281 35.87L6.91604 28.1954C-1.12925 21.7422 -1.21749 11.3213 7.27195 4.82156C15.6697 -1.60719 29.2278 -1.60719 37.6256 4.82156C45.4517 10.8139 46.1317 20.3693 39.3564 26.9798C42.9137 30.4808 42.4903 35.3775 38.772 38.4965L40.3117 42.2204C40.5534 42.7787 39.9727 43.2975 39.4594 43.039L34.7595 40.6657C34.1264 40.868 33.4614 41.0291 32.7721 41.1415C26.6708 42.1324 19.5942 38.9495 19.0768 33.0939ZM38.4081 26.1573C31.9866 21.019 19.7865 24.0612 19.0782 31.8332C17.3736 31.6159 15.7252 31.2356 14.1179 30.6887C13.9455 30.6306 13.7638 30.6512 13.614 30.7336L5.53462 34.8135L8.25454 28.2346C8.36506 27.9668 8.27514 27.667 8.05598 27.5004C0.334382 21.6092 0.0313098 11.945 8.03163 5.81975C15.9776 -0.264684 28.92 -0.264684 36.8661 5.81975C44.1062 11.3609 44.7128 20.0823 38.4077 26.1572L38.4081 26.1573Z" fill="#2D719F"/>
            <Path fillRule="evenodd" clipRule="evenodd" d="M19.9821 10.6387C25.4816 10.6387 28.7343 16.8087 25.7022 21.359H27.526C27.6577 21.359 27.7762 21.3048 27.8639 21.2182L27.8654 21.2197L28.0043 20.8808L28.0029 11.0948L19.8108 6.35214L11.6187 11.0948V20.8793C11.6187 21.1478 11.8381 21.359 12.097 21.359H14.2616C11.2296 16.8086 14.4828 10.6387 19.9821 10.6387ZM24.908 22.3418C22.1715 25.1261 17.7924 25.1261 15.0561 22.3418H12.0973C11.2943 22.3418 10.6391 21.6817 10.6391 20.8793V11.6641L9.27596 12.4533C8.94981 12.6425 8.54467 12.4005 8.54467 12.0293H8.54175V9.58397C8.54175 9.38886 8.65437 9.22163 8.81818 9.14095L19.5668 2.91684C19.7262 2.82442 19.9135 2.83175 20.0612 2.91977L30.8345 9.15853C30.991 9.24948 31.0788 9.41377 31.0788 9.58247H31.0803V12.0278C31.0803 12.3872 30.6853 12.6733 30.3065 12.4283L28.9844 11.6626V20.8793C28.9844 21.2798 28.8206 21.6451 28.5558 21.912L28.5573 21.9135C28.2926 22.1775 27.9284 22.3433 27.5262 22.3433L24.908 22.3418ZM24.1504 13.3538C26.3823 15.5923 26.4511 19.22 24.3449 21.5085C20.6285 25.5455 14.0875 22.8317 14.0875 17.5331C14.0875 12.2831 20.447 9.63936 24.1504 13.3538ZM20.472 13.2408V13.9449C21.604 14.0887 22.7931 14.8075 22.7931 15.9693H21.8132C21.8132 15.3972 21.0409 15.0334 20.472 14.9366V17.1179C21.6889 17.3277 22.7931 17.6519 22.7931 19.0983C22.7931 20.2616 21.6026 20.9789 20.472 21.1241V21.8267H19.492V21.1241C18.36 20.9803 17.1709 20.2615 17.1709 19.0983H18.1508C18.1508 19.6703 18.9231 20.0342 19.492 20.131V17.9496C18.2751 17.7399 17.1709 17.4157 17.1709 15.9693C17.1709 14.806 18.3614 14.0887 19.492 13.9449V13.2408H20.472ZM20.472 18.1141C22.6176 18.5307 21.9082 19.8877 20.472 20.1297V18.1141ZM19.492 16.9523C17.3464 16.5342 18.0558 15.1788 19.492 14.9367V16.9523ZM9.52443 11.1785V9.86408L19.811 3.90698L30.0991 9.86408V11.1785L20.0555 5.36217L19.5641 5.36511L9.52443 11.1785Z" fill="#2D719F"/>
        </Svg>        
    );
}

export default CreditoCury;