import React from 'react';
import Svg, { Path } from 'react-native-svg';

import { getSizesByWidth } from './calculator';
  
const FilledStar = (props) => {
    const originalWidth = 17;
    const originalHeight = 16;
    const newWidth = props?.style?.width ? props.style.width : 17;
    const color = props?.style?.color ? props.style.color : "#FF6542";

    const sizes = getSizesByWidth(originalWidth, originalHeight, newWidth);

    return (
        <Svg style={[{width: sizes.width, height: sizes.height}, props.style]} viewBox="0 0 17 16" fill="none" xmlns="http://www.w3.org/2000/svg">
            <Path d="M16.8047 5.79829C16.7547 5.65349 16.6637 5.5263 16.5429 5.43215C16.422 5.338 16.2765 5.28094 16.1238 5.26788L11.6192 4.61079L9.60049 0.517876C9.53566 0.384028 9.43445 0.271147 9.30843 0.192163C9.18242 0.11318 9.03671 0.0712891 8.88799 0.0712891C8.73927 0.0712891 8.59356 0.11318 8.46754 0.192163C8.34153 0.271147 8.24031 0.384028 8.17549 0.517876L6.15674 4.60288L1.65215 5.26788C1.50563 5.2887 1.36789 5.35018 1.25454 5.44533C1.14119 5.54049 1.05679 5.66551 1.0109 5.80621C0.968902 5.9437 0.965133 6.09004 1 6.22951C1.03487 6.36898 1.10706 6.49633 1.20882 6.59788L4.4784 9.76454L3.68674 14.2612C3.65848 14.4096 3.67327 14.563 3.72939 14.7033C3.7855 14.8436 3.88058 14.9649 4.0034 15.0529C4.12312 15.1385 4.26432 15.189 4.41115 15.1988C4.55798 15.2086 4.70464 15.1772 4.83466 15.1083L8.88799 12.9945L12.9255 15.1162C13.0366 15.1789 13.1621 15.2116 13.2897 15.2112C13.4574 15.2118 13.6209 15.1591 13.7567 15.0608C13.8796 14.9728 13.9746 14.8515 14.0308 14.7112C14.0869 14.571 14.1017 14.4175 14.0734 14.2691L13.2817 9.77246L16.5513 6.60579C16.6656 6.50896 16.7501 6.38167 16.7949 6.23874C16.8398 6.0958 16.8432 5.94307 16.8047 5.79829Z" fill={color}/>
        </Svg>

    );
}

export default FilledStar;