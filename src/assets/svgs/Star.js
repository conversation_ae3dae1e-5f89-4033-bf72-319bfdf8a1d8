import React from 'react';
import Svg, { Path } from 'react-native-svg';

import { getSizesByWidth } from './calculator';
  
const Star = (props) => {
    const originalWidth = 29;
    const originalHeight = 28;
    const newWidth = props?.style?.width ? props.style.width : 17;
    const color = props?.style?.color ? props.style.color : "#979797";

    const sizes = getSizesByWidth(originalWidth, originalHeight, newWidth);

    return (
        <Svg style={[{width: sizes.width, height: sizes.height}, props.style]} viewBox="0 0 29 28" fill="none" xmlns="http://www.w3.org/2000/svg">
            <Path d="M14.3333 1L18.4533 9.34667L27.6667 10.6933L21 17.1867L22.5733 26.36L14.3333 22.0267L6.09333 26.36L7.66667 17.1867L1 10.6933L10.2133 9.34667L14.3333 1Z" stroke={color} strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
        </Svg>
    );
}

export default Star;