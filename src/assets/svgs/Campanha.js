import React from 'react';
import Svg, { Path } from 'react-native-svg';

import { getSizesByWidth } from './calculator';
  
const Campanha = (props) => {
    const originalWidth = 29;
    const originalHeight = 40;
    const newWidth = props?.style?.width ? props.style.width : 37;

    const sizes = getSizesByWidth(originalWidth, originalHeight, newWidth);

    return (
        <Svg style={[{width: sizes.width, height: sizes.height}, props.style]} viewBox="0 0 29 40" fill="none" xmlns="http://www.w3.org/2000/svg">
            <Path d="M21.9155 39.5814C21.4244 39.5814 21.0261 39.1834 21.0261 38.692V29.2453C21.0255 28.9553 21.1666 28.6829 21.404 28.516C24.1299 26.4217 25.5815 23.0681 25.2427 19.6475C24.9042 16.2267 22.8236 13.2228 19.7399 11.7034C16.6563 10.1839 13.0069 10.3642 10.0876 12.1796C7.16836 13.9953 5.39362 17.1891 5.3933 20.6264C5.39175 20.7831 5.34863 20.9366 5.26859 21.0713L2.19554 26.297L4.15246 27.0173C4.50302 27.1442 4.73755 27.4761 4.73941 27.8491V32.5498C4.73941 32.7928 4.8362 33.0257 5.00775 33.1976C5.17962 33.3695 5.41259 33.4659 5.65581 33.4659H11.077C11.3131 33.4659 11.5392 33.5596 11.7061 33.7265C11.8727 33.8934 11.9664 34.1196 11.9664 34.3553V38.6917C11.9664 39.1831 11.5684 39.5811 11.077 39.5811C10.5859 39.5811 10.1876 39.1831 10.1876 38.6917V35.2451H5.6559C4.94146 35.2439 4.25649 34.9594 3.75148 34.4544C3.24612 33.949 2.96196 33.2644 2.96072 32.5499V28.4717L0.581303 27.5823C0.333744 27.4902 0.139854 27.2932 0.0520584 27.0441C-0.0369763 26.7953 -0.0109161 26.5201 0.123411 26.2924L3.61467 20.355L3.61436 20.3553C3.70556 16.3618 5.82255 12.6891 9.23249 10.6085C12.6422 8.52781 16.8764 8.3249 20.47 10.0702C24.063 11.8158 26.5212 15.2693 26.9938 19.2358C27.4662 23.2026 25.8878 27.1368 22.8051 29.6775V38.6922C22.8051 38.928 22.7114 39.1544 22.5445 39.321C22.378 39.4879 22.1515 39.5816 21.9157 39.5816L21.9155 39.5814Z" fill="#62829A"/>
            <Path d="M15.3422 6.22615C14.8511 6.22615 14.4528 5.82813 14.4528 5.33676V0.889393C14.4528 0.397999 14.8511 0 15.3422 0C15.8335 0 16.2319 0.398015 16.2319 0.889393V5.33676C16.2319 5.57253 16.1382 5.79899 15.9713 5.96558C15.8044 6.13249 15.5782 6.22617 15.3421 6.22617L15.3422 6.22615Z" fill="#62829A"/>
            <Path d="M20.5726 7.14662C20.469 7.14879 20.3663 7.13048 20.2701 7.09326C19.8085 6.92604 19.5696 6.41635 19.7362 5.95473L21.2572 1.77413C21.4257 1.31221 21.9363 1.07427 22.3979 1.24272C22.8598 1.41086 23.0977 1.92149 22.9296 2.38339L21.4086 6.56399C21.2799 6.91424 20.9457 7.1469 20.5726 7.14659V7.14662Z" fill="#62829A"/>
            <Path d="M25.1716 9.80148C24.9616 9.80179 24.7584 9.72765 24.598 9.5927C24.2248 9.27627 24.1771 8.71784 24.4913 8.34283L27.3466 4.93623H27.3463C27.6621 4.559 28.2236 4.50936 28.6005 4.82486C28.9774 5.14067 29.0274 5.70219 28.7116 6.0791L25.8519 9.4857C25.6829 9.68641 25.4337 9.80182 25.1716 9.80151L25.1716 9.80148Z" fill="#62829A"/>
            <Path d="M5.53175 9.8015C5.26806 9.80305 5.01709 9.68734 4.84678 9.48569L1.97378 6.07909C1.80967 5.9004 1.7256 5.66215 1.74142 5.41987C1.75693 5.17758 1.87109 4.95236 2.05692 4.79631C2.24274 4.64027 2.48441 4.56675 2.72577 4.59312C2.96712 4.61949 3.18707 4.74357 3.33474 4.93622L6.19439 8.34282C6.50741 8.71913 6.45777 9.27756 6.08333 9.59269C5.92853 9.72298 5.73403 9.79651 5.53176 9.80147L5.53175 9.8015Z" fill="#62829A"/>
            <Path d="M10.1173 7.14658C9.74381 7.14689 9.41001 6.91422 9.28097 6.56398L7.75561 2.38338C7.6219 1.93293 7.8617 1.45579 8.30315 1.29481C8.7446 1.1335 9.23535 1.34351 9.42333 1.77411L10.9487 5.95471C11.1165 6.41384 10.882 6.92233 10.4241 7.09323C10.3261 7.13046 10.2218 7.14845 10.1173 7.14659L10.1173 7.14658Z" fill="#62829A"/>
            <Path d="M17.5658 23.5708H13.1185C12.6274 23.5708 12.2291 23.1725 12.2291 22.6811V21.1247C11.1697 20.2555 10.5232 18.982 10.4471 17.6136C10.3711 16.2452 10.8724 14.9078 11.8291 13.9266C12.7859 12.9454 14.1102 12.4105 15.4802 12.4524C16.8179 12.4872 18.0829 13.0688 18.9807 14.0612C19.8782 15.054 20.3302 16.3709 20.2309 17.7055C20.1316 19.0401 19.4901 20.2756 18.4555 21.1248V22.6812C18.4555 22.9173 18.3619 23.1434 18.195 23.3103C18.0281 23.4772 17.8019 23.5708 17.5658 23.5708ZM14.0079 21.792H16.6764V20.9023C16.6777 20.4817 16.8772 20.0862 17.2147 19.8349C18.0067 19.2442 18.468 18.3101 18.4556 17.3224C18.4339 16.521 18.1103 15.7576 17.5494 15.1852C16.9886 14.6129 16.2319 14.2735 15.4312 14.2356C14.5452 14.2102 13.6902 14.5639 13.0806 15.2079C12.4713 15.8519 12.1655 16.7251 12.2399 17.6087C12.314 18.4919 12.7617 19.3018 13.4699 19.8348C13.8074 20.0861 14.0069 20.4816 14.0082 20.9022L14.0079 21.792Z" fill="#62829A"/>
            <Path d="M16.8995 26.2393H13.7865C13.2951 26.2393 12.8971 25.8412 12.8971 25.3499C12.8971 24.8585 13.2951 24.4605 13.7865 24.4605H16.8995C17.3909 24.4605 17.7889 24.8585 17.7889 25.3499C17.7889 25.8412 17.3909 26.2393 16.8995 26.2393Z" fill="#62829A"/>
        </Svg>

    );
}

export default Campanha;