import React from 'react';
import Svg, { Path } from 'react-native-svg';

import { getSizesByWidth } from './calculator';
  
const ArrowRight = (props) => {
    const originalWidth = 8;
    const originalHeight = 14;
    const newWidth = props?.style?.width ? props.style.width : 50;
    const color = props?.style?.color ? props.style.color : 'white';

    const sizes = getSizesByWidth(originalWidth, originalHeight, newWidth);

    return (
      <Svg style={[{width: sizes.width, height: sizes.height}, props.style]} viewBox="0 0 8 14" fill="none" xmlns="http://www.w3.org/2000/svg">
        <Path d="M1 0.999999L7 7L1 13" stroke={color} strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
      </Svg>
    );
}

export default ArrowRight;