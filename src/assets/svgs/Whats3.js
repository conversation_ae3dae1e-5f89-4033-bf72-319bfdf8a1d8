import React from 'react';
import Svg, { Path } from 'react-native-svg';

import { getSizesByWidth } from './calculator';
  
const Whats3 = (props) => {
    const originalWidth = 33;
    const originalHeight = 33;
    const newWidth = props?.style?.width ? props.style.width : 43;
    const color = props?.style?.color ? props.style.color : "#1B9C20";

    const sizes = getSizesByWidth(originalWidth, originalHeight, newWidth);

    return (
        <Svg style={[{width: sizes.width, height: sizes.height}, props.style]} viewBox="0 0 33 33" fill="none" xmlns="http://www.w3.org/2000/svg">
			<Path d="M16.5 0.399902C7.6167 0.399902 0.400024 7.61658 0.400024 16.4999C0.400024 19.3553 1.18311 22.02 2.48909 24.3462L0.426001 31.7112C0.393041 31.8291 0.391552 31.9535 0.421682 32.0721C0.451812 32.1907 0.512508 32.2994 0.597718 32.3872C0.682929 32.4751 0.789677 32.539 0.907318 32.5727C1.02496 32.6065 1.14939 32.6088 1.26819 32.5794L8.94768 30.6763C11.2043 31.8809 13.7648 32.5999 16.5 32.5999C25.3833 32.5999 32.6 25.3832 32.6 16.4999C32.6 7.61658 25.3833 0.399902 16.5 0.399902ZM16.5 1.7999C24.6267 1.7999 31.2 8.37323 31.2 16.4999C31.2 24.6266 24.6267 31.1999 16.5 31.1999C13.8946 31.1999 11.4545 30.5206 9.33323 29.3337C9.17808 29.247 8.99577 29.2226 8.82327 29.2653L2.09807 30.9319L3.90139 24.4979C3.9272 24.407 3.93432 24.3118 3.92234 24.2181C3.91035 24.1243 3.87951 24.034 3.83166 23.9524C2.54251 21.7664 1.80002 19.223 1.80002 16.4999C1.80002 8.37323 8.37335 1.7999 16.5 1.7999ZM10.6498 8.0999C10.2011 8.0999 9.56026 8.26684 9.03381 8.83408C8.71761 9.17478 7.40002 10.4586 7.40002 12.7155C7.40002 15.0686 9.032 17.099 9.22932 17.3599H9.23069V17.3612C9.21197 17.3366 9.48114 17.7264 9.84045 18.1938C10.1998 18.6613 10.7022 19.2751 11.3334 19.9397C12.5959 21.2689 14.3708 22.8051 16.5739 23.7446C17.5884 24.1766 18.3887 24.4375 18.9951 24.6278C20.1189 24.9807 21.1425 24.9271 21.9114 24.8138C22.4869 24.729 23.1198 24.4527 23.7461 24.0563C24.3724 23.66 24.9862 23.1672 25.2569 22.4185C25.4508 21.8817 25.5493 21.3856 25.585 20.9774C25.6028 20.7733 25.6051 20.5932 25.5918 20.4265C25.5785 20.2597 25.5927 20.132 25.4373 19.8769C25.1114 19.3418 24.7423 19.3278 24.3572 19.1372C24.1433 19.0313 23.5341 18.7338 22.9231 18.4427C22.3127 18.1518 21.7843 17.8943 21.4588 17.7782C21.2532 17.7042 21.0021 17.5977 20.6399 17.6388C20.2776 17.6798 19.9198 17.9412 19.7115 18.2499C19.5141 18.5425 18.7193 19.4807 18.477 19.7565C18.4738 19.7546 18.4948 19.7643 18.399 19.7169C18.0994 19.5686 17.7328 19.4425 17.1905 19.1563C16.6481 18.8702 15.9696 18.4477 15.2272 17.7933V17.7919C14.1221 16.8191 13.3481 15.5977 13.1039 15.1874C13.1204 15.1678 13.102 15.1915 13.1367 15.1573L13.1381 15.156C13.3877 14.9101 13.6088 14.6166 13.7957 14.4013C14.0607 14.096 14.1776 13.8269 14.3043 13.5755C14.5568 13.0744 14.4162 12.523 14.2701 12.2329V12.2315C14.2802 12.2516 14.1911 12.055 14.0951 11.8282C13.9989 11.6008 13.8762 11.3059 13.7451 10.9915C13.483 10.3627 13.1904 9.65734 13.0164 9.24424V9.24287C12.8114 8.75628 12.5341 8.40574 12.1715 8.23662C11.8089 8.0675 11.4886 8.11558 11.4756 8.11494H11.4742C11.2155 8.103 10.9316 8.0999 10.6498 8.0999ZM10.6498 9.4999C10.9197 9.4999 11.1858 9.50319 11.4086 9.51357C11.6379 9.525 11.6236 9.52594 11.5795 9.50537C11.5347 9.48448 11.5955 9.4777 11.7258 9.78701C11.8962 10.1915 12.1902 10.8994 12.4531 11.5302C12.5846 11.8456 12.7076 12.1416 12.8059 12.3737C12.9041 12.6059 12.9574 12.7355 13.0192 12.8591V12.8604L13.0205 12.8618C13.0811 12.9813 13.0758 12.9048 13.0547 12.9466C12.907 13.2398 12.8869 13.3117 12.7375 13.4839C12.51 13.746 12.2779 14.0383 12.1551 14.1593C12.0476 14.2648 11.8537 14.4293 11.7326 14.7513C11.6114 15.0737 11.668 15.5161 11.8625 15.8464C12.1215 16.2861 12.9751 17.6754 14.3016 18.8433C15.137 19.5797 15.9152 20.0671 16.5369 20.395C17.1587 20.723 17.665 20.9145 17.7783 20.9706C18.0474 21.1038 18.3415 21.2072 18.6834 21.1661C19.0254 21.1251 19.3202 20.9176 19.5078 20.7054L19.5092 20.704C19.759 20.4208 20.5011 19.5734 20.8572 19.0511C20.8723 19.0564 20.8674 19.0523 20.9858 19.0948V19.0962H20.9871C21.0412 19.1154 21.718 19.4184 22.3215 19.706C22.925 19.9935 23.5375 20.2924 23.7365 20.3909C24.0238 20.5331 24.1595 20.6256 24.1946 20.6261C24.1969 20.6877 24.1993 20.7544 24.1905 20.8558C24.1659 21.1369 24.0908 21.5261 23.9403 21.9427C23.8665 22.1466 23.4825 22.5673 22.9983 22.8737C22.514 23.1802 21.9246 23.3967 21.7063 23.4288C21.0499 23.5255 20.2707 23.5608 19.4149 23.2921C18.8215 23.1058 18.0815 22.8647 17.1235 22.4567C15.1804 21.6281 13.5289 20.218 12.3492 18.9759C11.7594 18.3548 11.2868 17.7767 10.9506 17.3394C10.615 16.9028 10.4685 16.6756 10.3463 16.5149L10.3449 16.5136C10.1281 16.2268 8.80002 14.3795 8.80002 12.7155C8.80002 10.9545 9.61797 10.2639 10.0606 9.78701C10.2929 9.53665 10.547 9.4999 10.6498 9.4999Z" fill={color} />
		</Svg>

    );
}

export default Whats3;