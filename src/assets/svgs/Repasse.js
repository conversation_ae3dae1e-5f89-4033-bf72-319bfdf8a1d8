import React from 'react';
import Svg, { Path } from 'react-native-svg';

import { getSizesByWidth } from './calculator';
  
const Repasse = (props) => {
    const originalWidth = 38;
    const originalHeight = 33;
    const newWidth = props?.style?.width ? props.style.width : 41;

    const sizes = getSizesByWidth(originalWidth, originalHeight, newWidth);

    return (
        <Svg style={[{width: sizes.width, height: sizes.height}, props.style]} viewBox="0 0 38 33" fill="none" xmlns="http://www.w3.org/2000/svg">
			<Path d="M17.651 29.0599H2.30973C1.03907 29.0599 0.00012207 28.1075 0.00012207 26.9426V8.19876C0.00012207 7.03394 1.03907 6.08154 2.30973 6.08154H32.9862C34.2569 6.08154 35.2958 7.03394 35.2958 8.19876V17.571C35.2958 17.865 35.0406 18.099 34.7198 18.099C34.3991 18.099 34.1439 17.865 34.1439 17.571L34.1436 8.19876C34.1436 7.61102 33.6212 7.13751 32.986 7.13751H2.30949C1.66835 7.13751 1.1518 7.6164 1.1518 8.19876V26.9426C1.1518 27.5304 1.67421 28.0039 2.30949 28.0039H17.6508C17.9715 28.0039 18.2267 28.2379 18.2267 28.5319C18.2265 28.8256 17.9651 29.0596 17.6505 29.0596L17.651 29.0599Z" fill="#2D719F"/>
			<Path d="M32.4223 7.13736C32.1016 7.13736 31.8464 6.90338 31.8464 6.60939L31.8466 3.74677C31.8466 3.58884 31.704 3.46371 31.5378 3.46371H14.3028C13.8517 3.46371 13.4361 3.25136 13.1926 2.90319L12.1774 1.42837C12.1477 1.38488 12.1003 1.35767 12.0409 1.35767L3.87701 1.35742C3.70473 1.35742 3.56823 1.48814 3.56823 1.64048V6.60943C3.56823 6.90342 3.31299 7.1374 2.99228 7.1374C2.67157 7.1374 2.41634 6.90342 2.41634 6.60943L2.4166 1.64048C2.4166 0.90043 3.0755 0.301758 3.87697 0.301758H12.0463C12.4974 0.301758 12.913 0.514114 13.1566 0.862283L14.1717 2.3371C14.2014 2.38059 14.2488 2.40246 14.3082 2.40246H31.5432C32.3505 2.40246 33.0036 3.00648 33.0036 3.74118V6.6038C32.998 6.8978 32.7428 7.13736 32.4221 7.13736L32.4223 7.13736Z" fill="#2D719F"/>
			<Path d="M36.9163 33.0001H21.6347C21.2546 33.0001 20.9045 32.8205 20.7086 32.5212C20.5128 32.2219 20.5008 31.8571 20.6789 31.5469L28.3197 18.3979C28.5098 18.0767 28.8718 17.8755 29.2755 17.8755C29.6733 17.8755 30.0415 18.0769 30.2313 18.3979L37.872 31.5469C38.0502 31.8572 38.0443 32.2219 37.8424 32.5212C37.6462 32.8259 37.2961 33.0001 36.916 33.0001H36.9163ZM21.7534 31.9495H36.7981L29.2754 19.002L21.7534 31.9495Z" fill="#2D719F"/>
			<Path d="M29.2753 28.4613C28.9546 28.4613 28.6993 28.2273 28.6993 27.9333V23.6012C28.6993 23.3072 28.9546 23.0732 29.2753 23.0732C29.596 23.0732 29.8512 23.3072 29.8512 23.6012V27.9333C29.8512 28.2217 29.596 28.4613 29.2753 28.4613Z" fill="#2D719F"/>
			<Path d="M29.2875 30.6272C28.9668 30.6272 28.7116 30.3933 28.7116 30.0993C28.7116 29.8053 28.9668 29.5713 29.2814 29.5713H29.2873C29.608 29.5713 29.8632 29.8053 29.8632 30.0993C29.8635 30.3933 29.6082 30.6272 29.2875 30.6272H29.2875Z" fill="#2D719F"/>
		</Svg>
    );
}

export default Repasse;