import React from 'react';
import Svg, { Path } from 'react-native-svg';

import { getSizesByWidth } from './calculator';
  
const Training2 = (props) => {
    const originalWidth = 30;
    const originalHeight = 30;
    const newWidth = props?.style?.width ? props.style.width : 42;

    const sizes = getSizesByWidth(originalWidth, originalHeight, newWidth);

    return (
        <Svg style={[{width: sizes.width, height: sizes.height}, props.style]} viewBox="0 0 30 30" fill="none" xmlns="http://www.w3.org/2000/svg">
			<Path d="M13.75 5H5C4.33696 5 3.70107 5.26339 3.23223 5.73223C2.76339 6.20107 2.5 6.83696 2.5 7.5V25C2.5 25.663 2.76339 26.2989 3.23223 26.7678C3.70107 27.2366 4.33696 27.5 5 27.5H22.5C23.163 27.5 23.7989 27.2366 24.2678 26.7678C24.7366 26.2989 25 25.663 25 25V16.25" stroke="#00467F" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
			<Path d="M23.125 3.12504C23.6223 2.62776 24.2967 2.34839 25 2.34839C25.7033 2.34839 26.3777 2.62776 26.875 3.12504C27.3723 3.62232 27.6517 4.29678 27.6517 5.00004C27.6517 5.7033 27.3723 6.37776 26.875 6.87504L15 18.75L10 20L11.25 15L23.125 3.12504Z" stroke="#00467F" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
		</Svg>

    );
}

export default Training2;