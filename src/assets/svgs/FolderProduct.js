import React from 'react';
import Svg, { Path } from 'react-native-svg';

import { getSizesByWidth } from './calculator';
  
const FolderProduct = (props) => {
    const originalWidth = 55;
    const originalHeight = 55;
    const newWidth = props?.style?.width ? props.style.width : 50;

    const sizes = getSizesByWidth(originalWidth, originalHeight, newWidth);

    return (
        <Svg style={[{width: sizes.width, height: sizes.height}, props.style]} viewBox="0 0 55 55" fill="none" xmlns="http://www.w3.org/2000/svg">
            <Path d="M41.4065 43.3498H33.4799V21.5748C33.4799 21.4328 33.3778 21.307 33.2278 21.2646L21.7536 18.0146C21.6455 17.9842 21.5261 18.0017 21.4333 18.0627C21.3407 18.1239 21.2863 18.2211 21.2863 18.3247V43.3498H13.3597C13.161 43.3498 13 43.4954 13 43.6749C13 43.8545 13.1608 44 13.3597 44H41.4063C41.6049 44 41.766 43.8545 41.766 43.6749C41.7657 43.4954 41.6049 43.3498 41.406 43.3498L41.4065 43.3498ZM28.9364 24.982V29.2404H25.8308V24.982H28.9364ZM25.8308 24.3321V19.8504L28.9364 20.73V24.332L25.8308 24.3321ZM32.7609 29.2404H29.6553V24.982H32.7609V29.2404ZM25.1117 29.2404H22.006V24.982H25.1117V29.2404ZM22.006 29.8904H25.1117V34.1487H22.006V29.8904ZM25.8307 29.8904H28.9363V34.1487H25.8307V29.8904ZM29.6554 29.8904H32.761V34.1487H29.6554V29.8904ZM32.761 24.3321H29.6554V20.9336L32.761 21.8132V24.3321ZM25.1118 19.6466V24.3321H22.0062V18.767L25.1118 19.6466ZM22.0062 34.7992H32.7612L32.7615 43.3502H30.0604V38.9388C30.0604 38.7592 29.8997 38.6137 29.7008 38.6137L25.0672 38.6139C24.8685 38.6139 24.7075 38.7595 24.7075 38.939V43.3502H22.0065L22.0062 34.7992ZM29.3408 43.3502H25.426V39.2639H29.3405L29.3408 43.3502Z" fill="#00467F"/>
            <Path d="M50.4167 43.5417C50.4167 44.7572 49.9338 45.923 49.0742 46.7826C48.2147 47.6421 47.0489 48.125 45.8333 48.125H9.16667C7.95109 48.125 6.7853 47.6421 5.92576 46.7826C5.06622 45.923 4.58334 44.7572 4.58334 43.5417V11.4583C4.58334 10.2428 5.06622 9.07697 5.92576 8.21743C6.7853 7.35789 7.95109 6.875 9.16667 6.875H20.625L25.2083 13.75H45.8333C47.0489 13.75 48.2147 14.2329 49.0742 15.0924C49.9338 15.952 50.4167 17.1178 50.4167 18.3333V43.5417Z" stroke="#00467F" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
        </Svg>

    );
}

export default FolderProduct;