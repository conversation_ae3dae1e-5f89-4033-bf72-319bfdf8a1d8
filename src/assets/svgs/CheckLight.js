import React from 'react';
import Svg, { Path } from 'react-native-svg';

import { getSizesByWidth } from './calculator';
  
const CheckLight = (props) => {
    const originalWidth = 85;
    const originalHeight = 88;
    const newWidth = props?.style?.width ? props.style.width : 110;
    const color = props?.style?.color ? props.style.color : "#FFF";

    const sizes = getSizesByWidth(originalWidth, originalHeight, newWidth);

    return (
        <Svg style={[{width: sizes.width, height: sizes.height}, props.style]} viewBox="0 0 85 88" fill="none" xmlns="http://www.w3.org/2000/svg">
			<Path d="M77.9168 40.6267V44C77.9125 51.9069 75.4394 59.6005 70.8666 65.9334C66.2937 72.2663 59.866 76.8991 52.5421 79.141C45.2181 81.3829 37.3905 81.1137 30.2264 78.3735C23.0624 75.6333 16.9458 70.5691 12.789 63.9359C8.63215 57.3028 6.65776 49.4563 7.16027 41.5665C7.66278 33.6768 10.6153 26.1666 15.5774 20.156C20.5395 14.1454 27.2454 9.95643 34.695 8.2139C42.1446 6.47137 49.9386 7.2686 56.9148 10.4867" stroke={color} strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
			<Path d="M77.9167 14.6666L42.5 51.37L31.875 40.37" stroke={color} strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
		</Svg>

    );
}

export default CheckLight;