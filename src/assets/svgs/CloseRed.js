import React from 'react';
import Svg, { Path } from 'react-native-svg';

import { getSizesByWidth } from './calculator';
  
const CloseRed = (props) => {
    const originalWidth = 33;
    const originalHeight = 32;
    const newWidth = props?.style?.width ? props.style.width : 33;
    const color = props?.style?.color ? props.style.color : "#FF6542";

    const sizes = getSizesByWidth(originalWidth, originalHeight, newWidth);

    return (
        <Svg style={[{width: sizes.width, height: sizes.height}, props.style]} viewBox="0 0 33 32" fill="none" xmlns="http://www.w3.org/2000/svg">
			<Path d="M23.087 9.51313C22.7612 9.18738 22.2339 9.18738 21.9086 9.51313L16.6002 14.8215L11.2919 9.51313C10.9661 9.18738 10.4388 9.18738 10.1135 9.51313C9.78818 9.83889 9.78771 10.3662 10.1135 10.6915L15.4218 15.9999L10.1135 21.3082C9.78771 21.634 9.78771 22.1613 10.1135 22.4866C10.2762 22.6494 10.4894 22.7305 10.7029 22.7305C10.9163 22.7305 11.1293 22.6492 11.2923 22.4866L16.6002 17.1783L21.9085 22.4866C22.0713 22.6494 22.2845 22.7305 22.498 22.7305C22.7114 22.7305 22.9244 22.6492 23.0874 22.4866C23.4132 22.1609 23.4132 21.6335 23.0874 21.3082L17.7786 15.9999L23.0869 10.6915C23.4124 10.3662 23.4124 9.83891 23.0869 9.51313H23.087ZM16.6 0C7.77738 0 0.599976 7.17741 0.599976 16.0001C0.599976 24.8227 7.77738 32.0001 16.6 32.0001C25.4227 32.0001 32.6001 24.8227 32.6001 16.0001C32.6001 7.17741 25.4227 0 16.6 0ZM16.6 30.3335C8.69643 30.3335 2.26665 23.9037 2.26665 16.0001C2.26665 8.09646 8.69643 1.66667 16.6 1.66667C24.5036 1.66667 30.9334 8.09646 30.9334 16.0001C30.9334 23.9037 24.5036 30.3335 16.6 30.3335Z" fill={color} />
		</Svg>
    );
}

export default CloseRed;