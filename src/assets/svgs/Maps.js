import React from 'react';
import Svg, { Path } from 'react-native-svg';

import { getSizesByWidth } from './calculator';
  
const Maps = (props) => {
    const originalWidth = 23;
    const originalHeight = 31;
    const newWidth = props?.style?.width ? props.style.width : 31;

    const sizes = getSizesByWidth(originalWidth, originalHeight, newWidth);

    return (
        <Svg style={[{width: sizes.width, height: sizes.height}, props.style]} viewBox="0 0 23 31" fill="none" xmlns="http://www.w3.org/2000/svg">
            <Path d="M19.4231 3.62854C17.3343 1.50198 14.5314 0.319981 11.5309 0.300293C8.53844 0.278981 5.71738 1.43167 3.59363 3.54148C1.46969 5.65148 0.300003 8.46223 0.300003 11.4559C0.300003 13.7215 0.975753 15.9029 2.25413 17.764C3.65325 19.801 5.143 22.0012 6.55719 24.2887L10.2416 30.2482C10.5037 30.672 10.9577 30.925 11.456 30.925C11.9543 30.925 12.4082 30.6719 12.6703 30.2482L15.6662 25.4024C17.5741 22.3164 19.0919 19.9919 20.5873 17.8655C21.8856 16.0192 22.5855 13.8475 22.6111 11.5851C22.6451 8.58198 21.513 5.75629 19.4231 3.62854V3.62854ZM20.3279 7.02136L16.0829 11.2663C16.1237 10.0312 15.6748 8.78279 14.7342 7.84211C13.8607 6.96854 12.6993 6.48748 11.4638 6.48748C11.4131 6.48748 11.3626 6.48954 11.3121 6.49117L15.4329 2.37042C16.5803 2.86948 17.6308 3.58779 18.5312 4.50448C19.2684 5.25498 19.8708 6.10379 20.3279 7.02136ZM9.07732 8.72598C9.71475 8.08848 10.5623 7.73748 11.4638 7.73748C12.3653 7.73748 13.2128 8.08854 13.8503 8.72598C15.166 10.0417 15.1662 12.1824 13.8509 13.4984L13.8496 13.4996C13.2123 14.1367 12.365 14.4875 11.4638 14.4875C10.5623 14.4875 9.71482 14.1364 9.07732 13.499C7.76144 12.183 7.76144 10.0419 9.07732 8.72598V8.72598ZM11.5226 1.55017C12.4163 1.55604 13.2899 1.67842 14.1265 1.90892L8.63532 7.40011L5.09613 3.86092C6.87732 2.36417 9.10325 1.54998 11.4555 1.54998C11.4777 1.54998 11.5004 1.55004 11.5226 1.55017V1.55017ZM4.19044 4.72304L7.75144 8.28404L1.91382 14.1217C1.67407 13.2614 1.54994 12.3665 1.54994 11.4559C1.54994 8.93454 2.48494 6.55979 4.19044 4.72304V4.72304ZM2.37744 15.4259L6.84463 10.9587C6.80388 12.1938 7.25275 13.4422 8.19344 14.3829C9.067 15.2564 10.2284 15.7375 11.4638 15.7375C11.5146 15.7375 11.5649 15.7354 11.6154 15.7338L6.10294 21.2463C5.15457 19.7927 4.20013 18.3893 3.28444 17.0562C2.92888 16.5386 2.62594 15.993 2.37744 15.4259V15.4259ZM19.5647 17.1463C18.0546 19.2937 16.5243 21.6372 14.6028 24.745L11.6069 29.5908C11.5616 29.664 11.4929 29.675 11.4559 29.675C11.4189 29.675 11.3501 29.664 11.3048 29.5907L7.62038 23.6313C7.348 23.1907 7.073 22.7537 6.7965 22.3205L20.8459 8.27123C21.1971 9.31811 21.3741 10.4299 21.3612 11.5709C21.3383 13.5797 20.7172 15.5076 19.5647 17.1463V17.1463Z" fill="white"/>
        </Svg>
    );
}

export default Maps;