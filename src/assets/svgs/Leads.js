import React from 'react';
import Svg, { Path, Line } from 'react-native-svg';

import { getSizesByWidth } from './calculator';
  
const Leads = (props) => {
    const originalWidth = 24;
    const originalHeight = 22;
    const newWidth = props?.style?.width ?? 24;
    const color = props?.style?.color ?? "#2D719F";

    const sizes = getSizesByWidth(originalWidth, originalHeight, newWidth);

    return (
        <Svg width="37" height="31" viewBox="0 0 37 31" fill="none" xmlns="http://www.w3.org/2000/svg">
			<Path fillRule="evenodd" clipRule="evenodd" d="M14.3877 8.94081C15.5224 8.94081 16.4426 9.87727 16.4426 11.0317C16.4426 12.1861 15.5224 13.1225 14.3877 13.1225C13.253 13.1225 12.3325 12.1861 12.3325 11.0317C12.3325 9.87727 13.253 8.94081 14.3877 8.94081ZM13.7715 23.8455C13.7715 24.3067 13.9559 24.7247 14.2518 25.0256C14.5503 25.3293 14.9618 25.5171 15.4152 25.5171H28.3229C28.6289 25.5171 28.8959 25.6869 29.0375 25.9396C29.6259 26.8323 30.3893 27.5958 31.2765 28.1804L31.3172 28.209C31.5765 28.377 31.8475 28.5302 32.1282 28.667C31.7821 28.008 31.4973 27.3139 31.282 26.5962C31.151 26.156 31.3953 25.6904 31.8277 25.5568C31.9068 25.5326 31.9869 25.5206 32.0657 25.5206L33.7108 25.5171C34.1642 25.5171 34.5757 25.3295 34.8714 25.0284C35.1699 24.7248 35.3546 24.3067 35.3546 23.8455V11.4076C35.3546 10.9481 35.169 10.5292 34.8714 10.2264L34.839 10.1914C34.5456 9.90928 34.1482 9.73481 33.7112 9.73481H30.3779C29.924 9.73481 29.5555 9.35994 29.5555 8.89813C29.5555 8.43632 29.924 8.06145 30.3779 8.06145H33.7112C34.5895 8.06145 35.3921 8.41852 35.9846 8.99761L36.0342 9.04399C36.6301 9.65022 37 10.4875 37 11.4076V23.8455C37 24.7624 36.6309 25.597 36.0356 26.2038L36.0313 26.2082C35.4346 26.8144 34.613 27.1907 33.7114 27.1907H33.2443C33.3578 27.4545 33.4831 27.7132 33.6196 27.9668C33.9333 28.5491 34.3102 29.1063 34.7437 29.6262C34.8922 29.8065 34.9665 30.0489 34.9266 30.2995C34.8544 30.754 34.4323 31.0627 33.9855 30.9892C32.696 30.7762 31.4927 30.2972 30.4346 29.6104L30.3836 29.5792C29.4169 28.9429 28.5736 28.1322 27.8965 27.1908H15.4153C14.5141 27.1908 13.6931 26.8154 13.0969 26.2097L13.0926 26.2053C12.4967 25.5982 12.1268 24.763 12.1268 23.8455V23.6914C12.1268 23.2296 12.4953 22.8547 12.9492 22.8547C13.4032 22.8547 13.7716 23.2296 13.7716 23.6914V23.8455L13.7715 23.8455ZM4.93292 0.000396006H23.8426C25.1959 0.000396006 26.4286 0.564902 27.3242 1.47451L27.3285 1.47888C28.222 2.38965 28.776 3.64352 28.776 5.01905V17.0439C28.776 18.4241 28.2212 19.6791 27.3271 20.5888C26.4335 21.4978 25.1994 22.0623 23.8427 22.0623H10.4248C9.63877 23.1694 8.65001 24.1251 7.51333 24.8735C6.28143 25.686 4.87343 26.251 3.35648 26.5011C2.90972 26.5731 2.48936 26.263 2.41852 25.8085C2.37981 25.5591 2.45408 25.3181 2.60262 25.1393C3.11449 24.5249 3.55923 23.8664 3.92973 23.1776C4.12874 22.8042 4.30796 22.4252 4.46568 22.0396C3.29457 21.9264 2.23842 21.3919 1.44897 20.5888C0.555127 19.6794 0 18.4241 0 17.0439V5.01903C0 3.64231 0.554877 2.38814 1.44897 1.47707L1.45327 1.47269C2.3485 0.56364 3.58099 0 4.93305 0L4.93292 0.000396006ZM23.8426 1.67406H4.93292C4.02675 1.67406 3.20492 2.04952 2.61104 2.65399C2.01372 3.26109 1.64467 4.09748 1.64467 5.01908V17.0439C1.64467 17.9632 2.01459 18.7996 2.61104 19.4061C3.20635 20.0118 4.02932 20.3887 4.93292 20.3887H5.62343C5.702 20.3922 5.7823 20.4038 5.86144 20.4281C6.29416 20.5614 6.53819 21.0273 6.40714 21.4675C6.14362 22.3433 5.79549 23.1861 5.36854 23.9785L5.2286 24.2279C5.71609 24.017 6.18125 23.7649 6.62082 23.4761C7.67551 22.785 8.58109 21.8756 9.28106 20.8117C9.42243 20.5593 9.68997 20.389 9.99566 20.389H23.8423C24.7459 20.389 25.5689 20.012 26.1642 19.4064C26.7603 18.7999 27.1305 17.9635 27.1305 17.0442V5.01938C27.1305 4.09748 26.7615 3.26139 26.1673 2.6572C25.5706 2.04952 24.7484 1.67406 23.8426 1.67406L23.8426 1.67406ZM21.3759 8.94089C22.5112 8.94089 23.4311 9.87735 23.4311 11.0317C23.4311 12.1861 22.5109 13.1226 21.3759 13.1226C20.2412 13.1226 19.321 12.1861 19.321 11.0317C19.321 9.87735 20.2412 8.94089 21.3759 8.94089ZM7.39935 8.94089C8.53405 8.94089 9.45454 9.87735 9.45454 11.0317C9.45454 12.1861 8.53405 13.1226 7.39935 13.1226C6.26465 13.1226 5.34446 12.1861 5.34446 11.0317C5.34446 9.87735 6.26465 8.94089 7.39935 8.94089Z" fill={color} />
		</Svg>       

    );
}

export default Leads;