
import React from 'react';
import Svg, { Path } from 'react-native-svg';

import { getSizesByWidth } from './calculator';
  
const Logo = (props) => {
    const originalWidth = 42;
    const originalHeight = 49;
    const newWidth = props?.style?.width ? props.style.width : 50;

    const sizes = getSizesByWidth(originalWidth, originalHeight, newWidth);

    return (
      <Svg style={[{width: sizes.width, height: sizes.height}, props.style]} viewBox="0 0 42 49" fill="none" xmlns="http://www.w3.org/2000/svg">
        <Path fill-rule="evenodd" clip-rule="evenodd" d="M28.659 49C29.2724 47.6995 29.9594 46.35 30.8673 45.0005C31.8242 43.6019 32.9775 42.2033 34.2288 40.7556C33.5909 39.7496 33.002 38.7681 32.4622 37.7621C30.9164 39.2589 29.4932 40.7311 28.2419 42.2033C27.0396 43.6019 26.0581 45.025 25.1748 46.3746C22.5493 45.8593 19.9484 45.344 17.3475 44.8042C14.7712 44.2889 12.1948 43.7737 9.59389 43.2339C10.4772 44.1662 11.4341 45.0741 12.4402 45.982C15.0901 46.4972 17.7646 46.988 20.4637 47.5033C23.1873 48.0185 25.9109 48.5093 28.659 49ZM21.9604 43.332C19.4577 42.7922 16.9549 42.2524 14.4767 41.7126C11.974 41.1482 9.4712 40.6084 6.96845 40.0686C6.28142 39.0871 5.64346 38.1057 5.03004 37.1242C7.50826 37.664 9.96194 38.2283 12.4156 38.7927C14.8448 39.3325 17.2494 39.8968 19.6785 40.4612C21.1507 38.989 22.7211 37.4186 24.4387 35.8973C26.2789 34.3024 28.2173 32.7076 30.3029 31.0881C30.5238 32.1678 30.7937 33.2474 31.0881 34.3024C29.2479 35.8728 27.5303 37.3941 25.9845 38.9399C24.4877 40.4121 23.1873 41.8843 21.9604 43.332ZM17.6665 37.1242C19.4331 35.5538 21.2489 33.9589 23.2609 32.364C25.322 30.7201 27.5548 29.0761 29.8858 27.3831C29.8613 26.2544 29.8368 25.1257 29.8858 23.997C27.334 25.7391 24.8803 27.4567 22.5739 29.1252C20.3655 30.7692 18.2799 32.4131 16.317 34.0326C13.986 33.4682 11.655 32.8793 9.29945 32.2904C6.89484 31.7261 4.4657 31.1617 2.01202 30.6219C2.40461 31.677 2.84627 32.7076 3.28793 33.7381C5.74161 34.3025 8.14622 34.8423 10.5754 35.4066C12.9554 35.971 15.311 36.5598 17.6665 37.1242ZM15.1883 30.4992C12.8818 29.9349 10.5508 29.346 8.17075 28.7817C5.76615 28.2173 3.337 27.653 0.883325 27.0886C0.61342 26.0335 0.392589 24.9539 0.245368 23.8498C2.69905 24.4141 5.12819 24.9785 7.50826 25.5183C9.88833 26.0826 12.2193 26.6715 14.5258 27.2604C16.9549 25.5428 19.5068 23.8007 22.2058 22.034C25.003 20.2183 27.972 18.4026 31.0881 16.5623C30.7691 17.7646 30.4992 18.9424 30.2784 20.1202C27.4321 21.9359 24.7331 23.678 22.1813 25.4447C19.7276 27.1377 17.4211 28.8553 15.1883 30.4992ZM14.1577 23.5799C16.8568 21.8132 19.6785 19.973 22.6965 18.1818C25.8127 16.2924 29.1252 14.4276 32.5849 12.4647C33.1738 11.2133 33.8117 9.9374 34.5478 8.63695C32.6339 9.64296 30.7446 10.649 28.9289 11.6304C27.1377 12.6119 25.3465 13.5934 23.6289 14.5503C20.3165 16.4397 17.1758 18.329 14.2068 20.1447C11.9249 19.5558 9.6675 18.9669 7.3365 18.3781C5.03004 17.7892 2.67451 17.2003 0.318977 16.636C0.14722 17.8137 0.0490736 18.9424 0 20.0956C2.40461 20.66 4.80921 21.2243 7.16475 21.8132C9.54482 22.4021 11.8513 22.991 14.1577 23.5799ZM14.8202 16.2679C12.6119 15.679 10.4036 15.0656 8.17075 14.4767C5.91337 13.8878 3.65598 13.2744 1.37406 12.6855C1.84026 11.4587 2.38007 10.2073 3.04256 8.93139C5.2018 9.54482 7.38558 10.1582 9.54481 10.7717C11.6795 11.3851 13.8142 12.023 15.9244 12.6365C17.7646 11.655 19.654 10.6244 21.5924 9.61843C23.5553 8.58788 25.5673 7.55734 27.6039 6.52679C29.7386 5.44717 31.9224 4.39209 34.1552 3.31247C36.4371 2.23285 38.7681 1.10416 41.1482 0C39.8478 1.42313 38.6455 2.82173 37.5658 4.1958C35.4311 5.25088 33.321 6.30596 31.2599 7.36104C29.2233 8.39159 27.2359 9.42213 25.3465 10.4281C23.4817 11.4342 21.666 12.3911 19.8993 13.3726C18.1818 14.354 16.4642 15.3355 14.8202 16.2679Z" fill="white"/>
      </Svg>
    );
}

export default Logo;