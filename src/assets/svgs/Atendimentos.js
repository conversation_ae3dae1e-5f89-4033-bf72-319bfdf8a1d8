import React from 'react';
import Svg, { Path } from 'react-native-svg';

import { getSizesByWidth } from './calculator';
  
const Atendimentos = (props) => {
    const originalWidth = 46;
    const originalHeight = 28;
    const newWidth = props?.style?.width ? props.style.width : 55;

    const sizes = getSizesByWidth(originalWidth, originalHeight, newWidth);

    return (
        <Svg style={[{width: sizes.width, height: sizes.height}, props.style]} viewBox="0 0 46 28" fill="none" xmlns="http://www.w3.org/2000/svg">
			<Path d="M45.9172 12.292L38.006 0.244101C37.8393 -0.00939225 37.5061 -0.074764 37.2526 0.0920117L31.7392 3.71459C31.4857 3.88136 31.4203 4.21457 31.5798 4.46806L32.2245 5.44598L31.1885 6.11973C26.1461 4.4027 22.0966 4.6925 20.1114 4.98238C19.0681 5.13447 18.5321 5.59809 18.4957 5.61977H18.4884L16.286 5.99668C15.5326 6.12709 14.7575 5.96032 14.1054 5.56208L14.7067 4.71456C14.7865 4.59848 14.8228 4.45373 14.8008 4.30897C14.7791 4.16422 14.6994 4.04081 14.5763 3.95409L9.18636 0.142723C8.93987 -0.0310548 8.59966 0.0266488 8.4329 0.273132L0.101113 12.0316C0.0140624 12.1477 -0.0149603 12.2924 0.00705218 12.4372C0.0287325 12.582 0.108448 12.7054 0.231523 12.7921L5.62185 16.6028C5.71591 16.6678 5.82464 16.7042 5.93337 16.7042C6.10014 16.7042 6.27391 16.6245 6.37531 16.4724L6.94032 15.6755L8.74444 17.0664L7.92594 18.03C7.54204 18.4936 7.36093 19.0806 7.41896 19.689C7.477 20.2904 7.76684 20.8337 8.22313 21.2103C8.63604 21.5508 9.12864 21.7172 9.62132 21.7246C9.43287 22.5361 9.6937 23.4126 10.3748 23.9776C10.795 24.3255 11.302 24.492 11.8094 24.492C12.1282 24.492 12.4467 24.4269 12.7366 24.2892C12.8306 24.7818 13.0844 25.2527 13.4974 25.6006C13.9176 25.9485 14.4246 26.1149 14.9319 26.1149C15.2725 26.1149 15.613 26.0352 15.9245 25.8831C16.0259 26.3614 16.2724 26.8103 16.678 27.1436C17.0983 27.4915 17.6126 27.6579 18.1199 27.6579C18.7646 27.6579 19.4024 27.3827 19.8443 26.8611L20.5327 26.1003C20.5544 26.0786 20.5617 26.0496 20.5834 26.0279L22.3368 27.332C22.7497 27.6362 23.2351 27.8029 23.7276 27.8029C23.8507 27.8029 23.9668 27.7956 24.0899 27.7739C24.7203 27.6869 25.2709 27.361 25.6475 26.8537C25.93 26.4768 26.0821 26.0352 26.1111 25.5933L26.6248 25.9925L27.1754 26.4054C27.1754 26.4054 27.1828 26.4054 27.1828 26.4128C27.6897 26.7897 28.3131 26.9487 28.9288 26.8474C29.5445 26.7603 30.1099 26.4271 30.4791 25.9345C30.7616 25.5576 30.921 25.116 30.95 24.674C31.3846 24.8985 31.8846 24.9782 32.3773 24.9058C32.964 24.8188 33.4856 24.5219 33.8625 24.0653C34.2538 23.58 34.4276 22.957 34.3478 22.3193C34.3188 22.0728 34.2538 21.834 34.1524 21.6165C34.7321 21.5078 35.2537 21.1962 35.616 20.711C36.0653 20.0953 36.174 19.3128 35.9492 18.6027C36.3114 17.7044 37.3838 16.7189 39.0064 15.7843L39.4847 16.5161C39.5861 16.6755 39.7599 16.7626 39.941 16.7626C40.0424 16.7626 40.1438 16.7335 40.2382 16.6755L45.7516 13.0529C45.8746 12.9732 45.9544 12.8501 45.9834 12.7124C46.0191 12.5606 45.9974 12.4085 45.9177 12.2925L45.9172 12.292ZM5.80293 15.4073L1.29652 12.2196L8.9979 1.3449L13.5043 4.52552L5.80293 15.4073ZM8.49788 19.5948C8.46886 19.2833 8.56292 18.9791 8.75871 18.74L10.6135 16.5593C10.8453 16.2841 11.1786 16.139 11.5191 16.139C11.7799 16.139 12.0481 16.2261 12.2652 16.4072C12.5117 16.61 12.6564 16.8925 12.6854 17.197V17.2044C12.7145 17.5085 12.6204 17.8201 12.432 18.0519L10.5701 20.2325C10.1572 20.7325 9.41809 20.7976 8.91841 20.3846C8.67159 20.1818 8.52684 19.899 8.49782 19.5948L8.49788 19.5948ZM11.0624 23.145C10.5625 22.7321 10.4974 21.993 10.8957 21.5003L14.3442 17.4795C14.576 17.2043 14.9092 17.0592 15.2424 17.0592C15.5032 17.0592 15.7714 17.1463 15.9885 17.3274C16.2277 17.5302 16.3798 17.8127 16.4088 18.1242C16.4378 18.4284 16.3438 18.7399 16.148 18.9717L12.6995 22.9926C12.3016 23.4852 11.5625 23.5579 11.0625 23.145L11.0624 23.145ZM14.1923 24.775C13.6997 24.3621 13.6273 23.623 14.0329 23.1303L15.9167 20.928C16.1412 20.6671 16.4674 20.5294 16.7933 20.5294C17.0541 20.5294 17.3223 20.6164 17.5467 20.7976C17.7859 20.993 17.938 21.2759 17.967 21.5874C17.996 21.8989 17.902 22.2031 17.7062 22.4349L15.8513 24.6156C15.4311 25.1082 14.6923 25.1806 14.1923 24.775L14.1923 24.775ZM19.7131 25.3981L19.0246 26.1589C18.6117 26.6589 17.8726 26.7239 17.3729 26.3183C16.8803 25.9127 16.8079 25.181 17.1992 24.6953L17.2135 24.681L17.8946 23.9275C17.9596 23.8478 18.0323 23.7827 18.1191 23.7247C18.3218 23.58 18.561 23.5002 18.8001 23.5002C19.0683 23.5002 19.3291 23.5943 19.5463 23.7684C19.7854 23.9639 19.9375 24.2467 19.9665 24.5582C19.9809 24.732 19.9592 24.9061 19.9158 25.0509C19.8578 25.1953 19.7928 25.311 19.713 25.3981L19.7131 25.3981ZM34.7384 20.0515C34.5283 20.3267 34.2314 20.5008 33.8909 20.5515C33.5867 20.6022 33.2969 20.5371 33.036 20.3777H33.0287L26.5663 15.567C26.3272 15.3859 25.9866 15.4366 25.8055 15.6758C25.6244 15.9149 25.6751 16.2555 25.9142 16.4366L32.6664 21.4647L32.7027 21.4937C33.0069 21.7182 33.2097 22.0734 33.2608 22.4573C33.3041 22.8052 33.2174 23.131 33.0073 23.3845C32.8045 23.631 32.522 23.7901 32.2031 23.8408C31.8626 23.8915 31.5294 23.8044 31.2542 23.609L30.2182 22.8412C30.1892 22.8195 30.1602 22.8121 30.1312 22.7978C30.0948 22.7328 30.0441 22.6817 29.9864 22.631L24.8129 18.7617C24.5737 18.5806 24.2332 18.6313 24.0521 18.8704C23.871 19.1096 23.9217 19.4501 24.1608 19.6312L29.348 23.4856C29.3844 23.5073 29.4204 23.522 29.4567 23.5437C29.4784 23.5727 29.4857 23.6087 29.5148 23.6307C29.9424 24.087 29.9857 24.7754 29.6088 25.2754C29.4134 25.5436 29.1089 25.7247 28.7683 25.7681C28.4351 25.8188 28.1019 25.739 27.8264 25.5289L26.4789 24.529L21.4799 20.8124C21.2407 20.6313 20.9002 20.682 20.719 20.9211C20.5379 21.1602 20.5886 21.5008 20.8278 21.6819L24.5227 24.4276C24.5227 24.4276 24.5227 24.4349 24.53 24.4349C25.088 24.8625 25.2038 25.6593 24.7835 26.217C24.5807 26.4852 24.2835 26.6663 23.936 26.7096C23.6028 26.7603 23.2696 26.6806 23.0014 26.4705L21.0309 25.0069C21.0599 24.8258 21.0673 24.6373 21.0526 24.4562C20.9945 23.8548 20.7047 23.3115 20.2411 22.935C19.8572 22.6161 19.4006 22.4497 18.9296 22.4207C19.031 22.1235 19.0817 21.805 19.0457 21.4788C18.9877 20.8774 18.6978 20.3341 18.2342 19.9575C17.9084 19.6894 17.5241 19.5299 17.1259 19.4722C17.4084 19.0446 17.5535 18.5303 17.5028 18.0233C17.4448 17.422 17.1623 16.8786 16.6913 16.4947C15.7858 15.7486 14.4743 15.8353 13.6701 16.6395C13.5397 16.2266 13.3005 15.8497 12.953 15.5671C11.9968 14.7773 10.5765 14.9077 9.77972 15.857L9.45386 16.2409L7.57007 14.792L13.4673 6.46053C14.3586 7.02555 15.4305 7.24301 16.4739 7.0619L17.206 6.93816L14.5543 10.4083C14.3442 10.6835 14.2354 11.0097 14.2354 11.3572C14.2354 11.8642 14.4819 12.3425 14.9019 12.6324C16.5466 13.7697 18.8359 13.3715 19.995 11.7485L21.77 9.29259C22.7336 10.3576 25.0881 12.705 27.7687 13.4368L34.4702 18.2618C34.6513 18.3922 34.7817 18.559 34.8687 18.7688C35.0645 19.1964 35.0138 19.6893 34.7383 20.0515L34.7384 20.0515ZM35.2817 17.5229C35.231 17.4796 35.173 17.4289 35.115 17.3925L29.8914 13.6326C30.768 13.5455 31.5867 13.2413 32.3403 12.7054C32.5868 12.5316 32.6445 12.1911 32.4634 11.9446C32.2896 11.6981 31.949 11.6404 31.7025 11.8215C30.6449 12.5749 29.4785 12.7704 28.1307 12.4229C25.0081 11.6187 22.1827 8.13393 22.1173 8.06152C22.0159 7.95279 21.8708 7.88774 21.7187 7.88774H21.6824C21.5229 7.89508 21.3708 7.9818 21.2768 8.11221L19.0961 11.1334C18.2776 12.2781 16.6619 12.5533 15.5026 11.7565C15.3721 11.6694 15.2998 11.5247 15.2998 11.3726C15.2998 11.2638 15.3288 11.1698 15.3938 11.0827L18.357 7.19938C19.0597 6.25749 20.1754 6.07638 20.2478 6.06204C22.1603 5.75785 26.1162 5.50402 31.05 7.23575C31.2094 7.29379 31.3832 7.27211 31.5283 7.17772L32.7888 6.35922L38.3819 14.8718C37.2528 15.5308 35.9921 16.4434 35.282 17.5231L35.2817 17.5229ZM40.0997 15.458L32.7826 4.31512L37.3904 1.29391L44.7076 12.4368L40.0997 15.458Z" fill="#2D719F"/>
		</Svg>

    );
}

export default Atendimentos;