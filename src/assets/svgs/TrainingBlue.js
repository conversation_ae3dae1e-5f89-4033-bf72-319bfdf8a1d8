import React from 'react';
import Svg, { Path } from 'react-native-svg';

import { getSizesByWidth } from './calculator';
  
const TrainingBlue = (props) => {
    const originalWidth = 48;
    const originalHeight = 52;
    const newWidth = props?.style?.width ? props.style.width : 48;

    const sizes = getSizesByWidth(originalWidth, originalHeight, newWidth);

    return (
        <Svg width="48" height="52" viewBox="0 0 48 52" fill="none" xmlns="http://www.w3.org/2000/svg">
			<Path d="M45.1318 21.3241C43.2736 20.9879 41.3884 20.8236 39.5001 20.8333C38.4151 20.8333 37.3559 20.8333 36.2968 21.04C34.9782 19.2804 33.2951 17.8264 31.3626 16.7775C33.0225 14.9646 33.944 12.5963 33.9459 10.1383C33.9459 7.49364 32.8954 4.95731 31.0253 3.08726C29.1553 1.21721 26.6189 0.166626 23.9743 0.166626C21.3296 0.166626 18.7933 1.21721 16.9232 3.08726C15.0532 4.95731 14.0026 7.49364 14.0026 10.1383C14.0045 12.5963 14.926 14.9646 16.5859 16.7775C14.665 17.8319 12.9846 19.2747 11.6518 21.0141C10.6443 20.8333 9.5851 20.8333 8.5001 20.8333C6.61019 20.8406 4.72499 21.0222 2.86843 21.3758C2.26663 21.4858 1.7234 21.8059 1.33548 22.279C0.947561 22.752 0.740122 23.3474 0.7501 23.9591V45.2716C0.749483 45.6511 0.832496 46.0261 0.993233 46.3699C1.15397 46.7137 1.38849 47.0179 1.6801 47.2608C1.96987 47.5051 2.30987 47.6827 2.67596 47.7809C3.04205 47.8791 3.42527 47.8956 3.79843 47.8291C5.34433 47.4924 6.91851 47.3022 8.5001 47.2608C13.5043 47.2553 18.3999 48.7195 22.5793 51.4716L22.9151 51.6008C23.2578 51.7495 23.6266 51.8285 24.0001 51.8333C24.2467 51.8302 24.491 51.7866 24.7234 51.7041H24.9043L25.2401 51.575C29.4593 48.7522 34.4237 47.2503 39.5001 47.2608C41.0784 47.2681 42.6526 47.4238 44.2018 47.7258C44.5749 47.7923 44.9582 47.7758 45.3242 47.6776C45.6903 47.5794 46.0303 47.4018 46.3201 47.1575C46.6117 46.9146 46.8462 46.6104 47.007 46.2666C47.1677 45.9228 47.2507 45.5478 47.2501 45.1683V23.8558C47.2479 23.2529 47.0349 22.6698 46.648 22.2075C46.2612 21.7451 45.7248 21.4326 45.1318 21.3241ZM24.0001 5.33329C25.2103 5.42221 26.342 5.96568 27.1679 6.85458C27.9939 7.74347 28.453 8.91196 28.453 10.1254C28.453 11.3388 27.9939 12.5073 27.1679 13.3962C26.342 14.2851 25.2103 14.8285 24.0001 14.9175C22.7899 14.8285 21.6582 14.2851 20.8323 13.3962C20.0063 12.5073 19.5472 11.3388 19.5472 10.1254C19.5472 8.91196 20.0063 7.74347 20.8323 6.85458C21.6582 5.96568 22.7899 5.42221 24.0001 5.33329ZM21.4168 44.9358C17.3678 43.0642 12.9607 42.0947 8.5001 42.0941C7.6476 42.0941 6.7951 42.0941 5.91677 42.2233V26C8.08305 25.7576 10.2718 25.8011 12.4268 26.1291H12.7109C15.7892 26.6949 18.7391 27.8153 21.4168 29.4358V44.9358ZM24.0001 24.9666C22.8514 24.304 21.6605 23.7171 20.4351 23.21H20.2801C19.4276 22.8741 18.5751 22.5383 17.6968 22.28C19.4904 20.8523 21.7079 20.0617 24.0001 20.0325C26.2863 20.047 28.5031 20.8192 30.3034 22.2283C28.1104 22.913 25.9972 23.831 24.0001 24.9666ZM42.0834 42.2233C36.779 41.6903 31.4283 42.5821 26.5834 44.8066V29.3066C29.266 27.7277 32.2177 26.6591 35.2893 26.155H35.8059C37.8829 25.8315 39.9931 25.7794 42.0834 26V42.2233Z" fill="#4EA1CC"/>
		</Svg>

    );
}

export default TrainingBlue;