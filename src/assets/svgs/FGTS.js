import React from 'react';
import Svg, { Path } from 'react-native-svg';

import { getSizesByWidth } from './calculator';
  
const FGTS = (props) => {
    const originalWidth = 28;
    const originalHeight = 28;
    const newWidth = props?.style?.width ? props.style.width : 35;
    const color = props?.style?.color ? props.style.color : "#FFF";

    const sizes = getSizesByWidth(originalWidth, originalHeight, newWidth);

    return (
        <Svg style={[{width: sizes.width, height: sizes.height}, props.style]} viewBox="0 0 28 28" fill="none" xmlns="http://www.w3.org/2000/svg">
            <Path d="M13.5298 25.4816C7.01544 25.4816 1.72768 20.1656 1.72768 13.608C1.72768 7.0542 7.01544 1.73815 13.5298 1.73815C20.0516 1.73815 25.3319 7.0542 25.3319 13.608C25.3319 14.0896 25.7208 14.4809 26.192 14.4809C26.6744 14.4809 27.0521 14.0896 27.0521 13.608C27.0521 6.09483 20.9977 0 13.526 0C6.05437 0 0 6.09483 0 13.608C0 21.1287 6.05437 27.2236 13.526 27.2236C14.001 27.2236 14.3936 26.8323 14.3936 26.3545C14.3936 25.8767 14.0047 25.4816 13.5298 25.4816Z" fill={color}/>
            <Path d="M12.6697 4.3454V13.9015H7.77456C7.29964 13.9015 6.91446 14.289 6.91446 14.7668C6.91446 15.2446 7.30337 15.6396 7.77456 15.6396H13.5298C14.0047 15.6396 14.3973 15.2446 14.3973 14.7668V4.3454C14.3973 3.86383 14.0047 3.47632 13.5298 3.47632C13.0548 3.47632 12.6697 3.86383 12.6697 4.3454Z" fill={color}/>
            <Path d="M23.6229 17.9533C23.2452 17.6636 22.7029 17.7388 22.4113 18.1263L19.6589 21.787L17.7144 20.3009C17.3292 20.0112 16.7869 20.0902 16.5027 20.4702C16.211 20.8539 16.2896 21.3957 16.671 21.6892L19.3149 23.6944C19.6888 23.9804 20.2348 23.9089 20.519 23.5251L23.7837 19.1685C24.0828 18.7885 24.0043 18.243 23.6229 17.9533Z" fill={color}/>
            <Path d="M20.4405 13.9016C16.7795 13.9016 13.8215 16.8851 13.8215 20.5608C13.8215 24.244 16.7795 27.2237 20.4405 27.2237C24.0941 27.2237 27.0596 24.2402 27.0596 20.5608C27.0558 16.8851 24.0903 13.9016 20.4405 13.9016ZM20.4405 25.4818C17.7368 25.4818 15.5454 23.2809 15.5454 20.557C15.5454 17.8369 17.7368 15.6398 20.4405 15.6398C23.1442 15.6398 25.3356 17.8369 25.3356 20.557C25.3319 23.2809 23.1405 25.4818 20.4405 25.4818Z" fill={color}/>
        </Svg>
    );
}

export default FGTS;