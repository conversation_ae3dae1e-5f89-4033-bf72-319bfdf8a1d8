import React from 'react';
import Svg, { Path } from 'react-native-svg';

import { getSizesByWidth } from './calculator';
  
const Bank = (props) => {
    const originalWidth = 64;
    const originalHeight = 64;
    const newWidth = props?.style?.width ? props.style.width : 64;

    const sizes = getSizesByWidth(originalWidth, originalHeight, newWidth);

    return (
    <Svg style={[{width: sizes.width, height: sizes.height}, props.style]} viewBox="0 0 64 64" fill="none" xmlns="http://www.w3.org/2000/svg">
      <Path fillRule="evenodd" clipRule="evenodd" d="M4.57132 19.4292H1.14279C0.622791 19.4292 0.165682 19.0778 0.0370982 18.5721C-0.0943308 18.0693 0.131386 17.5407 0.588524 17.2864L31.4457 0.143569C31.7885 -0.0478563 32.2114 -0.0478563 32.5542 0.143569L63.4114 17.2864C63.8686 17.5407 64.0943 18.0693 63.9628 18.5721C63.8343 19.0778 63.3771 19.4292 62.8572 19.4292H59.4286V22.952C59.4286 23.5834 58.9172 24.0949 58.2858 24.0949H56.0001V48.0003H58.2858C58.9172 48.0003 59.4286 48.5118 59.4286 49.1432V52.5717H62.8572C63.4886 52.5717 64 53.0831 64 53.7145V58.2858C64 58.9173 63.4886 59.4287 62.8572 59.4287V62.8572C62.8572 63.4886 62.3457 64 61.7143 64H2.28638C1.65496 64 1.14353 63.4886 1.14353 62.8572V59.4287C0.512112 59.4287 0.00068811 58.9173 0.00068811 58.2858V53.7145C0.00068811 53.0831 0.512112 52.5717 1.14353 52.5717H4.57207V49.1432C4.57207 48.5118 5.08349 48.0003 5.71491 48.0003H8.0006V24.0949H5.71491C5.08349 24.0949 4.57207 23.5834 4.57207 22.952L4.57132 19.4292ZM43.428 19.4292H20.5711V22.952C20.5711 23.5834 20.0597 24.0949 19.4283 24.0949H17.1426V48.0003H19.4283C20.0597 48.0003 20.5711 48.5118 20.5711 49.1432V52.5717H43.428V49.1432C43.428 48.5118 43.9395 48.0003 44.5709 48.0003H46.8566V24.0949H44.5709C43.9395 24.0949 43.428 23.5834 43.428 22.952V19.4292ZM54.8565 21.8092H57.1422V19.5235H45.7137V21.8092H54.8565ZM53.7136 48.0003V24.0949H49.1423V48.0003H53.7136ZM57.1422 52.5717V50.286H54.8565H47.9994H45.7137V52.5717H57.1422ZM61.7136 57.143V54.8573H58.285H44.5709H19.4283H5.71417H2.28563V57.143H61.7136ZM3.42811 59.4287V61.7143H60.5703V59.4287H3.42811ZM9.14233 50.286H6.85665V52.5717H18.2851V50.286H15.9994H9.14233ZM10.2852 24.0949V48.0003H14.8566V24.0949H10.2852ZM15.9994 21.8092H18.2851V19.5235H6.85665V21.8092H15.9994ZM31.9992 2.45235L5.55398 17.1435H58.4445L31.9992 2.45235ZM32.0478 24.0949C38.3277 24.0949 43.428 29.1948 43.428 35.4771C43.428 41.7569 38.328 46.8571 32.0478 46.8571C25.765 46.8571 20.6654 41.7572 20.6654 35.4771C20.6654 29.1944 25.7654 24.0949 32.0478 24.0949ZM32.0478 26.3805C27.025 26.3805 22.9504 30.4576 22.9504 35.4778C22.9504 40.4977 27.0246 44.5722 32.0478 44.5722C37.0678 44.5722 41.1423 40.498 41.1423 35.4778C41.1423 30.4579 37.0681 26.3805 32.0478 26.3805ZM32.0478 36.6203C29.7593 36.6203 27.9964 34.9604 27.9964 33.0547C27.9964 31.4804 29.1964 30.0747 30.905 29.6319V28.6919C30.905 28.0633 31.4164 27.549 32.0478 27.549C32.6792 27.549 33.1906 28.0633 33.1906 28.6919V29.6319C34.8963 30.0747 36.0964 31.4804 36.0964 33.0547C36.0964 33.6832 35.5849 34.1975 34.9535 34.1975C34.325 34.1975 33.8107 33.6832 33.8107 33.0547C33.8107 32.2832 32.9707 31.7747 32.0478 31.7747C31.1221 31.7747 30.2821 32.2832 30.2821 33.0547C30.2821 33.8232 31.122 34.3346 32.0478 34.3346C34.3363 34.3346 36.0964 35.9917 36.0964 37.8974C36.0964 39.4716 34.8964 40.8773 33.1906 41.3202V42.2602C33.1906 42.8916 32.6792 43.4031 32.0478 43.4031C31.4164 43.4031 30.905 42.8916 30.905 42.2602V41.3202C29.1964 40.8774 27.9964 39.4717 27.9964 37.8974C27.9964 37.2689 28.5078 36.7546 29.1392 36.7546C29.7707 36.7546 30.2821 37.2689 30.2821 37.8974C30.2821 38.6688 31.122 39.1774 32.0478 39.1774C32.9706 39.1774 33.8107 38.6688 33.8107 37.8974C33.8107 37.1288 32.9707 36.6203 32.0478 36.6203Z" fill="#2D719F"/>
    </Svg>    
    );
}

export default Bank;