import React from 'react';
import Svg, { Path } from 'react-native-svg';

import { getSizesByWidth } from './calculator';
  
const Celphone = (props) => {
    const originalWidth = 24;
    const originalHeight = 39;
    const newWidth = props?.style?.width ? props.style.width : 43;
    const color = props?.style?.color ? props.style.color : "#00467F";

    const sizes = getSizesByWidth(originalWidth, originalHeight, newWidth);

    return (
		<Svg style={[{width: sizes.width, height: sizes.height}, props.style]} viewBox="0 0 24 39" fill="none" xmlns="http://www.w3.org/2000/svg">
			<Path d="M21.2547 0.116109H2.94139C1.92183 0.116109 1.09055 0.911995 1.09055 1.90283V36.2525C1.09055 37.2436 1.92215 38.0392 2.94139 38.0392H21.3547C22.3743 38.0392 23.2056 37.2433 23.2056 36.2525V1.90283C23.2056 0.911706 22.374 0.11582 21.3547 0.11582H21.2547V0.116109ZM22.0505 36.2523C22.0505 36.6169 21.7424 36.919 21.3547 36.919L2.94139 36.9193C2.55367 36.9193 2.24559 36.6172 2.24559 36.2526V31.9055H22.0505V36.2523ZM22.0505 30.7852H2.24559V6.14272H22.0505V30.7852ZM22.0505 5.02316H2.24559V1.90305C2.24559 1.53846 2.5537 1.23639 2.94139 1.23639H21.3547C21.7424 1.23639 22.0505 1.53849 22.0505 1.90305V5.02316Z" fill={color} stroke={color} strokeWidth="0.2"/>
			<Path d="M15.5991 2.56992H8.69715C8.38426 2.56992 8.11965 2.81993 8.11965 3.12994C8.11965 3.4403 8.38463 3.68996 8.69715 3.68996H15.5991C15.912 3.68996 16.1766 3.43994 16.1766 3.12994C16.1766 2.82044 15.919 2.56992 15.5991 2.56992Z" fill={color} stroke={color} strokeWidth="0.2"/>
			<Path d="M12.148 36.2235C13.1801 36.2235 14.0243 35.4156 14.0243 34.4122C14.0243 33.4089 13.1802 32.6009 12.148 32.6009C11.1157 32.6009 10.2716 33.4089 10.2716 34.4122C10.2716 35.4156 11.1157 36.2235 12.148 36.2235ZM12.148 33.721C12.5483 33.721 12.8693 34.0355 12.8693 34.4122C12.8693 34.789 12.5483 35.1035 12.148 35.1035C11.7477 35.1035 11.4267 34.7889 11.4267 34.4122C11.4267 34.0355 11.7477 33.721 12.148 33.721Z" fill={color} stroke={color} strokeWidth="0.2"/>
		</Svg>
    );
}

export default Celphone;