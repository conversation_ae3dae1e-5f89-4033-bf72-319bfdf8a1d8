import React from 'react';
import Svg, { Path } from 'react-native-svg';

import { getSizesByWidth } from './calculator';
  
const Site = (props) => {
    const originalWidth = 33;
    const originalHeight = 33;
    const newWidth = props?.style?.width ? props.style.width : 42;

    const sizes = getSizesByWidth(originalWidth, originalHeight, newWidth);

    return (
        <Svg style={[{width: sizes.width, height: sizes.height}, props.style]} viewBox="0 0 33 33" fill="none" xmlns="http://www.w3.org/2000/svg">
            <Path d="M16.5 22C19.5376 22 22 19.5376 22 16.5C22 13.4624 19.5376 11 16.5 11C13.4624 11 11 13.4624 11 16.5C11 19.5376 13.4624 22 16.5 22Z" stroke="#00467F" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
            <Path d="M22 11.0001V17.8751C22 18.9691 22.4346 20.0183 23.2082 20.7919C23.9818 21.5655 25.031 22.0001 26.125 22.0001C27.219 22.0001 28.2682 21.5655 29.0418 20.7919C29.8154 20.0183 30.25 18.9691 30.25 17.8751V16.5001C30.2498 13.3968 29.1998 10.3848 27.2708 7.95385C25.3417 5.52293 22.647 3.81606 19.6249 3.11078C16.6028 2.40551 13.4309 2.74331 10.6251 4.06925C7.81931 5.3952 5.54456 7.63131 4.17074 10.414C2.79693 13.1967 2.40484 16.3623 3.05824 19.396C3.71165 22.4298 5.37211 25.1533 7.76963 27.1237C10.1672 29.0942 13.1607 30.1956 16.2636 30.249C19.3665 30.3023 22.3962 29.3044 24.86 27.4176" stroke="#00467F" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
        </Svg>

    );
}

export default Site;