import React from 'react';
import Svg, { Mask, Path } from 'react-native-svg';

import { getSizesByWidth } from './calculator';
  
const Hypnobox = (props) => {
    const originalWidth = 42;
    const originalHeight = 30;
    const newWidth = props?.style?.width ? props.style.width : 48;

    const sizes = getSizesByWidth(originalWidth, originalHeight, newWidth);

    return (
        <Svg style={[{width: sizes.width, height: sizes.height}, props.style]} viewBox="0 0 42 30" fill="none" xmlns="http://www.w3.org/2000/svg">
			<Path d="M41.4079 12.6315L36.2122 5.4436C36.2014 5.4291 36.1873 5.41794 36.1761 5.40454C36.1125 5.31079 36.0221 5.23639 35.9094 5.19695L20.976 0.0287387C20.8629 -0.00957956 20.7401 -0.00957956 20.627 0.0287387L5.69361 5.19695C5.55187 5.24643 5.44547 5.35431 5.38632 5.48602L0.104293 12.6267C0.00347395 12.7633 -0.0262881 12.94 0.0239359 13.1018C0.074159 13.264 0.198414 13.3923 0.358757 13.4478L5.33459 15.1699V24.2979C5.33459 24.5252 5.47856 24.7276 5.69285 24.802L20.6262 29.9706C20.6285 29.9717 20.6315 29.971 20.6337 29.9717C20.688 29.99 20.7442 30 20.8007 30C20.8573 30 20.9138 29.99 20.9678 29.9717C20.97 29.9714 20.973 29.9717 20.9752 29.9706L35.9086 24.802C36.1236 24.7272 36.268 24.5249 36.268 24.2979V23.7458L36.2683 15.1379L41.1501 13.4482C41.3097 13.3927 41.4332 13.2651 41.4838 13.104C41.5347 12.9437 41.5065 12.7681 41.4079 12.6316L41.4079 12.6315ZM20.8012 1.09816L34.8384 5.95532L31.2956 7.22618L20.8775 10.8322L13.6877 8.39581L6.70217 5.97761L20.8012 1.09816ZM5.72874 6.81771C5.75329 6.83036 5.77933 6.84189 5.80649 6.85119L20.0923 11.6924L15.6652 17.6161L1.39466 12.6767L5.72874 6.81771ZM6.40136 15.5391L15.6834 18.7519C15.7403 18.7716 15.8002 18.7812 15.8582 18.7812C16.0231 18.7812 16.1819 18.7046 16.2853 18.5673L20.2672 13.2392V28.7174L6.40139 23.9178L6.40136 15.5391ZM21.3338 28.7182V12.9372L25.2111 18.5518C25.3122 18.6991 25.4782 18.782 25.65 18.782C25.7081 18.782 25.7669 18.7724 25.8238 18.7527L35.2011 15.5075V23.9186L21.3338 28.7182ZM25.8584 17.6115L21.816 11.7576L35.6941 6.7818C35.738 6.76618 35.7786 6.74497 35.8162 6.7193L40.1212 12.675L25.8584 17.6115Z" fill="#2D719F"/>
		</Svg>
    );
}

export default Hypnobox;