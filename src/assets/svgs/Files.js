import React from 'react';
import Svg, { Path } from 'react-native-svg';

import { getSizesByWidth } from './calculator';
  
const Files = (props) => {
    const originalWidth = 25;
    const originalHeight = 28;
    const newWidth = props?.style?.width ? props.style.width : 28;
    const color = props?.style?.color ? props.style.color : "#2D719F";

    const sizes = getSizesByWidth(originalWidth, originalHeight, newWidth);

    return (
        <Svg style={[{width: sizes.width, height: sizes.height}, props.style]} viewBox="0 0 25 28" fill="none" xmlns="http://www.w3.org/2000/svg">
            <Path d="M24.3753 3.75001H21.2501V0.625072L20.6252 0H6.87505L6.4312 0.181222L0.181013 6.43141L0 6.87526V23.1251L0.625072 23.7502H3.75V26.8751L4.37508 27.5002H24.3749L25 26.8751L24.9998 4.37508L24.3753 3.75001ZM6.24987 2.13134V6.25008H2.13113L6.24987 2.13134ZM1.24994 22.4998V7.49974H6.87494L7.50002 6.87467V1.24966H19.9999V22.4993L1.24994 22.4998ZM23.75 26.2498H4.99994V23.7499H20.6251L21.2502 23.1248L21.25 4.99999H23.7501L23.75 26.2498Z" fill={color} />
            <Path d="M7.50027 10.6255H16.2501V11.8754H7.50027V10.6255Z" fill={color} />
            <Path d="M5.00005 16.875H16.2501V18.1249H5.00005V16.875Z" fill={color} />
        </Svg>
    );
}

export default Files;