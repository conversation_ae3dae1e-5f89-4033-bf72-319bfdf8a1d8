import React from 'react';
import Svg, { Path } from 'react-native-svg';

import { getSizesByWidth } from './calculator';
  
const PDF = (props) => {
    const originalWidth = 34;
    const originalHeight = 33;
    const newWidth = props?.style?.width ? props.style.width : 40;
    const color =  props?.style?.color ? props.style.color : '#4EA1CC';

    const sizes = getSizesByWidth(originalWidth, originalHeight, newWidth);

    return (
        <Svg style={[{width: sizes.width, height: sizes.height}, props.style]} viewBox="0 0 34 33" fill="none" xmlns="http://www.w3.org/2000/svg">
            <Path d="M12.4276 20.982C12.4276 21.7742 12.1931 22.4532 11.6069 22.9058C11.0207 23.3585 10.3173 23.5848 9.37932 23.5848H8.32415V26.1876H6.68277V18.2661H9.37932C10.3173 18.2661 11.1379 18.4924 11.6069 18.9451C12.0759 19.3977 12.4276 20.0767 12.4276 20.982ZM10.7862 20.982C10.7862 20.6425 10.669 20.3031 10.4345 20.0767C10.2 19.8504 9.84829 19.7372 9.37932 19.7372H8.32415V22.34H9.37932C9.84829 22.34 10.2 22.2268 10.4345 22.0005C10.669 21.661 10.7862 21.3215 10.7862 20.982Z" fill={color}/>
            <Path d="M20.7517 22.2268C20.7517 23.5848 20.4 24.4901 19.6966 25.1691C18.9931 25.8481 17.9379 26.1876 16.531 26.1876H13.7173V18.2661H16.531C17.8207 18.2661 18.7586 18.6056 19.5793 19.2846C20.4 20.0767 20.7517 20.982 20.7517 22.2268ZM19.1104 22.2268C19.1104 21.4347 18.8759 20.8689 18.4069 20.4162C17.9379 19.9636 17.3517 19.7372 16.531 19.7372H15.1242V24.9428H16.2966C18.2897 24.8296 19.1104 24.0374 19.1104 22.2268Z" fill={color}/>
            <Path d="M27.3173 18.2661V19.6241H23.8V21.4347H27.2V22.7927H23.8V26.1876H22.2759V18.2661H27.3173Z" fill={color}/>
            <Path d="M28.7241 17.3609H5.27587C4.92415 17.3609 4.68967 17.1346 4.68967 16.7951V0.612692C4.68967 0.273202 4.92415 0.046875 5.27587 0.046875H19.3448C19.4621 0.046875 19.6966 0.160038 19.8138 0.160038L29.1931 9.32628C29.3104 9.43944 29.3104 9.5526 29.3104 9.77893V16.7951C29.3104 17.0214 29.0759 17.3609 28.7241 17.3609ZM5.86208 16.2292H28.1379V10.0053L19.1104 1.17851H5.86208V16.2292Z" fill={color}/>
            <Path d="M28.7241 32.8642H5.27587C4.92415 32.8642 4.68967 32.6379 4.68967 32.2984V27.7719C4.68967 27.4324 4.92415 27.2061 5.27587 27.2061H28.7241C29.0759 27.2061 29.3104 27.4324 29.3104 27.7719V32.2984C29.3104 32.6379 29.0759 32.8642 28.7241 32.8642ZM5.86208 31.7326H28.1379V28.3377H5.86208V31.7326Z" fill={color}/>
            <Path d="M28.7241 10.3447H19.3448C18.9931 10.3447 18.7586 10.1184 18.7586 9.77893V0.612691C18.7586 0.386364 18.8758 0.160037 19.1103 0.0468738C19.3448 -0.0662896 19.5793 0.0468738 19.6965 0.160037L28.9586 9.21311C29.0758 9.32627 29.1931 9.43944 29.1931 9.66576C29.3103 10.1184 29.0758 10.3447 28.7241 10.3447ZM19.931 9.21311H27.3172L19.931 1.97065V9.21311Z" fill={color}/>
            <Path d="M33.4138 28.3379H0.586207C0.234483 28.3379 0 28.1115 0 27.772V16.682C0 16.3425 0.234483 16.1162 0.586207 16.1162H33.4138C33.7655 16.1162 34 16.3425 34 16.682V27.772C34 28.1115 33.7655 28.3379 33.4138 28.3379ZM1.17241 27.2062H32.8276V17.2478H1.17241V27.2062Z" fill={color}/>
        </Svg>
    );
}

export default PDF;