import React from 'react';
import Svg, { Path } from 'react-native-svg';

import { getSizesByWidth } from './calculator';
  
const Home = (props) => {
    const originalWidth = 34;
    const originalHeight = 38;
    const newWidth = props?.style?.width ? props.style.width : 34;

    const sizes = getSizesByWidth(originalWidth, originalHeight, newWidth);

    return (
        <Svg style={[{width: sizes.width, height: sizes.height}, props.style]} viewBox="0 0 34 38" fill="none" xmlns="http://www.w3.org/2000/svg">
            <Path d="M2 14L17 2.33337L32 14V32.3334C32 33.2174 31.6488 34.0653 31.0237 34.6904C30.3986 35.3155 29.5507 35.6667 28.6667 35.6667H5.33333C4.44928 35.6667 3.60143 35.3155 2.97631 34.6904C2.35119 34.0653 2 33.2174 2 32.3334V14Z" stroke="#4EA1CC" strokeWidth="3.63893" strokeLinecap="round" strokeLinejoin="round"/>
            <Path d="M12.0001 35.6667V19H22.0001V35.6667" stroke="#4EA1CC" strokeWidth="3.63893" strokeLinecap="round" strokeLinejoin="round"/>
        </Svg>
        
    );
}

export default Home;