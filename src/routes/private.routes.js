import {createStackNavigator} from '@react-navigation/stack';
import React from 'react';

import PrivateContacts from '../pages/private/PrivateContacts';
import PrivateMain from '../pages/private/PrivateMain';
import PrivateShifts from '../pages/private/PrivateShifts';

import PrivateNotification from '../pages/private/PrivateNotification';
import PrivateNotifications from '../pages/private/PrivateNotifications';

import GoTo from '../pages/private/GoTo';
import ProvisionalRow from '../pages/private/ProvisionalRow';
import RowCheckin from '../pages/private/RowCheckin';
import Turn from '../pages/private/Turn';
import TurnActive from '../pages/private/TurnActive';
import TurnEnd from '../pages/private/TurnEnd';
import TurnExtra from '../pages/private/TurnExtra';
import WaitTurnExtra from '../pages/private/WaitTurnExtra';

import AboutTrainingStart from '../pages/private/training/AboutTrainingStart';
import TrainingList from '../pages/private/training/TrainingList';
import TrainingPdf from '../pages/private/training/TrainingPdf';
import TrainingQuestion from '../pages/private/training/TrainingQuestion';
import TrainingResultApproved from '../pages/private/training/TrainingResultApproved';
import TrainingResultDisapproved from '../pages/private/training/TrainingResultDisapproved';
import TrainingVideo from '../pages/private/training/TrainingVideo';

import AboutPendency from '../pages/private/Pay/AboutPendency';
import PayListPendency from '../pages/private/Pay/PayListPendency';
import PaySearch from '../pages/private/Pay/PaySearch';

import RhAddress from '../pages/private/RhAddress';
import RhDocuments from '../pages/private/RhDocuments';
import RhList from '../pages/private/RhList';
import RhMyData from '../pages/private/RhMyData';
import RhPassword from '../pages/private/RhPassword';
import RhPersonalData from '../pages/private/RhPersonalData';
import RhPicture from '../pages/private/RhPicture';

import {useAuth} from '../context/auth';
import FilesAboutProduct from '../pages/private/Files/FilesAboutProduct';
import FilesList from '../pages/private/Files/FilesList';
import FilesProducts from '../pages/private/Files/FilesProducts';

import CustomerList from '../pages/private/Schedulings/CustomerList';
import CustomerNew from '../pages/private/Schedulings/CustomerNew';
import SchedulingEdit from '../pages/private/Schedulings/SchedulingEdit';
import SchedulingList from '../pages/private/Schedulings/SchedulingList';
import SchedulingNew from '../pages/private/Schedulings/SchedulingNew';
import SchedulingShow from '../pages/private/Schedulings/SchedulingShow';
import ServiceDone from '../pages/private/Schedulings/ServiceDone';

import MyCustomerData from '../pages/private/Customer/CustomerData';
import MyCustomerEdit from '../pages/private/Customer/CustomerEdit';

import LeadsContent from '../pages/private/Leads/LeadsContent';
import LeadsList from '../pages/private/Leads/LeadsList';

import IAContentCampanha from '../pages/private/IA/IAContentCampanha';
import IAContentTexto from '../pages/private/IA/IAContentTexto';
import IACriandoEstrategia from '../pages/private/IA/IACriandoEstrategia';
import IACriandoTexto from '../pages/private/IA/IACriandoTexto';
import IAEstrategiaCriada from '../pages/private/IA/IAEstrategiaCriada';
import IAList from '../pages/private/IA/IAList';
import IATextoCriado from '../pages/private/IA/IATextoCriado';
import IATextoGA from '../pages/private/IA/IATextoGA';
import IATextoWhats from '../pages/private/IA/IATextoWhats';
import IATiraDuvidas from '../pages/private/IA/IATiraDuvidas';

import ContasMain from '../pages/private/Contas/ContasMain';
import ContasDigitalizarDocumento from '../pages/private/Contas/ContasDigitalizarDocumento';
import ContasAnalisandoDocumento from '../pages/private/Contas/ContasAnalisandoDocumento';
import ContasCriarManualmente from '../pages/private/Contas/ContasCriarManualmente';
import ContasEmPreenchimento from '../pages/private/Contas/ContasEmPreenchimento';
import ContasDadosPessoais from '../pages/private/Contas/ContasDadosPessoais';
import ContasEndereco from '../pages/private/Contas/ContasEndereco';
import ContasDocumento from '../pages/private/Contas/ContasDocumento';
import ContasAutorizacoes from '../pages/private/Contas/ContasAutorizacoes';
import ContasSucesso from '../pages/private/Contas/ContasSucesso';
import ContaPDF from '../pages/private/Contas/ContaPDF';
import NovaFicha from '../pages/private/Contas/NovaFicha';
import ContasEnderecoComercial from '../pages/private/Contas/ContasEnderecoComercial';

import RepasseHome from '../pages/private/Repasse/RepasseHome';
import RepasseInterna from '../pages/private/Repasse/RepasseInterna';
import RepasseListaProcessos from '../pages/private/Repasse/RepasseListaProcessos';
import RepasseListaEtapas from '../pages/private/Repasse/RepasseListaEtapas';

import BankTerms from '../pages/private/Bank/Register/BankTerms';
import BankInitAccount from '../pages/private/Bank/Register/BankInitAccount';
import BankDocuments from '../pages/private/Bank/Register/BankDocuments';
import BankMainData from '../pages/private/Bank/Register/BankMainData';
import BankComplementaryData from '../pages/private/Bank/Register/BankComplementaryData';
import BankAddress from '../pages/private/Bank/Register/BankAddress';
import BankSelfie from '../pages/private/Bank/Register/BankSelfie';
import BankAnalysis from '../pages/private/Bank/Register/BankAnalysis';
import BankHome from '../pages/private/Bank/BankHome';

const Stack = createStackNavigator();

const PrivateRoutes = () => {
  const {user} = useAuth();

  return (
    <Stack.Navigator
      initialRouteName={
        user.status_cadastro === 'aprovado' ? 'PrivateMain' : 'RhList'
      }>
      <Stack.Screen
        name="PrivateMain"
        component={PrivateMain}
        options={{headerShown: false}}
      />
      <Stack.Screen
        name="PrivateShifts"
        component={PrivateShifts}
        options={{headerShown: false}}
      />

      <Stack.Screen
        name="PrivateNotifications"
        component={PrivateNotifications}
        options={{headerShown: false}}
      />
      <Stack.Screen
        name="PrivateNotification"
        component={PrivateNotification}
        options={{headerShown: false}}
      />
      <Stack.Screen
        name="PrivateContacts"
        component={PrivateContacts}
        options={{headerShown: false}}
      />

      <Stack.Screen
        name="GoTo"
        component={GoTo}
        options={{headerShown: false}}
      />

      <Stack.Screen
        name="ProvisionalRow"
        component={ProvisionalRow}
        options={{headerShown: false}}
      />
      <Stack.Screen
        name="WaitTurnExtra"
        component={WaitTurnExtra}
        options={{headerShown: false}}
      />
      <Stack.Screen
        name="Turn"
        component={Turn}
        options={{headerShown: false}}
      />
      <Stack.Screen
        name="TurnExtra"
        component={TurnExtra}
        options={{headerShown: false}}
      />
      <Stack.Screen
        name="TurnActive"
        component={TurnActive}
        options={{headerShown: false, gestureEnabled: false}}
      />
      <Stack.Screen
        name="TurnEnd"
        component={TurnEnd}
        options={{headerShown: false, gestureEnabled: false}}
      />
      <Stack.Screen
        name="RowCheckin"
        component={RowCheckin}
        options={{headerShown: false, gestureEnabled: false}}
      />

      <Stack.Screen
        name="TrainingList"
        component={TrainingList}
        options={{headerShown: false}}
      />
      <Stack.Screen
        name="AboutTrainingStart"
        component={AboutTrainingStart}
        options={{headerShown: false}}
      />
      <Stack.Screen
        name="TrainingVideo"
        component={TrainingVideo}
        options={{headerShown: false}}
      />
      <Stack.Screen
        name="TrainingQuestion"
        component={TrainingQuestion}
        options={{headerShown: false}}
      />
      <Stack.Screen
        name="TrainingResultApproved"
        component={TrainingResultApproved}
        options={{headerShown: false}}
      />
      <Stack.Screen
        name="TrainingResultDisapproved"
        component={TrainingResultDisapproved}
        options={{headerShown: false}}
      />
      <Stack.Screen
        name="TrainingPdf"
        component={TrainingPdf}
        options={{headerShown: false}}
      />

      <Stack.Screen
        name="PaySearch"
        component={PaySearch}
        options={{headerShown: false}}
      />
      <Stack.Screen
        name="PayListPendency"
        component={PayListPendency}
        options={{headerShown: false}}
      />
      <Stack.Screen
        name="AboutPendency"
        component={AboutPendency}
        options={{headerShown: false}}
      />
      <Stack.Screen
        name="RepasseHome"
        component={RepasseHome}
        options={{headerShown: false}}
      />
      <Stack.Screen
        name="RepasseListaProcessos"
        component={RepasseListaProcessos}
        options={{headerShown: false}}
      />
      <Stack.Screen
        name="RepasseListaEtapas"
        component={RepasseListaEtapas}
        options={{headerShown: false}}
      />
      <Stack.Screen
        name="RepasseInterna"
        component={RepasseInterna}
        options={{headerShown: false}}
      />
      <Stack.Screen
        name="FilesList"
        component={FilesList}
        options={{headerShown: false}}
      />
      <Stack.Screen
        name="FilesProducts"
        component={FilesProducts}
        options={{headerShown: false}}
      />
      <Stack.Screen
        name="FilesAboutProduct"
        component={FilesAboutProduct}
        options={{headerShown: false}}
      />

      <Stack.Screen
        name="RegisterDisapproved"
        component={RhList}
        options={{headerShown: false}}
      />
      <Stack.Screen
        name="RhList"
        component={RhList}
        options={{headerShown: false}}
      />

      <Stack.Screen
        name="SchedulingList"
        component={SchedulingList}
        options={{headerShown: false}}
      />
      <Stack.Screen
        name="SchedulingNew"
        component={SchedulingNew}
        options={{headerShown: false}}
      />
      <Stack.Screen
        name="CustomerList"
        component={CustomerList}
        options={{headerShown: false}}
      />
      <Stack.Screen
        name="CustomerNew"
        component={CustomerNew}
        options={{headerShown: false}}
      />
      <Stack.Screen
        name="SchedulingShow"
        component={SchedulingShow}
        options={{headerShown: false}}
      />
      <Stack.Screen
        name="SchedulingEdit"
        component={SchedulingEdit}
        options={{headerShown: false}}
      />
      <Stack.Screen
        name="ServiceDone"
        component={ServiceDone}
        options={{headerShown: false}}
      />
      <Stack.Screen
        name="MyCustomerEdit"
        component={MyCustomerEdit}
        options={{headerShown: false}}
      />
      <Stack.Screen
        name="MyCustomerData"
        component={MyCustomerData}
        options={{headerShown: false}}
      />

      <Stack.Screen
        name="RhMyData"
        component={RhMyData}
        options={{headerShown: false}}
      />
      <Stack.Screen
        name="RhPersonalData"
        component={RhPersonalData}
        options={{headerShown: false}}
      />
      <Stack.Screen
        name="RhAddress"
        component={RhAddress}
        options={{headerShown: false}}
      />
      <Stack.Screen
        name="RhDocuments"
        component={RhDocuments}
        options={{headerShown: false}}
      />
      <Stack.Screen
        name="RhPicture"
        component={RhPicture}
        options={{headerShown: false}}
      />
      <Stack.Screen
        name="RhPassword"
        component={RhPassword}
        options={{headerShown: false}}
      />

      <Stack.Screen
        name="LeadsList"
        component={LeadsList}
        options={{headerShown: false}}
      />
      <Stack.Screen
        name="LeadsContent"
        component={LeadsContent}
        options={{headerShown: false}}
      />

      <Stack.Screen
        name="IAList"
        component={IAList}
        options={{headerShown: false}}
      />
      <Stack.Screen
        name="IAContentCampanha"
        component={IAContentCampanha}
        options={{headerShown: false}}
      />
      <Stack.Screen
        name="IATiraDuvidas"
        component={IATiraDuvidas}
        options={{headerShown: false}}
      />
      <Stack.Screen
        name="IACriandoEstrategia"
        component={IACriandoEstrategia}
        options={{headerShown: false}}
      />
      <Stack.Screen
        name="IAEstrategiaCriada"
        component={IAEstrategiaCriada}
        options={{headerShown: false}}
      />
      <Stack.Screen
        name="IAContentTexto"
        component={IAContentTexto}
        options={{headerShown: false}}
      />
      <Stack.Screen
        name="IACriandoTexto"
        component={IACriandoTexto}
        options={{headerShown: false}}
      />
      <Stack.Screen
        name="IATextoCriado"
        component={IATextoCriado}
        options={{headerShown: false}}
      />
      <Stack.Screen
        name="IATextoWhats"
        component={IATextoWhats}
        options={{headerShown: false}}
      />
      <Stack.Screen
        name="IATextoGA"
        component={IATextoGA}
        options={{headerShown: false}}
      />

      <Stack.Screen
        name="ContasMain"
        component={ContasMain}
        options={{headerShown: false}}
      />
      <Stack.Screen
        name="ContasDigitalizarDocumento"
        component={ContasDigitalizarDocumento}
        options={{headerShown: false}}
      />
      <Stack.Screen
        name="ContasAnalisandoDocumento"
        component={ContasAnalisandoDocumento}
        options={{headerShown: false}}
      />
      <Stack.Screen
        name="ContasCriarManualmente"
        component={ContasCriarManualmente}
        options={{headerShown: false}}
      />
      <Stack.Screen
        name="ContasEmPreenchimento"
        component={ContasEmPreenchimento}
        options={{headerShown: false, gestureEnabled: false}}
      />
      <Stack.Screen
        name="ContasDadosPessoais"
        component={ContasDadosPessoais}
        options={{headerShown: false}}
      />
      <Stack.Screen
        name="ContasEndereco"
        component={ContasEndereco}
        options={{headerShown: false}}
      />
      <Stack.Screen
        name="ContasDocumento"
        component={ContasDocumento}
        options={{headerShown: false}}
      />
      <Stack.Screen
        name="ContasAutorizacoes"
        component={ContasAutorizacoes}
        options={{headerShown: false}}
      />
      <Stack.Screen
        name="ContasSucesso"
        component={ContasSucesso}
        options={{headerShown: false}}
      />
      <Stack.Screen
        name="ContaPDF"
        component={ContaPDF}
        options={{headerShown: false}}
      />
      <Stack.Screen
        name="NovaFicha"
        component={NovaFicha}
        options={{headerShown: false}}
      />
      <Stack.Screen
        name="ContasEnderecoComercial"
        component={ContasEnderecoComercial}
        options={{headerShown: false}}
      />

      <Stack.Screen
        name="BankTerms"
        component={BankTerms}
        options={{headerShown: false}}
      />
      <Stack.Screen
        name="BankInitAccount"
        component={BankInitAccount}
        options={{headerShown: false, animationEnabled: false }}
      />
      <Stack.Screen
        name="BankDocuments"
        component={BankDocuments}
        options={{headerShown: false, gestureEnabled: false}}
      />
      <Stack.Screen
        name="BankMainData"
        component={BankMainData}
        options={{headerShown: false}}
      />
      <Stack.Screen
        name="BankComplementaryData"
        component={BankComplementaryData}
        options={{headerShown: false}}
      />
      <Stack.Screen
        name="BankAddress"
        component={BankAddress}
        options={{headerShown: false}}
      />
      <Stack.Screen
        name="BankSelfie"
        component={BankSelfie}
        options={{headerShown: false}}
      />
      <Stack.Screen
        name="BankAnalysis"
        component={BankAnalysis}
        options={{headerShown: false, gestureEnabled: false, animationEnabled: false}}
      />
      <Stack.Screen
        name="BankHome"
        component={BankHome}
        options={{headerShown: false}}
      />
    </Stack.Navigator>
  );
};

export default PrivateRoutes;
