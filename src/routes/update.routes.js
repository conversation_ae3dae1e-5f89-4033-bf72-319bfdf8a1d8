import React from 'react';
import { createStackNavigator } from '@react-navigation/stack';

import UpdateRequired from '../pages/update/UpdateRequired';

const Stack = createStackNavigator();

const DisconnectedRoutes = () => {
  return (
    <Stack.Navigator initialRouteName="UpdateRequired">
      <Stack.Screen name="UpdateRequired" component={UpdateRequired} options={{headerShown: false}} />
    </Stack.Navigator>
  );
}

export default DisconnectedRoutes;