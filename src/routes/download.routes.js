import React from 'react';
import { createStackNavigator } from '@react-navigation/stack';

import DownloadRequired from '../pages/update/DownloadRequired';

const Stack = createStackNavigator();

const DownloadRoutes = () => {
  return (
    <Stack.Navigator initialRouteName="DownloadRequired">
      <Stack.Screen name="DownloadRequired" component={DownloadRequired} options={{headerShown: false}} />
    </Stack.Navigator>
  );
}

export default DownloadRoutes;