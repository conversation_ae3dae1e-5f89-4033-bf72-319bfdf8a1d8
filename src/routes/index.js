import React, { useEffect, useState } from 'react';
import { View, ActivityIndicator } from 'react-native';

import RNRestart from 'react-native-restart';

import NetInfo from "@react-native-community/netinfo";

import PrivateRoutes from './private.routes';
import PublicRoutes from './public.routes';
import DisconnectedRoutes from './disconnected.routes';
import UpdateRoutes from './update.routes';
import { useAuth } from '../context/auth';
import DownloadRoutes from './download.routes';

const Routes = () => {

  const { signed, updateRequired, downloadFromStoreRequired } = useAuth();
  
  const [lastStatus, setLastStatus] = useState(null);
  const [isConnected, setIsConnected] = useState(null);

  useEffect(() => {
    const unsubscribe = NetInfo.addEventListener((state) => {
      setIsConnected(state.isConnected && state.isInternetReachable);
    });
  
    return () => unsubscribe();
  }, []);

  useEffect(() => {
    setLastStatus(isConnected);
    if(isConnected === true && lastStatus !== null){
      RNRestart.Restart();
    }
  },[isConnected]);

  if(isConnected === null){
    return (
      <View style={{flex: 1, justifyContent: "center", alignItems: "center"}}>
        <ActivityIndicator size="large" color="#00467F" />
      </View>
    );
  } else if(isConnected === true){

    // console.log('updatedrequred', updateRequired);
    if(downloadFromStoreRequired){
      return <DownloadRoutes />;
    } else if(updateRequired) {
      return <UpdateRoutes />;
    } else {
      return signed ? <PrivateRoutes /> : <PublicRoutes />;
    }

  } else {
    return <DisconnectedRoutes />;
  }
};

export default Routes;