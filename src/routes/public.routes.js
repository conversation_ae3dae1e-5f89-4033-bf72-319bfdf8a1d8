import React from 'react';
import { createStackNavigator } from '@react-navigation/stack';

import RegisterMain from '../pages/register/RegisterMain';
import RegisterInit from '../pages/register/RegisterInit';
import RegisterImobiliaria from '../pages/register/RegisterImobiliaria';
import RegisterRegion from '../pages/register/RegisterRegion';
import RegisterName from '../pages/register/RegisterName';
import RegisterEmail from '../pages/register/RegisterEmail';
import RegisterUploadDocument from '../pages/register/RegisterUploadDocument';
import RegisterUploadCreci from '../pages/register/RegisterUploadCreci';
import RegisterUploadProtocol from '../pages/register/RegisterUploadProtocol';
import RegisterThanks from '../pages/register/RegisterThanks';
import RegisterAddress from '../pages/register/RegisterAddress';
import RegisterAddressCity from '../pages/register/RegisterAddressCity';
import RegisterType from '../pages/register/RegisterType';
import RegisterPassword from '../pages/register/RegisterPassword';
import RegisterTShirt from '../pages/register/RegisterTShirt';
import RegisterPicture from '../pages/register/RegisterPicture';
import RegisterManagerCury from '../pages/register/RegisterManagerCury';
import RegisterRealState from '../pages/register/RegisterRealState';
import RegisterCanal from "../pages/register/RegisterCanal";

import RegisterDisapproved from '../pages/register/RegisterDisapproved';
import RegisterAlreadyRegistered from '../pages/register/RegisterAlreadyRegistered';

import AuthPasswordRecovery from '../pages/auth/AuthPasswordRecovery';
import AuthLogin from '../pages/auth/AuthLogin';

const Stack = createStackNavigator();

const PublicRoutes = () => {
  return (
    <Stack.Navigator initialRouteName="RegisterMain">
      <Stack.Screen name="RegisterMain" component={RegisterMain} options={{headerShown: false}} />
      <Stack.Screen name="RegisterInit" component={RegisterInit} options={{headerShown: false}} />
      <Stack.Screen name="RegisterCanal" component={RegisterCanal} options={{headerShown: false}} />
      <Stack.Screen name="RegisterImobiliaria" component={RegisterImobiliaria} options={{headerShown: false}} />
      <Stack.Screen name="RegisterRegion" component={RegisterRegion} options={{headerShown: false}} />
      <Stack.Screen name="RegisterName" component={RegisterName} options={{headerShown: false}} />
      <Stack.Screen name="RegisterEmail" component={RegisterEmail} options={{headerShown: false}} />
      <Stack.Screen name="RegisterUploadDocument" component={RegisterUploadDocument} options={{headerShown: false}} />
      <Stack.Screen name="RegisterUploadCreci" component={RegisterUploadCreci} options={{headerShown: false}} />
      <Stack.Screen name="RegisterUploadProtocol" component={RegisterUploadProtocol} options={{headerShown: false}} />
      <Stack.Screen name="RegisterThanks" component={RegisterThanks} options={{headerShown: false, gestureEnabled: false}} />
      <Stack.Screen name="RegisterAddress" component={RegisterAddress} options={{headerShown: false}} />
      <Stack.Screen name="RegisterAddressCity" component={RegisterAddressCity} options={{headerShown: false}} />
      <Stack.Screen name="RegisterType" component={RegisterType} options={{headerShown: false}} />
      <Stack.Screen name="RegisterPassword" component={RegisterPassword} options={{headerShown: false}} />
      <Stack.Screen name="RegisterTShirt" component={RegisterTShirt} options={{headerShown: false}} />
      <Stack.Screen name="RegisterPicture" component={RegisterPicture} options={{headerShown: false}} />
      <Stack.Screen name="RegisterManagerCury" component={RegisterManagerCury} options={{headerShown: false}} />
      <Stack.Screen name="RegisterRealState" component={RegisterRealState} options={{headerShown: false}} />

      <Stack.Screen name="RegisterDisapproved" component={RegisterDisapproved} options={{headerShown: false}} />
      <Stack.Screen name="RegisterAlreadyRegistered" component={RegisterAlreadyRegistered} options={{headerShown: false}} />

      <Stack.Screen name="AuthPasswordRecovery" component={AuthPasswordRecovery} options={{headerShown: false}} />
      <Stack.Screen name="AuthLogin" component={AuthLogin} options={{headerShown: false}} />
    </Stack.Navigator>
  );
}

export default PublicRoutes;