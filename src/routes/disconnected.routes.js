import React from 'react';
import { createStackNavigator } from '@react-navigation/stack';

import IsDisconnected from '../pages/connection/IsDisconnected';

const Stack = createStackNavigator();

const DisconnectedRoutes = () => {
  return (
    <Stack.Navigator initialRouteName="IsDisconnected">
      <Stack.Screen name="IsDisconnected" component={IsDisconnected} options={{headerShown: false}} />
    </Stack.Navigator>
  );
}

export default DisconnectedRoutes;