import React from 'react';
import { TouchableOpacity, View, Text } from 'react-native';
import styles from './styles';
import Close from '../../assets/svgs/Close';
import mainStyles from '../../mainStyles';

const AlertFooter = (props) => {
    return (
        <View style={styles.container}>
            <TouchableOpacity style={mainStyles.overlayBlue} onPress={() => props.close()} activeOpacity={1}></TouchableOpacity>
            <View style={styles.box}>
                <View>
                    <TouchableOpacity style={styles.btnClose} onPress={() => props.close()}>
                        <Close style={styles.icClose} />
                    </TouchableOpacity>
                    <Text style={styles.text}>{props.text}</Text>
                    <View style={styles.divider}></View>
                    <TouchableOpacity style={props.btnBorder ? [mainStyles.btnBlueNew, styles.btn] : [mainStyles.btnBlueNew, styles.btn]} onPress={() => props.action()}>
                        <Text style={mainStyles.btnTextBlueNew}>{props.btnText}</Text>
                    </TouchableOpacity>
                </View>
            </View>
        </View>
    );
}

export default AlertFooter;