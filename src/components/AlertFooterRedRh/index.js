import React from 'react';
import { TouchableOpacity, View, Text } from 'react-native';
import styles from './styles';
import Close from '../../assets/svgs/Close';
import mainStyles from '../../mainStyles';

const AlertFooterRedRh = (props) => {
    return (
        <View style={styles.container}>
            <TouchableOpacity style={mainStyles.overlayBlue} onPress={() => props.close()} activeOpacity={1}></TouchableOpacity>
            <View style={styles.box}>
                <View>
                    <TouchableOpacity style={styles.btnClose} onPress={() => props.close()}>
                        <Close style={styles.close} />
                    </TouchableOpacity>
                    <Text style={styles.nameTraining}>{props.title}</Text>
                    <View style={styles.divider}></View>
                    <Text style={styles.text}>Se excluir sua conta, <Text style={styles.textAlert}>não será mais possível</Text> {`acessar o aplicativo e nenhuma de suas funcionalidades.\nDeseja mesmo assim solicitar a exclusão?`}</Text>
                    <View style={{flexDirection: "row", justifyContent: "space-between"}}>
                        <TouchableOpacity style={styles.btn} onPress={() => props.close()}>
                            <Text style={styles.btnTextRed}>CANCELAR</Text>
                        </TouchableOpacity>
                        <TouchableOpacity style={styles.btnBorder} onPress={() => props.route()}>
                            <Text style={styles.btnText}>SIM, EXCLUIR</Text>
                        </TouchableOpacity>
                    </View>
                </View>
            </View>
        </View>
    );
}

export default AlertFooterRedRh;