import { StyleSheet } from 'react-native';

const styles = StyleSheet.create({
    container: {
        position: "absolute",
        top: 0,
        bottom: 0,
        left: 0,
        right: 0,
        zIndex: 9999,
        flexDirection: "column",
        justifyContent: "center",
        alignItems: "center"
    },
    box: {
        backgroundColor: "#FFF",
        paddingTop: 20,
        paddingBottom: 20,
        width: "95%",
        paddingLeft: 15,
        paddingRight: 15
    },
    marginBox: {
        marginLeft: 0,
        marginRight: 0,
        width: "100%"
    },
    nameTraining: {
        fontFamily: 'Ubuntu-Regular',
        color: '#00467F',
        letterSpacing: 1,
        fontSize: 16,
        marginBottom: 5
    },
    divider: {
        height: 1,
        backgroundColor: "#DADADA",
        marginTop: 10
    },
    text: {
        fontFamily: 'Ubuntu-Regular',
        color: '#828282',
        letterSpacing: 1,
        fontSize: 15,
        marginTop: 15,
        lineHeight: 19
    },
    textAlert: {
        fontFamily: 'Ubuntu-Medium',
        color: '#FF6542'
    },
    btn: {
        backgroundColor: '#FFF',
        height: 55,
        alignItems: "center",
        justifyContent: "center",
        borderWidth: 1,
        borderColor: "#1A374D",
        marginTop: 30,
        width: "48%"
    },
    btnBorder: {
        height: 55,
        alignItems: "center",
        justifyContent: "center",
        marginTop: 30,
        backgroundColor: "#FF8689",
        width: "48%"
    },
    btnText: {
        fontSize: 13,
        fontFamily: 'Ubuntu-Medium',
        letterSpacing: 1,
        textAlign: "center",
        color: '#FFF'
    },
    btnTextRed: {
        fontSize: 13,
        fontFamily: 'Ubuntu-Medium',
        letterSpacing: 1,
        textAlign: "center",
        color: '#1A374D'
    },
    btnClose: {
        position: "absolute",
        top: -5,
        right: -5,
        zIndex: 999
    },
    close: {
        color: "#2D719F",
        width: 20
    }
});

export default styles;