import { StyleSheet } from 'react-native';

const styles = StyleSheet.create({
    centeredView: {
        flex: 1,
        marginTop: 0,
        backgroundColor: "rgba(13, 23, 28, 0.85)",
        position: "relative",
        padding: 30,
        paddingTop: 120
    },
    button: {
        borderRadius: 50,
        height: 40,
        width: 40,
        justifyContent: "center",
        alignItems: "center"
    },
    boxImg: {
        paddingTop: 50,
        paddingBottom: 50,
        alignItems: 'center',
        justifyContent: 'center'
    },
    buttonClose: {
        backgroundColor: "#2D719F",
        position: "absolute",
        top: 60,
        right: 25,
        borderRadius: 0
    },
    textStyle: {
        color: "white",
        fontSize: 16
    },
    modalTextTitle: {
        fontFamily: "Ubuntu-Regular",
        marginBottom: 15,
        color: "#FFF",
        letterSpacing: 1,
        fontSize: 12,
        lineHeight: 18
    },
    img: {
        resizeMode: "cover"
    },
    boxBtns: {
        flexDirection: "row",
        justifyContent: "space-between",
        marginTop: 10
    },
    btnAct: {
        borderWidth: 1,
        borderColor: "#FFF",
        flexDirection: "row",
        justifyContent: "center",
        alignItems: "center",
        padding: 10,
        width: '48%',
        height: 50
    },
    textBtnAct: {
        fontFamily: "Ubuntu-Regular",
        color: "#FFF",
        letterSpacing: 1,
        fontSize: 12,
        marginLeft: 5
    },
    fileInfoText: {
        marginTop: 30,
        fontFamily: "Ubuntu-Regular",
        fontSize: 13,
        textAlign: "center",
        paddingLeft: 15,
        paddingRight: 15,
        color: "#FFF",
        letterSpacing: 1,
        lineHeight: 18
    }
});

export default styles;