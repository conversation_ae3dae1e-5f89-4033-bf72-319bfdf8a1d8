import React, { useState } from 'react';
import { TouchableOpacity, View, Text, Modal, useWindowDimensions, Image, Linking, Alert, Platform, ActivityIndicator, PermissionsAndroid } from 'react-native';
import styles from './styles';
import ShareIcon from '../../assets/svgs/Share';
import ImageIcon from '../../assets/svgs/Image';
import mainStyles from '../../mainStyles';

import Share from 'react-native-share';

import Download from '../../assets/svgs/Download';
import ReactNativeBlobUtil from 'react-native-blob-util';
import XLS from '../../assets/svgs/XLS';
import PDF from '../../assets/svgs/PDF';
import YT from '../../assets/svgs/YT';
import { CameraRoll } from '@react-native-camera-roll/camera-roll';
import Video from 'react-native-video';
import ImageLoad from 'react-native-image-placeholder';
import DeviceInfo from "react-native-device-info";

const FileDetails = ({close, file}) => {
    const [loading, setLoading] = useState(false);
    const [contentLoading, setContentLoading] = useState(true);
    const [contentError, setContentError] = useState(false);

    const contentWidth = useWindowDimensions().width;
    const contentHeight = useWindowDimensions().height;
      
    const shareFile = file => {
        setLoading(true);
        let dirs = ReactNativeBlobUtil.fs.dirs;

        ReactNativeBlobUtil.config({
            fileCache: true,
            path: dirs.DocumentDir + `/${file.filename}.${file.extension}`
        })
        .fetch('GET', file.url_download)
        .then(async res => {
            setLoading(false);
            let filePath = res.path();            
            await Share.open({
                type: file.mimetype,
                url: Platform.OS === 'ios' ? filePath : 'file://' + filePath
            }).catch(err => console.log(err));
            ReactNativeBlobUtil.fs.unlink(filePath);
        });
    }

    const hasAndroidPermission = async () => {
        let deviceVersion = DeviceInfo.getSystemVersion();
        console.log(deviceVersion);

        let granted = PermissionsAndroid.RESULTS.DENIED;
        if(deviceVersion >= 13){
            granted = PermissionsAndroid.RESULTS.GRANTED;
        }else{
            granted = await PermissionsAndroid.request( PermissionsAndroid.PERMISSIONS.WRITE_EXTERNAL_STORAGE );
        }

        return granted === 'granted';
    }

    const downloadFile = async file => {
        if(!'jpg,jpeg,png,gif'.includes(file.extension)){
            Linking.openURL(file.url_download);
            return;
        }

        setLoading(true);

        if (!await hasAndroidPermission()) {
            Alert.alert('Permissão negada', 'Você precisa dar permissão para o app poder salvar imagens em seu dispositivo.');
            setLoading(false);
            return;
        }

        let dirs = ReactNativeBlobUtil.fs.dirs;

        ReactNativeBlobUtil.config({
            path: dirs.DocumentDir + `/${file.filename}.${file.extension}`
        })
        .fetch('GET', file.url_download)
        .then(async res => {
            setLoading(false);
            let filePath = res.path();
            
            CameraRoll.save(`${Platform.OS === 'ios' ? '' : 'file://'}`+filePath, { type: 'photo', album: 'App Cury' }).then(res => {
                Alert.alert('Imagem salva na galeria', 'Essa imagem agora está disponível em sua galeria', [
                    {
                        text: "Voltar",
                        onPress: () => close()
                    }
                ]);
            }).catch(err => {
                Alert.alert('A imagem não pode ser salva', JSON.stringify(err) );
            });            
        }).catch(err => {
            console.log(err, 'error');
            Alert.alert('A imagem não pode ser salva', 'Não conseguimos baixar esta imagem. Por favor, tente novamente.');
        });
    }

    return (
        <Modal
            animationType="slide"
            transparent={true}
            >
            <View style={styles.centeredView}>
                <TouchableOpacity
                    style={[styles.button, styles.buttonClose]}
                    onPress={() => close()}
                    >
                    <Text style={styles.textStyle}>X</Text>
                </TouchableOpacity>
                <View style={styles.modalView}>
                    <Text style={styles.modalTextTitle}>{file.filename}.{file.extension}</Text>
                    <View style={styles.boxImg}>
                        {'jpg,jpeg,gif,png'.includes(file.extension) &&
                            <ImageLoad 
                                source={{ uri: file.url_preview }}
                                loadingStyle={{ size: 'large', color: 'gray' }}
                                style={{ width: contentWidth * 0.8, height: contentHeight * 0.4 }}
                                customImagePlaceholderDefaultStyle={{ display: 'none' }}
                                resizeMethod="resize"
                                resizeMode="contain"
                            />
                        }
                        {'xls,xlsx'.includes(file.extension) &&
                            <XLS style={{ width: 220, color: '#FFF'}} />
                        }
                        {'pdf'.includes(file.extension) &&
                            <PDF style={{ width: 220, color: '#FFF'}} />
                        }
                        {contentLoading && !contentError && 'mp4,ogg'.includes(file.extension) &&
                            <View style={{ flex: 1, justifyContent: "center", alignItems: "center", marginTop: 100 }}>
                                <ActivityIndicator size="large" color="#FFF" />
                            </View>
                        }
                        {'mp4,ogg'.includes(file.extension) && !contentError &&
                            <Video
                                resizeMode={"contain"}
                                source={{ uri: file.url_preview }}
                                style={{ width:contentWidth * 0.8, height: contentHeight * 0.4 }}
                                onReadyForDisplay={() => setContentLoading(false)}
                                onError={() => setContentError(true)}
                                controls={true}
                            />
                        }
                        {'mp4,ogg'.includes(file.extension) && contentError &&
                            <YT style={{ width: 220, color: '#FFF'}} />
                        }
                        {!'jpg,jpeg,gif,png,xls,xlsx,pdf,mp4'.includes(file.extension) &&
                            <ImageIcon style={{ width: 220, color: "#FFF" }} />
                        }
                        {(!'jpg,jpeg,gif,png,mp4,ogg'.includes(file.extension) || contentError) &&
                            <Text style={styles.fileInfoText}>Não é possível visualizar este arquivo no aplicativo, mas é possível baixar ou compartilhar usando os botões abaixo.</Text>
                        }
                    </View>
                    {loading &&
                        <Text style={styles.fileInfoText}>Carregando...</Text>
                    }
                    {!loading &&
                        <View style={styles.boxBtns}>
                            {Platform.OS === 'android' &&
                                <TouchableOpacity style={styles.btnAct} onPress={() => downloadFile(file)}>
                                    <Download />
                                    <Text style={styles.textBtnAct}>BAIXAR</Text>
                                </TouchableOpacity>
                            }
                            <TouchableOpacity style={styles.btnAct} onPress={() => shareFile(file)}>
                                <ShareIcon style={{ width: 20}} />
                                <Text style={styles.textBtnAct}>COMPARTILHAR</Text>
                            </TouchableOpacity>
                        </View>
                    }
                    
                </View>
            </View>
        </Modal>
    );
}

export default FileDetails;