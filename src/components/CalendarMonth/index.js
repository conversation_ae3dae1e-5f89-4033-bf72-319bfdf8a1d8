import moment from 'moment';
import React, { useEffect, useRef, useState } from 'react';
import { FlatList, Text, TouchableOpacity, View } from 'react-native';
import ArrowLeft from '../../assets/svgs/ArrowLeft';
import ArrowRight from '../../assets/svgs/ArrowRight';

import styles from './styles';

const CalendarMonth = ({selectedDate, setSelectedDate, setCalendar, minDate}) => {

    const prev = () => {
        let newDate = moment(selectedDate).subtract(1,'months').endOf('month').format('YYYY-MM-DD');
        setSelectedDate(newDate);
    }

    const next = () => {
        let newDate = moment(selectedDate).add(1,'months').startOf('month').format('YYYY-MM-DD');
        setSelectedDate(newDate);
    }

    const prevIsEnabled = () => {
        if(!minDate) return true;

        let selectedMonth = moment(selectedDate).format('YYYY-MM');
        let minMonth = moment(minDate).format('YYYY-MM');

        if(moment(selectedMonth).isSameOrBefore(moment(minMonth))){
            return false;
        }

        return true;
    }

    return (
        <View style={styles.buttonDate}>
            <TouchableOpacity style={styles.btnArrow} onPress={prev} disabled={!prevIsEnabled()}>
                <ArrowLeft style={{ width: 10, color: "#FFF", opacity: prevIsEnabled() ? 1 : 0}} />
            </TouchableOpacity>
            <TouchableOpacity style={styles.btnMonth} onPress={() => setCalendar(true)}>
                <Text style={styles.btnTextButtonDate}>{moment(selectedDate).format('MMMM')} {moment(selectedDate).format('YYYY')}</Text>
            </TouchableOpacity>
            <TouchableOpacity style={styles.btnArrow} onPress={next}>
                <ArrowRight style={{ width: 10, color: "#FFF"}} />
            </TouchableOpacity>
        </View>
    );
}
export default CalendarMonth;