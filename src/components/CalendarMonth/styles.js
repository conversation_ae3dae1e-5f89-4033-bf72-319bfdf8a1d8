import { StyleSheet } from 'react-native';

const styles = StyleSheet.create({
    buttonDate: {
        backgroundColor: "#2D719F",
        height: 50,
        width: '100%',
        alignItems: "center",
        justifyContent: "space-between",
        flexDirection: "row",
        
    },
    btnMonth: {
        flex: 1,
        justifyContent: "center",
        alignItems: "center",
        height: "100%"
    },
    btnArrow: {
        paddingHorizontal: 50,
        justifyContent: "center",
        alignItems: "center",
        height: "100%"
    },
    btnTextButtonDate: {
        fontFamily: "Ubuntu-Regular",
        color: "#FFF",
        fontSize: 15,
        letterSpacing: 0.46,
        textTransform: "uppercase"
    }
});

export default styles;