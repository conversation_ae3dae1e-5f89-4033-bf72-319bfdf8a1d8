import React, { useEffect, useState } from 'react';
import { View, Image } from 'react-native';
import { SvgXml } from 'react-native-svg';

const IconRenderer = ({ imagem_url, fill, titulo }) => {
  const isSvg = imagem_url.endsWith('.svg');
  const [svgContent, setSvgContent] = useState(null);

  useEffect(() => {
    if (isSvg) {
      const fetchSvg = async () => {
        try {
          const response = await fetch(imagem_url);
          let svgText = await response.text();

          if (fill && titulo === 'Leads') {
            svgText = svgText.replace(/fill=".*?"/g, `fill="#fff"`);
          }

          setSvgContent(svgText);
        } catch (error) {
          console.error('Erro ao carregar o SVG:', error);
        }
      };

      fetchSvg();
    }
  }, [imagem_url, isSvg, fill]);

  return (
    <View>
      {isSvg ? (
        svgContent ? (
          <SvgXml xml={svgContent} width="45" height="45" />
        ) : null
      ) : (
        <Image
          source={{ uri: imagem_url }}
          style={{ width: 45, height: 45 }}
        />
      )}
    </View>
  );
};

export default IconRenderer;