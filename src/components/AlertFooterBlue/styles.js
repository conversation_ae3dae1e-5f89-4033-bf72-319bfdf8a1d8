import { StyleSheet } from 'react-native';

const styles = StyleSheet.create({
    container: {
        position: "absolute",
        top: 0,
        bottom: 0,
        left: 0,
        right: 0,
        zIndex: 9999
    },
    box: {
        backgroundColor: "#4EA1CC",
        paddingTop: 40,
        paddingBottom: 40,
        position: "absolute",
        bottom: 0,
        left: 0,
        right: 0,
        zIndex: 999,
        borderTopLeftRadius: 20,
        borderTopRightRadius: 20,
    },
    nameTraining: {
        fontFamily: 'Ubuntu-Regular',
        color: '#FFF',
        letterSpacing: 0.8,
        fontSize: 12,
        marginBottom: 5
    },
    text: {
        fontFamily: 'Ubuntu-Bold',
        color: '#FFF',
        letterSpacing: 0.8,
        fontSize: 20,
    },
    btn: {
        backgroundColor: '#00467F',
        height: 46,
        alignItems: "center",
        justifyContent: "center",
        borderRadius: 3,
        marginTop: 30,
        width: "45%"
    },
    btnBorder: {
        height: 46,
        alignItems: "center",
        justifyContent: "center",
        borderRadius: 3,
        marginTop: 30,
        borderColor: "#00467F",
        borderWidth: 1,
        width: "45%"
    },
    btnText: {
        fontSize: 14,
        fontFamily: 'Ubuntu-Medium',
        letterSpacing: 1,
        textAlign: "center",
        color: '#F2F2F2'
    },
    textBlue: {
        color: "#00467F"
    },
    btnClose: {
        position: "absolute",
        width: 40,
        height: 40,
        alignItems: "center",
        justifyContent: "center",
        top: -60,
        right: 0,
        backgroundColor: '#00467F',
        borderRadius: 50
    }
});

export default styles;