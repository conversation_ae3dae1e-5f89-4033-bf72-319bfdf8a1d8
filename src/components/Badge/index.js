import React from 'react';
import { TouchableOpacity, View, Text, Image } from 'react-native';
import styles from './styles';
import Close from '../../assets/svgs/Close';
import QrCode from '../../assets/svgs/QrCode';
import mainStyles from '../../mainStyles';

const Badge = (props) => {
    return (
        <View style={styles.container}>
            <TouchableOpacity style={styles.btnClose} onPress={() => props.close()}>
                <Close />
            </TouchableOpacity>
            <View style={styles.dFlex}>
                <View style={styles.box}>                    
                    <View style={styles.boxCenter}>
                        <Image
                            style={styles.logo}
                            source={require('../../assets/svgs/logo.png')}
                        />
                        <Image
                            style={styles.profile}
                            source={require('../../assets/svgs/ProfileBig.png')}
                        />
                        <Text style={styles.name}><PERSON>ires Santos</Text>
                        <Text style={[mainStyles.textBlue, {marginTop: 3 }]}>CRECI: <Text style={mainStyles.textBlueLight}>12345-6</Text></Text>
                        <Text style={[mainStyles.textBlue, {marginTop: 3 }]}>VALIDADE: <Text style={mainStyles.textBlueLight}>12/12/2022</Text></Text>
                        <Text style={[mainStyles.textBlue, {marginTop: 3 }]}>GERENTE: <Text style={mainStyles.textBlueLight}>André Cruz</Text></Text>
                        <QrCode style={styles.qrCode} />
                    </View>
                </View>
                <TouchableOpacity style={[styles.btn, styles.btnWhite]}>
                    <Text style={[styles.textBtn, styles.textBtnWhite]}>Solicitar 2ª via</Text>
                </TouchableOpacity>
                {/* <TouchableOpacity style={[styles.btn, styles.btnGray]}>
                    <Text style={[styles.textBtn, styles.textBtnGray]}>2ª via solicitada</Text>
                </TouchableOpacity> */}
                {/* <Text style={styles.textAlert}>Seu crachá está pronto, favor retirar na Gestão de Autônomos!</Text>
                <TouchableOpacity style={[styles.btn, styles.btnWhite]}>
                    <Text style={[styles.textBtn, styles.textBtnWhite]}>Crachá recebido</Text>
                </TouchableOpacity> */}
                {/* <Text style={styles.textAlert}>Obrigado</Text> */}
            </View>
        </View>
    );
}

export default Badge;