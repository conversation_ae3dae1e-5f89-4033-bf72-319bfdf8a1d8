import { StyleSheet } from 'react-native';

const styles = StyleSheet.create({
    container: {
        position: "absolute",
        top: 0,
        bottom: 0,
        left: 0,
        right: 0,
        zIndex: 9,
        backgroundColor: "rgba(0, 0, 0, 0.8)"
    },
    dFlex: {
        flexDirection: "column",
        alignItems: "center",
        top: 100
    },
    box: {
        backgroundColor: "#FFF",
        paddingTop: 10,
        paddingBottom: 40,
        zIndex: 999,
        borderRadius: 20,
        width: "90%",
        marginBottom: 15
    },
    name: {
        fontFamily: 'Ubuntu-Regular',
        color: '#00467F',
        letterSpacing: 1.58,
        fontSize: 20,
        marginTop: 10,
        marginBottom: 15,
        textTransform: "uppercase",
        textAlign: "center",
        width: 270,
        justifyContent: "center"
    },
    btnClose: {
        position: "absolute",
        width: 60,
        height: 60,
        alignItems: "center",
        justifyContent: "center",
        top: 50,
        right: 10,
    },
    boxCenter: {
        justifyContent: "center",
        alignItems: "center",
        flexDirection: "column"
    },
    logo: {
        width: 160,
        height: 60,
        resizeMode: "contain",
        marginBottom: 10
    },
    profile: {
        width: 190,
        height: 190,
        resizeMode: "cover"
    },
    qrCode: {
        marginTop: 15
    },
    btn: {        
        width: 230,
        height: 56,        
        justifyContent: "center",
        alignItems: "center",
        borderRadius: 5
    },
    btnWhite: {
        backgroundColor: "#FFF",
    },
    textBtn: {
        fontFamily: "Roboto-Medium",
        fontSize: 19,
        letterSpacing: 1.58,
        textTransform: "uppercase"
    },
    textBtnWhite: {
        color: "#00467F"
    },
    btnGray: {
        backgroundColor: "#979797",
    },
    textBtnGray: {
        color: "#FFF"
    },
    textAlert: {
        color: "#FFF",
        fontFamily: "Roboto-Medium",
        fontSize: 19,
        letterSpacing: 1.58,
        textTransform: "uppercase",
        textAlign: "center",
        maxWidth: 290,
        marginBottom: 15
    }
});

export default styles;