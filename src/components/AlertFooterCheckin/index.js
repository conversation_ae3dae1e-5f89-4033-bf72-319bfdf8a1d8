import React from 'react';
import { TouchableOpacity, View, Text } from 'react-native';
import styles from './styles';
import Close from '../../assets/svgs/Close';
import mainStyles from '../../mainStyles';

const AlertFooterCheckin = (props) => {
    return (
        <View style={styles.container}>
            <TouchableOpacity style={mainStyles.overlayBlue} onPress={() => props.close()} activeOpacity={1}></TouchableOpacity>
            <View style={styles.box}>
                <View>
                    <TouchableOpacity style={styles.btnClose} onPress={() => props.close()}>
                        <Close style={styles.icClose} />
                    </TouchableOpacity>
                    <Text style={styles.title}>Você está no plantão do produto:</Text>
                    <Text style={styles.product}>{props.product}</Text>
                    <View style={styles.divider}></View>
                    <Text style={styles.confirm}>Confirmar Check In?</Text>
                    <TouchableOpacity style={[mainStyles.btnBlueNew, styles.btn]} onPress={() => props.action()}>
                        <Text style={mainStyles.btnTextBlueNew}>FAZER CHECK IN</Text>
                    </TouchableOpacity>
                </View>
            </View>
        </View>
    );
}

export default AlertFooterCheckin;