import React from 'react';
import { TouchableOpacity, View, Text } from 'react-native';
import styles from './styles';
import Close from '../../assets/svgs/Close';
import mainStyles from '../../mainStyles';

const AlertFooterRed = (props) => {
    return (
        <View style={styles.container}>
            <TouchableOpacity style={mainStyles.overlay} onPress={() => props.close()} activeOpacity={1}></TouchableOpacity>
            <View style={styles.box}>
                <View style={mainStyles.container}>
                    <TouchableOpacity style={styles.btnClose} onPress={() => props.close()}>
                        <Close />
                    </TouchableOpacity>
                    <Text style={styles.nameTraining}>{props.title}</Text>
                    <Text style={styles.text}>Se você sair perderá todo o progresso, deseja sair?</Text>
                    <View style={{flexDirection: "row", justifyContent: "space-between"}}>
                        <TouchableOpacity style={styles.btn} onPress={() => props.close()}>
                            <Text style={styles.btnTextRed}>NÃO</Text>
                        </TouchableOpacity>
                        <TouchableOpacity style={styles.btnBorder} onPress={() => props.route()}>
                            <Text style={styles.btnText}>SIM</Text>
                        </TouchableOpacity>
                    </View>
                </View>
            </View>
        </View>
    );
}

export default AlertFooterRed;