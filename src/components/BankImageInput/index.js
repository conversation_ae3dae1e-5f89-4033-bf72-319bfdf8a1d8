import React, { useEffect, useState } from 'react';
import { TouchableOpacity, View, Text, Image, Alert, ActivityIndicator } from 'react-native';
import styles from './styles';
import mainStyles from '../../mainStyles';
import ImageCropPicker from 'react-native-image-crop-picker';
import PictureBank from '../../assets/svgs/PictureBank';
import api from '../../services/api';
import { requestCameraPermission } from '../../useful/permissions';
import { useBank } from '../../context/bank';

const BankImageInput = ({type, label}) => {
    const key = `bank_document_${type}`;

    const [loading, setLoading] = useState(true);
    const [imagePath, setImagePath] = useState(null);

    const { bankAccount, setBankAccount } = useBank();

    useEffect(() => {
        getImagePath();
    }, [])

    const getImagePath = () => {
        setLoading(true);
        const storedPath = bankAccount[key] ?? null;
        setImagePath(storedPath);
        setLoading(false);
    }

    const openUploadPicture = () => {
        let alertProperties;
        if(Platform.OS === 'ios'){
            alertProperties = [
                { text: "Tirar foto", onPress: () => openCamera() },
                { text: "Galeria", onPress: () => openImageLibrary() },
                { text: "Fechar" }
            ];
        } else {
            alertProperties = [
                { text: "Fechar" },
                { text: "Galeria", onPress: () => openImageLibrary() },
                { text: "Tirar foto", onPress: () => openCamera() }
            ];
        }
        Alert.alert(
            "Enviar foto",
            "Como deseja enviar sua foto?",
            alertProperties,
            { cancelable: false }
        );
        
    }
    
    const openImageLibrary = () => {
        setLoading(true);

        ImageCropPicker.openPicker({
            width: 500,
            height: 500,
            cropping: false
        }).then(image => {
            saveImage(image);
        }).catch(() => {
            setLoading(false);
        });
    }

    const openCamera = async () => {
        setLoading(true);

        const hasPermission = await requestCameraPermission();
        if(hasPermission){
            ImageCropPicker.openCamera({
                width: 500,
                height: 500,
                cropping: false
            }).then(image => {
                saveImage(image);
            }).catch(() => {
                setLoading(false);
            });
        }
    }

    const saveImage = async image => {
        console.log('image');
        console.log(image);
        
        const path = image.path;
        const data = new FormData;

        console.log(type);
        data.append(type, {
            uri: path,
            type: image.mime,
            name: image?.filename ?? 'image.jpg'
        });

        await api.post('/bank-account/user/upload', data).then(() => {
                setImagePath(path);
                setBankAccount(prev => {
                    let newBankAccount = { ...prev };
                    newBankAccount[key] = path;
                    return newBankAccount;
                });
            }).catch(error => {
                Alert.alert('Erro ao enviar imagem', error?.response?.data?.message ?? '');
            }).finally(() => setLoading(false));
    }

    const handleImageError = () => {
        setImagePath(null);
        setBankAccount(prev => {
            const newBankAccount = { ...prev };
            delete newBankAccount[key];
            return newBankAccount;
        });
    };

    return (
        <View>
            <TouchableOpacity style={styles.btnUpload} onPress={() => openUploadPicture()}>
                {loading &&
                    <ActivityIndicator size="small" color="#CCC" />
                }
                {!loading && imagePath &&
                    <Image 
                        source={{ uri: imagePath }}
                        style={styles.image} 
                        onError={handleImageError}
                    />
                }
                {!loading && ! imagePath &&
                    <PictureBank />
                }
            </TouchableOpacity>
            {label &&
                <Text style={[mainStyles.label, styles.labelSmaller]}>{label}</Text>
            }
        </View>
    )
}

export default BankImageInput;