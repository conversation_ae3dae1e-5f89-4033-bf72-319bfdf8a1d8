import React, { useState, useEffect } from 'react';
import { TouchableOpacity, View, Text, Linking, Alert, ActivityIndicator } from 'react-native';
import styles from './styles';
import Close from '../../assets/svgs/Close';
import mainStyles from '../../mainStyles';

import { ScrollView } from 'react-native-gesture-handler';

const LightboxSurname = (props) => {
    const [selectedNickname, setSelectedNickname] = useState('');

    const updateNickname = () => {
        props.updateNickname(selectedNickname);
        props.close();
    }

    return (
        <View style={styles.container}>
            <TouchableOpacity style={mainStyles.overlayBlack} onPress={() => props.close()}></TouchableOpacity>
            <View style={styles.content}>
                <TouchableOpacity style={styles.btnClose} onPress={() => props.close()}>
                    <Close />
                </TouchableOpacity>
                <Text style={styles.title}><Text style={styles.titleBold}>Sugestões de apelidos disponíveis:</Text></Text>
                <ScrollView showsVerticalScrollIndicator={false}>
                    {!props.gettingNicknames && props.nicknames.map((nickname, index) => (
                        <View key={index} style={styles.boxMargin}>
                            <TouchableOpacity 
                                onPress={() => setSelectedNickname(nickname)}
                                style={[styles.boxBoxShadow, selectedNickname === nickname ? styles.boxSelected : null]}
                            >
                                <Text style={[styles.textSurname, selectedNickname === nickname ? styles.textSelected : null]}>{nickname}</Text>
                            </TouchableOpacity>
                        </View>
                    ))}
                    {props.gettingNicknames &&
                        <View style={{flex: 1, justifyContent: "center", alignItems: "center"}}>
                            <ActivityIndicator size="large" color="#00467F" />
                        </View>
                    }
                </ScrollView>
                <TouchableOpacity style={mainStyles.btnCenterBlue} onPress={updateNickname}>
                    <Text style={[mainStyles.btnTextCenterBlue, styles.textBtn]}>SELECIONAR</Text>
                </TouchableOpacity>
            </View>
        </View>
    );
}

export default LightboxSurname;