import React, { useState, useEffect } from 'react';
import { TouchableOpacity, View, Text, Linking, ScrollView, Alert, ActivityIndicator } from 'react-native';
import styles from './styles';
import Close from '../../assets/svgs/Close';
import mainStyles from '../../mainStyles';

const LightboxWhats = (props) => {

    const [loading, setLoading] = useState(true);

    return (
        <View style={styles.container}>
            <TouchableOpacity style={mainStyles.overlay} onPress={() => props.close()} activeOpacity={1}></TouchableOpacity>
            <View style={styles.content}>
                <TouchableOpacity style={styles.btnClose} onPress={() => props.close()}>
                    <Close style={styles.close} />
                </TouchableOpacity>
                <Text style={styles.nameTraining}>Atenção</Text>
                <View style={styles.divider}></View>
                <Text style={styles.title}>Este canal é exclusivo para tratar dos aplicativos <Text style={styles.titleBold}>Cury Corretor e Cury Gestor.</Text> {`\n\nPara questões relacionadas a:\n • SalesForce\n • Hypnobox\n • Gestão de autônomos\n • E-mail Corporativo \n • Agilitas \n\nFavor contatar o seu gestor.`}</Text>
                <TouchableOpacity style={[mainStyles.btnBlueLight, styles.btnWhats]} onPress={() => Linking.openURL('https://wa.me/5511973507272')}>
                    <Text style={mainStyles.btnTextCenterBlueLight}>SEGUIR</Text>
                </TouchableOpacity>
                <TouchableOpacity style={mainStyles.btnOutlineBlue} onPress={() => props.close()}>
                    <Text style={[mainStyles.btnTextOutlineBlue, styles.textBtn]}>VOLTAR</Text>
                </TouchableOpacity>
            </View>
        </View>
    );
}

export default LightboxWhats;