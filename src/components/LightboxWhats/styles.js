import { StyleSheet, Dimensions } from 'react-native';

const windowWidth = Dimensions.get('window').width;
const windowHeight = Dimensions.get('window').height;

const styles = StyleSheet.create({
    container: {
        position: "absolute",
        top: 0,
        bottom: 0,
        left: 0,
        right: 0,
        zIndex: 999999,
        flexDirection: "column",
        justifyContent: "center",
        alignItems: "center"
    },
    content: {
        backgroundColor: "#FFF",
        width: '90%',
        shadowColor: "#000",
        shadowOffset: {
            width: 0,
            height: 9,
        },
        shadowOpacity: 0.1,
        shadowRadius: 11.95,
        elevation: 12,
        paddingTop: 40,
        paddingBottom: 40,
        paddingLeft: 20,
        paddingRight: 20
    },
    title: {
        fontFamily: "Ubuntu-Regular",
        fontSize: 15,
        letterSpacing: 1,
        lineHeight: 19,
        color: "#00467F",
        marginBottom: 25,
        marginTop: 15
    },
    titleBold: {
        fontFamily: "Ubuntu-Bold",
    },
    btnClose: {
        position: "absolute",
        top: 10,
        right: 10,
        zIndex: 999
    },
    close: {
        color: "#2D719F",
        width: 20
    },
    btnWhats: {
        marginBottom: 15
    },
    nameTraining: {
        fontFamily: 'Ubuntu-Regular',
        color: '#00467F',
        letterSpacing: 1,
        fontSize: 16,
        marginBottom: 5,
        textTransform: "uppercase"
    },
    divider: {
        height: 1,
        backgroundColor: "#DADADA",
        marginTop: 10
    },
});

export default styles;