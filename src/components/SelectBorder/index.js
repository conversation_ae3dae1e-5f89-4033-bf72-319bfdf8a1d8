import React from 'react';
import RNPickerSelect from 'react-native-picker-select';
import ArrowDownGrey from '../../assets/svgs/ArrowDownGrey';
import styles from './styles';

const SelectBorder = props => {
  const propStyles = props.styles ?? {};
  const disabledStyles = props?.disabled === true ? { backgroundColor: "#eee" } : {};

  return (
    <RNPickerSelect
      disabled={props?.disabled === true}
      onValueChange={value => props.onChange(value)}
      textInputProps={props.textInputProps}
      style={{
        ...styles,
        ...propStyles,
        iconContainer: {
          top: 21,
          right: 20,
        },
        inputAndroid: {
          ...styles.inputAndroid,
          ...disabledStyles,
          borderBottomWidth: props?.removeBorder
            ? 0
            : styles.inputAndroid.borderBottomWidth,
        },
        inputIOS: {
          ...styles.inputIOS,
          ...disabledStyles,
          borderBottomWidth: props?.removeBorder
            ? 0
            : styles.inputIOS.borderBottomWidth,
        },
      }}
      value={props.value}
      useNativeAndroidPickerStyle={false}
      fixAndroidTouchableBug={true}
      placeholder={{label: props.placeholder, value: ''}}
      Icon={() => {
        return <ArrowDownGrey />;
      }}
      items={props.options ? props.options : []}
    />
  );
};

export default SelectBorder;
