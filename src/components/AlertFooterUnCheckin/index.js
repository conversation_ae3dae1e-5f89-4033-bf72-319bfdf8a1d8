import React from 'react';
import { TouchableOpacity, View, Text } from 'react-native';
import styles from './styles';
import Close from '../../assets/svgs/Close';
import mainStyles from '../../mainStyles';

const AlertFooterUnCheckin = (props) => {
    return (
        <View style={styles.container}>
            <TouchableOpacity style={mainStyles.overlayBlue} onPress={() => props.close()} activeOpacity={1}></TouchableOpacity>
            <View style={styles.box}>
                <View>
                    <TouchableOpacity style={styles.btnClose} onPress={() => props.close()}>
                        <Close style={styles.icClose} />
                    </TouchableOpacity>
                    <Text style={styles.title}>Check In não disponível</Text>
                    <View style={styles.divider}></View>
                    <Text style={styles.desc}>{props.message}</Text>
                    <TouchableOpacity style={[mainStyles.btnBlueNew, styles.btn]} onPress={() => props.close()}>
                        <Text style={mainStyles.btnTextBlueNew}>VOLTAR</Text>
                    </TouchableOpacity>
                </View>
            </View>
        </View>
    );
}

export default AlertFooterUnCheckin;