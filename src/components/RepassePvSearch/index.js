import React, { useEffect, useState } from 'react';
import { useNavigation } from '@react-navigation/native';
import { ActivityIndicator, Alert, Text, TextInput, TouchableOpacity, View } from 'react-native';
import Search2 from '../../assets/svgs/Search2';
import styles from './styles';
import api from '../../services/api';

export const RepassePvSearch = () => {
    const [loading, setLoading] = useState(true);
    const [loadingSearch, setLoadingSearch] = useState(false);
    const [search, setSearch] = useState('');

    const navigation = useNavigation();

    const handleSearch = () => {
        
        if(search.length < 6){
            Alert.alert('PV inválido', 'Informe os 6 dígitos da PV.');
            return;
        }

        setLoadingSearch(true);
        setSearch('');

        api.get(`repasses-salesforce/busca/${search}`).then(res => {
            if(res.data.processos.length > 0){
                navigation.navigate('RepasseInterna', { pv: `PV-${search}` });
            } else {
                Alert.alert('Não encontrado', 'Não encontramos a PV informada. Por favor, tente novamente.');
            }
            setSearch('');
        }).catch(error => {
            console.log('error', error);
        }).then(() => setLoadingSearch(false));
        
    }

    return (
        <View>
            <Text style={styles.textSearch}>Busque pelo PV:</Text>
            {!loadingSearch &&
                <View style={styles.searchBox}>
                    <Text style={[styles.textSearch, styles.textBold]}>PV -</Text>
                    <TextInput 
                        style={styles.input} 
                        placeholder="00000"
                        keyboardType="phone-pad"
                        value={search}
                        onChangeText={text => setSearch(text.trim())}
                    /> 
                    <TouchableOpacity style={styles.btnOk} onPress={handleSearch}>
                        <Search2 />
                    </TouchableOpacity>
                </View>
            }
            {loadingSearch &&
                <View style={{ flex: 1, justifyContent: "center", alignItems: "center", marginBottom: 20 }}>
                    <ActivityIndicator size="large" color="#00467F" />
                </View>
            }
        </View>
    );
}