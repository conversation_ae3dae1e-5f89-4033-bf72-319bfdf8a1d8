import { Platform, StyleSheet, Dimensions } from 'react-native';

const windowWidth = Dimensions.get('window').width;
const windowHeigth = Dimensions.get('window').height;

const styles = StyleSheet.create({
    header: {
        backgroundColor: "#FFF",
        shadowColor: "#000",
        shadowOffset: {
            width: 0,
            height: 9,
        },
        shadowOpacity: 0.1,
        shadowRadius: 11.95,
        elevation: 12,
        position: 'relative',
        zIndex: 9999  ,
        paddingTop: Platform.OS === 'ios' ? 50 : 0,
        flexDirection: "row",
        alignItems: "center",
        height: Platform.OS === 'ios' ? 120 : 70,
    },
    sectionTitle: {
        flex: 1,
        alignItems: "center",
        justifyContent: "center"
    },
    icBox: {
        alignItems: "center",
        height: "100%",
        alignItems: 'center',
        justifyContent: 'center',
        paddingHorizontal: windowWidth * 0.05,
    },
    icBoxActive: {
        backgroundColor: "#90B0C0"
    },
    icBoxContainer: {
        width: 40,
    },
    dotNotification: {
        position: "absolute",
        right: 0,
        width: 16,
        height: 16,
        borderRadius: 15,
        backgroundColor: "#FF312E"
    },
    icTitle: {
        fontFamily: 'Ubuntu-Regular',
        color: "#BDBDBD",
        fontSize: Platform.OS === 'ios' ? 16 : 14,
        textAlign: "center"
    },
    icMenu: {
        width: 38
    },
    icNotification: {
        width: 44
    },
    icLogout: {
        width: 38,
        marginBottom: 5
    },
    name: {
        fontFamily: 'Ubuntu-Regular',
        color: '#BDBDBD',
        fontSize: 14,
        textAlign: "center",
        overflow: 'hidden'
    },
    rowTitle: {
        flexDirection: "row",
        alignItems: "center",
        justifyContent: "center",
        minWidth: 80
    },
    arrow: {
        marginTop: 3,
        width: 10,
        marginRight: 10
    },
    title: {
        fontFamily: 'Ubuntu-Regular',
        color: '#00467F',
        fontSize: 24,
        textAlign: "center"
    },
    homologTag: {
        position: "absolute",
        bottom: -10,
        textAlign: "center",
        fontSize: 8,
        backgroundColor: 'yellow',
        textTransform: 'uppercase',
        paddingVertical: 0,
        width: 50,
        alignSelf: 'center',
        color: "#000"
    },
    menu: {
        backgroundColor: "#90B0C0",
        height: windowHeigth
    },
    menuItem: {
        flexDirection: 'row',
        alignItems: 'center',
        paddingLeft: 25,
        paddingVertical: 20,
    },
    menuItemDivider: {
        height: 1,
        width: '100%',
        backgroundColor: '#fff'
    },
    menuItemIcon: {
        marginRight: 15
    },
    menuItemText: {
        fontFamily: 'Ubuntu-Regular',
        color: "#fff",
        fontSize: 18
    }
});

export default styles;