import React, { useState, useEffect } from 'react';
import { View, Text, TouchableOpacity, Platform, Alert, Image, Linking } from 'react-native';
import Home2 from '../../../assets/svgs/HomeV2';
import Notification2 from '../../../assets/svgs/NotificationV2';
import Logout from '../../../assets/svgs/Logout';
import ArrowLeft from '../../../assets/svgs/ArrowLeft';

import styles from './styles';
import mainStyles from '../../../mainStyles';

import { useAuth } from '../../../context/auth';
import { useService } from '../../../context/service';

import AlertFooter from '../../../components/AlertFooter';

import { useNavigation, useRoute } from '@react-navigation/native';
import OneSignal from 'react-native-onesignal';
import Menu from '../../../assets/svgs/Menu';
import { SvgUri } from 'react-native-svg';
import { Animated } from 'react-native';

const PrivateHeader = (props) => {
    const { signOut, user, hasNotification, hasPermission, production } = useAuth();
    const [showConfirmLogout, setShowConfirmLogout] = useState(false);
    const navigation = useNavigation();
    
    const [showMenu, setShowMenu] = useState(false);
    const [menus, setMenus] = useState([]);
    const [fadeAnim] = useState(new Animated.Value(0));
    const [translateYAnim] = useState(new Animated.Value(-50));

    const { home } = useAuth();

    useEffect(() => {
        let menu = home?.menu;
        if(Array.isArray(menu)){
            setMenus(menu);
        }
    }, [home]);

    useEffect(() => {
        if (showMenu) {
            Animated.parallel([
                Animated.timing(fadeAnim, {
                    toValue: 1,
                    duration: 300,
                    useNativeDriver: true,
                }),
                Animated.timing(translateYAnim, {
                    toValue: 0,
                    duration: 300,
                    useNativeDriver: true,
                }),
            ]).start();
        } else {
            Animated.parallel([
                Animated.timing(fadeAnim, {
                    toValue: 0,
                    duration: 300,
                    useNativeDriver: true,
                }),
                Animated.timing(translateYAnim, {
                    toValue: -50,
                    duration: 300,
                    useNativeDriver: true,
                }),
            ]).start();
        }
    }, [showMenu]);

    const goToNotifications = () => {
        if(!hasPermission('alertas')){
            Alert.alert('Sem permissão', 'Você não tem permissão para acessar essa área');
            return;
        }

        if(Platform.OS === 'ios'){
            OneSignal.promptForPushNotificationsWithUserResponse(event => {
                console.log(event);
            });
        }
        navigation.navigate('PrivateNotifications');
    }

    return (
        <>
            {showConfirmLogout && 
                <AlertFooter 
                    text={
                        <Text>
                            {`Você deseja realmente\nfazer logout?`}
                        </Text>
                    }
                    btnText={`SIM, SAIR DO APLICATIVO`}
                    btnBorder={true}
                    close={() => setShowConfirmLogout(false)}
                    action={() => signOut()}
                />
            }
            <View style={styles.header}>
                    <TouchableOpacity style={[styles.icBox, styles.icLeft, showMenu ? styles.icBoxActive : null]} 
                        onLongPress={() => setShowConfirmLogout(true)} 
                        delayLongPress={3000} 
                        onPress={() => setShowMenu(!showMenu)}
                    >
                        <View style={styles.icBoxContainer}>
                            <Menu style={[styles.icMenu, showMenu ? styles.icMenuActive : null]} color={showMenu ? "#fff" : "#2D719F"} />
                        </View>
                    </TouchableOpacity>
                    <View style={styles.sectionTitle}>
                        <Text style={styles.name}>Olá, {user.apelido}</Text>
                        <View style={styles.rowTitle}>

                            <Text style={styles.title}>{props.title}</Text>
                            {!production &&
                                <Text style={styles.homologTag}>Homolog</Text>
                            }
                        </View>
                    </View>

                    <TouchableOpacity 
                        style={[styles.icBox, styles.icRight]} 
                        onPress={goToNotifications}
                    >
                        <View style={styles.icBoxContainer}>
                            <Notification2 style={styles.icNotification} />
                            {hasNotification &&
                                <View style={styles.dotNotification}></View>
                            }
                        </View>
                    </TouchableOpacity>
            </View>
            {showMenu &&
                <Animated.View
                    style={[
                        styles.menu,
                        {
                            opacity: fadeAnim,
                            transform: [{ translateY: translateYAnim }],
                        },
                    ]}
                >
                    {menus.map((menu, index) => (
                        <View key={index}>
                            <TouchableOpacity 
                                style={styles.menuItem} 
                                disabled={menu.url === null}
                                onPress={() => {
                                    Linking.openURL(menu.url);
                                    setShowMenu(false);
                                }}
                            >
                                <SvgUri
                                    
                                    uri={menu.icone_imagem_url}
                                    width={35}
                                    height={35}
                                    style={styles.menuItemIcon}
                                    
                                />
                                <Text style={styles.menuItemText}>{menu.texto}</Text>
                            </TouchableOpacity>
                            <View style={styles.menuItemDivider}></View>
                        </View>
                    ))}
                </Animated.View>
            }
        </>
    );
}

export default PrivateHeader;