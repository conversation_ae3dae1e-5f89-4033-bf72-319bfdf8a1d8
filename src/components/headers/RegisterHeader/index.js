import React from 'react';
import { View, Text } from 'react-native';
import Logo from '../../../assets/svgs/Logo'
import Intersect from '../../../assets/svgs/Intersect';

import styles from './styles';
import mainStyles from '../../../mainStyles';

const RegisterHeader = (props) => {
    return (
        <View style={styles.header}>
            <View style={styles.bgSectionTitle}>
                <View style={[mainStyles.container, styles.sectionTitle]}>
                    <View>
                        <Logo style={{width: 50, ...styles.logo}} />
                    </View>
                    <View style={styles.titles}>
                        <Text style={styles.title}>{props.title}</Text>
                    </View>
                </View>
                <View style={mainStyles.container}>
                    <Text style={styles.subtitle}>{props.subtitle}</Text>
                </View>
            </View>
            {/* <Intersect /> */}
        </View>
    );
}

export default RegisterHeader;