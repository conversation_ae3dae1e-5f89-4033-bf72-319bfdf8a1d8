import { Platform, StyleSheet, Dimensions } from 'react-native';

const windowWidth = Dimensions.get('window').width;
const windowHeight = Dimensions.get('window').height;

const styles = StyleSheet.create({
    header: {
        marginBottom: 0
    },
    // logo: {
    //     position: "absolute",
    //     top: Platform.OS === 'ios' ? 50 : 25,
    //     right: 25 
    // },
    bgSectionTitle: {
        paddingTop: Platform.OS === 'ios' ? 15 : 0,
        width: windowWidth,
        backgroundColor: '#2D719F',
        marginBottom: -18,
        zIndex: 2,
        paddingBottom: 30
    },
    sectionTitle: {
        marginTop: 30,
        flexDirection: "row",
        alignItems: "center"
    },
    titles: {
        marginTop: 10,
        marginLeft: 10
    },
    title: {
        fontFamily: 'Ubuntu-Medium',
        fontSize: Platform.OS === 'ios' ? 38 : 36,
        color: '#FFF',
        letterSpacing: 1,
        marginBottom: 10
    },
    subtitle: {
        fontFamily: 'Ubuntu-Regular',
        fontSize: Platform.OS === 'ios' ? 14 : 12,
        color: '#FFF',
        letterSpacing: 1,
        lineHeight: 18,
        marginTop: 10
    }
});

export default styles;