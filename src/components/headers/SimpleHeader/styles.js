import { Platform, StyleSheet, Dimensions } from 'react-native';

const windowWidth = Dimensions.get('window').width;

const styles = StyleSheet.create({
    bgTop: {
        width: windowWidth,
        backgroundColor: '#2D719F',
        marginBottom: -3,
        paddingTop: 10,
        paddingBottom: 15
    },
    vFlex: {
        flexDirection: "row",
        alignItems: "center"
    },
    logo: {
        width: 60,
        marginTop: Platform.OS === 'ios' ? 45 : 30,
        marginBottom: 30,
        marginLeft: 0,
        marginRight: 13
    },
    title: {
        fontFamily: 'Ubuntu-Medium',
        fontSize: Platform.OS === 'ios' ? 36 : 34,
        color: '#FFF',
        lineHeight: 38
    },
    subtitle: {
        fontFamily: 'Ubuntu-Light',
        fontSize: Platform.OS === 'ios' ? 36 : 34,
        color: '#FFF',
        lineHeight: 38
    },
});

export default styles;