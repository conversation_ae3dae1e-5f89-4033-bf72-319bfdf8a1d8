import React from 'react';
import { View, Text } from 'react-native';
import Logo from '../../../assets/svgs/Logo'
import Intersect from '../../../assets/svgs/Intersect';

import styles from './styles';
import mainStyles from '../../../mainStyles';

const SimpleHeader = (props) => {
    return (
        <>
            <View style={styles.bgTop}>
                <View style={mainStyles.container}>
                    <View style={styles.vFlex}>
                        <View>
                            <Logo style={styles.logo} />
                        </View>
                        <View>
                            <Text style={styles.title}>Bem Vindo</Text>
                            <Text style={styles.subtitle}>Corretor Cury</Text>
                        </View>
                    </View>
                </View>
            </View>
            {/* <Intersect /> */}
        </>
    );
}

export default SimpleHeader;
