import React from 'react';
import { TouchableOpacity, View, Text, ActivityIndicator } from 'react-native';
import styles from './styles';
import Close from '../../assets/svgs/Close';
import mainStyles from '../../mainStyles';

const AlertConfirmarPresencaSairFile = ({close, action, loading}) => {
    return (
        <View style={styles.container}>
            <TouchableOpacity style={mainStyles.overlayBlue} onPress={() => close()} activeOpacity={1}></TouchableOpacity>
            <View style={styles.box}>
                <View>
                    <TouchableOpacity style={styles.btnClose} onPress={() => close()}>
                        <Close style={styles.icClose} />
                    </TouchableOpacity>
                    <Text style={styles.title}>Sair da fila</Text>
                    <View style={styles.divider}></View>
                    <Text style={styles.confirm}>{`Você deseja realmente sair da fila?`}</Text>
                    {loading &&
                        <View style={{  justifyContent: "center", alignItems: "center", height: 140 }}>
                            <ActivityIndicator size="large" color="#00467F" />
                        </View>
                    }
                    {!loading &&
                        <>
                            <TouchableOpacity style={[mainStyles.btnBlueNew, styles.btn]} onPress={() => action()}>
                                <Text style={mainStyles.btnTextBlueNew}>SIM, SAIR DA FILA</Text>
                            </TouchableOpacity>
                            <TouchableOpacity style={styles.btnBorder} onPress={() => close()}>
                                <Text style={styles.btnText}>VOLTAR</Text>
                            </TouchableOpacity>
                        </>
                    }
                </View>
            </View>
        </View>
    );
}

export default AlertConfirmarPresencaSairFile;