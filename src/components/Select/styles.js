import { StyleSheet } from 'react-native';

const styles = StyleSheet.create({
    inputIOS: {
        fontFamily: 'Ubuntu-Regular',
        color: '#828282',
        fontSize: 16,
        borderWidth: 1,
        borderColor: '#E0E0E0',
        paddingLeft: 15,
        paddingRight: 30, // to ensure the text is never behind the icon,
        height: 50,
        marginBottom: 20,
        letterSpacing: 1
        
    },
    inputAndroid: {
        fontFamily: 'Ubuntu-Regular',
        color: '#828282',
        fontSize: 14,
        borderWidth: 1,
        borderColor: '#E0E0E0',
        paddingLeft: 15,
        paddingRight: 30, // to ensure the text is never behind the icon,
        height: 50,
        marginBottom: 20,
        letterSpacing: 1
    },
    placeholder: {
        fontSize: 14,
        fontFamily: 'Ubuntu-Medium',
        color: '#BDBDBD',
        letterSpacing: 1.5
    }
});

export default styles;