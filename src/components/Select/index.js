import React from 'react';
import RNPickerSelect from 'react-native-picker-select';
import ArrowDown from '../../assets/svgs/ArrowDown2';
import styles from './styles';

const Select = (props) => {
    let disableStyles = {};

    if(props.disabled){
      disableStyles = {
        color: '#828282',
        backgroundColor: '#EEE'
      };
    }

    return (
        <RNPickerSelect
            onValueChange={(value) => props.onChange(value)}
            style={{ ...styles, iconContainer: {
                top: 21,
                right: 22,
              },
              inputAndroid: {
                ...styles.inputAndroid,
                ...disableStyles,
                borderColor: props.borderColor || '#E0E0E0',
                color: props.color || '#828282',
              },
              inputIOS: {
                ...styles.inputIOS,
                ...disableStyles,
                borderColor: props.borderColor || '#E0E0E0',
                color: props.color || '#828282',
              }, 
              placeholder: {
                ...styles.placeholder,
                color: props.placeholderColor || '#BDBDBD',
                fontFamily: props.fontFamily || 'Ubuntu-Medium'
              },             
            }}
            value={props.value}
            useNativeAndroidPickerStyle={false}
            fixAndroidTouchableBug={true}
            placeholder={{ label: props.placeholder, value: '' }}
            Icon={() => {
                return <ArrowDown />
            }}
            items={props.options ? props.options : []}
            disabled={props?.disabled === true}
        />
    );
}

export default Select;