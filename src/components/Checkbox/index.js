import React from 'react';
import {Text, TouchableOpacity, View} from 'react-native';
import Check from '../../assets/svgs/CheckBox';
import styles from './styles';
const Checkbox = props => {
  const {text, isChecked, onPress} = props;
  return (
    <View style={styles.marginTop}>
      <TouchableOpacity onPress={onPress}>
        <View style={styles.viewCheck}>
          <View 
            style={[styles.check, { backgroundColor: isChecked ? '#00467F' : '#FFF' }]}
          >
            {isChecked ? <Check /> : <View />}
          </View>
          <Text style={styles.textOption}>{text}</Text>
        </View>
      </TouchableOpacity>
    </View>
  );
};

export default Checkbox;
