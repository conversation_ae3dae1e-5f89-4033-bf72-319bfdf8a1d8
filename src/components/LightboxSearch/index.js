import React, { useEffect } from 'react';
import { TouchableOpacity, View, Text, TextInput, FlatList, Keyboard } from 'react-native';
import styles from './styles';
import Close from '../../assets/svgs/Close';
import Search from '../../assets/svgs/Search';
import mainStyles from '../../mainStyles';
import { useState } from 'react';
import { clearAcentos } from "../../useful/conversions";

const LightboxSearch = ({close, title, options, value, setValue}) => {
    const [inSearch, setInSearch] = useState('');

    const [keyboardHeight, setKeyboardHeight] = useState(0);
    const [optionsFiltered, setOptionsFiltered] = useState([]);

    useEffect(() => {
        filterOptions();
    }, [,inSearch]);

    useEffect(() => {
        const keyboardDidShowListener = Keyboard.addListener(
        'keyboardDidShow',
        (event) => {
            const keyboardHeight = event.endCoordinates.height;
            setKeyboardHeight(keyboardHeight);
        }
        );

        const keyboardDidHideListener = Keyboard.addListener(
        'keyboardDidHide',
        () => {
            setKeyboardHeight(0);
        }
        );

        return () => {
        keyboardDidShowListener.remove();
        keyboardDidHideListener.remove();
        };
    }, []);

    const filterOptions = () => {
        let toOptionsFiltered = [];
        options.map(option => {
            let label = option.label.toLowerCase();
            label = clearAcentos(label);
            let search = inSearch.toLocaleLowerCase().trim();
            search = clearAcentos(search);
            if(label.indexOf(search) > -1) toOptionsFiltered.push(option);
        });
        setOptionsFiltered(toOptionsFiltered);
    }

    return (
        <View style={styles.container}>
            <TouchableOpacity style={[mainStyles.overlayBlue]} onPress={() => close()} activeOpacity={1}></TouchableOpacity>
            <View style={[styles.box, { marginBottom: keyboardHeight - 20 }]}>
                <View style={mainStyles.container}>
                    <TouchableOpacity style={styles.btnClose} onPress={() => close()}>
                        <Close style={styles.close} />
                    </TouchableOpacity>
                    <Text style={styles.nameTraining}>{title}</Text>
                    <View style={styles.searchBox}>
                        <Search style={styles.icSearch} />
                        <TextInput
                            underlineColorAndroid="transparent"  
                            style={[mainStyles.inputText, styles.inputText]}
                            value={inSearch}
                            onChangeText={text => setInSearch(text)}
                            autoCapitalize="words"
                            autoFocus={true}
                        />
                    </View>

                    <FlatList
                        keyboardShouldPersistTaps='handled'
                        style={{ maxHeight: 200}}
                        data={optionsFiltered}
                        renderItem={({item, index}) => (
                            <TouchableOpacity key={index} onPress={() => {
                                setValue(item.value);
                                close();
                            }}>
                                <Text style={mainStyles.optionText}>{item.label}</Text>
                            </TouchableOpacity>
                        )}
                    />
                </View>
            </View>
        </View>
    );
}

export default LightboxSearch;