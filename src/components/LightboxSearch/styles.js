import { Dimensions, StyleSheet } from 'react-native';

const windowWidth = Dimensions.get('window').width;

const styles = StyleSheet.create({
    container: {
        position: "absolute",
        top: 0,
        bottom: 0,
        left: 0,
        right: 0,
        zIndex: 9999,
        alignItems: "center",
        justifyContent: "center"
    },
    box: {
        backgroundColor: "#FFF",
        paddingTop: 20,
        paddingBottom: 20,
        width: windowWidth - 40
    },
    nameTraining: {
        fontFamily: 'Ubuntu-Regular',
        color: '#00467F',
        letterSpacing: 1,
        fontSize: 15,
        marginBottom: 5,
        textTransform: "uppercase"
    },
    divider: {
        borderBottomColor: "#DADADA",
        borderBottomWidth: 1,
        marginTop: 15
    },
    text: {
        fontFamily: 'Ubuntu-Light',
        color: '#828282',
        letterSpacing: 1,
        fontSize: 14,
        marginTop: 15,
        marginBottom: 25
    },
    btn: {
        backgroundColor: '#00467F',
        height: 46,
        alignItems: "center",
        justifyContent: "center",
        borderRadius: 3,
        marginTop: 30,
        width: "45%"
    },
    btnBorder: {
        height: 46,
        alignItems: "center",
        justifyContent: "center",
        borderRadius: 3,
        marginTop: 30,
        borderColor: "#00467F",
        borderWidth: 1,
        width: "45%"
    },
    btnText: {
        fontSize: 14,
        fontFamily: 'Roboto-Medium',
        letterSpacing: 1,
        textAlign: "center",
        color: '#F2F2F2'
    },
    textBlue: {
        color: "#00467F"
    },
    btnClose: {
        position: "absolute",
        top: -8,
        right: 35,
        width: 35,
        height: 35,
        justifyContent: 'center',
        alignItems: 'center',
        zIndex: 9
    },
    close: {
        color: "#2D719F",
        width: 18
    },
    searchBox: {
        marginTop: 15
    },
    icSearch: {
        color: "#828282",
        width: 30,
        position: "absolute",
        top: 10,
        left: 8
    },
    inputText: {
        paddingLeft: 50,
        width: windowWidth - 80
    },
    result: {
        maxHeight: 200
    }
});

export default styles;