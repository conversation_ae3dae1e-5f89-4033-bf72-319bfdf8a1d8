import React from 'react';
import { TouchableOpacity, View, Text } from 'react-native';
import styles from './styles';
import Close from '../../assets/svgs/Close';
import Alert from '../../assets/svgs/Alert2';
import Check from '../../assets/svgs/Check2';
import mainStyles from '../../mainStyles';

const AlertConfirmarPresencaResultado = ({close, nomePlantao, success, error}) => {
    return (
        <View style={styles.container}>
            <TouchableOpacity style={mainStyles.overlayBlue} onPress={() => close()} activeOpacity={1}></TouchableOpacity>
            <View style={styles.box}>
                <View>
                    <TouchableOpacity style={styles.btnClose} onPress={() => close()}>
                        <Close style={styles.icClose} />
                    </TouchableOpacity>
                    <Text style={styles.title}>{`Status de plantão\n${nomePlantao}`}</Text>
                    <View style={styles.divider}></View>
                    {error &&
                        <>
                            <View style={styles.vFlex}>
                                <Alert />
                                <Text style={styles.textAlert}>Atenção!</Text>
                            </View>
                            <Text style={styles.confirm}>{error}</Text>
                        </>
                    }
                    {success &&
                        <View style={styles.vFlex}>
                            <Check />
                            <Text style={styles.textAlert}>{`Confirmação efetuada\ncom sucesso!`}</Text>
                        </View>
                    }
                    <TouchableOpacity style={styles.btnBorder} onPress={() => close()}>
                        <Text style={styles.btnText}>VOLTAR</Text>
                    </TouchableOpacity>
                </View>
            </View>
        </View>
    );
}

export default AlertConfirmarPresencaResultado;