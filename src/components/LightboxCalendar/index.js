import React, { useState, useEffect } from 'react';
import { TouchableOpacity, View, Text, Linking, ScrollView, Alert, ActivityIndicator } from 'react-native';
import styles from './styles';
import Close from '../../assets/svgs/Close';
import mainStyles from '../../mainStyles';
import { Calendar, LocaleConfig } from 'react-native-calendars';

LocaleConfig.locales.br = {
  monthNames: [
    "Janeiro",
    "Fevereiro",
    "Março",
    "Abril",
    "Maio",
    "Jun<PERSON>",
    "Jul<PERSON>",
    "Agosto",
    "Setembro",
    "Outubro",
    "Novembro",
    "Dezembro"
  ],
  monthNamesShort: [
    "Jan.",
    "Fev.",
    "Mar",
    "Abr",
    "Mai",
    "Jun",
    "Jul.",
    "Ago",
    "Set.",
    "Out.",
    "Nov.",
    "Dez."
  ],
  dayNames: [
    "Domingo",
    "Segunda",
    "Ter<PERSON>",
    "Q<PERSON>rta",
    "<PERSON><PERSON><PERSON>",
    "<PERSON><PERSON>",
    "<PERSON><PERSON>bado"
  ],
  dayNamesShort: ["Dom.", "Seg.", "Ter.", "Qua.", "Qui.", "Sex.", "Sáb."]
};

LocaleConfig.defaultLocale = "br";

const LightboxCalendar = ({close, initialDate, setSelectedDate, minDate}) => {
    const handleDateChage = date => {
        setSelectedDate(date.dateString);
        close();
    }

    return (
        <View style={styles.container}>
            <TouchableOpacity style={mainStyles.overlay} onPress={() => close()} activeOpacity={1}></TouchableOpacity>
            <View style={styles.content}>
                <TouchableOpacity style={styles.btnClose} onPress={() => close()}>
                    <Close />
                </TouchableOpacity>
                <Calendar 
                  minDate={minDate ?? null}
                  initialDate={initialDate}
                  onDayPress={handleDateChage}
                />
            </View>
        </View>
    );
}

export default LightboxCalendar;