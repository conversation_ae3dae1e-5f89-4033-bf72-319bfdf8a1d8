import { StyleSheet, Dimensions } from 'react-native';

const windowWidth = Dimensions.get('window').width;
const windowHeight = Dimensions.get('window').height;

const styles = StyleSheet.create({
    container: {
        position: "absolute",
        top: -50,
        bottom: 0,
        left: 0,
        right: 0,
        zIndex: 999999
    },
    content: {
        backgroundColor: "#FFF",
        width: windowWidth * 0.8,
        marginLeft: windowWidth * 0.1,
        height: windowHeight * 0.65,
        marginTop: windowHeight * 0.25,
        borderRadius: 12,
        shadowColor: "#000",
        shadowOffset: {
            width: 0,
            height: 9,
        },
        shadowOpacity: 0.1,
        shadowRadius: 11.95,
        elevation: 12,
        paddingTop: 40,
        paddingBottom: 40,
        paddingLeft: 20,
        paddingRight: 20
    },
    title: {
        fontFamily: "Roboto-Light",
        fontSize: 21,
        letterSpacing: 1,
        color: "#00467F",
        marginBottom: 25
    },
    titleBold: {
        fontFamily: "Roboto-Bold",
    },
    btnClose: {
        position: "absolute",
        width: 50,
        height: 50,
        alignItems: "center",
        justifyContent: "center",
        top: -25,
        right: 40,
        backgroundColor: '#00467F',
        borderRadius: 50
    },
    btnWhats: {
        marginBottom: 20
    }
});

export default styles;