import { StyleSheet, Dimensions, Platform } from 'react-native';

const windowWidth = Dimensions.get('window').width;
const windowH = Dimensions.get('window').height;

const styles = StyleSheet.create({
    btn: {
        position: 'absolute',
        bottom: 30,
        right: 20,
        width: 55,
        height: 55,
        // borderRadius: 55,
        backgroundColor: '#2D719F',
        alignItems: 'center',
        justifyContent: 'center',
        zIndex: 10,
        shadowColor: "#CCC",
        shadowOffset: {
            width: 0,
            height: 9,
        },
        shadowOpacity: 0.48,
        shadowRadius: 11.95,
        elevation: 7,
    },
    icon: {
        width: 35,
        color: "#FFF"
    },
    overlay: {
        position: 'absolute',
        left: 0,
        right: 0,
        bottom: 0,
        top: 0,
        backgroundColor: '#000',
        zIndex: 10,
        opacity: 0.4,
        elevation: 14
    },
    searchBox: {
        position: 'absolute',
        bottom: windowWidth * 0.125,
        alignSelf: 'center',
        backgroundColor: '#FFF',
        width: windowWidth * 0.85,
        height: 60,
        zIndex: 12,
        // borderTopLeftRadius: 20,
        // borderBottomLeftRadius: 20,
        // borderTopEndRadius: 60,
        // borderBottomEndRadius: 60,
        borderColor: "#2D719F",
        borderWidth: 3,
        shadowColor: "#000",
        shadowOffset: {
            width: 0,
            height: 9,
        },
        shadowOpacity: 0.48,
        shadowRadius: 11.95,
        elevation: 7,
        justifyContent: 'center',
    },
    btnOk: {
        width: 60,
        height: 60,
        backgroundColor: "#2D719F",
        position: "absolute",
        right: -1,
        alignItems: 'center',
        justifyContent: 'center',
    },
    btnTextOk: {
        fontFamily: 'Ubuntu-Bold',
        fontSize: 18,
        color: "#FFF",
        textAlign: 'center'
    },
    input: {
        paddingLeft: 20,
        height: 60,
        width: (windowWidth * 0.85) - 60,
        fontFamily: "Ubuntu-Regular",
        fontSize: Platform.OS === 'ios' ? 19 : 15,
        color: "#666"
    },
    searchContainer: {
        position: 'absolute',
        bottom: 0,
        alignSelf: 'center',
        backgroundColor: 'transparent',
        width: windowWidth * 0.85,
        zIndex: 10,
        elevation: 18
    }
});

export default styles;