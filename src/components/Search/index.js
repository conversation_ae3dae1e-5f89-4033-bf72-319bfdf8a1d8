import React, { useState } from 'react';
import { TextInput, View, Text, KeyboardAvoidingView, TouchableOpacity, Platform } from 'react-native';

import mainStyles from '../../mainStyles';
import styles from './styles';

import SearchIcon from '../../assets/svgs/Search';

const Search = ({ searchText, setSearchText, navigation }) => {
    const [showInput, setShowInput] = useState(false);
    const [text, setText] = useState(searchText);

    const handleSearch = () => {
        setSearchText(text);
        setShowInput(false);
    }

    const handleClose = () => {
        setText(searchText);
        setShowInput(false);
    }

    return (
        <>
            {showInput &&
                <TouchableOpacity style={styles.overlay} onPress={handleClose}></TouchableOpacity>
            }
            {!showInput &&
            <>
                <TouchableOpacity style={styles.btn} onPress={() => setShowInput(true)}>
                    <SearchIcon style={styles.icon} />
                </TouchableOpacity>
            </>
            }
            {showInput &&
                <KeyboardAvoidingView behavior={Platform.OS === 'ios' ? 'position' : 'padding'} style={styles.searchContainer}>
                    {}
                    <View style={styles.searchBox}>
                        <TextInput style={styles.input} onChangeText={setText} value={text} onSubmitEditing={handleSearch} autoFocus />
                        <TouchableOpacity style={styles.btnOk} onPress={handleSearch}>
                            <Text style={styles.btnTextOk}>OK</Text>
                        </TouchableOpacity>
                    </View>
                </KeyboardAvoidingView>
            }
        </>
    );
}
export default Search;