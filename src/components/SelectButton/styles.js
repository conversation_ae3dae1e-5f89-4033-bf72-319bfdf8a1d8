import { StyleSheet } from 'react-native';

const styles = StyleSheet.create({
    box: {
        borderColor: '#DADADA',
        borderWidth: 1,
        justifyContent: 'center',
        height: 50,
        paddingLeft: 15,
        marginBottom: 10,
    },
    arrow: {
        position: 'absolute',
        height: 48,
        width: 55,
        alignItems: 'center',
        justifyContent: 'center',
        right: 0,
        backgroundColor: '#FFF'
    },
    value: {
        fontFamily: 'Ubuntu-Regular',
        color: '#828282',
        fontSize: 16,
        letterSpacing: 1
    },
    placeholder: {
        fontSize: 14,
        fontFamily: 'Ubuntu-Medium',
        color: '#BDBDBD',
        letterSpacing: 1.5
    },
    arrowDisabled: {
        backgroundColor: '#EEE'
    },
    boxDisabled: {
        backgroundColor: '#EEE'
    },
    valueDisabled: {
        color: '#828282'
    }
});

export default styles;