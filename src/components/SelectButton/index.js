import React, { useState } from 'react';
import RNPickerSelect from 'react-native-picker-select';
import ArrowDown from '../../assets/svgs/ArrowDown2';
import styles from './styles';
import { Text, TouchableOpacity, View } from "react-native";
import LightboxSearch from "../LightboxSearch";

const SelectButton = ({onPress, value, placeholder, disabled}) => {
  const [showModal, setShowModal] = useState(false);

  return (
    <>
      <TouchableOpacity
          disabled={disabled === true}
          onPress={onPress}
          style={[styles.box, disabled ? styles.boxDisabled : null]}
      >
          <Text style={[styles.value, disabled ? styles.valueDisabled : null]}>{value ?? placeholder}</Text>
          <View style={[styles.arrow, disabled ? styles.arrowDisabled : null]}>
              <ArrowDown />
          </View>
      </TouchableOpacity>
    </>
  );
}

export default SelectButton;