import React, {useEffect, useRef, useState} from 'react';
import {AppState, Text, View} from 'react-native';
import {loadingtexts} from './loadingtexts';
import styles from './styles';
const Timer = props => {
  const {open, close, startValue} = props;
  const closeTimer = () => {
    close(false);
    clearInterval(interval);
  };
  let interval;
  const timer = useRef(0);
  const [showTimer, setShowTimer] = useState(false);
  // const [actualState, setActualState] = useState(false);
  const actualState = useRef('');
  const [text, setText] = useState('');
  const [currentTime, setCurrentTime] = useState(
    convertToMinutesSeconds(startValue),
  );

  const startTimer = async () => {
    clearInterval(interval);
    timer.current = startValue;
    setShowTimer(true);
    setText(loadingtexts[Math.floor(Math.random() * 100)]);
  };

  const handleAppStateChange = state => {
    // setActualState(state);
    actualState.current = state;
  };

  AppState.addEventListener('change', handleAppStateChange);
  useEffect(() => {
    // eslint-disable-next-line react-hooks/exhaustive-deps
    interval = setInterval(counter, 1000);
    return () => {
      clearInterval(interval);
    };
  }, [timer.current]);
  function counter() {
    if (actualState.current !== 'background') {
      timer.current--;
      setCurrentTime(formatTime(timer));
      if (timer.current % 7 === 0) {
        setText(loadingtexts[Math.floor(Math.random() * 100)]);
      }
      if (timer.current <= 0) {
        clearInterval(interval);
        setShowTimer(false);
        timer.current = 0;
      }
    }
  }
  function convertToMinutesSeconds(number) {
    const minutes = Math.floor(number / 60);
    const seconds = number % 60;
    const formattedMinutes = minutes < 10 ? '0' + minutes : minutes;
    const formattedSeconds = seconds < 10 ? '0' + seconds : seconds;

    return formattedMinutes + ':' + formattedSeconds;
  }

  useEffect(() => {
    if (open) {
      startTimer();
    }
  }, [open]);

  const formatTime = timer => {
    const minutes = Math.floor(timer.current / 60)
      .toString()
      .padStart(2, '0');
    const seconds = (timer.current % 60).toString().padStart(2, '0');
    return `${minutes}:${seconds}`;
  };

  return (
    <>
      {showTimer && (
        <>
          <View style={styles.boxFlexColumn}>
            <Text style={styles.textSubtitle}>{text}</Text>
            <Text style={styles.textTitle}>{currentTime}</Text>
          </View>
        </>
      )}
    </>
  );
};

export default Timer;
