import React, {useRef} from 'react';
import {Animated, Image, Text, View} from 'react-native';

import mainStyles from '../../../mainStyles';
import useStyles from './styles';
const BoxBotLoading = props => {
  const {styles} = useStyles();
  const fadeIn = useRef(new Animated.Value(0)).current;
  Animated.timing(fadeIn, {
    toValue: 1,
    duration: 600,
    useNativeDriver: true,
  }).start();
  return (
    <Animated.View style={[{opacity: fadeIn}]}>
      <View style={styles.BoxChat}>
        <View style={styles.Arrow} />
        <Text style={mainStyles.labelMargin}>WoxAi</Text>
        {/* <LoadingAnimate /> */}
        <View style={styles.viewLoading}>
          <Image
            style={styles.logo}
            source={require('../../../assets/loadingChat.gif')}
          />
        </View>
      </View>
    </Animated.View>
  );
};

export default BoxBotLoading;
