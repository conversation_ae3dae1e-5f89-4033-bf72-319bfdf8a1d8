import {StyleSheet, useWindowDimensions} from 'react-native';

const useStyle = () => {
  const dimensions = useWindowDimensions();
  const windowWidth = dimensions.width;

  const styles = StyleSheet.create({
    viewLoading: {
      width: '50%',
      resizeMode: 'contain',
    },
    logo: {
      width: '50%',
      height: 50,
      resizeMode: 'contain',
    },
    BoxChat: {
      marginTop: 10,
      marginBottom: 10,
      shadowRadius: 2,
      shadowOffset: {
        width: 1,
        height: 3,
      },
      backgroundColor: '#F5F5F5',
      shadowOpacity: 0.25,
      borderRadius: 8,
      padding: 12,
      width: '80%',
      shadowColor: 'black',
      marginLeft: windowWidth * 0.05,
    },
    optionTextBold: {
      fontFamily: 'Ubuntu-Regular',
      fontSize: 14,
      letterSpacing: 0.7,
      color: '#808080',
      lineHeight: 18,
    },
    Arrow: {
      position: 'absolute',
      left: -30,
      top: 12,
      width: 0,
      height: 0,
      backgroundColor: 'transparent',
      borderTopWidth: 11,
      borderRightWidth: 11,
      borderBottomWidth: 11,
      borderLeftWidth: 0,
      borderTopColor: 'transparent',
      borderRightColor: '#F5F5F5',
      borderBottomColor: 'transparent',
      zIndex: 10,
      marginLeft: windowWidth * 0.05,
    },
  });

  return {styles};
};

export default useStyle;
