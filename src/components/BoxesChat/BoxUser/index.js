import React, {useEffect, useRef} from 'react';
import {Animated, Text, View} from 'react-native';
import styles from './styles';
const BoxUser = props => {
  const {text} = props;
  const fadeIn = useRef(new Animated.Value(0)).current;

  useEffect(() => {
    Animated.timing(fadeIn, {
      toValue: 1,
      duration: 600,
      useNativeDriver: true,
    }).start();
  }, [fadeIn]);
  return (
    <Animated.View style={[{opacity: fadeIn}]}>
      <View style={styles.BoxChat}>
        <View style={styles.Arrow} />
        <Text style={styles.optionTextBold}>{text}</Text>
      </View>
    </Animated.View>
  );
};

export default BoxUser;
