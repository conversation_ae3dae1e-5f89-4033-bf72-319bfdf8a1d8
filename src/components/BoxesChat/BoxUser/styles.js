import {StyleSheet} from 'react-native';

const styles = StyleSheet.create({
  BoxChat: {
    marginTop: 10,
    marginBottom: 10,
    shadowRadius: 2,
    shadowOffset: {
      width: 1,
      height: 3,
    },
    backgroundColor: '#90B0C0',
    shadowOpacity: 0.25,
    borderRadius: 8,
    padding: 12,
    width: '85%',
    shadowColor: 'black',
    marginLeft: '9%',
  },
  optionTextBold: {
    fontFamily: 'Ubuntu-Regular',
    fontSize: 14,
    letterSpacing: 0.7,
    color: '#FFFFFF',
    lineHeight: 18,
  },
  Arrow: {
    position: 'absolute',
    right: -10,
    top: 12,
    width: 0,
    height: 0,
    backgroundColor: 'transparent',
    borderTopWidth: 11,
    borderLeftWidth: 11,
    borderBottomWidth: 11,
    borderRightWidth: 0,
    borderTopColor: 'transparent',
    borderLeftColor: '#90B0C0',
    borderBottomColor: 'transparent',
    zIndex: 10,
  },
});

export default styles;
