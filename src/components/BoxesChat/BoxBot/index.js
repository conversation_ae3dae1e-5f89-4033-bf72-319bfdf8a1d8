import Clipboard from '@react-native-clipboard/clipboard';
import React, {useEffect, useRef} from 'react';

import {Animated, Image, Text, TouchableOpacity, View} from 'react-native';
import RenderHtml from 'react-native-render-html';
// import Toast from 'react-native-tiny-toast';
import {HapticFeedback} from 'react-native';

import mainStyles from '../../../mainStyles';
import useStyle from './styles';

const copyToClipboard = async text => {
  const regex = /<[^>]*>/gm;
  const htmlText = text.replace(regex, '');
  await Clipboard.setString(htmlText);
  // Alert.alert('Texto copiado');
  HapticFeedback.notification(() => {});
};

const mixedStyle = {
  div: {
    fontFamily: 'Ubuntu-Regular',
    fontSize: 14,
    lineHeight: 20,
    letterSpacing: 0.7,
    color: '#808080',
  },
  h1: {
    fontFamily: 'Ubuntu-Bold',
    fontSize: 18,
    lineHeight: 20,
  },
};
const BoxBot = props => {
  const {styles} = useStyle();
  const {text, showCopy} = props;

  const renderHtmlRef = useRef();
  const source = {
    html: `<div>${text}</div>`,
  };

  const fadeIn = useRef(new Animated.Value(0)).current;

  useEffect(() => {
    Animated.timing(fadeIn, {
      toValue: 1,
      duration: 600,
      useNativeDriver: true,
    }).start();
  }, [fadeIn]);

  return (
    <Animated.View style={[{opacity: fadeIn}]}>
      <View style={styles.Arrow} />
      <View style={styles.Box}>
        <View style={styles.BoxChat}>
          <Text style={mainStyles.labelMargin}>WoxAi</Text>
          <RenderHtml
            contentWidth={''}
            source={source}
            tagsStyles={mixedStyle}
            ref={renderHtmlRef}
          />
        </View>
        {showCopy && (
          <TouchableOpacity
            style={styles.icoCopy}
            onPress={() => copyToClipboard(text)}>
            <Image source={require('../../../assets/iconeCopiar.png')} />
          </TouchableOpacity>
        )}
      </View>
    </Animated.View>
  );
};

export default BoxBot;
