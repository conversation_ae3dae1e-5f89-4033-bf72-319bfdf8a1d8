import {StyleSheet, useWindowDimensions} from 'react-native';

const useStyle = () => {
  const dimensions = useWindowDimensions();
  const windowWidth = dimensions.width;

  const styles = StyleSheet.create({
    Box: {width: '100%'},
    BoxChat: {
      marginTop: 10,
      marginBottom: 10,
      shadowRadius: 2,
      shadowOffset: {
        width: 1,
        height: 3,
      },
      backgroundColor: '#F5F5F5',
      shadowOpacity: 0.25,
      borderRadius: 8,
      padding: 12,
      width: '80%',
      shadowColor: 'black',
      marginLeft: windowWidth * 0.05,
    },
    optionTextBold: {
      fontFamily: 'Ubuntu-Regular',
      fontSize: 14,
      letterSpacing: 0.7,
      color: '#808080',
      lineHeight: 18,
    },
    Arrow: {
      position: 'absolute',
      left: -10,
      top: 12,
      width: 0,
      height: 0,
      backgroundColor: 'transparent',
      borderTopWidth: 11,
      borderRightWidth: 11,
      borderBottomWidth: 11,
      borderLeftWidth: 0,
      borderTopColor: 'transparent',
      borderRightColor: '#F5F5F5',
      borderBottomColor: 'transparent',
      zIndex: 10000,
      marginLeft: windowWidth * 0.05,
    },
    icoCopy: {
      position: 'absolute',
      justifyContent: 'flex-end',
      alignItems: 'flex-end',
      right: 10,
      marginLeft: 50,
      width: 100,
      height: 100,
      bottom: 10,
      zIndex: 100000000000,
      // backgroundColor: '#000',
    },
    boxCopyToClipboard: {
      // position: 'absolute',
      // right: -50,
      // bottom: -15,
      width: 100,
      height: 100,
      backgroundColor: '#000',
    },
  });

  return {styles};
};

export default useStyle;
