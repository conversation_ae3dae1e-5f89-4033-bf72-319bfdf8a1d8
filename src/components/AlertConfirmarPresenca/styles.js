import { StyleSheet } from 'react-native';

const styles = StyleSheet.create({
    container: {
        position: "absolute",
        top: 0,
        bottom: 0,
        left: 0,
        right: 0,
        zIndex: 9999,
        flexDirection: "column",
        justifyContent: "center",
        alignItems: "center"
    },
    box: {
        backgroundColor: "#FFF",
        paddingTop: 20,
        paddingBottom: 20,
        width: "95%",
        paddingLeft: 15,
        paddingRight: 15
    },
    title: {
        fontFamily: "Ubuntu-Regular",
        color: '#00467F',
        fontSize: 16,
        letterSpacing: 1
    },
    product: {
        fontFamily: "Ubuntu-Bold",
        color: '#00467F',
        fontSize: 16,
        letterSpacing: 1
    },
    confirm: {
        fontFamily: "Ubuntu-Light",
        color: '#828282',
        fontSize: 15,
        letterSpacing: 1,
        lineHeight: 20
    },
    divider: {
        borderTopColor: "#DADADA",
        borderTopWidth: 1,
        marginTop: 20,
        marginBottom: 20
    },
    btn: {
        width: '100%',
        marginTop: 30
    },
    btnText: {
        fontSize: 16,
        fontFamily: 'Ubuntu-Medium',
        letterSpacing: 1,
        textAlign: "center",
        color: '#F2F2F2'
    },
    btnClose: {
        position: "absolute",
        top: 0,
        right: 0,
        zIndex: 999
    },
    icClose: {
        color: "#2D719F",
        width: 18
    }
});

export default styles;