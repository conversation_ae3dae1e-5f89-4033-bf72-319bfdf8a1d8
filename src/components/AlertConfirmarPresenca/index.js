import React from 'react';
import { TouchableOpacity, View, Text } from 'react-native';
import styles from './styles';
import Close from '../../assets/svgs/Close';
import mainStyles from '../../mainStyles';
import { ActivityIndicator } from 'react-native';

const AlertConfirmarPresenca = ({close, actionPositive, actionNegative, loading}) => {
    return (
        <View style={styles.container}>
            <TouchableOpacity style={mainStyles.overlayBlue} onPress={() => close()} activeOpacity={1}></TouchableOpacity>
            <View style={styles.box}>
                <View>
                    <TouchableOpacity style={styles.btnClose} onPress={() => close()}>
                        <Close style={styles.icClose} />
                    </TouchableOpacity>
                    <Text style={styles.title}>Status de plantão</Text>
                    <View style={styles.divider}></View>
                    <Text style={styles.confirm}>{`Você ainda está no plantão de vendas?\nEm caso positivo, responda no botão abaixo:`}</Text>
                    {!loading &&
                        <>
                            <TouchableOpacity style={[mainStyles.btnBlueNew, styles.btn]} onPress={() => actionPositive()}>
                                <Text style={mainStyles.btnTextBlueNew}>ESTOU NO PLANTÃO</Text>
                            </TouchableOpacity>
                            <TouchableOpacity style={[mainStyles.btnBlueNew, styles.btn, {marginTop: 15, backgroundColor: "#FF8689"}]} onPress={() => actionNegative()}>
                                <Text style={mainStyles.btnTextBlueNew}>SAIR DA FILA</Text>
                            </TouchableOpacity>
                        </>
                    }
                    {loading &&
                        <View style={{  justifyContent: "center", alignItems: "center", height: 140 }}>
                            <ActivityIndicator size="large" color="#00467F" />
                        </View>
                    }
                </View>
            </View>
        </View>
    );
}

export default AlertConfirmarPresenca;