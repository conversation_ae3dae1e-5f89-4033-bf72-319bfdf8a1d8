import { StyleSheet, Dimensions } from 'react-native';

const windowWidth = Dimensions.get('window').width;

const marginBetweenDates = 7;

const styles = StyleSheet.create({
    dates: {
        paddingLeft: (windowWidth * 0.05) - marginBetweenDates,
        paddingRight: (windowWidth * 0.05) - marginBetweenDates
    },
    boxDays: {
        backgroundColor: "#F2F2F2",
        height: 67,
        width: 67,
        alignItems: "center",
        justifyContent: "center",
        marginLeft: marginBetweenDates,
        marginRight: marginBetweenDates,
        overflow: "visible",
        position: "relative"
    },
    boxDaysActive: {
      backgroundColor: "#90B0C0"  
    },
    day: {
        fontFamily: "Ubuntu-Regular",
        color: "#00467F",
        fontSize: 14,
        textTransform: "uppercase",
        letterSpacing: 0.46
    },
    dayActive: {
        color: "#FFF"
    },
    numberDay: {
        fontFamily: "Ubuntu-Light",
        color: "#000",
        fontSize: 27
    },
    numberDayActive: {
        color: "#FFF"
    },
    borderDayActive: {
        fontFamily: "Ubuntu-Light",
        color: "#00467F",
        fontSize: 12,
        letterSpacing: 0.46,
        position: "absolute",
        bottom: -20
    },
    boxBorderActive: {
        borderColor: "#4EA1CC",
        borderWidth: 1
    }
});

export default styles;