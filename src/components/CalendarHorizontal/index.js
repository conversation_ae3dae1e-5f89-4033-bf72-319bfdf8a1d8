import moment, { min } from 'moment';
import React, { useEffect, useRef, useState } from 'react';
import { FlatList, Text, TouchableOpacity, View } from 'react-native';

import styles from './styles';

const CalendarHorizontal = ({selectedDate, setSelectedDate, minDate}) => {
    const flatListRef = useRef();

    const [days, setDays] = useState([]);

    useEffect(() => {
        getDays();
    }, [selectedDate]);

    useEffect(() => {
        goToCurrentDay();
    }, [days, selectedDate]);

    const monthIsSameMinDate = () => {
        if(!minDate) return false;

        let selectedMonth = moment(selectedDate).format('YYYY-MM');
        let minMonth = moment(minDate).format('YYYY-MM');

        if(moment(selectedMonth).isSame(moment(minMonth))){
            console.log('true')
            return true;
        }

        return false;
    }

    const getDays = () => {
        let startOfMonth = moment(selectedDate).startOf('month');
        let endOfMonth = moment(selectedDate).endOf('month');

        if(monthIsSameMinDate()){
            startOfMonth = moment(minDate);
        }

        let toDays = [];
        let currentDay = startOfMonth;
        let i = 1;

        while (currentDay <= endOfMonth) {
            toDays.push({
                id: i,
                date: currentDay.format('YYYY-MM-DD'),
                isToday: currentDay.isSame(new Date(), "day"),
                number: currentDay.format('DD'),
                text: currentDay.format('ddd')
            })
            currentDay = currentDay.clone().add(1, 'd');
            i++;
        }

        setDays(toDays);
    }

    const goToCurrentDay = () => {
        let minPosition = monthIsSameMinDate() ? parseInt(minDate.split('-')[2]) : 0;
        let currentDay = parseInt(selectedDate.split('-')[2]);
        console.log(minPosition, currentDay);
        let position = currentDay - minPosition - 2.75;
        position = position < 0 ? 0 : position;
        setTimeout(() => {
            if (flatListRef.current) {
                flatListRef.current.scrollToIndex({ animated: true, index: position })
            }
        }, 200)
    }

    return (
        <View style={styles.dates}>
            <FlatList
                ref={flatListRef}
                data={days}
                horizontal={true}
                contentContainerStyle={{ paddingTop: 15, paddingBottom: 20 }}
                keyExtractor={(item) => item.id}
                // snapToInterval={74}
                onScrollToIndexFailed={info => {
                    const wait = new Promise(resolve => setTimeout(resolve, 200));
                    wait.then(() => {
                        flatListRef.current?.scrollToIndex({ index: info.index, animated: true });
                    });
                }}
                renderItem={({ item, index }) => {
                    return (
                        <TouchableOpacity
                            key={index}
                            style={[styles.boxDays, selectedDate === item.date ? styles.boxDaysActive : null, item.isToday ? styles.boxBorderActive : null]}
                            onPress={() => setSelectedDate(item.date)}
                        >
                            <Text style={[styles.day, selectedDate === item.date ? styles.dayActive : null]}>{item.text}</Text>
                            <Text style={[styles.numberDay, selectedDate === item.date ? styles.numberDayActive : null]}>{item.number}</Text>
                            {item.isToday &&
                                <Text style={styles.borderDayActive}>Hoje</Text>
                            }
                        </TouchableOpacity>
                    );
                }}
            />
        </View>
    );
}
export default CalendarHorizontal;