import React from 'react';
import { TouchableOpacity, View, Text } from 'react-native';
import styles from './styles';
import Close from '../../assets/svgs/Close';
import mainStyles from '../../mainStyles';

const AlertRed = (props) => {
    return (
        <View style={styles.container}>
            <TouchableOpacity style={mainStyles.overlayBlue} onPress={() => props.close()} activeOpacity={1}></TouchableOpacity>
            <View style={styles.box}>
                <View>
                    <TouchableOpacity style={styles.btnClose} onPress={() => props.close()}>
                        <Close style={styles.close} />
                    </TouchableOpacity>
                    <Text style={styles.nameTraining}>{props.title}</Text>
                    <View style={styles.divider}></View>
                    <Text style={styles.text}>Se você sair <Text style={{color: "#FF8689"}}>perderá todo o progresso,</Text>{`\ndeseja sair?`}</Text>
                    <View style={{flexDirection: "row", justifyContent: "space-between"}}>
                        <TouchableOpacity style={[mainStyles.btnOutlineBlue, mainStyles.buttonW48]} onPress={() => props.close()}>
                            <Text style={mainStyles.btnTextOutlineBlue}>NÃO</Text>
                        </TouchableOpacity>
                        <TouchableOpacity style={[mainStyles.btnBlueLight, styles.btnRed, mainStyles.buttonW48]} onPress={() => props.route()}>
                            <Text style={mainStyles.btnTextCenterBlueLight}>SIM</Text>
                        </TouchableOpacity>
                    </View>
                </View>
            </View>
        </View>
    );
}

export default AlertRed;