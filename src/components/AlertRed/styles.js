import { StyleSheet } from 'react-native';

const styles = StyleSheet.create({
    container: {
        position: "absolute",
        top: 0,
        bottom: 0,
        left: 0,
        right: 0,
        zIndex: 9999,
        flexDirection: "column",
        justifyContent: "center",
        alignItems: "center"
    },
    box: {
        backgroundColor: "#FFF",
        paddingTop: 20,
        paddingBottom: 20,
        width: "95%",
        paddingLeft: 15,
        paddingRight: 15
    },
    nameTraining: {
        fontFamily: 'Ubuntu-Regular',
        color: '#00467F',
        letterSpacing: 1,
        fontSize: 15,
        marginBottom: 5
    },
    divider: {
        borderBottomColor: "#DADADA",
        borderBottomWidth: 1,
        marginTop: 15
    },
    text: {
        fontFamily: 'Ubuntu-Light',
        color: '#828282',
        letterSpacing: 1,
        fontSize: 14,
        marginTop: 15,
        marginBottom: 25
    },
    btn: {
        backgroundColor: '#00467F',
        height: 46,
        alignItems: "center",
        justifyContent: "center",
        borderRadius: 3,
        marginTop: 30,
        width: "45%"
    },
    btnBorder: {
        height: 46,
        alignItems: "center",
        justifyContent: "center",
        borderRadius: 3,
        marginTop: 30,
        borderColor: "#00467F",
        borderWidth: 1,
        width: "45%"
    },
    btnText: {
        fontSize: 14,
        fontFamily: 'Ubuntu-Medium',
        letterSpacing: 1,
        textAlign: "center",
        color: '#F2F2F2'
    },
    textBlue: {
        color: "#00467F"
    },
    btnClose: {
        position: "absolute",
        top: -10,
        right: -10,
        zIndex: 999
    },
    close: {
        color: "#2D719F",
        width: 18
    },
    btnRed: {
        backgroundColor: "#FF8689",
        borderColor: "#FF8689"
    }
});

export default styles;