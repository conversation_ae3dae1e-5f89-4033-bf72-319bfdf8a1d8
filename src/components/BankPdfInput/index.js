import React, { useEffect, useState } from 'react';
import { TouchableOpacity, View, Text, Alert, ActivityIndicator } from 'react-native';
import styles from './styles';
import mainStyles from '../../mainStyles';
import DocumentPicker from 'react-native-document-picker';
import api from '../../services/api';
import PictureBank from '../../assets/svgs/PictureBank';
import { useBank } from '../../context/bank';

const BankPdfInput = ({ type, label }) => {
    const key = `bank_document_${type}`;

    const [loading, setLoading] = useState(true);
    const [pdfPath, setPdfPath] = useState(null);

    const { bankAccount, setBankAccount } = useBank();

    useEffect(() => {
        getPdfPath();
    }, []);

    const getPdfPath = async () => {
        setLoading(true);
        const storedPath = bankAccount[key] ?? null;
        setPdfPath(storedPath);
        setLoading(false);
    };

    const openFilePicker = async () => {
        try {
            setLoading(true);
            const res = await DocumentPicker.pickSingle({
                type: [DocumentPicker.types.pdf],
            });

            savePdf(res);
        } catch (err) {
            setLoading(false);
        }
    };

    const savePdf = async (file) => {
        const path = file.uri;
        const data = new FormData();

        data.append(type, {
            uri: path,
            type: file.type,
            name: file.name,
        });

        await api.post('/bank-account/user/upload', data).then(() => {
            setPdfPath(path);
            setBankAccount(prev => {
                let newBankAccount = { ...prev };
                newBankAccount[key] = path;
                return newBankAccount;
            });
        }).catch(error => {
            Alert.alert('Erro ao enviar arquivo', error?.response?.data?.message ?? '');
        }).finally(() => setLoading(false));
    };

    return (
        <View>
            <TouchableOpacity style={styles.btnUpload} onPress={() => openFilePicker()}>
                {loading &&
                    <ActivityIndicator size="small" color="#CCC" />
                }
                {!loading && pdfPath &&
                    <Text style={styles.fileName}>{pdfPath.split('/').pop()}</Text>
                }
                {!loading && !pdfPath &&
                    <PictureBank /> // Ícone de arquivo PDF
                }
            </TouchableOpacity>
            {label &&
                <Text style={[mainStyles.label, styles.labelSmaller]}>{label}</Text>
            }
        </View>
    );
};

export default BankPdfInput;
