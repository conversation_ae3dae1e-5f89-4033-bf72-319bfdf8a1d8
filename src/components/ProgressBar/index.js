import React from "react";
import { View, Text, StyleSheet } from "react-native";

const ProgressBar = ({ progress }) => {
  const barColor = progress === 100 ? "#1B9C20" : "#2D719F";
  const textColor = progress === 100 ? "#fff" : "#000";

  return (
    <View style={styles.container}>
      <View style={[styles.fill, { width: `${progress}%`, backgroundColor: barColor }]} />
      <Text style={[styles.text, { color: textColor }]}>{progress}%</Text>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    height: 18,
    backgroundColor: "#D9D9D9",
    borderRadius: 5,
    overflow: "hidden",
    position: "relative",
    justifyContent: "center",
  },
  fill: {
    height: "100%",
  },
  text: {
    position: "absolute",
    right: 5,
    fontSize: 10,
    fontWeight: "bold"
  },
});

export default ProgressBar;