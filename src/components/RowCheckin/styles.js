import { StyleSheet, Dimensions } from 'react-native';

const windowWidth = Dimensions.get('window').width;
const windowHeight = Dimensions.get('window').height;

const styles = StyleSheet.create({
    container: {
        position: "absolute",
        top: 0,
        bottom: 0,
        left: 0,
        right: 0,
        zIndex: 999999
    },
    content: {
        backgroundColor: "#FFF",
        width: windowWidth * 0.8,
        marginLeft: windowWidth * 0.1,
        height: windowHeight * 0.65,
        marginTop: windowHeight * 0.25,
        borderRadius: 12,
        shadowColor: "#000",
        shadowOffset: {
            width: 0,
            height: 9,
        },
        shadowOpacity: 0.1,
        shadowRadius: 11.95,
        elevation: 12,
        paddingTop: 40,
        paddingBottom: 40,
        paddingLeft: 20,
        paddingRight: 20
    },
    title: {
        fontFamily: "Roboto-Light",
        fontSize: 24,
        letterSpacing: 1,
        color: "#00467F",
        marginBottom: 25,
        marginLeft: 25
    },
    titleCheckin: {
        fontFamily: "Roboto-Bold",
    },
    count: {
        color: "#828282",
        fontFamily: "Roboto-Bold",
        fontSize: 28,
        marginRight: 10,
        width: 70,
        textAlign: "center",
    },
    countActive: {
        color: "#00467F",
        fontFamily: "Roboto-Bold",
        fontSize: 28,
        marginRight: 10,
        width: 70,
        textAlign: "center",
    },
    personName: {
        color: "#828282",
        fontFamily: "Roboto-Bold",
        letterSpacing: 1
    },
    personStatus: {
        color: "#828282",
        fontFamily: "Roboto-Regular",
        letterSpacing: 1
    },
    row: {
        flexDirection: "row",
        marginBottom: 5,
        alignItems: "center",
        borderWidth: 1,
        borderColor: "#FFF",
        borderRadius: 12,
        paddingVertical: 10,
        marginLeft: 10,
        marginRight: 10,
    },
    rowActive: {
        flexDirection: "row",
        marginBottom: 5,
        alignItems: "center",
        borderWidth: 1,
        borderColor: "#4EA1CC",
        borderRadius: 12,
        paddingVertical: 10,
        marginLeft: 10,
        marginRight: 10,
    },
    personNameActive: {
        color: "#00467F",
        fontFamily: "Roboto-Bold",
        letterSpacing: 1,
        maxWidth: 130
    },
    superTitle: {
        color: "#00467F",
        fontFamily: "Roboto-Regular",
        letterSpacing: 1
    },
    super: {
        marginVertical: 3
    },
    superName: {
        color: "#828282",
        fontFamily: "Roboto-Regular",
        letterSpacing: 1,
    },
    personStatusActive: {
        color: "#1B9C20",
        fontFamily: "Roboto-Regular",
        letterSpacing: 1
    },
    personStatusInactive: {
        color: "#FF6542",
        fontFamily: "Roboto-Regular",
        letterSpacing: 1
    },
    icSmile: {
        position: "absolute",
        right: 20,
        width: 30
    },

    btnClose: {
        position: "absolute",
        width: 50,
        height: 50,
        alignItems: "center",
        justifyContent: "center",
        top: -25,
        right: 40,
        backgroundColor: '#00467F',
        borderRadius: 50
    }
});

export default styles;