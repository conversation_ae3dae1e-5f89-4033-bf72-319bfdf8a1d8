import React, { useState, useEffect } from 'react';
import { TouchableOpacity, View, Text, ScrollView, Alert, ActivityIndicator } from 'react-native';
import styles from './styles';
import Close from '../../assets/svgs/Close';
import Calendar from '../../assets/svgs/Calendar';
import mainStyles from '../../mainStyles';

import { useService } from '../../context/service';
import api from '../../services/api';

const RowCheckin = (props) => {

    const [loading, setLoading] = useState(true);
    const [brokers, setBrokers] = useState([]);

    const { queue } = useService();
    
    useEffect(() => {
        getBrokers();
    }, []);

    const getBrokers = () => {
        api.get('/fila/lista')
        .then(res => {
            console.log(res.data.corretores);
            setBrokers(res.data.corretores);
            setLoading(false);
        })
        .catch(err => {
            Alert.alert('Erro', 'Não foi possível obter a lista');
            setLoading(false);
        });
    }

    return (
        <View style={styles.container}>
            <TouchableOpacity style={mainStyles.overlay} onPress={() => props.close()} activeOpacity={1}></TouchableOpacity>
            <View style={styles.content}>
                <TouchableOpacity style={styles.btnClose} onPress={() => props.close()}>
                    <Close />
                </TouchableOpacity>
                <Text style={styles.title}>{`Fila por ordem\nde`} <Text style={styles.titleCheckin}>{queue.tipo_fila === 'sorteio' ? 'sorteio' : 'Check In'}</Text></Text>
                {loading &&
                    <View style={{flex: 1, justifyContent: "center", alignItems: "center"}}>
                        <ActivityIndicator size="large" color="#00467F" />
                    </View>
                }
                {!loading &&
                    <ScrollView showsVerticalScrollIndicator={false}>
                        <View>
                            {brokers.map((broker,index) => {
                                if(broker.is_user_logado){
                                    return(
                                        <View key={index} style={styles.rowActive}>
                                            {broker.posicao &&
                                                <Text style={styles.countActive}>{broker.posicao}º</Text>
                                            }
                                            {!broker.posicao &&
                                                <Calendar style={{color: "#00467F", width: 20, marginLeft: 10, marginRight: 30}}/>
                                            }
                                            <View>
                                                <Text style={styles.personNameActive}>{broker.apelido}</Text>
                                                {broker.superintendente &&
                                                    <Text style={styles.super}>
                                                        <Text style={styles.superTitle}>Sup. </Text>
                                                        <Text style={styles.superName}>{broker.superintendente.apelido}</Text>
                                                    </Text>
                                                }
                                                {broker.status_atendimento !== 'Disponível' &&
                                                    <Text style={styles.personStatusInactive}>{broker.status_atendimento}</Text>
                                                }
                                                {broker.status_atendimento === 'Disponível' &&
                                                    <Text style={styles.personStatusActive}>{broker.status_atendimento}</Text>
                                                }
                                            </View>
                                        </View>
                                    );
                                } else {
                                    return(
                                        <View key={index} style={styles.row}>
                                            {broker.posicao &&
                                                <Text style={styles.count}>{broker.posicao}º</Text>
                                            }
                                            {!broker.posicao &&
                                                <Calendar style={{color: "#CCC", width: 20, marginRight: 32}}/>
                                            }
                                            <View>
                                                <Text style={styles.personName}>{broker.apelido}</Text>

                                                {broker.superintendente &&
                                                    <Text style={styles.super}>
                                                        <Text style={styles.superTitle}>Sup. </Text>
                                                        <Text style={styles.superName}>{broker.superintendente.apelido}</Text>
                                                    </Text>
                                                }

                                                {broker.status_atendimento !== 'Disponível' &&
                                                    <Text style={styles.personStatusInactive}>{broker.status_atendimento}</Text>
                                                }
                                                {broker.status_atendimento === 'Disponível' &&
                                                    <Text style={styles.personStatusActive}>{broker.status_atendimento}</Text>
                                                }
                                            </View>
                                        </View>
                                    );
                                }
                            })}
                        </View>
                    </ScrollView>
                }
            </View>
        </View>
    );
}

export default RowCheckin;