import React, { useState, useEffect } from 'react';
import { TouchableOpacity, View, Text, Linking, ScrollView, Alert, ActivityIndicator } from 'react-native';
import styles from './styles';
import Close from '../../assets/svgs/Close';
import mainStyles from '../../mainStyles';

const LightboxScheduling = (props) => {

    const [loading, setLoading] = useState(true);

    return (
        <View style={styles.container}>
            <TouchableOpacity style={mainStyles.overlay} onPress={() => props.close()} activeOpacity={1}></TouchableOpacity>
            <View style={styles.content}>
                <TouchableOpacity style={[styles.btnClose, props.style]} onPress={() => props.close()}>
                    <Close style={props.style} />
                </TouchableOpacity>
                <Text style={styles.title}>{props.title}</Text>
                {props.children}
            </View>
        </View>
    );
}

export default LightboxScheduling;