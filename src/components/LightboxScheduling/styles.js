import { StyleSheet, Dimensions } from 'react-native';

const windowWidth = Dimensions.get('window').width;
const windowHeight = Dimensions.get('window').height;

const styles = StyleSheet.create({
    container: {
        position: "absolute",
        top: 0,
        bottom: 0,
        left: 0,
        right: 0,
        zIndex: 999999
    },
    content: {
        backgroundColor: "#FFF",
        width: windowWidth * 0.8,
        marginLeft: windowWidth * 0.1,
        minHeight: windowHeight * 0.35,
        marginTop: windowHeight * 0.20,
        borderRadius: 12,
        shadowColor: "#000",
        shadowOffset: {
            width: 0,
            height: 9,
        },
        shadowOpacity: 0.1,
        shadowRadius: 11.95,
        elevation: 12,
        paddingTop: 40,
        paddingBottom: 40,
        paddingLeft: 20,
        paddingRight: 20
    },
    title: {
        fontFamily: "Ubuntu-Regular",
        fontSize: 13,
        letterSpacing: 1,
        color: "#00467F",
        textAlign: "center",
        paddingBottom: 12,
        marginBottom: 25,
        borderBottomWidth: 1,
        borderBottomColor: "rgba(0, 70, 127, 0.3)"
    },
    btnClose: {
        position: "absolute",
        width: 50,
        height: 50,
        alignItems: "center",
        justifyContent: "center",
        top: -25,
        right: 20,
        backgroundColor: '#00467F',
        borderRadius: 50
    },
    btnWhats: {
        marginBottom: 20
    }
});

export default styles;