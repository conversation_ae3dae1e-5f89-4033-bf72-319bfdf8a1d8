import React, { useRef, useState, useEffect } from 'react';
import { View, ScrollView, Image, Dimensions, StyleSheet, TouchableOpacity, Linking } from 'react-native';
import { useAuth } from '../../context/auth';

const { width: windowWidth } = Dimensions.get('window');

const margin = 40;
const viewportWidth = windowWidth - margin;

const Carousel = () => {
  const { home } = useAuth();

  const scrollViewRef = useRef(null);
  const [activeIndex, setActiveIndex] = useState(0);
  const [bannersList, setBannersList] = useState([]);

  const [banners, setBanners] = useState([]);

  useEffect(() => {
    let vitrine = home?.home?.vitrine;
    setBanners(Array.isArray(vitrine) ? vitrine : []);
  }, [home]);

  useEffect(() => {
    if(banners.length > 0){
      setBannersList([banners[banners.length - 1], ...banners, banners[0]]);
    }
  }, [banners]);

  const onScroll = (event) => {
    const index = Math.round(event.nativeEvent.contentOffset.x / viewportWidth);
    if (index === 0) {
      setTimeout(() => {
        scrollViewRef.current.scrollTo({ x: viewportWidth * banners.length, animated: false });
        setActiveIndex(banners.length - 1);
      }, 300);
    } else if (index === banners.length + 1) {
      setTimeout(() => {
        scrollViewRef.current.scrollTo({ x: viewportWidth, animated: false });
        setActiveIndex(0);
      }, 300);
    } else {
      setActiveIndex(index - 1);
    }
  };

  const goToImage = (index) => {
    scrollViewRef.current.scrollTo({ x: (index + 1) * viewportWidth, animated: true });
    setActiveIndex(index);
  };
  

  return (
    <View style={styles.container}>
      <ScrollView
        horizontal
        pagingEnabled
        ref={scrollViewRef}
        onScroll={onScroll}
        showsHorizontalScrollIndicator={false}
        scrollEventThrottle={16}
        onLayout={() => scrollViewRef.current.scrollTo({ x: viewportWidth, animated: false })}
      >
        {bannersList.map((banner, index) => (
          <TouchableOpacity 
            key={index} 
            style={styles.imageContainer} 
            disabled={banner.url === null}
            onPress={() => Linking.openURL(banner.url)}
          >
            <Image source={{ uri: banner.imagem_url }} style={styles.image} />
          </TouchableOpacity>
        ))}
      </ScrollView>
      {banners.length > 1 &&
        <View style={styles.pagination}>
          {banners.map((_, index) => (
            <TouchableOpacity key={index} onPress={() => goToImage(index)}>
              <View style={[styles.dot, activeIndex === index ? styles.activeDot : styles.inactiveDot]} />
            </TouchableOpacity>
          ))}
        </View>
      }
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    marginTop: margin / 2,
    marginBottom: margin / 2,
    width: viewportWidth,
    marginLeft: margin / 2
  },
  imageContainer: {
    height: viewportWidth * 9 / 16,
    width: viewportWidth
  },
  image: {
    height: '100%',
    width: '100%',
  },
  pagination: {
    flexDirection: 'row',
    justifyContent: 'center',
    marginVertical: 10,
  },
  dot: {
    width: 20,
    height: 10,
    borderRadius: 5,
    marginHorizontal: 5,
  },
  activeDot: {
    backgroundColor: '#90B0C0',
  },
  inactiveDot: {
    backgroundColor: '#F2F2F2',
  },
});

export default Carousel;