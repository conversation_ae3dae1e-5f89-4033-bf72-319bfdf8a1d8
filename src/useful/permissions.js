import { Alert, PermissionsAndroid, Platform, Linking } from "react-native";

export const requestCameraPermission = async () => {
    if (Platform.OS === 'android') {
        try {
            const granted = await PermissionsAndroid.request(
                PermissionsAndroid.PERMISSIONS.CAMERA
            );
            if (granted === PermissionsAndroid.RESULTS.GRANTED) {
                return true;
            } else {
                Alert.alert('Permissão da câmera negada', 'Acesse as configurações para conceder a permissão.', [
                    { text: "Cancelar" },
                    { text: "Configurações", onPress: () => Linking.openSettings() },
                ]);
                return false;
            }
        } catch (error) {
            Alert.alert('Erro ao solicitar permissão da câmera:', error, [
                { text: "Cancelar" },
                { text: "Configurações", onPress: () => Linking.openSettings() },
            ]);
            return false;
        }
    } else {
        return true;
    }
}