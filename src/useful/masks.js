export const cpfMask = value => {
    return value
        .replace(/\D/g, '') // substitui qualquer caracter que nao seja numero por nada
        .replace(/(\d{3})(\d)/, '$1.$2') // captura 2 grupos de numero o primeiro de 3 e o segundo de 1, apos capturar o primeiro grupo ele adiciona um ponto antes do segundo grupo de numero
        .replace(/(\d{3})(\d)/, '$1.$2')
        .replace(/(\d{3})(\d{1,2})/, '$1-$2')
        .replace(/(-\d{2})\d+?$/, '$1') // captura 2 numeros seguidos de um traço e não deixa ser digitado mais nada
}

export const cnpjMask = value => {

    value = value.replace(/\D/g, "")

    return value
        .replace(/(\d{2})(\d)/, '$1.$2') // captura 2 grupos de numero o primeiro de 3 e o segundo de 1, apos capturar o primeiro grupo ele adiciona um ponto antes do segundo grupo de numero
        .replace(/(\d{3})(\d)/, '$1.$2')
        .replace(/(\d{3})(\d)/, '$1/$2')
        .replace(/(\d{4})(\d)/, '$1-$2')
        .replace(/(-\d{2})\d+?$/, '$1');
}

export const cpfCnpjMask = value => {
    value = value.replace(/\D/g, "")

    if (value.length <= 11) { //CPF

        return value
            .replace(/(\d{3})(\d)/, '$1.$2') // captura 2 grupos de numero o primeiro de 3 e o segundo de 1, apos capturar o primeiro grupo ele adiciona um ponto antes do segundo grupo de numero
            .replace(/(\d{3})(\d)/, '$1.$2')
            .replace(/(\d{3})(\d{1,2})/, '$1-$2')
            .replace(/(-\d{2})\d+?$/, '$1')

    } else { //CNPJ

        return value
            .replace(/(\d{2})(\d)/, '$1.$2') // captura 2 grupos de numero o primeiro de 3 e o segundo de 1, apos capturar o primeiro grupo ele adiciona um ponto antes do segundo grupo de numero
            .replace(/(\d{3})(\d)/, '$1.$2')
            .replace(/(\d{3})(\d)/, '$1/$2')
            .replace(/(\d{4})(\d)/, '$1-$2')
            .replace(/(-\d{2})\d+?$/, '$1');

    }
}

export const phoneMask = value => {
    let res = value.replace(/\D/g, '');
    // if(res.length > 10){
    return res
        .replace(/\D/g, '')
        .replace(/(\d{1})(\d)/, '($1$2')
        .replace(/(\d{2})(\d)/, '$1) $2')
        .replace(/(\d{5})(\d)/, '$1-$2')
        .replace(/(-\d{4})\d+?$/, '$1');
    // } else {
    //     return res
    //     .replace(/\D/g, '')
    //     .replace(/(\d{1})(\d)/, '($1$2')
    //     .replace(/(\d{2})(\d)/, '$1) $2')
    //     .replace( /(\d{4})(\d)/, '$1-$2')
    //     .replace(/(-\d{4})\d+?$/, '$1');
    // }
}

export const cepMask = value => {
    return value
        .replace(/\D/g, '')
        .replace(/(\d{5})(\d)/, '$1-$2')
        .replace(/(-\d{3})\d+?$/, '$1')
}

// export const money = value => {
//     value = value.toFixed(2).split('.');
//     value[0] = "R$" + value[0].split(/(?=(?:...)*$)/).join('.');
//     return value.join(',');
// }

export const money = (value) => {
    value = String(value).replace(/\D/g, '');
    value = value.replace(/^0+(?!$)/, '');
    while (value.length < 3) value = '0' + value;  
    const cents = value.slice(-2);
    let reais = value.slice(0, -2);
    reais = reais.replace(/\B(?=(\d{3})+(?!\d))/g, '.');  
    return `R$ ${reais},${cents}`;
};

export const dateMask = value => {
    let v = value.replace(/\D/g, '').slice(0, 8);
    if (v.length >= 5) {
        return `${v.slice(0, 2)}/${v.slice(2, 4)}/${v.slice(4)}`;
    }
    else if (v.length >= 3) {
        return `${v.slice(0, 2)}/${v.slice(2)}`;
    }
    return v;
}

export const timeMask = value => {
    let res = value.replace(/\D/g, '');

    return res
        .replace(/\D/g, '')
        .replace(/(\d{2})(\d)/, '$1:$2')
        .replace(/(:\d{2})\d+?$/, '$1');
}