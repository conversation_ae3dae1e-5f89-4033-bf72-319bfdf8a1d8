export const convertDateToAmerican = date => {
    const arrDate = date.split('/');
    return arrDate[2]+"-"+arrDate[1]+"-"+arrDate[0];
}

export const convertDateToBrazil = date => {
    const arrDate = date.split('-');
    return arrDate[2]+"/"+arrDate[1]+"/"+arrDate[0];
}

export const clearPhone = phone => {
    if(!phone) return null;
    phone = phone.replace(/\D/g, "");
    return `${phone}`;
}

export const clearAcentos = text =>
{                                                            
    text = text.replace(new RegExp('[ÁÀÂÃ]','gi'), 'a');
    text = text.replace(new RegExp('[ÉÈÊ]','gi'), 'e');
    text = text.replace(new RegExp('[ÍÌÎ]','gi'), 'i');
    text = text.replace(new RegExp('[ÓÒÔÕ]','gi'), 'o');
    text = text.replace(new RegExp('[ÚÙÛ]','gi'), 'u');
    text = text.replace(new RegExp('[Ç]','gi'), 'c');
    return text;                 
}

export const formatCurrencyToNumber = (value) => {
  if (!value || typeof value !== 'string') {
    return 0; // Ou outro valor padrão, como null, dependendo do caso
  }
  return parseFloat(value.replace(/[R$\s.]/g, '').replace(',', '.'));
};