import {Dimensions, Platform, StyleSheet} from 'react-native';

const windowWidth = Dimensions.get('window').width;
const windowHeight = Dimensions.get('window').height;

const mainStyles = StyleSheet.create({
  overlay: {
    backgroundColor: 'rgba(255, 255, 255, 0.75)',
    position: 'absolute',
    top: 0,
    bottom: 0,
    left: 0,
    right: 0,
    zIndex: -9,
  },
  overlayBlack: {
    backgroundColor: 'rgba(0, 0, 0, 0.75)',
    position: 'absolute',
    top: 0,
    bottom: 0,
    left: 0,
    right: 0,
    zIndex: -9,
  },
  overlayBlue: {
    backgroundColor: 'rgba(13, 23, 28, 0.85)',
    position: 'absolute',
    top: 0,
    bottom: 0,
    left: 0,
    right: 0,
    zIndex: -9,
  },
  bold: {
    fontFamily: 'Ubuntu-Bold',
  },
  container: {
    width: windowWidth * 0.9,
    marginLeft: windowWidth * 0.052,
    marginRight: windowWidth * 0.05,
  },
  containerRh: {
    width: windowWidth,
    paddingLeft: windowWidth * 0.05,
    paddingRight: windowWidth * 0.05,
    paddingTop: 30,
    backgroundColor: '#FFF',
  },
  containerRegister: {
    width: windowWidth * 0.7,
    marginLeft: windowWidth * 0.15,
    marginRight: windowWidth * 0.15,
    paddingTop: 20,
    paddingBottom: 20,
  },
  centerContainer: {
    alignItems: 'center',
    justifyContent: 'center',
    paddingBottom: 20,
  },
  privateContainer: {
    paddingLeft: windowWidth * 0.05,
    paddingRight: windowWidth * 0.05,
    backgroundColor: '#FFF',
  },
  privateContainerHeaderFixed: {
    flex: 1,
  },
  wrapper: {
    flex: 1,
    backgroundColor: '#FFF',
  },
  wrapperMinHeight: {
    width: windowWidth,
    minHeight: windowHeight,
    backgroundColor: '#FFF',
  },
  contentBottomRegister: {
    marginTop: 0,
    backgroundColor: '#FFF',
    shadowRadius: 2,
    shadowOffset: {
      width: 0,
      height: -2,
    },
    shadowColor: '#CCC',
    elevation: 2,
    shadowOpacity: 0.5,
    paddingTop: 20,
    paddingBottom: 20,
  },
  wrapperRegister: {
    flex: 1,
    justifyContent: 'space-between',
  },
  buttonW48: {
    width: '48%',
  },
  btnDisabled: {
    opacity: 0.4
  },
  btnTextDisabled: {
    color: '#FFF',
  },
  btnOutlineWhite: {
    backgroundColor: 'transparent',
    borderWidth: 2,
    borderColor: '#4EA1CC',
    borderRadius: 5,
    height: 60,
    justifyContent: 'center',
  },
  btnTextOutlineWhite: {
    fontFamily: 'Ubuntu-Medium',
    color: '#F2F2F2',
    fontSize: Platform.OS === 'ios' ? 16 : 14,
    marginLeft: 25,
    letterSpacing: 1,
  },
  btnOutlineFullWhite: {
    backgroundColor: 'transparent',
    borderWidth: 2,
    borderColor: '#F2F2F2',
    borderRadius: 5,
    height: 60,
    justifyContent: 'center',
  },
  btnTextOutlineFullWhite: {
    fontFamily: 'Ubuntu-Medium',
    color: '#F2F2F2',
    fontSize: 14,
    marginLeft: 25,
    letterSpacing: 1,
  },
  btnTransparentWhite: {
    height: 40,
    justifyContent: 'center',
  },
  btnTextTransparentWhite: {
    fontFamily: 'Ubuntu-Regular',
    color: '#F2F2F2',
    fontSize: Platform.OS === 'ios' ? 14 : 12,
    letterSpacing: 2,
  },
  btnTextLink: {
    fontFamily: 'Ubuntu-Regular',
    color: '#4EA1CC',
    fontSize: Platform.OS === 'ios' ? 15 : 13,
    letterSpacing: 1,
    textDecorationLine: 'underline',
    textDecorationColor: '#4EA1CC',
  },
  btnOutlineBlue: {
    backgroundColor: 'transparent',
    borderWidth: 1,
    borderColor: '#1A374D',
    borderRadius: 0,
    height: 50,
    justifyContent: 'center',
    alignItems: 'center',
  },
  btnTextOutlineBlue: {
    fontFamily: 'Ubuntu-Regular',
    color: '#00467F',
    fontSize: Platform.OS === 'ios' ? 16 : 13,
    letterSpacing: 0,
  },
  btnOutlineBlueDark: {
    backgroundColor: 'transparent',
    borderWidth: 1,
    borderColor: '#00467F',
    borderRadius: 0,
    height: 60,
    justifyContent: 'center',
    alignItems: 'center',
    width: '85%',
    marginTop: 25
  },
  btnOutlineBlueDarkDisabled: {
    borderColor: "#979797"
  },
  btnTextOutlineBlueDark: {
    fontFamily: 'Ubuntu-Regular',
    color: '#00467F',
    fontSize: Platform.OS === 'ios' ? 15 : 13,
    textAlign: 'center',
    letterSpacing: 1
  },
  btnTextOutlineBlueDarkDisabled: {
    color: "#979797"
  },
  btnBlueLight: {
    backgroundColor: '#2D719F',
    borderWidth: 1,
    borderColor: '#2D719F',
    borderRadius: 0,
    height: 50,
    justifyContent: 'center',
    alignItems: 'center',
  },
  btnTextCenterBlueLight: {
    fontFamily: 'Ubuntu-Regular',
    color: '#FFF',
    fontSize: Platform.OS === 'ios' ? 16 : 13,
    letterSpacing: 0,
  },
  btnOutlineRed: {
    backgroundColor: 'transparent',
    borderWidth: 2,
    borderColor: '#FF312E',
    borderRadius: 5,
    height: 60,
    justifyContent: 'center',
  },
  btnTextOutlineRed: {
    fontFamily: 'Ubuntu-Medium',
    color: '#FF312E',
    fontSize: Platform.OS === 'ios' ? 16 : 14,
    marginLeft: 25,
    letterSpacing: 1,
  },
  btnBlue: {
    backgroundColor: '#00467F',
    borderWidth: 1,
    borderColor: '#00467F',
    borderRadius: 5,
    height: 60,
    justifyContent: 'center',
  },
  btnTextBlue: {
    fontFamily: 'Ubuntu-Medium',
    color: '#FFF',
    fontSize: Platform.OS === 'ios' ? 16 : 14,
    marginLeft: 25,
    letterSpacing: 2,
  },
  btnCenterOutlineBlue: {
    backgroundColor: 'transparent',
    borderWidth: 2,
    borderColor: '#4EA1CC',
    borderRadius: 5,
    height: 60,
    justifyContent: 'center',
    alignItems: 'center',
  },
  btnTextCenterOutlineBlue: {
    fontFamily: 'Ubuntu-Medium',
    color: '#00467F',
    fontSize: 14,
    letterSpacing: 1,
  },
  btnCenterBlue: {
    backgroundColor: '#4EA1CC',
    borderWidth: 1,
    borderColor: '#4EA1CC',
    borderRadius: 5,
    height: 60,
    justifyContent: 'center',
    alignItems: 'center',
  },
  btnTextCenterBlue: {
    fontFamily: 'Ubuntu-Medium',
    color: '#FFF',
    fontSize: Platform.OS === 'ios' ? 16 : 14,
    letterSpacing: 1,
  },
  btnTextCenterBlueDark: {
    fontFamily: 'Ubuntu-Medium',
    color: '#FFF',
    fontSize: Platform.OS === 'ios' ? 20 : 18,
    letterSpacing: 1,
  },
  btnCenterOutlineBlueDark: {
    backgroundColor: 'transparent',
    borderWidth: 2,
    borderColor: '#00467F',
    borderRadius: 5,
    height: 60,
    justifyContent: 'center',
    alignItems: 'center',
  },
  btnTextCenterOutlineBlueDark: {
    fontFamily: 'Ubuntu-Medium',
    color: '#00467F',
    fontSize: Platform.OS === 'ios' ? 20 : 18,
    letterSpacing: 1,
  },
  btnCenterBlueDark: {
    backgroundColor: '#00467F',
    borderWidth: 1,
    borderColor: '#00467F',
    borderRadius: 5,
    height: 60,
    justifyContent: 'center',
    alignItems: 'center',
  },
  btnTextCenterBlueDark: {
    fontFamily: 'Ubuntu-Medium',
    color: '#FFF',
    fontSize: Platform.OS === 'ios' ? 20 : 18,
    letterSpacing: 1,
  },
  rowUploads: {
    flexDirection: 'row',
    justifyContent: 'center',
    paddingBottom: 10
  },
  btnUploadCol: {
    width: '47%',
    flexDirection: 'column',
    paddingVertical: 10,
    alignItems: 'center',
    justifyContent: 'center',
    borderWidth: 1,
    borderColor: '#E0E0E0',
    borderRadius: 20,
    height: 140,
  },
  btnIconUploadM: {
    marginBottom: 10,
    marginTop: 3,
  },
  btnUploadCol2: {
    backgroundColor: '#F2F2F2',
    width: '35%',
    flexDirection: 'column',
    paddingVertical: 10,
    alignItems: 'center',
    justifyContent: 'center',
    borderBottomWidth: 14,
    borderBottomColor: '#90B0C0',
    height: 160,
    marginLeft: 7,
    marginRight: 7,
  },
  btnDocCol2: {
    width: '35%',
    flexDirection: 'column',
    paddingVertical: 10,
    alignItems: 'center',
    justifyContent: 'center',
    height: 160,
    marginLeft: 7,
    marginRight: 7,
    marginTop: 25,
    marginBottom: 15
  },
  btnTextUploadCol: {
    textAlign: 'center',
  },
  btnTextUploadCol2: {
    marginTop: -5,
  },
  btnUpload: {
    flexDirection: 'row',
    height: 80,
    alignItems: 'center',
    borderWidth: 1,
    borderColor: '#E0E0E0',
    borderRadius: 20,
    paddingRight: 20,
  },
  btnTextUpload: {
    color: '#4EA1CC',
    fontSize: 14,
    lineHeight: 20,
  },
  btnIconUpload: {
    marginRight: 20,
    marginLeft: 20,
  },
  btnUpload2: {
    backgroundColor: '#F2F2F2',
    flexDirection: 'column',
    alignItems: 'center',
    borderBottomWidth: 14,
    borderBottomColor: '#90B0C0',
    paddingTop: 20,
    paddingBottom: 20,
    paddingLeft: 5,
    paddingRight: 5,
    minWidth: 150,
  },
  btnTextUpload2: {
    fontFamily: 'Ubuntu-Regular',
    color: '#00467F',
    fontSize: 14,
    lineHeight: 20,
    letterSpacing: 1,
    marginTop: 10,
  },
  btnBlueNew: {
    backgroundColor: '#2D719F',
    height: 50,
    justifyContent: 'center',
    alignItems: 'center',
    // marginLeft: 8
  },
  btnTextBlueNew: {
    fontFamily: 'Ubuntu-Regular',
    color: '#FFF',
    fontSize: 13,
    letterSpacing: 1,
  },
  btnCenterOutlineBlueDarkNewBorder: {
    backgroundColor: 'transparent',
    borderWidth: 1,
    borderColor: '#1A374D',
    height: 50,
    justifyContent: 'center',
    alignItems: 'center',
  },
  btnTextOutlineBlueBorder: {
    fontFamily: 'Ubuntu-Regular',
    color: '#1A374D',
    fontSize: 13,
    lineHeight: 12.77,
    letterSpacing: 1,
    textTransform: 'uppercase',
  },
  boxPreview: {
    width: '47%',
    resizeMode: 'contain',
    borderRadius: 5,
    justifyContent: 'center',
    alignItems: 'center',
  },
  boxPreviewImageContent: {
    borderWidth: 2,
    borderColor: '#4EA1CC',
    width: '100%',
    resizeMode: 'contain',
    borderRadius: 5,
    justifyContent: 'center',
    alignItems: 'center',
  },
  boxPreview2: {
    width: 130,
    resizeMode: 'contain',
    justifyContent: 'center',
    alignItems: 'center',
    marginLeft: 7,
    marginRight: 7,
    marginTop: -9,
  },
  boxPreviewImageContent2: {
    marginTop: -5,
    height: 160,
    backgroundColor: '#F2F2F2',
    borderBottomWidth: 14,
    borderColor: '#FF6542',
    width: '100%',
    resizeMode: 'contain',
    justifyContent: 'center',
    alignItems: 'center',
  },
  boxPreviewOcr: {
    width: 130,
    resizeMode: 'contain',
    justifyContent: 'center',
    alignItems: 'center',
    marginLeft: 7,
    marginRight: 7,
    marginTop: -50,
  },
  boxPreviewImageContentOcr: {
    marginTop: 0,
    height: 160,
    backgroundColor: '#F2F2F2',
    borderBottomWidth: 14,
    borderColor: '#90B0C0',
    width: '100%',
    resizeMode: 'contain',
    justifyContent: 'center',
    alignItems: 'center',
  },
  boxPreviewImageContentOcrRed: {
    marginTop: 0,
    height: 160,
    backgroundColor: '#F2F2F2',
    borderBottomWidth: 14,
    borderColor: '#FF6542',
    width: '100%',
    resizeMode: 'contain',
    justifyContent: 'center',
    alignItems: 'center',
  },
  textCenter: {
    textAlign: 'center',
  },
  previewCol: {
    width: '90%',
    height: 130,
    resizeMode: 'contain',
    marginVertical: 10,
  },
  deletePreview: {
    fontFamily: 'Ubuntu-Medium',
    fontSize: 14,
    marginVertical: 10,
    color: '#FF6542',
    letterSpacing: 1,
  },
  inputText: {
    fontFamily: 'Ubuntu-Regular',
    color: '#828282',
    fontSize: Platform.OS === 'ios' ? 16 : 14,
    borderWidth: 1,
    borderColor: '#E0E0E0',
    paddingLeft: 15,
    height: 50,
    marginBottom: 20,
    letterSpacing: 1,
  },
  inputTextDisabled: {
    color: '#828282',
    backgroundColor: '#EEE'
  },
  inputBorder: {
    fontFamily: 'Ubuntu-Regular',
    color: '#828282',
    fontSize: Platform.OS === 'ios' ? 16 : 14,
    borderWidth: 1,
    borderColor: '#E0E0E0',
    paddingLeft: 15,
    height: 45,
    marginBottom: 25,
    letterSpacing: 1,
  },
  inputDisabled: {
    color: '#CCC',
  },
  label: {
    fontFamily: 'Ubuntu-Regular',
    marginBottom: Platform.OS === 'ios' ? 10 : 8,
    letterSpacing: 1,
    color: '#00467F',
    fontSize: Platform.OS === 'ios' ? 14 : 14,
  },
  labelMargin: {
    fontFamily: 'Ubuntu-Regular',
    marginBottom: Platform.OS === 'ios' ? 7 : 5,
    letterSpacing: 1.58,
    color: '#00467F',
    fontSize: Platform.OS === 'ios' ? 15 : 13,
  },
  inputReadOnly: {
    fontFamily: 'Ubuntu-Medium',
    color: '#BDBDBD',
    fontSize: Platform.OS === 'ios' ? 17 : 15,
    paddingLeft: 15,
    height: 50,
    marginBottom: 15,
    letterSpacing: 1,
    textTransform: 'uppercase',
    backgroundColor: '#F2F2F2',
  },
  labelBlue: {
    fontFamily: 'Ubuntu-Medium',
    paddingLeft: 15,
    letterSpacing: 1,
    marginBottom: 8,
    color: '#00467F',
    fontSize: Platform.OS === 'ios' ? 15 : 13,
  },
  inputEditable: {
    fontFamily: 'Ubuntu-Medium',
    color: '#828282',
    fontSize: Platform.OS === 'ios' ? 17 : 15,
    paddingTop: 0,
    paddingLeft: 15,
    paddingBottom: 15,
    letterSpacing: 1,
    textTransform: 'uppercase',
    backgroundColor: '#FFF',
    width: '100%',
  },
  inputLowercase: {
    textTransform: 'lowercase',
  },
  inputCapitalize: {
    textTransform: 'capitalize',
  },
  textService: {
    fontFamily: 'Ubuntu-Regular',
    letterSpacing: 0.8,
    fontSize: Platform.OS === 'ios' ? 14 : 10,
  },
  textBlueBold: {
    fontFamily: 'Ubuntu-Bold',
    letterSpacing: 0.8,
    color: '#00467F',
    fontSize: Platform.OS === 'ios' ? 18 : 16,
    maxWidth: 230,
    marginBottom: 2,
  },
  textBlue: {
    fontFamily: 'Ubuntu-Regular',
    letterSpacing: 0.8,
    color: '#00467F',
    fontSize: Platform.OS === 'ios' ? 18 : 16,
    maxWidth: 230,
  },
  textBlueLight: {
    fontFamily: 'Ubuntu-Regular',
    letterSpacing: 0.8,
    color: '#4EA1CC',
    fontSize: Platform.OS === 'ios' ? 18 : 16,
    maxWidth: 230,
    textTransform: 'uppercase',
  },
  textWhite: {
    fontFamily: 'Ubuntu-Regular',
    letterSpacing: 0.46,
    color: '#FFF',
    fontSize: Platform.OS === 'ios' ? 17 : 15,
    maxWidth: 230
  },
  progressBar: {
    width: '100%',
    height: 11,
    backgroundColor: '#F2F2F2',
    borderRadius: 3,
    marginTop: 5,
  },
  progress: {
    height: 11,
    backgroundColor: '#4EA1CC',
    borderRadius: 3,
  },
  progressBarNew: {
    width: '100%',
    height: 12,
    backgroundColor: '#D9D9D9',
    marginTop: 5,
  },
  progressNew: {
    height: 12,
    backgroundColor: '#2D719F',
  },
  questionOptions: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 15,
  },
  questionOptionsNew: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 15,
    backgroundColor: '#F2F2F2',
    minHeight: 65,
    paddingVertical: 10,
    borderLeftWidth: 15,
    borderLeftColor: '#90B0C0',
  },
  optionSelectedNew: {
    backgroundColor: '#62829A',
  },
  textSelectedNew: {
    color: '#FFF',
  },
  optionRadio: {
    height: 25,
    width: 25,
    borderRadius: 20,
    borderWidth: 1,
    borderColor: '#333',
    marginRight: 10,
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
  },
  optionRadioSelected: {
    borderColor: '#4EA1CC',
  },
  optionRadioNew: {
    borderWidth: 1,
    borderColor: '#333',
    marginRight: 10,
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
  },
  optionRadioSelectedNew: {
    borderColor: '#4EA1CC',
  },
  radioSelected: {
    height: 10,
    width: 10,
    borderRadius: 20,
    backgroundColor: '#4EA1CC',
  },
  textQuestion: {
    fontFamily: 'Ubuntu-Regular',
    color: '#00467F',
    fontSize: Platform.OS === 'ios' ? 15 : 15,
    flexShrink: 1,
    letterSpacing: 1,
  },
  textMargin: {
    marginLeft: 10,
  },
  textQuestionSelected: {
    color: '#4EA1CC',
  },
  textWhiteBold: {
    fontFamily: 'Ubuntu-Bold',
    letterSpacing: 0.8,
    color: '#FFF',
    fontSize: Platform.OS === 'ios' ? 30 : 26,
    marginBottom: 2,
  },
  textWhiteRegular: {
    fontFamily: 'Ubuntu-Regular',
    letterSpacing: 0.8,
    color: '#FFF',
    fontSize: Platform.OS === 'ios' ? 30 : 26,
    marginBottom: 2,
  },
  pdL: {
    paddingLeft: 10,
    marginTop: 10,
  },
  textAlert: {
    fontFamily: 'Ubuntu-Regular',
    letterSpacing: 1,
    color: '#828282',
    fontSize: Platform.OS === 'ios' ? 20 : 16,
  },
  textAlert2: {
    fontFamily: 'Ubuntu-Regular',
    letterSpacing: 1,
    color: '#828282',
    fontSize: Platform.OS === 'ios' ? 17 : 13,
    lineHeight: 18,
  },
  boxProfile: {
    alignItems: 'center',
    marginTop: 10,
  },
  imgProfile: {
    width: 180,
    height: 180,
    resizeMode: 'cover',
  },
  inputInfo: {
    fontFamily: 'Ubuntu-Regular',
    color: '#8e8e8e',
    fontSize: 14,
    marginTop: -20,
    marginBottom: 25,
  },
  inputInfoDashed: {
    marginTop: 10,
    marginBottom: 0,
    marginLeft: 15,
  },
  labelImage: {
    fontFamily: 'Ubuntu-Regular',
    color: '#8e8e8e',
    fontSize: 14,
    marginBottom: 10,
  },
  bgHourCheckin: {
    backgroundColor: '#2D719F',
    paddingTop: 4,
    paddingBottom: 4,
    width: '100%',
    maxWidth: 240,
    flexDirection: 'row',
    justifyContent: 'center',
    marginBottom: 15,
  },
  textHourCheckin: {
    fontFamily: 'Ubuntu-Regular',
    color: '#FFF',
    fontSize: 13,
    letterSpacing: 1,
  },
  sorteioText: {
    fontFamily: 'Ubuntu-Regular',
    color: '#00467F',
    marginTop: Platform.OS === 'ios' ? 8 : 5,
    fontSize: Platform.OS === 'ios' ? 16 : 14,
    letterSpacing: 1,
  },
  betaIco: {
    position: 'absolute',
    fontFamily: 'Ubuntu-Regular',
    color: '#FFF',
    fontSize: 12,
    letterSpacing: 0.46,
    textTransform: 'uppercase',
    marginLeft: 48,
    width: 58,
    top: 24,
    height: 16,
    justifyContent: 'center',
    textAlign: 'center',
    backgroundColor: '#4EA1CC',
  },
  woxAiTitle: {
    fontFamily: 'Ubuntu-Regular',
    color: '#FFF',
    fontSize: 15,
    letterSpacing: 0.46,
    textTransform: 'uppercase',
    marginLeft: 12,
    top: 4,
  },
  optionText: {
    fontFamily: "Ubuntu-Regular",
    fontSize: 14,
    lineHeight: 19,
    letterSpacing: 1,
    color: "#828282",
    borderBottomColor: "#DADADA",
    borderBottomWidth: 1,
    paddingBottom: 15,
    paddingTop: 15,
    width: windowWidth - 80
  }
});

export default mainStyles;
