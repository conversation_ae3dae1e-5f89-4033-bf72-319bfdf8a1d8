import {API_PRODUCTION_URL, API_SITE_PRODUCTION_URL} from '@env';
import axios from 'axios';

export const apisite = axios.create({
  baseURL: `${API_SITE_PRODUCTION_URL}/`,
});

const api = axios.create({
  baseURL: `${API_PRODUCTION_URL}/`,
});

api.interceptors.request.use(request => {
  console.log(`[API] ${request.method?.toUpperCase()} ${request.url}`);
  return request;
});

export default api;
