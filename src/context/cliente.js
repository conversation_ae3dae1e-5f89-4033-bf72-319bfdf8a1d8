import React, { createContext, useContext, useState } from "react";

export const ScheduleContext = createContext();

export const ClienteProvider = ({ children }) => {
    const [id, setId] = useState('');
    const [agendamentoId, setAgendamentoId] = useState('');
    const [inNome, setInNome] = useState('');
    const [imovelNome, setImovelNome] = useState('');
    const [origemTxt, setOrigemTxt] = useState('');
    const [inHorario, setInHorario] = useState('');
    const [inData, setInData] = useState('');
    const [inProduto, setInProduto] = useState('');

    const clearFields = () => {
        setId('');
        setAgendamentoId('');
        setInNome('');
        setImovelNome('');
        setOrigemTxt('');
        setInHorario('');
        setInData('');
        setInProduto('');
    }

    return (
        <ScheduleContext.Provider value={{
            id,
            agendamentoId,
            inNome,
            imovelNome,
            origemTxt,
            inHorario,
            inData,
            inProduto,
            setInProduto,
            setId,
            setInNome,
            setImovelNome,
            setOrigemTxt,
            setInHorario,
            setInData,
            setInProduto,
            setAgendamentoId,
            clearFields
        }}>
            {children}
        </ScheduleContext.Provider>
    );
}

export const useSchedule = () => {
    const context = useContext(ScheduleContext);
    return context;
}