import React, { createContext, useState, useContext, useEffect } from 'react';

const TrainginContext = createContext({});

export const TrainingProvider = ({children}) => {
    const [currentQuestion, setCurrentQuestion] = useState(0);
    const [questions, setQuestions] = useState([]);

    const updateQuestion = value => {

    }
    
    const updateQuestions = questions => {
        setQuestions(questions);
    }

    return (
        <TrainginContext.Provider 
            value={{ 
                questions,
                currentQuestion,
                updateQuestions,
                updateQuestion,
            }}
        >
            {children}
        </TrainginContext.Provider>
    );
}

export const useTraining = () => {
    const context = useContext(TrainginContext);
    return context;
}

export default TrainginContext;