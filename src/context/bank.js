import React, { createContext, useState, useContext, useEffect } from 'react';
import { Alert } from 'react-native';
import ImageCropPicker from 'react-native-image-crop-picker';
import api from '../services/api';
import { convertDateToAmerican, formatCurrencyToNumber } from '../useful/conversions';

const BankContext = createContext();

const bankAccountInitial = {
    document_type: 'rg_front_back',
    birthdate: '',
    mother_name: '',
    is_pep: 0,
    nationality: '',
    monthly_income: '',
    address_street: '',
    address_number: '',
    address_complement: '',
    address_neighborhood: '',
    address_city: '',
    address_state: '',
    address_postal_code: '',
};

export const BankProvider = ({children, navigationRef}) => {
    const [bankAccount, setBankAccount] = useState(bankAccountInitial);

    const createBankAccount = async () => {
        try {
            const formdata = new FormData;

            formdata.append("birthdate", convertDateToAmerican(bankAccount.birthdate));
            formdata.append("mother_name", bankAccount.mother_name);
            formdata.append("is_pep", bankAccount.is_pep);
            formdata.append("nationality", bankAccount.nationality);
            formdata.append("monthly_income", formatCurrencyToNumber(bankAccount.monthly_income));
            formdata.append("address[street]", bankAccount.address_street);
            formdata.append("address[number]", bankAccount.address_number);
            formdata.append("address[complement]",bankAccount.address_complement);
            formdata.append("address[neighborhood]", bankAccount.address_neighborhood);
            formdata.append("address[city]", bankAccount.address_city);
            formdata.append("address[state]", bankAccount.address_state);
            formdata.append("address[postal_code]", bankAccount.address_postal_code);

            await api.post('/bank-account', formdata);

            return true;
        } catch (error) {
            console.log(error);
            let errors = error?.response?.data?.errors ?? null;
            let errorText = '';
            if (errors) {
                Object.keys(errors).map(key => {
                    errors[key].map(error => {
                        errorText += `- ${error}\n`;
                    });
                });
            }

            Alert.alert('Não foi possível criar sua conta', errorText);

            return false;
        }
    }
    
    return (
        <BankContext.Provider 
            value={{ 
                bankAccount,
                setBankAccount,
                createBankAccount
            }}
        >
            {children}
        </BankContext.Provider>
    );
}

export const useBank = () => {
    const context = useContext(BankContext);
    return context;
}

export default BankContext;