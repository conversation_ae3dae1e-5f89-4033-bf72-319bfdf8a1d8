import React, { createContext, useState, useContext, useEffect } from 'react';
import { View, Alert, ActivityIndicator, Platform, Linking } from 'react-native';
import AsyncStorage from '@react-native-async-storage/async-storage';
import DeviceInfo from 'react-native-device-info';
import api from '../services/api';

import OneSignal from 'react-native-onesignal';

import {
    API_HOMOLOG_URL,
    API_PRODUCTION_URL
} from '@env';

const AuthContext = createContext({ signed: false, user: null, company: null });

export const AuthProvider = ({ children, routeInfo }) => {
    const [user, setUser] = useState(null);
    
    const [confirmarPresenca, setConfirmarPresenca] = useState(false);
    const [triggerConfirmarPresenca, setTriggerConfirmarPresenca] = useState(false);

    const [hasNotification, setHasNotification] = useState(false);
    const [loading, setLoading] = useState(true);
    const [token, setToken] = useState('');
    const [updateRequired, setUpdateRequired] = useState(false);
    const [updateAvailable, setUpdateAvailable] = useState(false);
    const [downloadFromStoreRequired, setDownloadFromStoreRequired] = useState(false);

    const [baseOs, setBaseOs] = useState(undefined);
    const [systemVersion, setSystemVersion] = useState(undefined);
    const [brand, setBrand] = useState(undefined);
    const [deviceId, setDeviceId] = useState(undefined);
    const [uniqueId, setUniqueId] = useState(undefined);
    const [readableVersion, setReadableVersion] = useState(undefined);
    const [device, setDevice] = useState(undefined);
    const [isAirplaneMode, setIsAirplaneMode] = useState(undefined);
    const [isLocationEnabled, setIsLocationEnabled] = useState(undefined);
    const [isEmulator, setIsEmulator] = useState(undefined);
    const [home, setHome] = useState(null);
    const [production, setProduction] = useState(null);

    useEffect(() => {
        if(
            ![undefined, 'AuthLogin', 'RegisterPicture'].includes(routeInfo.previous) && 
            routeInfo.current === 'PrivateMain'
        ){
            getUser();
        }
    }, [routeInfo,token]);

    useEffect(() => {
        if(user){
            getHome();
        } else {
            setHome(null);
        }
    }, [user?.status_cadastro])

    useEffect(() => {
        if (production !== null) {
            getDeviceInfos();
        }
    }, [production]);

    useEffect(() => {
        getProduction();
    }, []);

    useEffect(() => {
        if (user !== null && production !== null) {
            setOneSignal();
        }
    }, [user, production]);

    useEffect(() => {
        let arrCheck = [baseOs, systemVersion, brand, deviceId, uniqueId, readableVersion, device, isAirplaneMode, isLocationEnabled, isEmulator];
        if (!arrCheck.includes(undefined)) {
            api.defaults.headers.common['appcury-base-os'] = baseOs;
            api.defaults.headers.common['appcury-system-version'] = systemVersion;

            api.defaults.headers.common['appcury-brand'] = brand;
            api.defaults.headers.common['appcury-device'] = device;
            api.defaults.headers.common['appcury-device-id'] = deviceId;

            api.defaults.headers.common['appcury-unique-id'] = uniqueId;

            api.defaults.headers.common['appcury-readable-version'] = readableVersion;

            api.defaults.headers.common['appcury-is-airplane-mode'] = isAirplaneMode;
            api.defaults.headers.common['appcury-is-emulator'] = isEmulator;
            api.defaults.headers.common['appcury-is-location-enabled'] = isLocationEnabled;

            startApp();
        }
    }, [baseOs, systemVersion, brand, deviceId, uniqueId, readableVersion, device, isAirplaneMode, isLocationEnabled, isEmulator]);

    const getProduction = async () => {
        const homolog = await AsyncStorage.getItem('@CuryCorretor:homolog');
        if (homolog === "true") {
            api.defaults.baseURL = API_HOMOLOG_URL;
            setProduction(false);
        } else {
            api.defaults.baseURL = API_PRODUCTION_URL;
            setProduction(true);
        }
    }

    const startApp = () => {
        api.get('/check-app').then(res => {
            let { 
                atualizacao_disponivel, 
                atualizacao_obrigatoria, 
                download_loja_obrigatorio, 
                forcar_homolog 
            } = res.data;

            setUpdateAvailable(atualizacao_disponivel);
            setUpdateRequired(atualizacao_obrigatoria);
            setDownloadFromStoreRequired(download_loja_obrigatorio);

            if(forcar_homolog){
                AsyncStorage.setItem('@CuryCorretor:homolog', 'true');
                api.defaults.baseURL = API_HOMOLOG_URL;
            }

            refreshToken();
        }).catch(err => {
            console.log('error-check-app', err);
        });
    }

    const setOneSignal = () => {
        OneSignal.setExternalUserId(production ? user.id : `homolog-${user.id}`);
    }

    const getDeviceInfos = async () => {
        let toBaseOs = Platform.OS;

        let toSystemVersion = DeviceInfo.getSystemVersion();

        let toBrand = DeviceInfo.getBrand();
        let toDeviceId = DeviceInfo.getDeviceId();
        let toUniqueId = DeviceInfo.getUniqueId();
        let toReadableVersion = DeviceInfo.getReadableVersion();
        let arrToReadableVersion = toReadableVersion.split('.');
        toReadableVersion = `${arrToReadableVersion[0]}.${arrToReadableVersion[1]}.${arrToReadableVersion[2]}`

        let toDevice = toBaseOs === 'ios' ? null : await DeviceInfo.getDevice().then((d) => {
            return d;
        });
        let toIsAirplaneMode = toBaseOs === 'ios' ? null : await DeviceInfo.isAirplaneMode().then((airplaneModeOn) => {
            return airplaneModeOn;
        });
        let toIsLocationEnabled = await DeviceInfo.isLocationEnabled().then((enabled) => {
            return enabled;
        });
        let toIsEmulator = await DeviceInfo.isEmulator().then((isEmulator) => {
            return isEmulator;
        });

        setBaseOs(toBaseOs);
        setSystemVersion(toSystemVersion);
        setBrand(toBrand);
        setDeviceId(toDeviceId);
        setUniqueId(toUniqueId);
        setReadableVersion(toReadableVersion);
        setDevice(toDevice);
        setIsAirplaneMode(toIsAirplaneMode);
        setIsLocationEnabled(toIsLocationEnabled);
        setIsEmulator(toIsEmulator);
    }

    const refreshToken = async () => {
        const storagedToken = await AsyncStorage.getItem('@CuryCorretor:token');
        api.defaults.headers.Authorization = `Bearer ${storagedToken}`;
        api.post('/auth/refresh')
            .then(res => {
                const token = res.data.access_token;
                setApiToken(token);
                getUser();
            })
            .catch(err => {
                console.log('err', err.request);
                delete api.defaults.headers.Authorization;
                setUser(null);
                setLoading(false);
            });
    }

    const getHome = () => {
        api.get('/home').then(res => {
            setHome(res.data);
        });
    }

    const getUser = () => {
        api.get('/user').then(async res => {
            if (res.data.user.app?.ultima_versao) {
                let required = res.data.user.app.atualizacao_obrigatoria;
                let available = res.data.user.app.atualizacao_disponivel;

                setUpdateAvailable(available);
                setUpdateRequired(required && available);
            }
            let u = res.data.user;
            setUser(u);
            setConfirmarPresenca(u?.verificacao_presenca ? true : false);
            setHasNotification(u.notificacoes_novas > 0 ? true : false);
            setLoading(false);
        })
            .catch(err => {
                console.log(err);
                // setUser(null);
                setLoading(false);
            });
    }

    const setApiToken = token => {
        api.defaults.headers.Authorization = `Bearer ${token}`;
        AsyncStorage.setItem('@CuryCorretor:token', token);
        setToken(token);
    }

    const signInWithToken = async (cpf, password, accessToken) => {
        storeCredentials(cpf, password);
        setApiToken(accessToken);
        getUser();
    }

    const signIn = async (cpf, password) => {
        const data = { cpf, password };
        console.log(API_HOMOLOG_URL);
        const ready = await api.post('/auth/login', data)
            .then(async res => {
                storeCredentials(cpf, password);
                setApiToken(res.data.access_token);
                getUser();
                return true;
            })
            .catch(err => {
                console.log(err.response);
                if (err.response?.data) {
                    if (err.response.data?.inativo === true) {
                        let regional = err.response.data.regional;
                        let numero = regional == 'rj' ? '5521991143851' : '5511992509026';
                        let numeroFormatado = regional == 'rj' ? '(21) 99114-3851' : '(11) 99250-9026';
                        let mensagemHorario = regional == 'rj' ? '' : 'Sábado das 09:00 às 13:00';

                        Alert.alert('Erro ao logar', `Seu cadastro foi desativado, favor contatar seu gestor ou a Gestão de Autônomos no WhatsApp:\n\n${numeroFormatado}\n\nHorário de atendimento:\n2ª a 6ª das 09:00 às 18:00\n${mensagemHorario}`, [
                            {
                                text: 'Falar com a Gestão de Autônomos',
                                onPress: () => Linking.openURL(`whatsapp://send?&phone=${numero}`),
                                style: 'cancel',
                            },
                            {
                                text: 'Fechar',
                                style: 'cancel',
                            }
                        ]);

                    } else {
                        Alert.alert('Erro ao logar', err.response.data.error);
                    }
                } else {
                    console.log(err);
                    Alert.alert('Não foi possível fazer login');
                }
                return false;
            });

        return ready;
    }

    const storeCredentials = (cpf, password) => {
        AsyncStorage.setItem('@CuryCorretor:cpf', cpf);
        AsyncStorage.setItem('@CuryCorretor:password', password);
    }

    const getCredentials = async () => {
        let cpf = await AsyncStorage.getItem('@CuryCorretor:cpf');
        let password = await AsyncStorage.getItem('@CuryCorretor:password');
        return { cpf, password };
    }

    const signOut = () => {
        AsyncStorage.setItem('@CuryCorretor:token', '').then(() => {
            setUser(null);
            OneSignal.removeExternalUserId();
        });

        api.post('/auth/logout');
    }

    const updateHasNotification = value => {
        setHasNotification(value);
    }

    const hasPermission = permission => {
        return user.areas_app.includes(permission);
    }

    const handleChangeProduction = async () => {
        const homolog = await AsyncStorage.getItem('@CuryCorretor:homolog');
        if (homolog === null) {
            Alert.alert('Alterado para homologação');
            await AsyncStorage.setItem('@CuryCorretor:homolog', 'true');
            api.defaults.baseURL = API_HOMOLOG_URL;
            setProduction(false);
        } else {
            Alert.alert('Alterado para produção');
            await AsyncStorage.removeItem('@CuryCorretor:homolog');
            api.defaults.baseURL = API_PRODUCTION_URL;
            setProduction(true);
        }
    }

    if (loading) {
        return (
            <View style={{ flex: 1, justifyContent: "center", alignItems: "center" }}>
                <ActivityIndicator size="large" color="#00467F" />
            </View>
        );
    }

    return (
        <AuthContext.Provider value={{ 
            signed: !!user, 
            user, 
            updateAvailable, 
            updateRequired, 
            downloadFromStoreRequired, 
            production, 
            hasPermission, 
            getCredentials, 
            handleChangeProduction, 
            token, 
            hasNotification, 
            home, 
            updateHasNotification, 
            getUser, 
            signIn, 
            signInWithToken, 
            signOut, 
            confirmarPresenca,
            setConfirmarPresenca,
            triggerConfirmarPresenca,
            setTriggerConfirmarPresenca
        }}>
            {children}
        </AuthContext.Provider>
    );

};

export const useAuth = () => {
    const context = useContext(AuthContext);
    return context;
}

export default AuthContext;