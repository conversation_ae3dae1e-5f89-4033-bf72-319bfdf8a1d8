import React, {createContext, useContext, useState} from 'react';

import api from '../services/api';

const WoxAiContext = createContext({});

export const WoxAiProvider = ({children}) => {
  const [dataAi, setDataAi] = useState({});
  const [crtl, setCrtl] = useState(null);
  const setForm = (field, value) => {
    setDataAi(prev => ({
      ...prev,
      [field]: value,
    }));
  };
  const controller = new AbortController();
  let result = null;
  const getai = async (text = '') => {
    const signal = controller.signal;

    setCrtl(controller);
    let data = new FormData();
    data.append('profile', 'corretor');
    if (dataAi.product) {
      data.append('product', dataAi.product);
    }
    if (dataAi.description) {
      data.append('description', dataAi.description);
    }
    if (dataAi.money) {
      data.append('budget', dataAi.money);
    }
    if (dataAi.textDuvida) {
      data.append('textDuvida', dataAi.textDuvida);
    }
    if (text) {
      data.append('textDuvida', text);
    }
    if (dataAi.whereAd) {
      const keys = Object.keys(dataAi.whereAd).filter(
        key => dataAi.whereAd[key],
      );

      const whereAd = keys.join(', ');
      data.append('midias', whereAd);
    }

    if (dataAi.diferencial) {
      const keys = Object.keys(dataAi.diferencial);
      const diferencial = keys.join(', ');
      data.append('diferencial', diferencial);
    }
    setForm('textGeneratedAi', '');
    console.log(dataAi);
    console.log('api.post');

    try {
      result = await api.post(`/${dataAi.type}`, data, {signal});
    } catch (error) {
      console.log('error');
      console.log(error);
      if (error.response) {
        console.log('e1:' + error.response.data);
      } else if (error.request) {
        console.log('e2:' + error.request);
      } else {
        console.log('e3:' + error.message);
      }
    }
    return result;
  };

  return (
    <WoxAiContext.Provider
      value={{
        dataAi,
        setForm,
        getai,
        crtl,
      }}>
      {children}
    </WoxAiContext.Provider>
  );
};

export default WoxAiContext;

export const useWoxAi = () => {
  const context = useContext(WoxAiContext);
  return context;
};
