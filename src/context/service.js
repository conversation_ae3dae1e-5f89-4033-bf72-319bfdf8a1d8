import React, { createContext, useState, useContext, useEffect } from 'react';
import { Alert } from 'react-native';

window.Pusher = require('pusher-js');
window.axios = require('axios');
import Echo from 'laravel-echo';

import { useAuth } from './auth';
import api from '../services/api';

import {
    PUSHER_HOMOLOG_AUTH_URL,
    PUSHER_HOMOLOG_HOST,
    PUSHER_HOMOLOG_APP_KEY,
    PUSHER_HOMOLOG_PORT,
    PUSHER_PRODUCTION_AUTH_URL,
    PUSHER_PRODUCTION_HOST,
    PUSHER_PRODUCTION_APP_KEY,
    PUSHER_PRODUCTION_PORT
} from '@env';

const ServiceContext = createContext({});

export const ServiceProvider = ({children, navigationRef}) => {
    const [activeCheckin, setActiveCheckin] = useState(false);
    const [queue, setQueue] = useState({});

    const { token, user, updateHasNotification, production, getUser, setConfirmarPresenca } = useAuth();

    useEffect(() => {
        if(token && user && production !== null){
            if (!window?.pusherConnection) {
                initWebsocket();
            }
        }
    }, [token, user, production]);

    useEffect(() => {
        updateQueue();
    }, [user]);
    
    const reset = () => {
        setQueue({});
        setActiveCheckin(false);
    }

    const checkenabledToRedirect = () => {
        const routesEnabledToRedirect = [
            'ProvisionalRow',
            'WaitTurnExtra',
            'TurnExtra',
            'Turn',
            'TurnEnd',
            'TurnActive',
            'RowCheckin',
            'ProvisionalRow',
        ];

        const currentRoute = navigationRef.getCurrentRoute().name;

        return routesEnabledToRedirect.includes(currentRoute);
    }

    const updateQueue = () => {
        if(!user || !user.checkin_ativo){
            reset();
            return;
        }

        setQueue({...user.fila});
        setActiveCheckin(true);
    }

    const refreshQueue = () => {
        getUser();
    }

    const initWebsocket = () => {
        // Habilitar debug do Pusher
        // window.Pusher.logToConsole = true;

        let options = {
            authEndpoint: production ? PUSHER_PRODUCTION_AUTH_URL : PUSHER_HOMOLOG_AUTH_URL,
            broadcaster: 'pusher',
            cluster: 'us2',
            encrypted: true,
            key: production ? PUSHER_PRODUCTION_APP_KEY : PUSHER_HOMOLOG_APP_KEY,
            wsHost: production ? PUSHER_PRODUCTION_HOST : PUSHER_HOMOLOG_HOST,
            wssHost: production ? PUSHER_PRODUCTION_HOST : PUSHER_HOMOLOG_HOST,
            wsPort: production ? PUSHER_PRODUCTION_PORT : PUSHER_HOMOLOG_PORT,
            wssPort: production ? PUSHER_PRODUCTION_PORT : PUSHER_HOMOLOG_PORT,
            enableStats: false,
            enabledTransports: ['ws'],
            forceTLS: true,
            auth: {
                headers: {
                    Authorization: `Bearer ${token}`,
                },
            },
            namespace: 'App.Events'
        };

        let isFirstConnection = true;
        
        let echo = new Echo(options);
        window.pusherConnector = echo.connector.pusher;
        window.pusherConnection = echo.connector.pusher.connection;

        window.pusherConnection.bind('state_change', function(states) {
            let statesToReconnect = ['disconnected', 'unavailable', 'failed'];
            if(statesToReconnect.includes(states.current)) {
                window.pusherConnector.connect();
            }
        });

        echo.connector.pusher.connection.bind('connecting', (payload) => {
            // console.log('WS - connecting');
            // console.log(payload);
        });

        echo.connector.pusher.connection.bind('connected', (payload) => {
            window.pusherConnection.sendActivityCheck();

            // console.log('WS - connected');
            // console.log(payload);

            if(!isFirstConnection) {
                refreshQueue();
            }

            isFirstConnection = false;
        });

        echo.connector.pusher.connection.bind('unavailable', (payload) => {
            // console.log('WS - unavailable');
            // console.log(payload);
        });
//
        echo.connector.pusher.connection.bind('failed', (payload) => {
            // console.log('WS - failed');
            // console.log(payload);
        });

        echo.connector.pusher.connection.bind('disconnected', (payload) => {
            // console.log('WS - disconnected');
            // console.log(payload);
        });
        
        let channel = echo.private(`Corretor.${user.id}`);

        channel.listen('Corretor.AtualizacaoFila', data => {
            console.log('WS ---');
            console.log(data);

            refreshQueue();

            if(
                navigationRef.getCurrentRoute().name === 'ProvisionalRow' &&
                data.fila.tipo_atendimento === 'extra'
            ) {
                navigationRef.current?.navigate('WaitTurnExtra');
            }
            
            if(
                navigationRef.getCurrentRoute().name === 'WaitTurnExtra' &&
                data.fila.tipo_atendimento === 'normal'
            ) {
                navigationRef.current?.navigate('ProvisionalRow');
            }
        })
        .listen('Corretor.Checkout', data => {
            reset();
            Alert.alert('Checkin encerrado', data.message);
            navigationRef.current?.navigate('PrivateMain');
        })
        .listen('Corretor.AtendimentoDisponivel', data => {
            refreshQueue();

            if(data.fila.tipo_atendimento === 'extra'){
                if(data.fila.posicao !== null){
                    navigationRef.current?.navigate('TurnExtra');
                } else {
                    navigationRef.current?.navigate('Turn');
                }
            } else {
                navigationRef.current?.navigate('Turn');
            }
        })
        .listen('UserAppAprovado', data => {
            getUser();
            console.log('WEBSOCKET ====== UserAppAprovado');
        })
        .listen('UserAppReprovado', data => {
            getUser();
            console.log('WEBSOCKET ====== UserAppReprovado');
        })
        .listen('AtualizacaoAlteracoesRh', data => {
            getUser();
            console.log('WEBSOCKET ====== AtualizacaoAlteracoesRh');
        })
        .listen('Corretor.AtendimentoIniciado', data => {
            refreshQueue();
            navigationRef.current?.navigate('TurnActive');
        })
        .listen('Corretor.AtendimentoEncerrado', data => {
            refreshQueue();
            let name = data.fila.atendimento.cliente.nome;
            let email = data.fila.atendimento.cliente.email;
            let phone = data.fila.atendimento.cliente.celular;
            
            navigationRef.current?.navigate('TurnEnd', { name, email, phone });
        })
        .listen('Corretor.AtendimentoCancelado', data => {
            refreshQueue();
            Alert.alert('Atendimento cancelado', data.message);
            navigationRef.current?.navigate('PrivateMain');
        })
        .listen('Corretor.VerificacaoPresencaIniciada', data => {
            setConfirmarPresenca(true);
        })
        .listen('Corretor.VerificacaoPresencaEncerrada', data => {
            setConfirmarPresenca(false);
        })
        .notification(notification => {
            if(notification?.is_database === true){
                updateHasNotification(true);
            }
            if(notification?.is_lead === true){
                getUser();
            }
        });
    }

    const checkin = async (latitude, longitude, plantaoId) => {

        let data = new FormData();
        data.append('latitude', latitude);
        data.append('longitude', longitude);
        data.append('plantao_id', plantaoId);
        
        const result = await api.post('/checkin', data)
            .then(res => {
                setActiveCheckin(true);
                setQueue(res.data.fila);
                return true;
            })
            .catch(err => {
                return false;
            });

        return result;
    }

    const exit = async () => {
        const res = await api.post('/checkout')
            .then(res => { 
                reset();
                return true; 
            })
            .catch(err => {
                return false;
            });
        return res;
    }

    const redirectTo = (noValidate = false) => {
        if(!checkenabledToRedirect() && noValidate === false) return;

        if(!activeCheckin){
            navigationRef.current?.navigate('PrivateMain');
        }

        if(!queue.atendimento_disponivel && !queue.atendimento_ativo){
            if(!queue.tipo_atendimento){
                navigationRef.current?.navigate('PrivateMain');
            } else {
                if(queue.tipo_atendimento === 'extra'){
                    navigationRef.current?.navigate('WaitTurnExtra');
                } else {
                    navigationRef.current?.navigate('ProvisionalRow');
                }
            }
        }

        if(queue.atendimento_disponivel){
            if(queue.tipo_atendimento === 'extra'){
                navigationRef.current?.navigate('TurnExtra');
            } else {
                navigationRef.current?.navigate('Turn');
            }
        }

        if(queue.atendimento_ativo){
            navigationRef.current?.navigate('TurnActive');
        }
    }

    return (
        <ServiceContext.Provider 
            value={{ 
                activeCheckin,
                queue,
                refreshQueue,
                redirectTo,
                checkin,
                exit
            }}
        >
            {children}
        </ServiceContext.Provider>
    );
}

export const useService = () => {
    const context = useContext(ServiceContext);
    return context;
}

export default ServiceContext;