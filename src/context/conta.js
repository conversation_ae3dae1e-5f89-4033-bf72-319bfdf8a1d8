import React, { createContext, useContext, useState } from "react";

export const ContaContext = createContext();

export const ContaProvider = ({ children }) => {
    const [documentoFrenteId, setDocumentoFrenteId] = useState(null);

    return (
        <ContaContext.Provider value={{
            documentoFrenteId,
            setDocumentoFrenteId
        }}>
            {children}
        </ContaContext.Provider>
    );
}

export const useConta = () => {
    const context = useContext(ContaContext);
    return context;
}