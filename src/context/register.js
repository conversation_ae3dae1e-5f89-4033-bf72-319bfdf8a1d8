import React, { createContext, useState, useContext, useEffect } from 'react';
import { Alert } from 'react-native';

import OneSignal from 'react-native-onesignal';

import { convertDateToAmerican } from '../useful/conversions';
import api from '../services/api';
import { useAuth } from './auth';
import AsyncStorage from "@react-native-async-storage/async-storage";

const RegisterContext = createContext();

export const RegisterProvider = ({children}) => {
    const [imobiliaria, setImobiliaria] = useState('');
    const [canal, setCanal] = useState('');
    const [cpf, setCpf] = useState('');
    const [region, setRegion] = useState('');
    const [regionSlug, setRegionSlug] = useState('');
    const [manager, setManager] = useState('');
    const [name, setName] = useState('');
    const [nickname, setNickname] = useState('');
    const [birth, setBirth] = useState('');
    const [email, setEmail] = useState('');
    const [emailConfirm, setEmailConfirm] = useState('');
    const [phone, setPhone] = useState('');
    const [cep, setCep] = useState('');
    const [address, setAddress] = useState('');
    const [addressNumber, setAddressNumber] = useState('');
    const [addressComplement, setAddressComplement] = useState('');
    const [addressNeighborhood, setAddressNeighborhood] = useState('');
    const [addressCity, setAddressCity] = useState('');
    const [addressState, setAddressState] = useState('');
    const [addressImage, setAddressImage] = useState(null);
    const [addressImageId, setAddressImageId] = useState('');
    const [document, setDocument] = useState('');
    const [documentBack, setDocumentBack] = useState('');
    const [documentNumber, setDocumentNumber] = useState('');
    const [documentImage, setDocumentImage] = useState(null);
    const [documentBackImage, setDocumentBackImage] = useState(null);
    const [creci, setCreci] = useState('');
    const [creciBack, setCreciBack] = useState('');
    const [creciNumber, setCreciNumber] = useState('');
    const [creciImage, setCreciImage] = useState(null);
    const [creciBackImage, setCreciBackImage] = useState(null);
    const [creciDate, setCreciDate] = useState('');
    const [protocol, setProtocol] = useState('');
    const [protocolNumber, setProtocolNumber] = useState('');
    const [protocolImage, setProtocolImage] = useState(null);
    const [protocolDate, setProtocolDate] = useState('');

    const [type, setType] = useState('');

    const [password, setPassword] = useState('');
    const [tShirtSize, setTShirtSize] = useState('');
    const [selfie, setSelfie] = useState(null);
    const [selfieId, setSelfieId] = useState('');

    useEffect(() => {
        setImobiliaria('');
        setManager('');
    }, [canal]);

    const uploadDocument = async (image, name) => {

        const data = new FormData();

        data.append(name, {
            uri: image.uri,
            type: 'image/jpeg',
            name: `${name}.jpg`
        });

        const result = await api.post('/cadastro/upload', data)
            .then(res => {
                console.log(res.data.id);
                return res.data.id;
            })
            .catch(err =>{
                console.log('erro req', err.request);
                console.log('erro res', err.response);
                return null;
            });

        return result;
    }

    const updateName = (value) => {
        setName(value);
    }

    const updateNickname = (value) => {
        setNickname(value);
    }

    const updateBirth = (value) => {
        setBirth(value);
    }

    const updateEmail = (value) => {
        setEmail(value);
    }

    const updateEmailConfirm = (value) => {
        setEmailConfirm(value);
    }

    const updatePhone = (value) => {
        setPhone(value);
    }

    const updateCep = (value) => {
        setCep(value);
    }

    const updateAddress = (value) => {
        setAddress(value);
    }

    const updateAddressNumber = (value) => {
        setAddressNumber(value);
    }

    const updateAddressComplement = (value) => {
        setAddressComplement(value);
    }

    const updateAddressNeighborhood = (value) => {
        setAddressNeighborhood(value);
    }

    const updateAddressCity = (value) => {
        setAddressCity(value);
    }

    const updateAddressState = (value) => {
        setAddressState(value);
    }

    const updateAddressImage = (value) => {
        setAddressImage(value);
    }

    const updateAddressImageId = (value) => {
        setAddressImageId(value);
    }

    const updateDocument = (value) => {
        setDocument(value);
    }

    const updateDocumentBack = (value) => {
        setDocumentBack(value);
    }


    const updateDocumentNumber = (value) => {
        setDocumentNumber(value);
    }

    const updateDocumentImage = (value) => {
        setDocumentImage(value);
    }

    const updateDocumentBackImage = (value) => {
        setDocumentBackImage(value);
    }

    const updateCreci = (value) => {
        setCreci(value);
    }

    const updateCreciBack = (value) => {
        setCreciBack(value);
    }

    const updateCreciNumber = (value) => {
        setCreciNumber(value);
    }

    const updateCreciImage = (value) => {
        setCreciImage(value);
    }

    const updateCreciBackImage = (value) => {
        setCreciBackImage(value);
    }

    const updateCreciDate = (value) => {
        setCreciDate(value);
    }

    const updateProtocol = (value) => {
        setProtocol(value);
    }

    const updateProtocolNumber = (value) => {
        setProtocolNumber(value);
    }

    const updateProtocolImage = (value) => {
        setProtocolImage(value);
    }

    const updateProtocolDate = (value) => {
        setProtocolDate(value);
    }
    
    const updateType = (value) => {
        setType(value);
    }

    const updatePassword = (value) => {
        setPassword(value);
    }

    const updateTShirtSize = (value) => {
        setTShirtSize(value);
    }

    const updateSelfie = (value) => {
        setSelfie(value);
    }

    const updateSelfieId = (value) => {
        setSelfieId(value);
    }

    const register = async (params) => {
        let data = new FormData();
        data.append('canal', canal);
        data.append('imobiliaria_id', imobiliaria);
        data.append('cpf', cpf);
        data.append('nome', name);
        data.append('apelido', nickname);
        data.append('regional_id', region);
        data.append('gerente_id', manager);
        data.append('email', email);
        data.append('telefone', phone);
        data.append('cep', cep);
        data.append('endereco', address);
        data.append('numero', addressNumber);
        data.append('complemento', addressComplement);
        data.append('bairro', addressNeighborhood);
        data.append('cidade', addressCity);
        data.append('uf', addressState);

        data.append('documento_numero', documentNumber);
        data.append('documento_id', document);
        data.append('documento_verso_id', documentBack);

        data.append('protocolo_estagio_numero', type === 'credenciado' ? '' : protocolNumber);
        data.append('protocolo_estagio_id', type === 'credenciado' ? '' : protocol);
        data.append('protocolo_estagio_validade', type === 'credenciado' ? '' : convertDateToAmerican(protocolDate) );


        data.append('creci_numero', type === 'credenciado' ? creciNumber : '');
        data.append('creci_validade', type === 'credenciado' ? convertDateToAmerican(creciDate)  : '');
        data.append('creci_id', type === 'credenciado' ? creci : '');

        if(type === 'credenciado'){
            if(creciBack !== ''){
                data.append('creci_verso_id', type === 'credenciado' ? creciBack : '');
            }
        }

        data.append('tipo_contrato', type);
        data.append('comprovante_endereco_id', addressImageId);

        data.append('password', password);
        data.append('password_confirm', password);
        data.append('selfie_id', params.selfieId);
        data.append('camisa_tamanho', tShirtSize);

        console.log(data);

        const res = api.post('/cadastro', data)
            .then(res => { 
                console.log(res, 'registro');
                let id = res.data.id;
                OneSignal.setExternalUserId(id);

                AsyncStorage.setItem('@CuryCorretor:cpf', cpf);
                AsyncStorage.setItem('@CuryCorretor:password', password);

                cleanFields();

                return res.data.access_token;
            })
            .catch(err => {
                console.log(err.response);
                if(err.response.status === 422){
                    let errors = err.response.data.errors;
                    let firstError = errors[Object.keys(errors)[0]][0];
                    Alert.alert('Por favor, verifique os campos', firstError);
                } else {
                    alert('Ocorreu um erro ao gravar os dados.');
                }
                return false;
            });

        return res;
    }
    
    const cleanFields = () => {
        setImobiliaria('');
        setCanal('');
        setCpf('');
        setRegion('');
        setRegionSlug('');
        setManager('');
        setName('');
        setNickname('');
        setBirth('');
        setEmail('');
        setEmailConfirm('');
        setPhone('');
        setCep('');
        setAddress('');
        setAddressNumber('');
        setAddressComplement('');
        setAddressNeighborhood('');
        setAddressCity('');
        setAddressState('');
        setAddressImage(null);
        setAddressImageId('');
        setDocument('');
        setDocumentBack('');
        setDocumentNumber('');
        setDocumentImage(null);
        setDocumentBackImage(null);
        setCreci('');
        setCreciNumber('');
        setCreciImage(null);
        setCreciBackImage(null);
        setCreciDate('');
        setProtocol('');
        setProtocolNumber('');
        setProtocolImage(null);
        setProtocolDate('');
        setPassword('');
        setTShirtSize('');
        setSelfie(null);
        setSelfieId('');
    }

    return (
        <RegisterContext.Provider 
            value={{ 
                imobiliaria,
                canal, 
                cpf, 
                region, 
                regionSlug,
                manager,
                name,
                nickname,
                birth,
                email,
                emailConfirm,
                phone,
                cep,
                address,
                addressNumber,
                addressComplement,
                addressNeighborhood,
                addressCity,
                addressState,
                addressImage,
                addressImageId,
                document,
                documentNumber,
                documentImage,
                documentBackImage,
                creci,
                creciBack,
                creciNumber,
                creciImage,
                creciBackImage,
                creciDate,
                protocol,
                protocolNumber,
                protocolImage,
                protocolDate,
                type,
                password,
                tShirtSize,
                selfie,
                selfieId,
                setImobiliaria,
                setCanal,
                setCpf, 
                setRegion,
                setRegionSlug,
                updatePassword,
                updateTShirtSize,
                updateSelfie,
                updateSelfieId,
                setManager, 
                updateName,
                updateNickname,
                updateBirth,
                updateEmail,
                updateEmailConfirm,
                updatePhone,
                updateCep,
                updateAddress,
                updateAddressNumber,
                updateAddressComplement,
                updateAddressNeighborhood,
                updateAddressCity,
                updateAddressState,
                updateAddressImage,
                updateAddressImageId,
                updateDocument,
                updateDocumentBack,
                updateDocumentNumber,
                updateDocumentImage,
                updateDocumentBackImage,
                updateCreci,
                updateCreciBack,
                updateCreciNumber,
                updateCreciImage,
                updateCreciBackImage,
                updateCreciDate,
                updateProtocol,
                updateProtocolNumber,
                updateProtocolImage,
                updateProtocolDate,
                updateType,
                uploadDocument,
                register,
            }}
        >
            {children}
        </RegisterContext.Provider>
    );
}

export const useRegister = () => {
    const context = useContext(RegisterContext);
    return context;
}

export default RegisterContext;