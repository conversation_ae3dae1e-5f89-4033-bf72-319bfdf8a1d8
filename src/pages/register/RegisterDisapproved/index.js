import React from 'react';
import { Text, TouchableOpacity, View, ScrollView, Linking } from 'react-native';

import mainStyles from '../../../mainStyles';
import styles from './styles';

import Logo from '../../../assets/svgs/Logo';
import Disapproved from '../../../assets/svgs/Disapproved';

const RegisterAlreadyRegistered = () => {
    return (
        <ScrollView style={styles.wrapper} showsVerticalScrollIndicator={false}>
            <View style={mainStyles.container}>
                <Logo style={styles.logo}/>
                <Disapproved style={styles.ic}/>
                <Text style={styles.title}>{`Cadastro\nreprovado`}</Text>
                <Text style={styles.text}>
                    {`Seu cadastro foi reprovado. Favor entrar em contato com a Gestão de Autônomos.`}
                </Text>
                <TouchableOpacity 
                    style={[mainStyles.btnOutlineFullWhite, styles.button]}
                    onPress={() => Linking.openURL('https://cury.net/')}
                >
                    <Text style={mainStyles.btnTextOutlineFullWhite}>ACESSAR O SITE CURY</Text>
                </TouchableOpacity>
            </View>
        </ScrollView>
    );
}

export default RegisterAlreadyRegistered;