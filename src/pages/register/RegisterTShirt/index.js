import React, { useEffect, useState, useRef } from 'react';
import { ActivityIndicator, TextInput, Text, TouchableOpacity, View, Alert, KeyboardAvoidingView, ScrollView, Keyboard, Image, Platform } from 'react-native';

import RegisterHeader from '../../../components/headers/RegisterHeader';
import Select from '../../../components/Select';

import { useRegister } from '../../../context/register';

import mainStyles, { selectStyles } from '../../../mainStyles';
import styles from './styles';

const sizesOptions = [
    { label: 'P', value: 'P' },
    { label: 'M', value: 'M' },
    { label: 'G', value: 'G' },
    { label: '2G', value: '2G' },
    { label: '3G', value: '3G' }
]

const RegisterTShirt = ({navigation}) => {
    const { 
        tShirtSize,
        updateTShirtSize
    } = useRegister();

    const [loading, setLoading] = useState(false);
    const [inTShirtSize, setInTShirtSize] = useState(tShirtSize);
    const [keyboardVisible, setKeyboardVisible] = useState(false);
    const scrollViewRef = useRef();

    const update = () => {
        if(inTShirtSize === null || inTShirtSize === ''){
            Alert.alert('Informe o tamanho da camisa', 'Por favor, informe o tamanho da camisa');
            return;
        }

        updateTShirtSize(inTShirtSize);

        navigation.navigate('RegisterPicture');
    }

    useEffect(() => {
        const keyboardDidShowListener = Keyboard.addListener('keyboardDidShow', () => {
            setKeyboardVisible(true);
          }
        );
        
        const keyboardDidHideListener = Keyboard.addListener( 'keyboardDidHide', () => {
              setKeyboardVisible(false); // or some other action
            }
        );
      
        return () => {
            keyboardDidHideListener.remove();
            keyboardDidShowListener.remove();
        };

    }, []);

    useEffect(() => {
        scroll();
    }, [keyboardVisible]);

    const scroll = () => {
        scrollViewRef.current.scrollToEnd({ animated: true });
    }

    return (
        <KeyboardAvoidingView style={{ flex: 1 }} behavior="padding" >
            <ScrollView contentContainerStyle={{ flexGrow: 1, backgroundColor: "#FFF" }} ref={scrollViewRef} keyboardShouldPersistTaps="handled">
                
                    <RegisterHeader
                        title="Camisa"
                        subtitle={`Por favor escolha o tamanho da sua camisa.`}
                    />
                    {loading && 
                        <View style={{flex: 1, justifyContent: "center", alignItems: "center"}}>
                            <ActivityIndicator size="large" color="#00467F" />
                        </View>
                    }
                    {!loading &&
                        <>
                        <View style={mainStyles.wrapperRegister}>
                            <View style={[mainStyles.container, styles.container]}>                             
                                <View style={styles.marginTop}>
                                    <Text style={[mainStyles.textAlert, mainStyles.textAlert2]}>Essa informação servirá como base para camisas promocionais.</Text>
                                </View>
                                <View style={styles.divider}></View>                           
                                <Text style={mainStyles.label}>Tamanho:</Text>
                                <Select 
                                    options={sizesOptions} 
                                    value={inTShirtSize} 
                                    onChange={value => setInTShirtSize(value)} 
                                />
                            </View>
                        </View>
                            <View style={mainStyles.contentBottomRegister}>
                                <View style={[mainStyles.container, styles.contentButtons]}>              
                                    <TouchableOpacity style={[mainStyles.btnOutlineBlue, mainStyles.buttonW48]} onPress={() => navigation.goBack()}>
                                        <Text style={mainStyles.btnTextOutlineBlue}>VOLTAR</Text>
                                    </TouchableOpacity>
                                    <TouchableOpacity style={[mainStyles.btnBlueLight, styles.btnLogin, mainStyles.buttonW48]} onPress={() => update()}>
                                        <Text style={mainStyles.btnTextCenterBlueLight}>AVANÇAR</Text>
                                    </TouchableOpacity>
                                </View>
                            </View>
                        </>
                    }
            </ScrollView>
        </KeyboardAvoidingView>
    );
}

export default RegisterTShirt;