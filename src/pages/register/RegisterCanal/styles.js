import { StyleSheet, Dimensions } from 'react-native';

const windowWidth = Dimensions.get('window').width;

const styles = StyleSheet.create({
    container: {
        paddingTop: 50
    },
    wrapper: {
        justifyContent: "space-between"
    },
    contentBottom: {        
        marginBottom: 50
    },
    contentButtons: {
        flexDirection: "row",
        justifyContent: "space-between"
    },
    button: {
        width: '45%',
    },
    btnWhats: {
        flexDirection: "row",
        alignItems: "center",
        backgroundColor: "#F2F2F2"
    },
    boxWhats: {
        backgroundColor: "#90B0C0",
        height: 65,
        width: 60,
        alignItems: "center",
        justifyContent: "center"
    },
    flexWhats: {
        height: 65,
        justifyContent: "space-between",
        alignItems: "center",
        flexDirection: "row",
        paddingLeft: 11,
        paddingRight: 11,
        flex: 1
    },
    icArrow: {
        width: 28
    },
    textBlue: {
        fontFamily: 'Ubuntu-Regular',
        fontSize: Platform.OS === 'ios' ? 16 : 14,
        color: "#00467F",
        letterSpacing: 0.46
    },
    textManager: {
        fontFamily: 'Ubuntu-Regular',
        fontSize: Platform.OS === 'ios' ? 15 : 13,
        color: "#828282",
        letterSpacing: 1,
        lineHeight: 18,
        marginBottom: 20
    },
    canalButton: {
        width: "100%",
        backgroundColor: "#F2F2F2",
        marginBottom: 10,
        borderLeftWidth: 15,
        borderLeftColor: "#90B0C0",
        paddingLeft: 10,
        paddingVertical: 10,
        flexDirection: 'row',
        justifyContent: 'space-between'
    },
    canalTitle: {
        fontFamily: 'Ubuntu-Regular',
        fontSize: 15,
        color: "#00467F",
        letterSpacing: 1,
        lineHeight: 18,
        marginBottom: 5
    },
    canalDescription: {
        fontFamily: 'Ubuntu-Regular',
        fontSize: 14,
        color: "#828282",
        letterSpacing: 1,
        lineHeight: 18
    },
    canalArrow: {
        width: 50,
        alignItems: "center",
        justifyContent: "center"
    }
});

export default styles;