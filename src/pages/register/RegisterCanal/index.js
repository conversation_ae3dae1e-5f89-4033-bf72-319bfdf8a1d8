import React, { useState, useEffect, useRef } from 'react';
import { TextInput, Text, TouchableOpacity, View, Alert, KeyboardAvoidingView, ScrollView, Keyboard, ActivityIndicator } from 'react-native';

import RegisterHeader from '../../../components/headers/RegisterHeader';
import { useRegister } from '../../../context/register';
import { cpfMask } from '../../../useful/masks';
import { validateCpf } from '../../../useful/validate';

import mainStyles, { selectStyles } from '../../../mainStyles';
import styles from './styles';
import Select from '../../../components/Select';

import api from '../../../services/api';
import ArrowRightRegisterButton from "../../../assets/svgs/ArrowRightRegisterButton";

const RegisterCanal = ({navigation}) => {

    const { 
        canal,
        setCanal
    } = useRegister();

    const [loading, setLoading] = useState(true);
    const [canais, setCanais] = useState([]);

    useEffect(() => {
        getCanais();
    }, []);

    const getCanais = () => {
        setLoading(true);

        api.get(`/cadastro/canais`).then(res => {
            setCanais(res.data.canais);
        }).catch(err => {
            console.log(err?.response);
            Alert.alert('Não foi possível obter os canais');
        }).then(() => setLoading(false));
    }

    const handleCanalChange = value => {
        setCanal(value);
        if(value === 'CIA/Imobiliária'){
            navigation.navigate('RegisterImobiliaria');
        } else {
            navigation.navigate('RegisterManagerCury');
        }
    }
    
    return (
        <KeyboardAvoidingView style={{ flex: 1 }} behavior="padding" >
            <ScrollView contentContainerStyle={{ flexGrow: 1, backgroundColor: "#FFF" }} keyboardShouldPersistTaps="handled" showsVerticalScrollIndicator={false}>
                    <RegisterHeader
                        title="Cadastro"
                        subtitle={`Primeiro nos conte um pouco sobre você.`}
                    />
                    {loading && 
                        <View style={{flex: 1, justifyContent: "center", alignItems: "center"}}>
                            <ActivityIndicator size="large" color="#00467F" />
                        </View>
                    }
                    {!loading && 
                        <View style={mainStyles.wrapperRegister}>
                            <View style={[mainStyles.container, styles.container]}>
                                <Text style={mainStyles.label}>Qual sua relação com a Cury?</Text>
                                {canais.map((canal, index) => (
                                    <TouchableOpacity
                                        key={index}
                                        onPress={() => handleCanalChange(canal.value)}
                                        style={styles.canalButton}
                                    >
                                        <View style={{ flex: 1 }}>
                                            <Text style={styles.canalTitle}>{canal.text}</Text>
                                            <Text style={styles.canalDescription}>{canal.description}</Text>
                                        </View>
                                        <View style={styles.canalArrow}>
                                            <ArrowRightRegisterButton style={{ width: 10 }} />
                                        </View>
                                    </TouchableOpacity>
                                ))}
                            </View>
                            <View style={mainStyles.contentBottomRegister}>
                                <View style={[mainStyles.container]}>              
                                    <TouchableOpacity style={mainStyles.btnBlueLight} onPress={() => navigation.goBack()}>
                                        <Text style={mainStyles.btnTextCenterBlueLight}>VOLTAR</Text>
                                    </TouchableOpacity>
                                </View>
                            </View>
                        </View>
                    }
                
            </ScrollView>
        </KeyboardAvoidingView>
    );
}

export default RegisterCanal;