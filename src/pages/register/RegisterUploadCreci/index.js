import React, { useState, useRef, useEffect } from 'react';
import { Platform, Text, TouchableOpacity, View, Image, Alert, ActivityIndicator, TextInput, KeyboardAvoidingView, ScrollView, Keyboard  } from 'react-native';

import RegisterHeader from '../../../components/headers/RegisterHeader';
import { useRegister } from '../../../context/register';

import { dateMask } from '../../../useful/masks';
import { validateDate } from '../../../useful/validate';

import mainStyles, { selectStyles } from '../../../mainStyles';
import styles from './styles';

import Camera from '../../../assets/svgs/Camera2';

import * as ImagePicker from 'react-native-image-picker';
import { requestCameraPermission } from "../../../useful/permissions";

const RegisterUploadCreci = ({navigation}) => {
    const {
        cpf,
        creci,
        creciBack,
        creciNumber, 
        creciDate, 
        creciImage, 
        creciBackImage, 
        updateCreci, 
        updateCreciBack, 
        updateCreciDate, 
        updateCreciImage, 
        updateCreciBackImage, 
        updateCreciNumber,
        uploadDocument
    } = useRegister();

    const [image, setImage] = useState(creciImage);
    const [backImage, setBackImage] = useState(creciBackImage);
    const [loading, setLoading] = useState(false);
    const [inCreci, setInCreci] = useState(creciNumber);
    const [inCreciDate, setInCreciDate] = useState(creciDate);
    const [keyboardVisible, setKeyboardVisible] = useState(false);
    const scrollViewRef = useRef();

    useEffect(() => {
        const keyboardDidShowListener = Keyboard.addListener('keyboardDidShow', () => {
            setKeyboardVisible(true);
          }
        );
        
        const keyboardDidHideListener = Keyboard.addListener( 'keyboardDidHide', () => {
              setKeyboardVisible(false);
            }
        );
      
        return () => {
            keyboardDidHideListener.remove();
            keyboardDidShowListener.remove();
        };

    }, []);

    useEffect(() => {
        scroll();
    }, [keyboardVisible]);

    useEffect(() => {
        scroll();
    }, [image, backImage]);


    const scroll = () => {
        scrollViewRef.current.scrollToEnd({ animated: true });
    }

    const uploadAndSetImage = async (image) => {
        setLoading(true);
        const res = await uploadDocument(image, 'creci');
        if(res){
            setImage(image);
            updateCreciImage(image);

            updateCreci(res);

            setLoading(false);
        } else {
            setImage(null);
            updateCreciImage(null);

            updateCreci('');

            setLoading(false);
            Alert.alert('Falha no upload', 'Não conseguimos fazer o upload da sua imagem. Por favor, tente novamente.');
        }
    }

    const uploadAndSetBackImage = async (image) => {
        setLoading(true);
        const res = await uploadDocument(image, 'creci');
        if(res){
            setBackImage(image);
            updateCreciBackImage(image);

            updateCreciBack(res);

            setLoading(false);
        } else {
            setBackImage(null);
            updateCreciBackImage(null)
            updateCreciBack('');

            setLoading(false);
            Alert.alert('Falha no upload', 'Não conseguimos fazer o upload da sua imagem. Por favor, tente novamente.');
        }
    }

    const chooseImage = (type) => {
        Keyboard.dismiss();
        let alertProperties;
        if(Platform.OS === 'ios'){
            alertProperties = [
                { text: "Tirar foto", onPress: () => openCamera(type) },
                { text: "Galeria", onPress: () => openImageLibrary(type) },
                { text: "Fechar" }
            ];
        } else {
            alertProperties = [
                { text: "Fechar" },
                { text: "Galeria", onPress: () => openImageLibrary(type) },
                { text: "Tirar foto", onPress: () => openCamera(type) }
            ];
        }
        Alert.alert(
            "Enviar documento",
            "Como deseja enviar o documento?",
            alertProperties,
            { cancelable: false }
        );
    }

    const deleteImage = type => {
        if(type === 'front'){
            updateCreci('');
            updateCreciImage(null);
            setImage(null);
        } else {
            updateCreciBack('');
            updateCreciBackImage(null);
            setBackImage(null);
        }
    }

    const imageOptions = {
        mediaType: 'photo',
        includeBase64: false,
        maxHeight: 1024,
        maxWidth: 1024
    };

    const openCamera = async (type) => {
        const hasPermission = await requestCameraPermission();
        if(hasPermission){
            ImagePicker.launchCamera (imageOptions, res => {
                if(res.errorCode){
                    Alert.alert('Erro', 'Câmera não disponível');
                    return;
                }
                if(res?.assets){
                    if(type === 'front'){
                        updateCreci('');
                        uploadAndSetImage(res.assets[0]);
                    } else {
                        updateCreciBack('');
                        uploadAndSetBackImage(res.assets[0]);
                    }
        
                    scroll();
                }
            });
        }
    }

    const openImageLibrary = (type) => {
        ImagePicker.launchImageLibrary(imageOptions, res => {
            if(res?.assets){
                if(type === 'front'){
                    updateCreci('');
                    uploadAndSetImage(res.assets[0]);
                } else {
                    updateCreciBack('');
                    uploadAndSetBackImage(res.assets[0]);
                }
                scroll();
            }
        });
    }

    const update = async () => {
        if(inCreci.length < 1){
            Alert.alert('Verifique seu CRECI', 'Por favor, para avançar, informe seu CRECI.');
            return;
        }

        if(image === null){
            Alert.alert('Envie seu CRECI', 'Por favor, para avançar, envie seu CRECI.');
            return;
        }

        if( !validateDate(inCreciDate) ){
            Alert.alert('Verifique a data de validade', 'Por favor, verifique a data de validade do seu CRECI.\nO formato deve ser:\n dd/mm/aaaa');
            return;
        }

        updateCreciNumber(inCreci);
        updateCreciDate(inCreciDate);
            
        navigation.navigate('RegisterTShirt');
    }
    
    return (
        <>
            <KeyboardAvoidingView style={{ flex: 1 }} behavior="padding" >
                <ScrollView contentContainerStyle={{ flexGrow: 1, backgroundColor: "#FFF" }} ref={scrollViewRef} keyboardShouldPersistTaps="handled" showsVerticalScrollIndicator={false}>
                        <RegisterHeader
                            title="Documentos"
                            subtitle={`Tenha em mãos o seu Creci e um documento\ncom foto.`}
                        />
                        {loading && 
                            <View style={{flex: 1, justifyContent: "center", alignItems: "center"}}>
                                <ActivityIndicator size="large" color="#00467F" />
                            </View>
                        }
                        {!loading && 
                            <>
                                <View style={mainStyles.wrapperRegister}>
                                    <View style={[mainStyles.container, styles.container]}>
                                        {/* {inCreci.length > 0 && ''} */}
                                        <Text style={mainStyles.label}>CRECI:</Text>
                                        <TextInput  
                                            underlineColorAndroid="transparent"  
                                            style={[mainStyles.inputText, styles.input]}
                                            value={inCreci}
                                            onChangeText={text => setInCreci(text)}
                                        />
                                        {/* {inCreciDate.length > 0 && ''} */}
                                        <Text style={mainStyles.label}>Data de validade:</Text>
                                        <TextInput  
                                            underlineColorAndroid="transparent"  
                                            style={[mainStyles.inputText, styles.input]}
                                            value={inCreciDate}
                                            onChangeText={text => setInCreciDate( dateMask(text) )}
                                            keyboardType="phone-pad"
                                        />
                                        {/* <Text style={mainStyles.inputInfo}>O formato deve ser dd/mm/aaaa</Text> */}
                                        <View style={mainStyles.rowUploads}>
                                            {image === null &&                                                
                                                <TouchableOpacity style={mainStyles.btnUploadCol2} onPress={() => chooseImage('front')}>
                                                    <Camera style={[mainStyles.btnIconUpload, mainStyles.btnIconUploadM]} />
                                                    <Text style={[mainStyles.btnTextUpload2, mainStyles.btnTextUploadCol, mainStyles.btnTextUploadCol2]}>{`FOTO DO\nSEU CRECI\n`}<Text style={mainStyles.bold}>FRENTE</Text></Text>
                                                </TouchableOpacity>
                                            }
                                            {image !== null &&
                                                <View style={mainStyles.boxPreview2}>
                                                    <View style={mainStyles.boxPreviewImageContent2}>
                                                        <Image source={{ uri: image.uri }} style={mainStyles.previewCol} />
                                                    </View>
                                                    <TouchableOpacity onPress={() => deleteImage('front')}>
                                                        <Text style={mainStyles.deletePreview}>EXCLUIR</Text>
                                                    </TouchableOpacity>
                                                </View>
                                            }

                                            {backImage === null &&                                                
                                                <TouchableOpacity style={mainStyles.btnUploadCol2} onPress={() => chooseImage('back')}>
                                                    <Camera style={[mainStyles.btnIconUpload, mainStyles.btnIconUploadM]} />
                                                    <Text style={[mainStyles.btnTextUpload2, mainStyles.btnTextUploadCol, mainStyles.btnTextUploadCol2]}>{`FOTO DO\nSEU CRECI\n`}<Text style={mainStyles.bold}>VERSO</Text></Text>
                                                </TouchableOpacity>
                                            }
                                            {backImage !== null &&
                                                <View style={mainStyles.boxPreview2}>
                                                    <View style={mainStyles.boxPreviewImageContent2}>
                                                        <Image source={{ uri: backImage.uri }} style={mainStyles.previewCol} />
                                                    </View>
                                                    <TouchableOpacity onPress={() => deleteImage('back')}>
                                                        <Text style={mainStyles.deletePreview}>EXCLUIR</Text>
                                                    </TouchableOpacity>
                                                </View>
                                            }
                                        </View>
                                        {/* FIM DO PEDACINHO EM MANUTENÇÃO */}
                                    </View>
                                <View style={mainStyles.contentBottomRegister}>
                                    <View style={[mainStyles.container, styles.contentButtons]}>              
                                        <TouchableOpacity style={[mainStyles.btnOutlineBlue, mainStyles.buttonW48]} onPress={() => navigation.navigate('RegisterType')}>
                                            <Text style={mainStyles.btnTextOutlineBlue}>VOLTAR</Text>
                                        </TouchableOpacity>
                                        <TouchableOpacity style={[mainStyles.btnBlueLight, styles.btnLogin, mainStyles.buttonW48]} onPress={() => update()}>
                                            <Text style={mainStyles.btnTextCenterBlueLight}>AVANÇAR</Text>
                                        </TouchableOpacity>
                                    </View>
                                </View>
                            </View>
                            </>
                        }
                </ScrollView>
            </KeyboardAvoidingView>
        </>
    );
}

export default RegisterUploadCreci;