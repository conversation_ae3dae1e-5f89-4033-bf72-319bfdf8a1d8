import React, { useEffect, useState, useRef } from 'react';
import { TextInput, Text, TouchableOpacity, View, Alert, KeyboardAvoidingView, ScrollView, Keyboard, ActivityIndicator, Linking } from 'react-native';
import ArrowLeft from '../../../assets/svgs/ArrowLeft';

import RegisterHeader from '../../../components/headers/RegisterHeader';
import LightboxWhats from '../../../components/LightboxWhats';
import Select from '../../../components/Select';
import { useRegister } from '../../../context/register';

import Whats from '../../../assets/svgs/Whats4';
import ArrowRightGray from '../../../assets/svgs/ArrowRightGray';

import mainStyles from '../../../mainStyles';
import styles from './styles';

const RegisterRealState = ({navigation}) => {

    const [lightboxWhats, setLightboxWhats] = useState(false);

    const { updateIsBroker } = useRegister();

    const scrollViewRef = useRef();

    const update = (value) => {
        updateIsBroker(value);
        navigation.navigate('RegisterManagerCury');
    }

    const scroll = () => {
        scrollViewRef.current.scrollToEnd({ animated: true });
    }

    return (
        <KeyboardAvoidingView style={{ flex: 1 }} behavior="padding" >
            <ScrollView contentContainerStyle={{ flexGrow: 1, backgroundColor: "#FFF" }} ref={scrollViewRef} keyboardShouldPersistTaps="handled" showsVerticalScrollIndicator={false}>
                <View style={[]}>
                    <RegisterHeader
                        title="Cadastro"
                        subtitle={`Qual é a sua imobiliária?`}
                    />
                    <View style={[mainStyles.container, styles.container, mainStyles.wrapperRegister, styles.wrapper]}>
                        {/* <TouchableOpacity style={styles.btnBack} onPress={() => navigation.navigate('RegisterInit')}>
                            <ArrowLeft style={styles.iconBack} />
                        </TouchableOpacity> */}
                        <Text style={mainStyles.label}>Imobiliária parceira:</Text>
                        <Select />
                        <Text style={styles.textRegular}>Não encontrou sua imobiliária na lista?</Text>
                        <Text style={styles.textBlueRh}>Fale com a Gestão de Autônomos</Text>
                        <Text style={styles.textMedium}>Horário de atendimento:</Text>
                        <Text style={styles.textLight}>2ª a 6ª - 09:00 às 18:00</Text>
                        <Text style={styles.textLight}>Sábado - 09:00 às 13:00</Text>
                        <View style={styles.divider}></View>
                        <Text style={styles.textRegional}>Regional: <Text style={styles.textGreen}>SÃO PAULO</Text></Text>
                        <TouchableOpacity style={styles.btnWhats} onPress={() => setLightboxWhats(true)}>
                            <View style={styles.boxWhats}>
                                <Whats />
                            </View>
                            <View style={styles.flexWhats}>
                                <Text style={styles.textBlue}>Falar com a Gestão de Autônomos</Text>
                                <ArrowRightGray style={styles.icArrow} />
                            </View>
                        </TouchableOpacity>
                        {/* <TouchableOpacity style={[mainStyles.btnBlue, styles.btnAnswer]} onPress={() => update(true)}>
                            <Text style={mainStyles.btnTextBlue}>SIM</Text>
                        </TouchableOpacity> */}
                    </View>
                </View>
            </ScrollView>
            <View style={mainStyles.contentBottomRegister}>
                <View style={[mainStyles.container, styles.contentButtons]}>              
                    <TouchableOpacity style={[mainStyles.btnOutlineBlue, mainStyles.buttonW48]} onPress={() => navigation.navigate('RegisterRegion')}>
                    <Text style={mainStyles.btnTextOutlineBlue}>VOLTAR</Text>
                    </TouchableOpacity>
                    <TouchableOpacity style={[mainStyles.btnBlueLight, styles.btnLogin, mainStyles.buttonW48]} onPress={() => update(true)}>
                        <Text style={mainStyles.btnTextCenterBlueLight}>AVANÇAR</Text>
                    </TouchableOpacity>
                </View>
            </View>
            {lightboxWhats &&
                <LightboxWhats 
                    close={() => setLightboxWhats(false)}
                />
            }
        </KeyboardAvoidingView>
    );
}

export default RegisterRealState;