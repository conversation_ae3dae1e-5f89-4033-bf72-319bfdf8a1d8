import React, { useEffect, useState, useRef } from 'react';
import { TextInput, Text, TouchableOpacity, View, Alert, KeyboardAvoidingView, ScrollView, Keyboard } from 'react-native';

import RegisterHeader from '../../../components/headers/RegisterHeader';
import { useRegister } from '../../../context/register';
import { cepMask } from '../../../useful/masks';

import mainStyles, { selectStyles } from '../../../mainStyles';
import styles from './styles';
import api from '../../../services/api';

const RegisterAddress = ({navigation}) => {

    const { 
        cep, 
        address, 
        addressNumber, 
        addressComplement, 
        updateCep, 
        updateAddress, 
        updateAddressNumber, 
        updateAddressComplement,
        updateAddressNeighborhood,
        updateAddressCity,
        updateAddressState
    } = useRegister();

    const [inCep, setInCep] = useState(cep);
    const [inAddress, setInAddress] = useState(address);
    const [inAddressNumber, setInAddressNumber] = useState(addressNumber);
    const [inAddressComplement, setInAddressComplement] = useState(addressComplement);
    const [keyboardVisible, setKeyboardVisible] = useState(false);
    const scrollViewRef = useRef();

    const update = async () => {
        if(inCep.length < 9){
            Alert.alert('Verifique seu CEP', 'Por favor, verifique se digitou o CEP corretamente.');
            return;
        }

        if(inAddress === null || inAddress === ''){
            Alert.alert('Verifique seu endereço', 'Por favor, informe seu endereço.');
            return;
        }

        if(inAddressNumber === null || inAddressNumber === ''){
            Alert.alert('Verifique o número', 'Por favor, informe o número.');
            return;
        }

        updateCep(inCep);
        updateAddress(inAddress);
        updateAddressNumber(inAddressNumber);
        updateAddressComplement(inAddressComplement);

        navigation.navigate('RegisterAddressCity');
    }
    
    useEffect(() => {
        if(inCep.length > 8){
            api.post('/cadastro/endereco', {
                cep: inCep
            }).then(res => {
                let neigh = res.data.bairro;
                neigh = typeof neigh === 'string' || neigh instanceof String ? neigh : '';
                
                let city = res.data.cidade;
                city = typeof city === 'string' || city instanceof String ? city : '';

                let state = res.data.uf;
                state = typeof state === 'string' || state instanceof String ? state : '';

                let addr = res.data.logradouro;
                addr = typeof addr === 'string' || addr instanceof String ? addr : '';

                setInAddress( addr );
                updateAddressNeighborhood(neigh);
                updateAddressCity(city);
                updateAddressState(state);
            }).catch(err => console.log(err));
        }
    }, [inCep]);

    useEffect(() => {
        const keyboardDidShowListener = Keyboard.addListener('keyboardDidShow', () => {
            setKeyboardVisible(true);
          }
        );
        
        const keyboardDidHideListener = Keyboard.addListener( 'keyboardDidHide', () => {
              setKeyboardVisible(false); // or some other action
            }
        );
      
        return () => {
            keyboardDidHideListener.remove();
            keyboardDidShowListener.remove();
        };

    }, []);

    useEffect(() => {
        scroll();
    }, [keyboardVisible]);


    const scroll = () => {
        scrollViewRef.current.scrollToEnd({ animated: true });
    }

    return (
        <KeyboardAvoidingView style={{ flex: 1, backgroundColor: "#FFF" }} behavior="padding" >
            <ScrollView contentContainerStyle={{ flexGrow: 1 }} ref={scrollViewRef} keyboardShouldPersistTaps="handled" showsVerticalScrollIndicator={false}>
                
                    <RegisterHeader
                        title="Endereço"
                        subtitle={`Informe seu endereço completo, por favor.`}
                    />
                    <View style={mainStyles.wrapperRegister}>
                        <View style={[mainStyles.container, styles.container]}>
                            {/* {inCep.length > 0 && ''} */}
                            <Text style={mainStyles.label}>CEP:</Text>
                            <TextInput 
                                underlineColorAndroid="transparent"  
                                style={mainStyles.inputText}
                                keyboardType="phone-pad"
                                value={inCep}
                                onChangeText={text => setInCep( cepMask(text))}
                                onFocus={() => scroll()}
                            />
                            {/* {inAddress.length > 0 && ''} */}
                            <Text style={mainStyles.label}>Endereço:</Text>
                            <TextInput  
                                underlineColorAndroid="transparent"  
                                style={mainStyles.inputText}
                                value={inAddress}
                                onChangeText={text => setInAddress(text)}
                                onFocus={() => scroll()}
                            />
                            {/* {inAddressNumber.length > 0 && ''} */}
                            <Text style={mainStyles.label}>Número:</Text>
                            <TextInput
                                underlineColorAndroid="transparent"  
                                style={mainStyles.inputText}
                                value={inAddressNumber}
                                onChangeText={text => setInAddressNumber( text)}
                                onFocus={() => scroll()}
                            />
                            {/* {inAddressComplement.length > 0 && ''} */}
                            <Text style={mainStyles.label}>Complemento:</Text>
                            <TextInput
                                underlineColorAndroid="transparent"  
                                style={mainStyles.inputText}
                                value={inAddressComplement}
                                onChangeText={text => setInAddressComplement( text)}
                                onFocus={() => scroll()}
                            />
                        </View>
                    </View>
                    <View style={mainStyles.contentBottomRegister}>
                        <View style={[mainStyles.container, styles.contentButtons]}>              
                            <TouchableOpacity style={[mainStyles.btnOutlineBlue, mainStyles.buttonW48]} onPress={() => navigation.goBack()}>
                                <Text style={mainStyles.btnTextOutlineBlue}>VOLTAR</Text>
                            </TouchableOpacity>
                            <TouchableOpacity style={[mainStyles.btnBlueLight, styles.btnLogin, mainStyles.buttonW48]} onPress={() => update()}>
                                <Text style={mainStyles.btnTextCenterBlueLight}>AVANÇAR</Text>
                            </TouchableOpacity>
                        </View>
                    </View>
            </ScrollView>
        </KeyboardAvoidingView>
    );
}

export default RegisterAddress;