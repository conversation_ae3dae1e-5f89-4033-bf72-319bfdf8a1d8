import { Platform, StyleSheet, Dimensions } from 'react-native';

const windowWidth = Dimensions.get('window').width;

const styles = StyleSheet.create({
    pd25: {
        paddingTop: 25
    },
    textLogin: {
        fontFamily: 'Ubuntu-Regular',
        fontSize: Platform.OS === 'ios' ? 16 : 12,
        letterSpacing: 2,
        color: '#4EA1CC',
        marginTop: 40,
        marginBottom: 15
    },
    contentButtons: {
        flexDirection: "row",
        justifyContent: "space-between"
    },
    contentBottom: {
        paddingTop: 15,      
        paddingBottom: 15,
        shadowRadius: 2,
        shadowOffset: {
            width: 0,
            height: -2,
        },
        shadowColor: '#CCC',
        elevation: 2,
        shadowOpacity: 0.5,
        backgroundColor: "#FFF"
    },
    textPrimary: {
        fontFamily: 'Ubuntu-Regular',
        fontSize: Platform.OS === 'ios' ? 16 : 13,
        color: '#828282',
        marginBottom: 20,
        lineHeight: Platform.OS === 'ios' ? 24 : 18,
        letterSpacing: 1
    },
    textSecondary: {
        fontFamily: 'Ubuntu-Regular',
        fontSize: Platform.OS === 'ios' ? 16 : 13,
        color: '#828282',
        marginBottom: 30,
        lineHeight: Platform.OS === 'ios' ? 24 : 18,
        letterSpacing: 1
    },
    textInfoTitle: {
        fontFamily: 'Ubuntu-Medium',
        fontSize: Platform.OS === 'ios' ? 16 : 13,
        color: '#00467F',
        marginBottom: 20,
        lineHeight: Platform.OS === 'ios' ? 24 : 18,
        letterSpacing: 1
    },
    textInfo: {
        fontFamily: 'Ubuntu-Regular',
        fontSize: Platform.OS === 'ios' ? 16 : 13,
        color: '#00467F',
        lineHeight: Platform.OS === 'ios' ? 24 : 18,
        marginBottom: 30,
        letterSpacing: 1
    },
    btnWhats: {
        flexDirection: "row",
        alignItems: "center",
        marginTop: 50
    },
    boxWhats: {
        borderColor: "#2D719F",
        borderWidth: 1,
        borderRadius: 10,
        padding: 9,
        marginRight: 10
    },
    btnTextWhats: {
        fontFamily: 'Ubuntu-Regular',
        fontSize: Platform.OS === 'ios' ? 16 : 14,
        color: '#828282'
    },
    textBlue: {
        color: "#00467F"
    }
});

export default styles;