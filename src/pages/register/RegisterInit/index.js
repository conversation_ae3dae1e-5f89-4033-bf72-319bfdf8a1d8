import React, { useState } from 'react';
import { View, ScrollView, Text, TouchableOpacity, Linking } from 'react-native';

import styles from './styles';
import mainStyles from '../../../mainStyles';

import SimpleHeader from '../../../components/headers/SimpleHeader';
import LightboxWhats from '../../../components/LightboxWhats';

import Whats from '../../../assets/svgs/Whats';

const RegisterInit = ({navigation}) => {
    const [lightboxWhats, setLightboxWhats] = useState(false);

    return (
        <>
        <SimpleHeader />
        <ScrollView style={mainStyles.wrapper} showsVerticalScrollIndicator={false}>
            <View style={[mainStyles.container, styles.pd25]}>
                <Text style={styles.textPrimary}>{`Se você já é um Corretor Cury, faça o seu cadastro e aguarde a liberação a Gestão de Autônomos.`}</Text>
                <Text style={styles.textSecondary}>{`Caso queira se tornar um Corretor Cury, preencha o formulário e aguarde nosso contato.`}</Text>
                <Text style={styles.textInfoTitle}>{`IMPORTANTE:\n Tenha em mãos os seguintes documentos:`}</Text>
                <Text style={styles.textInfo}>{`- RG ou CNH\n- CRECI ou Protocolo de Estágio\n- Comprovante de Residência`}
                </Text>
            </View>
        </ScrollView>
        <View style={styles.contentBottom}>
            <View style={[mainStyles.container, styles.contentButtons]}>
                <TouchableOpacity style={[mainStyles.btnOutlineBlue, mainStyles.buttonW48]} onPress={() => navigation.goBack()}>
                    <Text style={mainStyles.btnTextOutlineBlue}>VOLTAR</Text>
                </TouchableOpacity>
                <TouchableOpacity style={[mainStyles.btnBlueLight, styles.btnLogin, mainStyles.buttonW48]} onPress={() => navigation.navigate('RegisterRegion')}>
                    <Text style={mainStyles.btnTextCenterBlueLight}>INICIAR</Text>
                </TouchableOpacity>
            </View>
        </View>
        </>
    );
}

export default RegisterInit;