import React, { useState, useRef, useEffect } from 'react';
import { TextInput, Text, TouchableOpacity, View, Alert, KeyboardAvoidingView, ScrollView, Keyboard, ActivityIndicator } from 'react-native';

import RegisterHeader from '../../../components/headers/RegisterHeader';
import { useRegister } from '../../../context/register';
import { phoneMask } from '../../../useful/masks';

import mainStyles, { selectStyles } from '../../../mainStyles';
import styles from './styles';

const RegisterPassword = ({navigation}) => {

    const { password, updatePassword } = useRegister();

    const [loading, setLoading] = useState(false);
    const [inPassword, setInPassword] = useState(password);
    const [inPasswordConfirm, setInPasswordConfirm] = useState(password);
    const [keyboardVisible, setKeyboardVisible] = useState(false);
    const scrollViewRef = useRef();

    const update = async () => {
        if(inPassword.length < 8){
            Alert.alert('Senha muito curta', 'Sua senha deve ter no mínimo 8 caracteres.');
            return;
        }

        if(inPassword === null || inPassword === ''){
            Alert.alert('Verifique sua senha', 'Por favor, informe sua senha.');
            return;
        }
            
        if(inPasswordConfirm !== inPassword){
            Alert.alert('Confirmação incorreta', 'Senha diferente da confirmação.');
            return;
        }
        
        
        updatePassword(inPassword);

        navigation.navigate('RegisterAddress');
    }

    useEffect(() => {
        const keyboardDidShowListener = Keyboard.addListener('keyboardDidShow', () => {
            setKeyboardVisible(true);
          }
        );
        
        const keyboardDidHideListener = Keyboard.addListener( 'keyboardDidHide', () => {
              setKeyboardVisible(false); // or some other action
            }
        );
      
        return () => {
            keyboardDidHideListener.remove();
            keyboardDidShowListener.remove();
        };

    }, []);

    useEffect(() => {
        scroll();
    }, [keyboardVisible]);


    const scroll = () => {
        scrollViewRef.current.scrollToEnd({ animated: true });
    }
    
    return (
        <KeyboardAvoidingView style={{ flex: 1, backgroundColor: "#FFF" }} behavior="padding" >
            <ScrollView contentContainerStyle={{ flexGrow: 1 }} ref={scrollViewRef} keyboardShouldPersistTaps="handled" showsVerticalScrollIndicator={false}>
                
                    <RegisterHeader
                        title="Senha"
                        subtitle={`Crie uma senha de acesso`}
                    />
                    {loading && 
                        <View style={{flex: 1, justifyContent: "center", alignItems: "center"}}>
                            <ActivityIndicator size="large" color="#00467F" />
                        </View>
                    }
                    {!loading && 
                        <>
                        <View style={mainStyles.wrapperRegister}>
                            <View style={[mainStyles.container, styles.container]}>
                                {/* {inPassword.length > 0 && '' } */}
                                <Text style={mainStyles.label}>Senha:</Text>
                                <TextInput
                                    autoCapitalize="none"  
                                    underlineColorAndroid="transparent"  
                                    style={mainStyles.inputText}
                                    value={inPassword}
                                    onChangeText={text => setInPassword(text)}
                                    secureTextEntry={true}
                                />
                                {/* {inPasswordConfirm.length > 0 && '' } */}
                                <Text style={mainStyles.label}>Confirmar senha:</Text>
                                <TextInput  
                                    autoCapitalize="none"
                                    underlineColorAndroid="transparent"  
                                    style={mainStyles.inputText}
                                    value={inPasswordConfirm}
                                    onChangeText={text => setInPasswordConfirm(text)}
                                    secureTextEntry={true}
                                />
                                <Text style={styles.textPass}>A senha deve ter ao menos 8 caracteres.</Text>
                                {/* <View style={{ marginTop: 20 }}>
                                    <BarPasswordStrengthDisplay
                                        password={inPassword}
                                        width={mainStyles.container.width - 20}
                                        levels={passwordLevels}
                                    />
                                </View> */}
                            </View>
                        </View>
                            <View style={mainStyles.contentBottomRegister}>
                                <View style={[mainStyles.container, styles.contentButtons]}>              
                                    <TouchableOpacity style={[mainStyles.btnOutlineBlue, mainStyles.buttonW48]} onPress={() => navigation.goBack()}>
                                        <Text style={mainStyles.btnTextOutlineBlue}>VOLTAR</Text>
                                    </TouchableOpacity>
                                    <TouchableOpacity style={[mainStyles.btnBlueLight, styles.btnLogin, mainStyles.buttonW48]} onPress={() => update()}>
                                        <Text style={mainStyles.btnTextCenterBlueLight}>AVANÇAR</Text>
                                    </TouchableOpacity>
                                </View>
                            </View>
                        </>
                    }
            </ScrollView>
        </KeyboardAvoidingView>
    );
}

export default RegisterPassword;