import React, { useEffect, useState, useRef } from 'react';
import { ActivityIndicator, TextInput, Text, TouchableOpacity, View, Alert, KeyboardAvoidingView, ScrollView, Keyboard, Image, Platform, PermissionsAndroid } from 'react-native';
import ImageCropPicker from 'react-native-image-crop-picker';
import RegisterHeader from '../../../components/headers/RegisterHeader';

import Camera from '../../../assets/svgs/Camera2';

import { useRegister } from '../../../context/register';

import api from '../../../services/api';

import mainStyles, { selectStyles } from '../../../mainStyles';
import styles from './styles';
import { useAuth } from '../../../context/auth';

const RegisterPicture = ({navigation}) => {
    const { signInWithToken } = useAuth();

    const { 
        register,
        selfie,
        selfieId,
        updateSelfie,
        updateSelfieId,
        cpf,
        password
    } = useRegister();

    const [loading, setLoading] = useState(false);
    const [image, setImage] = useState(selfie);
    const [keyboardVisible, setKeyboardVisible] = useState(false);
    const scrollViewRef = useRef();

    const update = async () => {
        if(image === null){
            Alert.alert('Envie sua foto', 'Por favor, para avançar, envie sua foto.');
            return;
        }

        setLoading(true);

        const data = new FormData();

        data.append('selfie', {
            uri: image.path,
            type: 'image/jpeg',
            name: 'selfie.jpg'
        });

        if(selfieId !== ''){     
            const params = {
                selfieId
            };
            finish(params);
            return;
        }

        api.post('/cadastro/upload', data, {
                onUploadProgress: progressEvent => {
                    let percentCompleted = Math.floor((progressEvent.loaded * 100) / progressEvent.total);
                }
            })
            .then(res => {
                updateSelfieId(res.data.id);
                updateSelfie(image);
                const params = {
                    selfieId: res.data.id
                };
                finish(params);
            })
            .catch(err =>{ 
                console.log(err.response);
                alert('Por favor, escolha uma imagem e tente novamente.');
                setLoading(false);
            });
    }

    const finish = async (params) => {
        let cpfToLogin = cpf;
        let passwordToLogin = password;

        let accessToken = await register(params);
        if(accessToken){
            signInWithToken(cpfToLogin, passwordToLogin, accessToken);
        } else {
            setLoading(false);
        }
    }

    useEffect(() => {
        const keyboardDidShowListener = Keyboard.addListener('keyboardDidShow', () => {
            setKeyboardVisible(true);
          }
        );
        
        const keyboardDidHideListener = Keyboard.addListener( 'keyboardDidHide', () => {
              setKeyboardVisible(false); // or some other action
            }
        );
      
        return () => {
            keyboardDidHideListener.remove();
            keyboardDidShowListener.remove();
        };

    }, []);

    const openUploadPicture = () => {
        let alertProperties;
        if(Platform.OS === 'ios'){
            alertProperties = [
                { text: "Tirar foto", onPress: () => openCamera() },
                { text: "Galeria", onPress: () => openImageLibrary() },
                { text: "Fechar" }
            ];
        } else {
            alertProperties = [
                { text: "Fechar" },
                { text: "Galeria", onPress: () => openImageLibrary() },
                { text: "Tirar foto", onPress: () => openCamera() }
            ];
        }
        Alert.alert(
            "Enviar foto",
            "Como deseja enviar sua foto?",
            alertProperties,
            { cancelable: false }
        );
        
    }

    const openImageLibrary = () => {
        ImageCropPicker.openPicker({
            width: 500,
            height: 500,
            cropping: true
        }).then(image => {
            storeImage(image);
        });
    }

    const openCamera = async () => {
        const hasPermission = await requestCameraPermission();
        if(hasPermission){
            ImageCropPicker.openCamera({
                width: 500,
                height: 500,
                cropping: true
            }).then(image => {
                storeImage(image);
            });
        }
    }

    const storeImage = image => {
        setImage(image);
        updateSelfie(image);
    }

    const deleteImage = () => {
        updateSelfieId('');
        updateSelfie(null);
        setImage(null);
    }

    const requestCameraPermission = async () => {
        try {
          const granted = await PermissionsAndroid.request(
            PermissionsAndroid.PERMISSIONS.CAMERA,
            {
              title: "Cool Photo App Camera Permission",
              message:
                "Cool Photo App needs access to your camera " +
                "so you can take awesome pictures.",
              buttonNeutral: "Ask Me Later",
              buttonNegative: "Cancel",
              buttonPositive: "OK"
            }
          );
          if (granted === PermissionsAndroid.RESULTS.GRANTED) {
            return true;
          } else {
            Alert.alert("Câmera não pode ser aberta", "Por favor, acesse as configurações do seu aparelho e dê permissão ao app para acessar a câmera.");
            return false;
        }
        } catch (err) {
          console.warn(err);
          Alert.alert('Não foi possível acessar a câmera');
          return false;
        }
    };

    useEffect(() => {
        scroll();
    }, [keyboardVisible]);

    const scroll = () => {
        scrollViewRef.current.scrollToEnd({ animated: true });
    }

    return (
        <KeyboardAvoidingView style={{ flex: 1 }} behavior="padding" >
            <ScrollView contentContainerStyle={{ flexGrow: 1, backgroundColor: "#FFF" }} ref={scrollViewRef} keyboardShouldPersistTaps="handled" showsVerticalScrollIndicator={false}>
                    <RegisterHeader
                        title="Foto"
                        subtitle={`Envie uma foto para finalizar o cadastro`}
                    />
                    {loading && 
                    <View style={{flex: 1, justifyContent: "center", alignItems: "center"}}>
                        <ActivityIndicator size="large" color="#00467F" />
                    </View>
                    }
                    {!loading &&
                    <>
                    <View style={mainStyles.wrapperRegister}>
                        <View style={[mainStyles.container, styles.container]}>
                            <View style={styles.shift}>
                            {image === null &&
                                <>
                                <View style={styles.marginTop}>
                                    <Text style={mainStyles.textAlert2}>Favor se atentar às seguintes recomendações:</Text>
                                    <View style={mainStyles.pdL}>
                                        <Text style={mainStyles.textAlert2}>• Foto somente do rosto</Text>
                                        <Text style={mainStyles.textAlert2}>• Usar um fundo branco</Text>
                                        <Text style={mainStyles.textAlert2}>• Escolha um local bem iluminado</Text>
                                    </View>
                                </View>
                                <View style={styles.divider}></View>
                                <View style={mainStyles.centerContainer}>
                                    <TouchableOpacity style={[mainStyles.btnUpload2, styles.btnMargin]} onPress={openUploadPicture}>
                                        <Camera style={mainStyles.btnIconUpload} />
                                        <Text style={[mainStyles.btnTextUpload2, mainStyles.btnTextUploadCol]}>{`CLIQUE AQUI\nPARA ENVIAR A\n`}<Text style={mainStyles.bold}>SUA FOTO</Text></Text>
                                    </TouchableOpacity>
                                </View>
                                </>
                            }
                            </View>
                            {image !== null &&
                                <>
                                <Text style={mainStyles.textAlert2}>Tirar a foto somente do rosto, em um local bem iluminado e com fundo branco.</Text>
                                <View style={styles.divider}></View>
                                <View style={{flexDirection: "row", justifyContent: "center"}}>
                                    <View style={[mainStyles.boxProfile, mainStyles.boxPreview2, styles.boxPictureProfile]}>
                                        <View style={[mainStyles.boxPreviewImageContent2, styles.boxPreviewImageProfile]}>
                                            <Image
                                                style={mainStyles.imgProfile}
                                                source={{ uri: image.path }}
                                            />
                                            </View>
                                        <Text style={mainStyles.deletePreview} onPress={deleteImage}>EXCLUIR</Text>
                                    </View>
                                </View>
                                </>
                            }
                        </View>
                    </View>
                    <View style={mainStyles.contentBottomRegister}>
                        <View style={[mainStyles.container, styles.contentButtons]}>              
                            <TouchableOpacity style={[mainStyles.btnOutlineBlue, mainStyles.buttonW48]} onPress={() => navigation.goBack()}>
                                <Text style={mainStyles.btnTextOutlineBlue}>VOLTAR</Text>
                            </TouchableOpacity>
                            <TouchableOpacity style={[mainStyles.btnBlueLight, styles.btnLogin, mainStyles.buttonW48]} onPress={() => update()}>
                                <Text style={mainStyles.btnTextCenterBlueLight}>FINALIZAR</Text>
                            </TouchableOpacity>
                        </View>
                    </View>
                        </>
                    }
            </ScrollView>
        </KeyboardAvoidingView>
    );
}

export default RegisterPicture;