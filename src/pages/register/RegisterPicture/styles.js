import { StyleSheet, Dimensions } from 'react-native';

const windowWidth = Dimensions.get('window').width;

const styles = StyleSheet.create({
    container: {
        paddingTop: 30
    },
    wrapper: {
        justifyContent: "space-between"
    },
    contentBottom: {        
        marginBottom: 50
    },
    contentButtons: {
        flexDirection: "row",
        justifyContent: "space-between"
    },
    button: {
        width: '45%',
    },
    marginTop: {
        marginBottom: 20
    },
    icWidth: {
        width: 45
    },
    textRed: {
        fontFamily: "Roboto-Regular",
        fontSize: 16,
        color: "#FF6542",
        letterSpacing: 1,
        marginTop: 15,
        marginBottom: 40
    },
    marginTop: {
        marginTop: 20
    },
    btnPicture: {
        width: "45%",
        alignItems: "center"
    },
    boxPicture: {
        borderColor: "#4EA1CC",
        borderWidth: 2,
        borderRadius: 8,
        paddingLeft: 10,  
        paddingRight: 10,  
        paddingTop: 7, 
        paddingBottom: 15
    },
    tinyLogo: {
        width: 130,
        height: 150,
        resizeMode: "cover"
    },
    btnUpload: {
        height: 140
    },
    btnMargin: {
        marginTop: 35,
        marginBottom: 25
    },
    divider: {
        borderBottomWidth: 1,
        borderBottomColor: '#DADADA',
        marginTop: 25,
        marginBottom: -5
    },
    boxPictureProfile: {
        marginTop: 45,
        width: 200,
        height: 240
    },
    boxPreviewImageProfile: {
        height: 220
    }
});

export default styles;