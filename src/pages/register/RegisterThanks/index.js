import React, { useEffect } from 'react';
import { ScrollView, Text, TouchableOpacity, View, Linking, BackHandler } from 'react-native';

import mainStyles from '../../../mainStyles';
import styles from './styles';

import Logo from '../../../assets/svgs/Logo';
import Ok from '../../../assets/svgs/Ok';

const RegisterThanks = ({navigation}) => {
    const back = () => {
        navigation.navigate('RegisterInit');
        return true;
    }

    useEffect(() => {
        const backHandler = BackHandler.addEventListener(
            "hardwareBackPress",
            back
        );
      
        return () => backHandler.remove();
    }, []);

    return (
        <ScrollView showsVerticalScrollIndicator={false}>
            <View style={styles.wrapper}>
                <View style={mainStyles.container}>
                    <Logo style={styles.logo}/>
                    <Ok style={styles.ok}/>
                    <Text style={styles.title}>Obrigado</Text>
                    <Text style={styles.text}>
                        {`Seus dados foram enviados para análise.\nEm breve entraremos em contato.`}
                    </Text>
                    <TouchableOpacity 
                        style={[mainStyles.btnOutlineWhite, styles.button]}
                        onPress={() => navigation.navigate('RegisterMain')}
                    >
                        <Text style={mainStyles.btnTextOutlineWhite}>VOLTAR PARA O INÍCIO</Text>
                    </TouchableOpacity>
                </View>
            </View>
        </ScrollView>
    );
}

export default RegisterThanks;