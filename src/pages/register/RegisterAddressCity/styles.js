import { StyleSheet, Dimensions } from 'react-native';

const windowWidth = Dimensions.get('window').width;

const styles = StyleSheet.create({
    container: {
        paddingTop: 50
    },
    wrapper: {
        justifyContent: "space-between"
    },
    contentBottom: {        
        marginBottom: 50
    },
    contentButtons: {
        flexDirection: "row",
        justifyContent: "space-between"
    },
    button: {
        width: '45%',
    },
    btnUpload: {
        marginTop: 25,
        flexDirection: "column",
        justifyContent: "center",
        alignItems: "center"
    },
    preview: {
        width: '100%',
        height: 150,
        resizeMode: "contain",
        borderRadius: 5,
        marginTop: 15,
        marginBottom: 15
    },
    divider: {
        borderBottomWidth: 1,
        borderBottomColor: '#DADADA',
        marginTop: 10,
    }
});

export default styles;