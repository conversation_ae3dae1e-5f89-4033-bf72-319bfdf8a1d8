import React, { useEffect, useState, useRef } from 'react';
import { ActivityIndicator, TextInput, Text, TouchableOpacity, View, Alert, KeyboardAvoidingView, ScrollView, Keyboard, Image, Platform } from 'react-native';

import RegisterHeader from '../../../components/headers/RegisterHeader';
import Select from '../../../components/Select';

import { useRegister } from '../../../context/register';

import api from '../../../services/api';

import mainStyles, { selectStyles } from '../../../mainStyles';
import styles from './styles';

import * as ImagePicker from 'react-native-image-picker';

import Camera from '../../../assets/svgs/Camera2';
import { requestCameraPermission } from "../../../useful/permissions";

const states = [
    { label: 'AC', value: 'AC' },
    { label: 'AL', value: 'AL' },
    { label: 'AP', value: 'AP' },
    { label: 'AM', value: 'AM' },
    { label: 'BA', value: 'BA' },
    { label: 'CE', value: 'CE' },
    { label: 'DF', value: 'DF' },
    { label: 'ES', value: 'ES' },
    { label: 'GO', value: 'GO' },
    { label: 'MA', value: 'MA' },
    { label: 'MS', value: 'MS' },
    { label: 'MT', value: 'MT' },
    { label: 'MG', value: 'MG' },
    { label: 'PA', value: 'PA' },
    { label: 'PB', value: 'PB' },
    { label: 'PR', value: 'PR' },
    { label: 'PE', value: 'PE' },
    { label: 'PI', value: 'PI' },
    { label: 'RJ', value: 'RJ' },
    { label: 'RN', value: 'RN' },
    { label: 'RS', value: 'RS' },
    { label: 'RO', value: 'RO' },
    { label: 'RR', value: 'RR' },
    { label: 'SC', value: 'SC' },
    { label: 'SP', value: 'SP' },
    { label: 'SE', value: 'SE' },
    { label: 'TO', value: 'TO' }
]

const RegisterAddressCity = ({navigation}) => {

    const { 
        addressNeighborhood,
        addressCity,
        addressState,
        addressImage,
        addressImageId,
        updateAddressNeighborhood,
        updateAddressCity,
        updateAddressState,
        updateAddressImageId,
        updateAddressImage
    } = useRegister();

    const [image, setImage] = useState(addressImage);
    const [loading, setLoading] = useState(false);
    const [inAddressNeighborhood, setInAddressNeighborhood] = useState(addressNeighborhood);
    const [inAddressCity, setInAddressCity] = useState(addressCity);
    const [inAddressState, setInAddressState] = useState(addressState);
    const [keyboardVisible, setKeyboardVisible] = useState(false);
    const scrollViewRef = useRef();

    const chooseImage = () => {
        Keyboard.dismiss();
        let alertProperties;
        if(Platform.OS === 'ios'){
            alertProperties = [
                { text: "Tirar foto", onPress: () => openCamera() },
                { text: "Galeria", onPress: () => openImageLibrary() },
                { text: "Fechar" }
            ];
        } else {
            alertProperties = [
                { text: "Fechar" },
                { text: "Galeria", onPress: () => openImageLibrary() },
                { text: "Tirar foto", onPress: () => openCamera() }
            ];
        }
        Alert.alert(
            "Enviar documento",
            "Como deseja enviar o documento?",
            alertProperties,
            { cancelable: false }
        );
    }

    const imageOptions = {
        mediaType: 'photo',
        includeBase64: false,
        maxHeight: 1024,
        maxWidth: 1024
    };
    

    const openCamera = async () => {
        const hasPermission = await requestCameraPermission();
        if(hasPermission){
            ImagePicker.launchCamera (imageOptions, res => {
                if(res.errorCode){
                    console.log(res);
                    Alert.alert('Erro', 'Câmera não disponível');
                    return;
                }
                if(res?.assets){
                    updateAddressImageId('');
                    setImage(res.assets[0]);
                }
                scroll();
            
            });
        }
    }

    const openImageLibrary = () => {
        ImagePicker.launchImageLibrary(imageOptions, res => {
            updateAddressImageId('');
            if(res?.assets){
                setImage(res.assets[0]);
                scroll();
            }
        });
    }

    const update = async () => {
        if(inAddressNeighborhood === null || inAddressNeighborhood === ''){
            Alert.alert('Verifique seu bairro', 'Por favor, informe seu bairro.');
            return;
        }

        if(inAddressCity === null || inAddressCity === ''){
            Alert.alert('Verifique sua cidade', 'Por favor, informe sua cidade.');
            return;
        }

        if(inAddressState === null || inAddressState === ''){
            Alert.alert('Verifique o estado', 'Por favor, informe o estado.');
            return;
        }

        if(image === null){
            Alert.alert('Envie o comprovante', 'Por favor, para avançar, envie seu comprovante de residência.');
            return;
        }

        setLoading(true);

        const data = new FormData();

        data.append('comprovante_endereco', {
            uri: image.uri,
            type: 'image/jpeg',
            name: 'comprovante_endereco.jpg'
        });

        console.log(data);

        if(addressImageId !== ''){            
            updateAddressNeighborhood(inAddressNeighborhood);
            updateAddressCity(inAddressCity);
            updateAddressState(inAddressState);

            navigation.navigate('RegisterUploadDocument');
            setLoading(false);
            return;
        }

        api.post('/cadastro/upload', data, {
                onUploadProgress: progressEvent => {
                    let percentCompleted = Math.floor((progressEvent.loaded * 100) / progressEvent.total);
                }
            })
            .then(res => {
                updateAddressImageId(res.data.id);
                updateAddressImage(image);
                
                updateAddressNeighborhood(inAddressNeighborhood);
                updateAddressCity(inAddressCity);
                updateAddressState(inAddressState);

                navigation.navigate('RegisterUploadDocument');
                setLoading(false);
            })
            .catch(err =>{ 
                alert('Por favor, escolha uma imagem e tente novamente.');
                setLoading(false);
            });
    }

    const changeSatate = value => {
        setInAddressState(value);
    }

    useEffect(() => {
        const keyboardDidShowListener = Keyboard.addListener('keyboardDidShow', () => {
            setKeyboardVisible(true);
          }
        );
        
        const keyboardDidHideListener = Keyboard.addListener( 'keyboardDidHide', () => {
              setKeyboardVisible(false); // or some other action
            }
        );
      
        return () => {
            keyboardDidHideListener.remove();
            keyboardDidShowListener.remove();
        };

    }, []);

    useEffect(() => {
        scroll();
    }, [keyboardVisible]);

    useEffect(() => {
        scroll();
    }, [image]);


    const scroll = () => {
        scrollViewRef.current.scrollToEnd({ animated: true });
    }

    const deleteImage = () => {
        updateAddressImageId('');
        setImage(null);
    }

    return (
        <KeyboardAvoidingView style={{ flex: 1, backgroundColor: "#FFF" }} behavior="padding" >
            <ScrollView contentContainerStyle={{ flexGrow: 1 }} ref={scrollViewRef} keyboardShouldPersistTaps="handled" showsVerticalScrollIndicator={false}>
                
                    <RegisterHeader
                        title="Endereço"
                        subtitle={`Informe seu endereço completo, por favor.`}
                    />
                    {loading && 
                        <View style={{flex: 1, justifyContent: "center", alignItems: "center"}}>
                            <ActivityIndicator size="large" color="#00467F" />
                        </View>
                    }
                    {!loading &&
                        <>
                        <View style={mainStyles.wrapperRegister}>
                            <View style={[mainStyles.container, styles.container]}>
                                {/* {inAddressNeighborhood.length > 0 && '' } */}
                                <Text style={mainStyles.label}>Bairro:</Text>
                                <TextInput  
                                    underlineColorAndroid="transparent"  
                                    style={mainStyles.inputText}
                                    value={inAddressNeighborhood}
                                    onChangeText={text => setInAddressNeighborhood(text)}
                                    onFocus={() => scroll()}
                                />
                                {/* {inAddressCity.length > 0 && '' } */}
                                <Text style={mainStyles.label}>Cidade:</Text>
                                <TextInput 
                                    underlineColorAndroid="transparent"  
                                    style={mainStyles.inputText}
                                    value={inAddressCity}
                                    onChangeText={text => setInAddressCity( text)}
                                    onFocus={() => scroll()}
                                />
                                {/* {inAddressState.length > 0 && ''} */}
                                <Text style={mainStyles.label}>UF:</Text>
                                <Select placeholder="UF" options={states} value={inAddressState} onChange={value => changeSatate(value)} />

                                <View style={styles.divider}></View>

                                <View style={mainStyles.centerContainer}>
                                    {image === null &&
                                        <TouchableOpacity style={[mainStyles.btnUpload2, styles.btnUpload]} onPress={() => chooseImage()}>
                                            <Camera style={mainStyles.btnIconUpload} />
                                            <Text style={[mainStyles.btnTextUpload2, mainStyles.btnTextUploadCol]}>FOTO DO SEU<Text style={mainStyles.bold}>{`\nCOMPROVANTE DE \nRESIDÊNCIA`}</Text></Text>
                                        </TouchableOpacity>
                                    }
                                    {image !== null &&
                                        <View style={[mainStyles.boxPreview2, { marginTop: 20 }]}>
                                            <View style={mainStyles.boxPreviewImageContent2}>
                                                <Image source={{ uri: image.uri }} style={mainStyles.previewCol} />
                                            </View>
                                                <TouchableOpacity onPress={() => deleteImage()}>
                                                    <Text style={mainStyles.deletePreview}>EXCLUIR</Text>
                                                </TouchableOpacity>
                                        </View>
                                    }
                                </View>

                                {/* {image !== null &&
                                    <Image source={{ uri: image.uri }} style={styles.preview} />
                                } */}
                            </View>
                        </View>
                            <View style={mainStyles.contentBottomRegister}>
                                <View style={[mainStyles.container, styles.contentButtons]}>              
                                    <TouchableOpacity style={[mainStyles.btnOutlineBlue, mainStyles.buttonW48]} onPress={() => navigation.navigate('RegisterAddress')}>
                                        <Text style={mainStyles.btnTextOutlineBlue}>VOLTAR</Text>
                                    </TouchableOpacity>
                                    <TouchableOpacity style={[mainStyles.btnBlueLight, styles.btnLogin, mainStyles.buttonW48]} onPress={() => update()}>
                                        <Text style={mainStyles.btnTextCenterBlueLight}>AVANÇAR</Text>
                                    </TouchableOpacity>
                                </View>
                            </View>
                        </>
                    }
            </ScrollView>
        </KeyboardAvoidingView>
    );
}

export default RegisterAddressCity;