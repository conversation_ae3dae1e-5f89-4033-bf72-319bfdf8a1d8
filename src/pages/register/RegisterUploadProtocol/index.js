import React, { useState, useRef, useEffect } from 'react';
import { Platform, Text, TouchableOpacity, View, Image, Alert, ActivityIndicator, TextInput, KeyboardAvoidingView, ScrollView, Keyboard  } from 'react-native';

import RegisterHeader from '../../../components/headers/RegisterHeader';
import { useRegister } from '../../../context/register';

import api from '../../../services/api';
import { dateMask } from '../../../useful/masks';
import { validateDate } from '../../../useful/validate';


import mainStyles, { selectStyles } from '../../../mainStyles';
import styles from './styles';

import Camera from '../../../assets/svgs/Camera2';

import * as ImagePicker from 'react-native-image-picker';
import { requestCameraPermission } from "../../../useful/permissions";

const RegisterUploadProtocol = ({navigation}) => {
    const { 
        cpf,
        protocol,
        protocolNumber, 
        protocolDate, 
        protocolImage, 
        updateProtocol, 
        updateProtocolDate, 
        updateProtocolImage, 
        updateProtocolNumber, 
        register 
    } = useRegister();

    const [image, setImage] = useState(protocolImage);
    const [loading, setLoading] = useState(false);
    const [inProtocol, setInProtocol] = useState(protocolNumber);
    const [inProtocolDate, setInProtocolDate] = useState(protocolDate);
    const [keyboardVisible, setKeyboardVisible] = useState(false);
    const scrollViewRef = useRef();

    useEffect(() => {
        // if(cpf === ''){
        //     navigation.navigate('Main');
        // }

        const keyboardDidShowListener = Keyboard.addListener('keyboardDidShow', () => {
            setKeyboardVisible(true);
          }
        );
        
        const keyboardDidHideListener = Keyboard.addListener( 'keyboardDidHide', () => {
              setKeyboardVisible(false); // or some other action
            }
        );
      
        return () => {
            keyboardDidHideListener.remove();
            keyboardDidShowListener.remove();
        };

    }, []);

    useEffect(() => {
        scroll();
    }, [keyboardVisible]);

    useEffect(() => {
        scroll();
    }, [image]);


    const scroll = () => {
        scrollViewRef.current.scrollToEnd({ animated: true });
    }

    const chooseImage = () => {
        Keyboard.dismiss();
        let alertProperties;
        if(Platform.OS === 'ios'){
            alertProperties = [
                { text: "Tirar foto", onPress: () => openCamera() },
                { text: "Galeria", onPress: () => openImageLibrary() },
                { text: "Fechar" }
            ];
        } else {
            alertProperties = [
                { text: "Fechar" },
                { text: "Galeria", onPress: () => openImageLibrary() },
                { text: "Tirar foto", onPress: () => openCamera() }
            ];
        }
        Alert.alert(
            "Enviar documento",
            "Como deseja enviar o documento?",
            alertProperties,
            { cancelable: false }
        );
    }

    const imageOptions = {
        mediaType: 'photo',
        includeBase64: false,
        maxHeight: 1024,
        maxWidth: 1024
    };

    const openCamera = async () => {
        const hasPermission = await requestCameraPermission();
        if(hasPermission){
            ImagePicker.launchCamera (imageOptions, res => {
                if(res.errorCode){
                    Alert.alert('Erro', 'Câmera não disponível');
                    return;
                }
                if(res?.assets){
                    updateProtocol('');
                    setImage(res.assets[0]);
                    scroll();
                }
            });
        }
    }

    const openImageLibrary = () => {
        ImagePicker.launchImageLibrary(imageOptions, res => {
            if(res?.assets){
                updateProtocol('');
                setImage(res.assets[0]);
                scroll();
            }
        });
    }

    const update = async () => {
        if(inProtocol.length < 1){
            Alert.alert('Verifique seu protocolo', 'Por favor, para avançar, informe seu protocolo.');
            return;
        }

        if(image === null){
            Alert.alert('Envie seu protocolo', 'Por favor, para avançar, envie seu protocolo.');
            return;
        }

        if( !validateDate(inProtocolDate) ){
            Alert.alert('Verifique a data de validade', 'Por favor, verifique a data de validade do seu protocolo.\nO formato deve ser:\n dd/mm/aaaa');
            return;
        }

        setLoading(true);

        const data = new FormData();

        data.append('protocolo_estagio', {
            uri: image.uri,
            type: 'image/jpeg',
            name: 'protocolo_estagio.jpg'
        });

        if(protocol !== ''){
            updateProtocolNumber(inProtocol);
            updateProtocolDate(inProtocolDate);
            navigation.navigate('RegisterTShirt');
            return;
        }

        api.post('/cadastro/upload', data, {
                onUploadProgress: progressEvent => {
                    let percentCompleted = Math.floor((progressEvent.loaded * 100) / progressEvent.total);
                    console.log(percentCompleted);
                }
            })
            .then(res => {
                updateProtocol(res.data.id);
                updateProtocolImage(image);
                updateProtocolNumber(inProtocol);
                updateProtocolDate(inProtocolDate);
                navigation.navigate('RegisterTShirt');
            })
            .catch(err =>{ 
                console.log(err);
                alert('Por favor, escolha uma imagem e tente novamente.')
            }).then(() => {
                setLoading(false);
            });
    }

    const deleteImage = () => {
        updateProtocol('');
        setImage(null);
    }

    return (
        <>
            <KeyboardAvoidingView style={{ flex: 1 }} behavior="padding" >
                <ScrollView contentContainerStyle={{ flexGrow: 1, backgroundColor: "#FFF" }} ref={scrollViewRef} keyboardShouldPersistTaps="handled" showsVerticalScrollIndicator={false}>
                    
                        <RegisterHeader
                            title="Documentos"
                            subtitle={`Tenha em mãos o seu Creci e um documento\ncom foto.`}
                        />
                        {loading && 
                            <View style={{flex: 1, justifyContent: "center", alignItems: "center"}}>
                                <ActivityIndicator size="large" color="#00467F" />
                            </View>
                        }
                        {!loading && 
                            <>
                                
                                <View style={mainStyles.wrapperRegister}>
                                    <View style={[mainStyles.container, styles.container]}>
                                        {/* {inProtocol.length > 0 && ''} */}
                                        <Text style={mainStyles.label}>Protocolo de estágio:</Text>
                                        <TextInput
                                            underlineColorAndroid="transparent"  
                                            style={[mainStyles.inputText, styles.input]}
                                            value={inProtocol}
                                            onChangeText={text => setInProtocol(text)}
                                        />
                                        {/* {inProtocolDate.length > 0 && '' } */}
                                        <Text style={mainStyles.label}>Data de validade:</Text>
                                        <TextInput  
                                            underlineColorAndroid="transparent"  
                                            style={[mainStyles.inputText, styles.input]}
                                            value={inProtocolDate}
                                            onChangeText={text => setInProtocolDate( dateMask(text) )}
                                            keyboardType="phone-pad"
                                        />
                                        {/* <Text style={mainStyles.inputInfo}>O formato deve ser dd/mm/aaaa</Text> */}
                                        
                                        <View style={mainStyles.centerContainer}>
                                            {image === null &&
                                                <TouchableOpacity style={[mainStyles.btnUpload2, styles.btnUpload]} onPress={() => chooseImage()}>
                                                    <Camera style={mainStyles.btnIconUpload} />
                                                    <Text style={[mainStyles.btnTextUpload2, mainStyles.btnTextUploadCol]}>FOTO DO SEU<Text style={mainStyles.bold}>{`\nPROTOCOLO DE\nESTÁGIO`}</Text></Text>
                                                </TouchableOpacity>
                                            }
                                            {image !== null &&
                                                <View style={mainStyles.boxPreview2}>
                                                    <View style={mainStyles.boxPreviewImageContent2}>
                                                        <Image source={{ uri: image.uri }} style={mainStyles.previewCol} />
                                                    </View>
                                                    <TouchableOpacity onPress={() => deleteImage()}>
                                                        <Text style={mainStyles.deletePreview}>EXCLUIR</Text>
                                                    </TouchableOpacity>
                                                </View>
                                            }
                                        </View>
                                    </View>
                                </View>
                                <View style={mainStyles.contentBottomRegister}>
                                    <View style={[mainStyles.container, styles.contentButtons]}>              
                                        <TouchableOpacity style={[mainStyles.btnOutlineBlue, mainStyles.buttonW48]} onPress={() => navigation.navigate('RegisterType')}>
                                            <Text style={mainStyles.btnTextOutlineBlue}>VOLTAR</Text>
                                        </TouchableOpacity>
                                        <TouchableOpacity style={[mainStyles.btnBlueLight, styles.btnLogin, mainStyles.buttonW48]} onPress={() => update()}>
                                            <Text style={mainStyles.btnTextCenterBlueLight}>AVANÇAR</Text>
                                        </TouchableOpacity>
                                    </View>
                                </View>
                            </>
                        }
                </ScrollView>
            </KeyboardAvoidingView>
        </>
    );
}

export default RegisterUploadProtocol;