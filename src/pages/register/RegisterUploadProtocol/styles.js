import { StyleSheet, Dimensions } from 'react-native';

const windowWidth = Dimensions.get('window').width;
const windowHeight = Dimensions.get('window').height;

const styles = StyleSheet.create({
    container: {
        paddingTop: 50
    },
    wrapper: {
        justifyContent: "space-between"
    },
    input: {
        marginBottom: 35
    },
    contentBottom: {        
        marginBottom: 50
    },
    contentButtons: {
        flexDirection: "row",
        justifyContent: "space-between"
    },
    button: {
        width: '45%',
    },
    camera: {
        width: windowWidth,
        height: windowHeight
    },
    capture: {
        position: "absolute",
        bottom: 40,
        width: 80,
        height: 80,
        borderRadius: 40,
        borderWidth: 2,
        borderColor: '#FFF',
        justifyContent: "center",

    },
    caputreIn: {
        position: "absolute",
        bottom: 3,
        left: 3,
        width: 70,
        height: 70,
        borderRadius: 35,
        backgroundColor: '#FFF'
    },
    preview: {
        width: '100%',
        height: 150,
        resizeMode: "contain",
        borderRadius: 5,
        marginTop: 20
    }
});

export default styles;