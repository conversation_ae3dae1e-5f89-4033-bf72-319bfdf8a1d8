import React from 'react';
import { Text, TouchableOpacity, View, ScrollView, Linking } from 'react-native';

import mainStyles from '../../../mainStyles';
import styles from './styles';

import Logo from '../../../assets/svgs/Logo';
import Wait from '../../../assets/svgs/Wait';

const RegisterAlreadyRegistered = ({navigation}) => {
    return (
        <ScrollView style={styles.wrapper} showsVerticalScrollIndicator={false}>
            <View style={mainStyles.container}>
                <Logo style={styles.logo}/>
                <Wait style={styles.wait}/>
                <Text style={styles.title}>{`CPF já\ncadastrado`}</Text>
                <Text style={styles.text}>
                    {`Seus dados já foram preenchidos\nanteriormente.`}
                </Text>
                <Text style={styles.text}>
                    {`Faça o login e acompanhe o seu processo.`}
                </Text>
                <TouchableOpacity 
                    style={[mainStyles.btnOutlineFullWhite, styles.button]}
                    onPress={() => navigation.navigate('AuthLogin')}
                >
                    <Text style={mainStyles.btnTextOutlineFullWhite}>IR PARA O LOGIN</Text>
                </TouchableOpacity>
            </View>
        </ScrollView>
    );
}

export default RegisterAlreadyRegistered;