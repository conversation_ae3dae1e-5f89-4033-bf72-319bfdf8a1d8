import React, { useState } from 'react';
import { View, ScrollView, Text, TouchableOpacity, Alert, Linking } from 'react-native';

import Logo from '../../../assets/svgs/Logo';
import Intersect from '../../../assets/svgs/Intersect';
import Whats from '../../../assets/svgs/Whats4';
import ArrowRightGray from '../../../assets/svgs/ArrowRightGray';

import styles from './styles';
import mainStyles from '../../../mainStyles';
import AsyncStorage from '@react-native-async-storage/async-storage';
import api from '../../../services/api';
import { useAuth } from '../../../context/auth';

import LightboxWhats from '../../../components/LightboxWhats';

import {
    API_HOMOLOG_URL,
    API_PRODUCTION_URL
} from '@env';


const RegisterMain = ({navigation}) => {
    const { production, handleChangeProduction } = useAuth();
    const [lightboxWhats, setLightboxWhats] = useState(false);

    return (
        <View style={{ flex: 1}}>
            {lightboxWhats &&
                <LightboxWhats 
                    close={() => setLightboxWhats(false)}
                />
            }
            <ScrollView style={mainStyles.wrapper}>
                <View style={styles.bgTop}>
                    <View style={mainStyles.container}>
                        <View style={styles.vFlex}>
                            <View>
                                <TouchableOpacity
                                    onLongPress={handleChangeProduction}
                                    delayLongPress={5000}
                                    activeOpacity={1}
                                >
                                    <Logo style={styles.logo} />
                                </TouchableOpacity>
                            </View>
                            <View>
                                <Text style={styles.title}>Bem Vindo</Text>
                                <Text style={styles.subtitle}>Corretor Cury</Text>
                            </View>
                        </View>
                    </View>
                </View>
                {/* <Intersect /> */}
                <View style={styles.contentBottom}>
                    <View style={mainStyles.container}>
                        <Text style={styles.textLogin}>Já tem cadastro?</Text>
                        <TouchableOpacity style={mainStyles.btnBlueLight} onPress={() => navigation.navigate('AuthLogin')}>
                            <Text style={mainStyles.btnTextCenterBlueLight}>FAZER LOGIN</Text>
                        </TouchableOpacity>
                        <TouchableOpacity style={mainStyles.btnTransparentWhite} onPress={() => navigation.navigate('AuthPasswordRecovery')}>
                            <Text style={[mainStyles.btnTextLink, styles.btnRecoverPassword]}>Esqueci a minha senha</Text>
                        </TouchableOpacity>
                        <View style={styles.divider}></View>
                        <Text style={styles.textLogin}>{`Ainda não tem cadastro?`}</Text>
                        <TouchableOpacity style={mainStyles.btnBlueLight} onPress={() => navigation.navigate('RegisterInit')}>
                            <Text style={mainStyles.btnTextCenterBlueLight}>QUERO ME CADASTRAR</Text>
                        </TouchableOpacity>
                        <View style={styles.divider}></View>
                        <Text style={styles.textLogin}>Precisa de ajuda?</Text>
                        <TouchableOpacity style={styles.btnWhats} onPress={() => setLightboxWhats(true)}>
                            <View style={styles.boxWhats}>
                                <Whats />
                            </View>
                            <View style={styles.flexWhats}>
                                <Text style={styles.textBlue}>Suporte Online</Text>
                                <ArrowRightGray style={styles.icArrow} />
                            </View>
                        </TouchableOpacity>
                    </View>
                </View>
            </ScrollView>
        </View>
    );
}

export default RegisterMain;