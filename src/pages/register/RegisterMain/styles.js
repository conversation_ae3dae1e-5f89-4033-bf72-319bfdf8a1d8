import { StyleSheet, Dimensions, Platform } from 'react-native';

const windowWidth = Dimensions.get('window').width;

const styles = StyleSheet.create({
    bgTop: {
        width: windowWidth,
        backgroundColor: '#2D719F',
        marginBottom: -3,
        paddingTop: 10,
        paddingBottom: 15
    },
    vFlex: {
        flexDirection: "row",
        alignItems: "center"
    },
    logo: {
        width: 60,
        marginTop: Platform.OS === 'ios' ? 45 : 30,
        marginBottom: 30,
        marginLeft: 0,
        marginRight: 13
    },
    title: {
        fontFamily: 'Ubuntu-Medium',
        fontSize: Platform.OS === 'ios' ? 36 : 34,
        color: '#FFF',
        lineHeight: 38
    },
    subtitle: {
        fontFamily: 'Ubuntu-Light',
        fontSize: Platform.OS === 'ios' ? 36 : 34,
        color: '#FFF',
        lineHeight: 38
    },
    textLogin: {
        fontFamily: 'Ubuntu-Regular',
        fontSize: Platform.OS === 'ios' ? 16 : 14,
        letterSpacing: 1,
        color: '#828282',
        marginTop: 0,
        marginBottom: 10
    },
    contentBottom: {
        marginTop: 40
    },
    textPrimary: {
        fontFamily: 'Ubuntu-Bold',
        fontSize: Platform.OS === 'ios' ? 16 : 12,
        color: '#828282',
        marginBottom: 20,
        lineHeight: 18
    },
    textSecondary: {
        fontSize: Platform.OS === 'ios' ? 16 : 12,
        color: '#828282',
        marginBottom: 20,
        lineHeight: 18
    },
    btnRecoverPassword: {
        marginBottom: -15
    },
    btnWhats: {
        flexDirection: "row",
        alignItems: "center",
        backgroundColor: "#F2F2F2"
    },
    boxWhats: {
        backgroundColor: "#90B0C0",
        height: 65,
        width: 60,
        alignItems: "center",
        justifyContent: "center"
    },
    flexWhats: {
        height: 65,
        justifyContent: "space-between",
        alignItems: "center",
        flexDirection: "row",
        paddingLeft: 11,
        paddingRight: 11,
        flex: 1
    },
    icArrow: {
        width: 28
    },
    textBlue: {
        fontFamily: 'Ubuntu-Regular',
        fontSize: Platform.OS === 'ios' ? 16 : 14,
        color: "#00467F",
        letterSpacing: 0.46
    },
    divider: {
        borderTopWidth: 1,
        borderTopColor: "#DADADA",
        marginTop: 25,
        marginBottom: 25
    }
});

export default styles;