import React, { useState, useEffect, useRef } from 'react';
import { Image, TextInput, Text, TouchableOpacity, View, Alert, KeyboardAvoidingView, ScrollView, Keyboard, ActivityIndicator } from 'react-native';
import ArrowLeft from '../../../assets/svgs/ArrowLeft';
import ArrowRightGray from '../../../assets/svgs/ArrowRightGray';

import RegisterHeader from '../../../components/headers/RegisterHeader';
import { useRegister } from '../../../context/register';

import mainStyles from '../../../mainStyles';
import RegisterThanks from '../RegisterThanks';
import styles from './styles';

const RegisterType = ({navigation}) => {

    const scrollViewRef = useRef();

    const { updateType } = useRegister();

    const update = (value) => {
        updateType(value);
        switch (value) {
            case 'credenciado':
                navigation.navigate('RegisterUploadCreci');
                break;
            case 'estagiario':
                navigation.navigate('RegisterUploadProtocol');
                break;
            case 'protocolo-definitivo':
                navigation.navigate('RegisterTShirt');
                break;
        }
    }

    return (
        <KeyboardAvoidingView style={{ flex: 1 }} behavior="padding" >
            <ScrollView contentContainerStyle={{ flexGrow: 1, backgroundColor: "#FFF" }} ref={scrollViewRef} keyboardShouldPersistTaps="handled" showsVerticalScrollIndicator={false}>
            <RegisterHeader
                title="Cadastro"
                subtitle={`Agora, nos diga como podemos entrar em\ncontato contigo.`}
            />
            <View style={mainStyles.wrapperRegister}>
                <View style={[mainStyles.container, styles.container]}>
                    {/* <TouchableOpacity style={styles.btnBack} onPress={() => navigation.navigate('RegisterUploadDocument')}>
                        <ArrowLeft style={styles.iconBack} />
                    </TouchableOpacity> */}
                    <Text style={styles.textQuestion}>Você é um corretor credenciado, estagiário ou está em treinamento?</Text>
                    <View style={styles.divider}></View>
                    <TouchableOpacity style={[styles.btnAnswer]} onPress={() => update('credenciado')}>
                        <Text style={styles.textBlue}>Corretor credenciado</Text>
                        <ArrowRightGray style={styles.icArrow} />
                    </TouchableOpacity>
                    <TouchableOpacity style={[styles.btnAnswer]} onPress={() => update('estagiario')}>
                        <Text style={styles.textBlue}>Estagiário</Text>
                        <ArrowRightGray style={styles.icArrow} />
                    </TouchableOpacity>
                    <TouchableOpacity style={[styles.btnAnswer]} onPress={() => update('protocolo-definitivo')}>
                        <Text style={styles.textBlue}>Em treinamento</Text>
                        <ArrowRightGray style={styles.icArrow} />
                    </TouchableOpacity>
                </View>
                <View style={mainStyles.contentBottomRegister}>
                    <View style={[mainStyles.container, styles.contentButtons]}>
                        <TouchableOpacity style={[mainStyles.btnBlueLight, styles.btnLogin]} onPress={() => navigation.navigate('RegisterUploadDocument')}>
                            <Text style={mainStyles.btnTextCenterBlueLight}>VOLTAR</Text>
                        </TouchableOpacity>
                    </View>
                </View>
            </View>
        </ScrollView>
    </KeyboardAvoidingView>
    );
}

export default RegisterType;