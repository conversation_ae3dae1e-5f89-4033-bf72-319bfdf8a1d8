import { StyleSheet, Dimensions } from 'react-native';

const windowWidth = Dimensions.get('window').width;

const styles = StyleSheet.create({
    container: {
        paddingTop: 50
    },
    btnBack: {
        marginBottom: 30,
        marginTop: 30,
        height: 100,
        justifyContent: "center",
    },
    iconBack: {
        width: 16,
    },
    textQuestion: {
        fontFamily: 'Ubuntu-Regular',
        fontSize: 13,
        color: '#828282',
        letterSpacing: 1,
        marginBottom: 25,
        lineHeight: 18
    },
    btnBorderLight: {
        borderColor: "#4EA1CC"
    },
    divider: {
        borderBottomWidth: 1,
        borderBottomColor: '#DADADA',
        marginBottom: 30,
        marginTop: -5
    },
    btnAnswer: {
        flexDirection: "row",
        justifyContent: "space-between",
        alignItems: "center",
        backgroundColor: "#F2F2F2",
        borderLeftColor: "#90B0C0",
        borderLeftWidth: 15,
        marginBottom: 12,
        height: 60,
        paddingLeft: 15,
        paddingRight: 10
    },
    boxWhats: {
        backgroundColor: "#90B0C0",
        height: 65,
        width: 60,
        alignItems: "center",
        justifyContent: "center"
    },
    flexWhats: {
        height: 65,
        justifyContent: "space-between",
        alignItems: "center",
        flexDirection: "row",
        paddingLeft: 11,
        paddingRight: 11,
        flex: 1
    },
    icArrow: {
        width: 28
    },
    textBlue: {
        fontFamily: 'Ubuntu-Regular',
        fontSize: Platform.OS === 'ios' ? 16 : 14,
        color: "#00467F",
        letterSpacing: 0.46
    }
});

export default styles;