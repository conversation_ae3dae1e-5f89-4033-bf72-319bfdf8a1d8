import React, { useEffect, useState, useRef } from 'react';
import { TextInput, Text, TouchableOpacity, View, Alert, KeyboardAvoidingView, ScrollView, Keyboard, ActivityIndicator, Linking } from 'react-native';
import ArrowLeft from '../../../assets/svgs/ArrowLeft';

import RegisterHeader from '../../../components/headers/RegisterHeader';
import LightboxWhats from '../../../components/LightboxWhats';
import Select from '../../../components/Select';
import { useRegister } from '../../../context/register';

import Whats from '../../../assets/svgs/Whats4';
import ArrowRightGray from '../../../assets/svgs/ArrowRightGray';

import mainStyles from '../../../mainStyles';
import styles from './styles';
import api from "../../../services/api";

const RegisterImobiliaria = ({navigation}) => {
    const { 
        canal,
        region, 
        regionSlug, 
        imobiliaria, 
        setImobiliaria
    } = useRegister();

    const [inImobiliaria, setInImobiliaria] = useState(imobiliaria);

    const [imobiliarias, setImobiliarias] = useState([]);
    const [loadingImobiliarias, setLoadingImobiliarias] = useState(true);

    useEffect(() => {
        getImobiliarias();
    }, []);

    const getImobiliarias = () => {
        setLoadingImobiliarias(true);

        api.get('cadastro/imobiliarias', {
            params: {
                regional_id: region
            }
        }).then(res => {
            setImobiliarias(res.data.imobiliarias.map(imobiliaria => {
                return {
                    label: imobiliaria.nome,
                    value: imobiliaria.id
                }
            }));
        }).catch(err => {
            console.log(err?.response);
            Alert.alert('Não foi possível obter as imobiliárias');
        }).then(() => setLoadingImobiliarias(false));
    }

    const update = () => {
        if(inImobiliaria === null || inImobiliaria === ''){
            Alert.alert('Informe a sua imobiliária');
            return;
        }

        setImobiliaria(inImobiliaria);
        navigation.navigate('RegisterManagerCury');
    }

    return (
        <KeyboardAvoidingView style={{ flex: 1 }} behavior="padding" >
            <ScrollView contentContainerStyle={{ flexGrow: 1, backgroundColor: "#FFF" }} keyboardShouldPersistTaps="handled" showsVerticalScrollIndicator={false}>
                <View style={[]}>
                    <RegisterHeader
                        title="Cadastro"
                        subtitle={`Primeiro nos conte um pouco sobre você.`}
                    />
                    <View style={[mainStyles.container, styles.container, mainStyles.wrapperRegister, styles.wrapper]}>
                        <Text style={mainStyles.label}>Imobiliária parceira:</Text>
                        <Select 
                            placeholder={loadingImobiliarias ? 'Carregando...' : 'Selecione'}
                            onChange={setInImobiliaria}
                            value={inImobiliaria}
                            options={imobiliarias}
                        />
                        <Text style={styles.textRegular}>Não encontrou sua imobiliária na lista?</Text>
                        <Text style={styles.textBlueRh}>Fale com a Gestão de Autônomos</Text>
                        <Text style={styles.textMedium}>Horário de atendimento:</Text>
                        <Text style={styles.textLight}>2ª a 6ª - 09:00 às 18:00</Text>
                        {regionSlug === 'sp' &&
                            <Text style={styles.textLight}>Sábado - 09:00 às 13:00</Text>
                        }
                        <View style={styles.divider}></View>
                        <Text style={styles.textRegional}>Regional: <Text style={styles.textGreen}>{regionSlug === 'sp' ? 'SÃO PAULO' : 'RIO DE JANEIRO'}</Text></Text>
                        <TouchableOpacity style={styles.btnWhats} onPress={() => {
                            if(regionSlug === 'sp'){
                                return Linking.openURL(`whatsapp://send?&phone=5511992509026`);
                            } else {
                                return Linking.openURL(`whatsapp://send?&phone=5521991143851`);
                            }
                        }}>
                            <View style={styles.boxWhats}>
                                <Whats />
                            </View>
                            <View style={styles.flexWhats}>
                                <Text style={styles.textBlue}>Falar com a Gestão de Autônomos</Text>
                                <ArrowRightGray style={styles.icArrow} />
                            </View>
                        </TouchableOpacity>
                    </View>
                </View>
            </ScrollView>
            <View style={mainStyles.contentBottomRegister}>
                <View style={[mainStyles.container, styles.contentButtons]}>              
                    <TouchableOpacity style={[mainStyles.btnOutlineBlue, mainStyles.buttonW48]} onPress={() => navigation.goBack()}>
                    <Text style={mainStyles.btnTextOutlineBlue}>VOLTAR</Text>
                    </TouchableOpacity>
                    <TouchableOpacity style={[mainStyles.btnBlueLight, styles.btnLogin, mainStyles.buttonW48, !loadingImobiliarias && inImobiliaria ? null : mainStyles.btnDisabled]} disabled={loadingImobiliarias || !inImobiliaria} onPress={() => update(true)}>
                        <Text style={mainStyles.btnTextCenterBlueLight}>AVANÇAR</Text>
                    </TouchableOpacity>
                </View>
            </View>
        </KeyboardAvoidingView>
    );
}

export default RegisterImobiliaria;