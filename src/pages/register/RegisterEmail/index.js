import React, { useState, useRef, useEffect } from 'react';
import { TextInput, Text, TouchableOpacity, View, Alert, KeyboardAvoidingView, ScrollView, Keyboard, ActivityIndicator } from 'react-native';

import RegisterHeader from '../../../components/headers/RegisterHeader';
import { useRegister } from '../../../context/register';
import { phoneMask } from '../../../useful/masks';

import mainStyles, { selectStyles } from '../../../mainStyles';
import styles from './styles';

import api from '../../../services/api';

const RegisterEmail = ({navigation}) => {

    const { canal, email, emailConfirm, phone, updateEmail, updateEmailConfirm, updatePhone } = useRegister();

    const [loading, setLoading] = useState(false);
    const [inEmail, setInEmail] = useState(email);
    const [inEmailConfirm, setInEmailConfirm] = useState(emailConfirm);
    const [inPhone, setInPhone] = useState(phone);
    const [keyboardVisible, setKeyboardVisible] = useState(false);
    const scrollViewRef = useRef();

    const update = async () => {
        if((inEmail === null || inEmail === '') && canal !== 'CIA/Imobiliária' && canal !== 'Autônomo/Parceiro'){
            Alert.alert('Verifique seu email', 'Por favor, informe seu email.');
            return;
        }

        if(inPhone.length < 14){
            if(canal !== 'CIA/Imobiliária' && canal !== 'Autônomo/Parceiro'){
                Alert.alert('Verifique seu telefone', 'Por favor, verifique o número do seu telefone.');
                return;
            } else if(inPhone.length > 0){
                Alert.alert('Verifique seu telefone', 'Por favor, verifique o número do seu telefone. Se você não deseja informar um telefone, deixe esse campo vazio.');
                return;
            }
        }
        
        if(inEmail.length > 0 || inEmailConfirm.length > 0){
            setLoading(true);
            
            const validateEmail = await api.post('cadastro/check-email', {email: inEmail})
                .then(res => { return true; })
                .catch(err => { return false; })
                .finally(() => setLoading(false));
    
            if(!validateEmail){
                Alert.alert('E-mail não disponível', 'Este e-mail já está cadastrado ou é inválido.');
                return;
            }

            if(inEmailConfirm !== inEmail){
                Alert.alert('Confirmação incorreta', 'E-mail diferente da confirmação.');
                return;
            }
        }     

        updateEmail(inEmail);
        updateEmailConfirm(inEmailConfirm);
        updatePhone(inPhone);

        navigation.navigate('RegisterPassword');
        
    }

    useEffect(() => {
        const keyboardDidShowListener = Keyboard.addListener('keyboardDidShow', () => {
            setKeyboardVisible(true);
          }
        );
        
        const keyboardDidHideListener = Keyboard.addListener( 'keyboardDidHide', () => {
              setKeyboardVisible(false); // or some other action
            }
        );
      
        return () => {
            keyboardDidHideListener.remove();
            keyboardDidShowListener.remove();
        };

    }, []);

    useEffect(() => {
        scroll();
    }, [keyboardVisible]);


    const scroll = () => {
        scrollViewRef.current.scrollToEnd({ animated: true });
    }
    
    return (
        <KeyboardAvoidingView style={{ flex: 1, backgroundColor: "#FFF" }} behavior="padding" >
            <ScrollView contentContainerStyle={{ flexGrow: 1 }} ref={scrollViewRef} keyboardShouldPersistTaps="handled" showsVerticalScrollIndicator={false}>
                
                    <RegisterHeader
                        title="Cadastro"
                        subtitle={`Agora, nos diga como podemos entrar em\ncontato contigo.`}
                    />
                    {loading && 
                        <View style={{flex: 1, justifyContent: "center", alignItems: "center"}}>
                            <ActivityIndicator size="large" color="#00467F" />
                        </View>
                    }
                    {!loading && 
                        <>
                        <View style={mainStyles.wrapperRegister}>
                            <View style={[mainStyles.container, styles.container]}>
                                {(canal === 'CIA/Imobiliária' || canal === 'Autônomo/Parceiro') &&
                                    <Text style={styles.alertText}><Text style={{ fontWeight: 'bold' }}>ATENÇÃO:</Text> Não é obrigatório cadastrar seu e-mail ou celular. Caso não cadastre seu e-mail pedimos que guarde muito bem a senha, pois não haverá como recuperá-la pelo aplicativo. Para problemas com recuperação de senha, entre em contato com a Gestão de Autônomos.</Text>
                                }
                                {/* {inEmail.length > 0 && ''} */}
                                <Text style={mainStyles.label}>E-mail:</Text>
                                <TextInput
                                    autoCapitalize="none"  
                                    underlineColorAndroid="transparent"  
                                    style={mainStyles.inputText}
                                    keyboardType="email-address"
                                    value={inEmail}
                                    onChangeText={text => setInEmail(text.trim())}
                                />
                                {/* {inEmailConfirm.length > 0 && ''} */}
                                <Text style={mainStyles.label}>Confirmar e-mail:</Text>
                                <TextInput  
                                    autoCapitalize="none"
                                    underlineColorAndroid="transparent"  
                                    style={mainStyles.inputText}
                                    keyboardType="email-address"
                                    value={inEmailConfirm}
                                    onChangeText={text => setInEmailConfirm(text.trim())}
                                />
                                {/* {inPhone.length > 0 && ''} */}
                                <Text style={mainStyles.label}>Telefone:</Text>
                                <TextInput
                                    underlineColorAndroid="transparent"  
                                    style={mainStyles.inputText}
                                    keyboardType="phone-pad"
                                    value={inPhone}
                                    onChangeText={text => setInPhone( phoneMask(text) )}
                                />
                            </View>
                        </View>
                            <View style={mainStyles.contentBottomRegister}>
                                <View style={[mainStyles.container, styles.contentButtons]}>              
                                    <TouchableOpacity style={[mainStyles.btnOutlineBlue, mainStyles.buttonW48]} onPress={() => navigation.navigate('RegisterName')}>
                                        <Text style={mainStyles.btnTextOutlineBlue}>VOLTAR</Text>
                                    </TouchableOpacity>
                                    <TouchableOpacity style={[mainStyles.btnBlueLight, styles.btnLogin, mainStyles.buttonW48]} onPress={() => update()}>
                                        <Text style={mainStyles.btnTextCenterBlueLight}>AVANÇAR</Text>
                                    </TouchableOpacity>
                                </View>
                            </View>
                        </>
                    }
            </ScrollView>
        </KeyboardAvoidingView>
    );
}

export default RegisterEmail;