import React, { useState, useRef, useEffect } from 'react';
import { TextInput, Text, TouchableOpacity, View, Alert, KeyboardAvoidingView, ScrollView, Keyboard, ActivityIndicator } from 'react-native';

import RegisterHeader from '../../../components/headers/RegisterHeader';
import { useRegister } from '../../../context/register';
import { dateMask } from '../../../useful/masks';
import { validateDate } from '../../../useful/validate';

import mainStyles, { selectStyles } from '../../../mainStyles';
import styles from './styles';

import api from '../../../services/api';
import LightboxSurname from '../../../components/LightboxSurname';

const RegisterName = ({navigation}) => {

    const { name, nickname, birth, region, updateName, updateNickname, updateBirth } = useRegister();

    const [loading, setLoading] = useState(false);
    const [inName, setInName] = useState(name);
    const [inNickname, setInNickname] = useState(nickname);
    const [inBirth, setInBirth] = useState(birth);
    const [keyboardVisible, setKeyboardVisible] = useState(false);
    const scrollViewRef = useRef();

    const [nicknames, setNicknames] = useState([]);
    const [gettingNicknames, setGettingNicknames] = useState(false);

    const [surname, setSurname] = useState(false);

    const update = async () => {
        if(inName === null || inName === ''){
            Alert.alert('Verifique seu nome', 'Por favor, informe seu nome.');
            return;
        }

        if(inNickname === null || inNickname === ''){
            Alert.alert('Verifique seu apelido', 'Por favor, informe seu apelido.');
            return;
        }

        setLoading(true);

        console.log(inNickname);
        console.log(region);
        
        const validateNickname = await api.post('cadastro/check-apelido', {apelido: inNickname, regional_id: region})
            .then(res => { return true; })
            .catch(err => { return false; });
        setLoading(false);

        if(!validateNickname){
            Alert.alert('Apelido não disponível', 'Este apelido já está cadastrado.');
            return;
        }

        updateName(inName);
        updateNickname(inNickname);

        navigation.navigate('RegisterEmail');
    }

    useEffect(() => {
        getNicknames();
        const keyboardDidShowListener = Keyboard.addListener('keyboardDidShow', () => {
            setKeyboardVisible(true);
          }
        );
        
        const keyboardDidHideListener = Keyboard.addListener( 'keyboardDidHide', () => {
              setKeyboardVisible(false); // or some other action
            }
        );
      
        return () => {
            keyboardDidHideListener.remove();
            keyboardDidShowListener.remove();
        };

    }, []);

    useEffect(() => {
        scroll();
    }, [keyboardVisible]);


    const scroll = () => {
        scrollViewRef.current.scrollToEnd({ animated: true });
    }

    const getNicknames = () => {
        setGettingNicknames(true);

        api.get('/cadastro/sugestao-apelidos').then(res => {
            setNicknames(res.data);
        }).catch(error => {
            console.log(error);
        }).then(() => {
            setGettingNicknames(false);
        });
    }

    return (
        <KeyboardAvoidingView style={{ flex: 1 }} behavior="padding" >
            <ScrollView contentContainerStyle={{ flexGrow: 1, backgroundColor: "#FFF" }} ref={scrollViewRef} keyboardShouldPersistTaps="handled" showsVerticalScrollIndicator={false}>
                
                    <RegisterHeader
                        title="Cadastro"
                        subtitle={`Primeiro nos conte um pouco sobre você.`}
                    />
                    {loading && 
                        <View style={{flex: 1, justifyContent: "center", alignItems: "center"}}>
                            <ActivityIndicator size="large" color="#00467F" />
                        </View>
                    }
                    {surname &&
                        <LightboxSurname 
                            gettingNicknames={gettingNicknames}
                            nicknames={nicknames}
                            updateNickname={value => setInNickname(value)}
                            close={() => setSurname(false)}
                        />
                    }
                    {!loading &&
                        <>
                        <View style={mainStyles.wrapperRegister}>
                            <View style={[mainStyles.container, styles.container]}>
                                {/* {inName.length > 0 && '' } */}
                                <Text style={mainStyles.label}>Nome completo:</Text>
                                <TextInput
                                    underlineColorAndroid="transparent"  
                                    style={mainStyles.inputText}
                                    value={inName}
                                    onFocus={() => scroll()}
                                    onChangeText={text => setInName(text)}
                                    autoCapitalize="words"
                                />
                                {/* {inNickname.length > 0 && '' } */}
                                <Text style={mainStyles.label}>Apelido:</Text>
                                <TextInput
                                    underlineColorAndroid="transparent"  
                                    style={mainStyles.inputText}
                                    value={inNickname}
                                    onFocus={() =>  scroll()}
                                    onChangeText={text => setInNickname(text)}
                                />
                                {/* <TouchableOpacity style={[mainStyles.btnCenterBlue, styles.btnMargin]} onPress={() => setSurname(true)}>
                                    <Text style={[mainStyles.btnTextCenterBlue, styles.textBtn]}>SUGESTÕES DE APELIDO</Text>
                                </TouchableOpacity> */}
                                {false &&
                                    <>
                                        {/* {inBirth.length > 0 && '' } */}
                                        <Text style={mainStyles.label}>Data de nascimento:</Text>
                                        <TextInput
                                            underlineColorAndroid="transparent"  
                                            style={mainStyles.inputText}
                                            keyboardType="phone-pad"
                                            value={inBirth}
                                            onFocus={() =>  scroll()}
                                            onChangeText={text => setInBirth( dateMask(text))}
                                        />
                                    </>
                                }
                            </View>
                        </View>
                            <View style={mainStyles.contentBottomRegister}>
                                <View style={[mainStyles.container, styles.contentButtons]}>              
                                    <TouchableOpacity style={[mainStyles.btnOutlineBlue, mainStyles.buttonW48]} onPress={() => navigation.goBack()}>
                                        <Text style={mainStyles.btnTextOutlineBlue}>VOLTAR</Text>
                                    </TouchableOpacity>
                                    <TouchableOpacity style={[mainStyles.btnBlueLight, styles.btnLogin, mainStyles.buttonW48]} onPress={() => update()}>
                                        <Text style={mainStyles.btnTextCenterBlueLight}>AVANÇAR</Text>
                                    </TouchableOpacity>
                                </View>
                            </View>
                        </>
                    }
                
            </ScrollView>
        </KeyboardAvoidingView>
    );
}

export default RegisterName;