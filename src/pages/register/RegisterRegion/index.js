import React, { useState, useEffect, useRef } from 'react';
import { TextInput, Text, TouchableOpacity, View, Alert, KeyboardAvoidingView, ScrollView, Keyboard, ActivityIndicator } from 'react-native';

import RegisterHeader from '../../../components/headers/RegisterHeader';
import { useRegister } from '../../../context/register';
import { cpfMask } from '../../../useful/masks';
import { validateCpf } from '../../../useful/validate';

import mainStyles, { selectStyles } from '../../../mainStyles';
import styles from './styles';
import Select from '../../../components/Select';

import api from '../../../services/api';

const RegisterRegion = ({navigation}) => {

    const {
        cpf, 
        region, 
        manager, 
        setCpf, 
        setRegion,
        setRegionSlug
    } = useRegister();

    const [loading, setLoading] = useState(false);
    const [loadingRegionais, setLoadingRegionais] = useState(true);

    const [inCpf, setInCpf] = useState(cpf);
    const [inRegional, setInRegional] = useState(region);
    const [inManager, setInManager] = useState(manager);
    const [regionais, setRegionais] = useState([]);
    const [optionsManagers, setOptionsManagers] = useState([]);
    
    const [loadingManagers, setLoadingManagers] = useState(true);
    const [keyboardVisible, setKeyboardVisible] = useState(false);
    const scrollViewRef = useRef();

    const update = async () => {
        if(inCpf.length < 14 || !validateCpf(inCpf)){
            Alert.alert('Verifique seu CPF', 'Por favor, verifique se digitou o CPF corretamente.');
            return;
        }

        if(inRegional === null || inRegional === ''){
            Alert.alert('Informe a região de interesse');
            return;
        }

        setLoading(true);

        api.post('cadastro/check-cpf', {cpf: inCpf})
            .then(res => { 
                setCpf(inCpf);
                setRegion(inRegional);
                setRegionSlug(regionais.filter(regional => regional.value === inRegional)[0].slug);

                navigation.navigate('RegisterCanal');
            })
            .catch(err => { 
                let status = err.response.data.status_cadastro;

                if(status === "reprovado"){
                    navigation.navigate('RegisterDisapproved');
                } else {
                    navigation.navigate('RegisterAlreadyRegistered');
                }
            }).then(() => setTimeout(() => {
                setLoading(false);
            }, 1000));
    }

    const getRegionais = async () => {
        setLoadingRegionais(true);
        api.get('/cadastro/regionais?gerentes=0').then(res => {
            console.log(res.data);
            setRegionais(res.data.regionais.map(regional => {
                return {
                    label: regional.nome, 
                    value: regional.id,
                    slug: regional.slug
                }
            }));
        })
        .catch(err => {
            console.log(err?.request);
            Alert.alert('Não foi possível obter as regiões');
        }).then(() => {
            setLoadingRegionais(false);
        });
    }

    useEffect(() => {
        getRegionais();

        const keyboardDidShowListener = Keyboard.addListener('keyboardDidShow', () => {
            setKeyboardVisible(true);
          }
        );
        
        const keyboardDidHideListener = Keyboard.addListener( 'keyboardDidHide', () => {
              setKeyboardVisible(false); // or some other action
            }
        );
      
        return () => {
            keyboardDidHideListener.remove();
            keyboardDidShowListener.remove();
        };

    }, []);

    useEffect(() => {
        scroll();
    }, [keyboardVisible]);

    const scroll = () => {
        scrollViewRef.current.scrollToEnd({ animated: true });
    }
    
    return (
        <KeyboardAvoidingView style={{ flex: 1 }} behavior="padding" >
            <ScrollView contentContainerStyle={{ flexGrow: 1, backgroundColor: "#FFF" }} ref={scrollViewRef} keyboardShouldPersistTaps="handled" showsVerticalScrollIndicator={false}>
                    <RegisterHeader
                        title="Cadastro"
                        subtitle={`Primeiro nos conte um pouco sobre você.`}
                    />
                    {loading && 
                        <View style={{flex: 1, justifyContent: "center", alignItems: "center"}}>
                            <ActivityIndicator size="large" color="#00467F" />
                        </View>
                    }
                    {!loading && 
                        <View style={mainStyles.wrapperRegister}>
                            <View style={[mainStyles.container, styles.container]}>
                                <Text style={mainStyles.label}>CPF:</Text>
                                <TextInput 
                                    underlineColorAndroid="transparent"  
                                    style={mainStyles.inputText}
                                    keyboardType="phone-pad"
                                    value={inCpf}
                                    onChangeText={ text => setInCpf( cpfMask(text) ) }
                                />
                                <Text style={mainStyles.label}>Região de interesse:</Text>                                
                                <Select 
                                    placeholder={loadingRegionais ? 'Carregando...' : 'Selecione'}
                                    value={inRegional} 
                                    options={regionais} 
                                    onChange={setInRegional} 
                                />
                            </View>
                            <View style={mainStyles.contentBottomRegister}>
                                <View style={[mainStyles.container, styles.contentButtons]}>              
                                    <TouchableOpacity style={[mainStyles.btnOutlineBlue, mainStyles.buttonW48]} onPress={() => navigation.goBack()}>
                                        <Text style={mainStyles.btnTextOutlineBlue}>VOLTAR</Text>
                                    </TouchableOpacity>
                                    <TouchableOpacity style={[mainStyles.btnBlueLight, styles.btnLogin, mainStyles.buttonW48]} onPress={() => update()}>
                                        <Text style={mainStyles.btnTextCenterBlueLight}>AVANÇAR</Text>
                                    </TouchableOpacity>
                                </View>
                            </View>
                        </View>
                    }
            </ScrollView>
        </KeyboardAvoidingView>
    );
}

export default RegisterRegion;