import React, { useState, useRef, useEffect } from 'react';
import { Platform, Text, TouchableOpacity, View, Image, Alert, ActivityIndicator, TextInput, PermissionsAndroid, KeyboardAvoidingView, ScrollView, Keyboard } from 'react-native';

import RegisterHeader from '../../../components/headers/RegisterHeader';
import { useRegister } from '../../../context/register';

import api from '../../../services/api';

import mainStyles, { selectStyles } from '../../../mainStyles';
import styles from './styles';

import Camera from '../../../assets/svgs/Camera2';

import * as ImagePicker from 'react-native-image-picker';
import { requestCameraPermission } from "../../../useful/permissions";

const RegisterUploadDocument = ({navigation}) => {
    const { 
        document,
        documentBack,
        documentNumber,
        documentImage,
        documentBackImage,
        updateDocument, 
        updateDocumentBack, 
        updateDocumentImage, 
        updateDocumentBackImage, 
        updateDocumentNumber,
        uploadDocument
    } = useRegister();

    const [image, setImage] = useState(documentImage);
    const [backImage, setBackImage] = useState(documentBackImage);
    const [loading, setLoading] = useState(false);
    const [inDocument, setInDocument] = useState(documentNumber);
    const [keyboardVisible, setKeyboardVisible] = useState(false);
    const scrollViewRef = useRef();

    useEffect(() => {
        const keyboardDidShowListener = Keyboard.addListener('keyboardDidShow', () => {
            setKeyboardVisible(true);
          }
        );
        
        const keyboardDidHideListener = Keyboard.addListener( 'keyboardDidHide', () => {
              setKeyboardVisible(false); // or some other action
            }
        );
      
        return () => {
            keyboardDidHideListener.remove();
            keyboardDidShowListener.remove();
        };

    }, []);

    useEffect(() => {
        scroll();
    }, [keyboardVisible]);

    useEffect(() => {
        scroll();
    }, [image, backImage]);

    const deleteImage = type => {
        if(type === 'front'){
            updateDocument('');
            updateDocumentImage(null);
            setImage(null);
        } else {
            updateDocumentBack('');
            updateDocumentBackImage(null);
            setBackImage(null);
        }
    }

    const scroll = () => {
        scrollViewRef.current.scrollToEnd({ animated: true });
    }

    const chooseImage = (type) => {
        Keyboard.dismiss();
        let alertProperties;
        if(Platform.OS === 'ios'){
            alertProperties = [
                { text: "Tirar foto", onPress: () => openCamera(type) },
                { text: "Galeria", onPress: () => openImageLibrary(type) },
                { text: "Fechar" }
            ];
        } else {
            alertProperties = [
                { text: "Fechar" },
                { text: "Galeria", onPress: () => openImageLibrary(type) },
                { text: "Tirar foto", onPress: () => openCamera(type) }
            ];
        }
        Alert.alert(
            "Enviar documento",
            "Como deseja enviar o documento?",
            alertProperties,
            { cancelable: false }
        );
    }

    const imageOptions = {
        mediaType: 'photo',
        includeBase64: false,
        maxHeight: 1024,
        maxWidth: 1024
    };

    const openCamera = async (type) => {
        const hasPermission = await requestCameraPermission();
        if(hasPermission){
            ImagePicker.launchCamera (imageOptions, res => {
                if(res.errorCode){
                    Alert.alert('Erro', 'Câmera não disponível');
                    return;
                }
                if(res?.assets){
                    if(type === 'front'){
                        updateDocument('');
                        uploadAndSetImage(res.assets[0]);
                    } else {
                        updateDocumentBack('');
                        uploadAndSetBackImage(res.assets[0]);
                    }   
                    scroll();
                }
            });
        }
    }

    const openImageLibrary = (type) => {
        ImagePicker.launchImageLibrary(imageOptions, res => {
            if(res?.assets){
                if(type === 'front'){
                    updateDocument('');
                    uploadAndSetImage(res.assets[0]);
                } else {
                    updateDocumentBack('');
                    uploadAndSetBackImage(res.assets[0]);
                }
                scroll();
            }

        });
    }

    const update = async () => {
        if(image === null){
            Alert.alert('Frente do documento', 'Por favor, para avançar, envie a frente do seu documento.');
            return;
        }

        if(backImage === null){
            Alert.alert('Envie seu documento', 'Por favor, para avançar, envie o verso do seu documento.');
            return;
        }

        if(inDocument.length < 5){
            Alert.alert('Verifique o documento', 'Por favor, para avançar, verifique o número do documento.');
            return;
        }

        setLoading(true);


        updateDocumentNumber(inDocument);
        navigation.navigate('RegisterType');
        setLoading(false);
    }

    const uploadAndSetImage = async (image) => {
        setLoading(true);
        const res = await uploadDocument(image, 'documento');
        if(res){
            setImage(image);
            updateDocumentImage(image);

            updateDocument(res);

            setLoading(false);
        } else {
            setImage(null);
            updateDocumentImage(null);

            updateDocument('');

            setLoading(false);
            Alert.alert('Falha no upload', 'Não conseguimos fazer o upload da sua imagem. Por favor, tente novamente.');
        }
    }

    const uploadAndSetBackImage = async (image) => {
        setLoading(true);
        const res = await uploadDocument(image, 'documento');
        if(res){
            setBackImage(image);
            updateDocumentBackImage(image);

            updateDocumentBack(res);

            setLoading(false);
        } else {
            setBackImage(null);
            updateDocumentBackImage(null);

            updateDocumentBack('');

            setLoading(false);
            Alert.alert('Falha no upload', 'Não conseguimos fazer o upload da sua imagem. Por favor, tente novamente.');
        }
    }

    return (
        <>
            <KeyboardAvoidingView style={{ flex: 1 }} behavior="padding" >
                <ScrollView contentContainerStyle={{ flexGrow: 1, backgroundColor: "#FFF" }} ref={scrollViewRef} keyboardShouldPersistTaps="handled" showsVerticalScrollIndicator={false}>
                    
                        <RegisterHeader
                            title="Documentos"
                            subtitle={`Tenha em mãos o seu Creci e um documento\ncom foto.`}
                        />
                        {loading && 
                            <View style={{flex: 1, justifyContent: "center", alignItems: "center"}}>
                                <ActivityIndicator size="large" color="#00467F" />
                            </View>
                        }
                        {!loading && 
                            <>
                            <View style={mainStyles.wrapperRegister}>
                                <View>
                                    <View style={[mainStyles.container, styles.container]}>
                                        {/* {inDocument.length > 0 && ''} */}
                                        <Text style={mainStyles.label}>RG ou CNH:</Text>
                                        <TextInput
                                            underlineColorAndroid="transparent"  
                                            style={[mainStyles.inputText, styles.input]}
                                            value={inDocument}
                                            onChangeText={text => setInDocument(text)}
                                        />
                                        <View style={styles.divider}></View>
                                        <View style={mainStyles.rowUploads}>
                                            {image === null &&                                                
                                                <TouchableOpacity style={mainStyles.btnUploadCol2} onPress={() => chooseImage('front')}>
                                                    <Camera style={[mainStyles.btnIconUpload, mainStyles.btnIconUploadM]} />
                                                    <Text style={[mainStyles.btnTextUpload2, mainStyles.btnTextUploadCol, mainStyles.btnTextUploadCol2]}>{`FOTO DO SEU\nDOCUMENTO\n`}<Text style={mainStyles.bold}>FRENTE</Text></Text>
                                                </TouchableOpacity>
                                            }
                                            {image !== null &&
                                                <View style={mainStyles.boxPreview2}>
                                                    <View style={mainStyles.boxPreviewImageContent2}>
                                                        <Image source={{ uri: image.uri }} style={mainStyles.previewCol} />
                                                    </View>
                                                    <TouchableOpacity onPress={() => deleteImage('front')}>
                                                        <Text style={mainStyles.deletePreview}>EXCLUIR</Text>
                                                    </TouchableOpacity>
                                                </View>
                                            }

                                            {backImage === null &&                                                
                                                <TouchableOpacity style={mainStyles.btnUploadCol2} onPress={() => chooseImage('back')}>
                                                    <Camera style={[mainStyles.btnIconUpload, mainStyles.btnIconUploadM]} />
                                                    <Text style={[mainStyles.btnTextUpload2, mainStyles.btnTextUploadCol, mainStyles.btnTextUploadCol2]}>{`FOTO DO SEU\nDOCUMENTO\n`}<Text style={mainStyles.bold}>VERSO</Text></Text>
                                                </TouchableOpacity>
                                            }
                                            {backImage !== null &&
                                                <View style={mainStyles.boxPreview2}>
                                                    <View style={mainStyles.boxPreviewImageContent2}>
                                                        <Image source={{ uri: backImage.uri }} style={mainStyles.previewCol} />
                                                    </View>
                                                    <TouchableOpacity onPress={() => deleteImage('back')}>
                                                        <Text style={mainStyles.deletePreview}>EXCLUIR</Text>
                                                    </TouchableOpacity>
                                                </View>
                                            }
                                        </View>
                                    </View>
                                </View>
                            </View>
                                <View style={mainStyles.contentBottomRegister}>
                                    <View style={[mainStyles.container, styles.contentButtons]}>              
                                        <TouchableOpacity style={[mainStyles.btnOutlineBlue, mainStyles.buttonW48]} onPress={() => navigation.navigate('RegisterAddressCity')}>
                                            <Text style={mainStyles.btnTextOutlineBlue}>VOLTAR</Text>
                                        </TouchableOpacity>
                                        <TouchableOpacity style={[mainStyles.btnBlueLight, styles.btnLogin, mainStyles.buttonW48]} onPress={() => update()}>
                                            <Text style={mainStyles.btnTextCenterBlueLight}>AVANÇAR</Text>
                                        </TouchableOpacity>
                                    </View>
                                </View>
                            </>
                        }
                </ScrollView>
            </KeyboardAvoidingView>
        </>
    );
}

export default RegisterUploadDocument;