import React, { useEffect, useState, useRef } from 'react';
import { TextInput, Text, TouchableOpacity, View, Alert, KeyboardAvoidingView, ScrollView, Keyboard, ActivityIndicator, Linking } from 'react-native';
import ArrowLeft from '../../../assets/svgs/ArrowLeft';

import RegisterHeader from '../../../components/headers/RegisterHeader';
import LightboxWhats from '../../../components/LightboxWhats';
import Select from '../../../components/Select';
import { useRegister } from '../../../context/register';

import Whats from '../../../assets/svgs/Whats4';
import ArrowRightGray from '../../../assets/svgs/ArrowRightGray';

import mainStyles from '../../../mainStyles';
import styles from './styles';
import api from "../../../services/api";

const RegisterManagerCury = ({navigation}) => {
    const { imobiliaria, manager, setManager, canal, region, regionSlug } = useRegister();

    const [inManager, setInManager] = useState(manager);
    const [managers, setManagers] = useState([]);

    const [loadingManagers, setLoadingManagers] = useState(true);

    const scrollViewRef = useRef();

    useEffect(() => {
        getManagers();
    }, []);

    const getManagers = () => {
        setLoadingManagers(true);

        api.get('cadastro/gerentes', {
            params: {
                regional_id: region,
                imobiliaria_id: imobiliaria ?? null,
                canal,
            }
        }).then(res => {
            console.log(res);
            setManagers(res.data.gerentes.map(gerente => {
                return {
                    label: gerente.apelido,
                    value: gerente.id
                }
            }));
        }).catch(err => {
            console.log(err?.response);
            Alert.alert('Não foi possível obter os gerentes');
        }).then(() => setLoadingManagers(false));
    }

    const update = () => {
        if(inManager === null || inManager === ''){
            Alert.alert('Informe seu gerente');
            return;
        }
        setManager(inManager);
        navigation.navigate('RegisterName');
    }

    return (
        <KeyboardAvoidingView style={{ flex: 1 }} behavior="padding" >
            <ScrollView contentContainerStyle={{ flexGrow: 1, backgroundColor: "#FFF" }} ref={scrollViewRef} keyboardShouldPersistTaps="handled" showsVerticalScrollIndicator={false}>
                <View style={[]}>
                    <RegisterHeader
                        title="Cadastro"
                        subtitle={`Vamos agora escolher o seu Gerente Cury`}
                    />
                    <View style={[mainStyles.container, styles.container, mainStyles.wrapperRegister, styles.wrapper]}>
                        {/* <TouchableOpacity style={styles.btnBack} onPress={() => navigation.navigate('RegisterInit')}>
                            <ArrowLeft style={styles.iconBack} />
                        </TouchableOpacity> */}
                        <Text style={mainStyles.label}>Gerente Cury Vendas:</Text>
                        <Select 
                            placeholder={loadingManagers ? 'Carregando...' : 'Não sei qual é meu gerente'}
                            options={managers}
                            onChange={setInManager}
                            value={inManager}
                        />
                        <Text style={styles.textRegular}>{`Se ainda não tem um Gerente Cury Vendas,\nfale com a Gestão de Autônomos`}</Text>
                        <Text style={styles.textMedium}>Horário de atendimento:</Text>
                        <Text style={styles.textLight}>2ª a 6ª - 09:00 às 18:00</Text>
                        {regionSlug === 'sp' &&
                            <Text style={styles.textLight}>Sábado - 09:00 às 13:00</Text>
                        }
                        <View style={styles.divider}></View>
                        <Text style={styles.textRegional}>Regional: <Text style={styles.textGreen}>{regionSlug === 'sp' ? 'SÃO PAULO' : 'RIO DE JANEIRO'}</Text></Text>
                        <TouchableOpacity style={styles.btnWhats} onPress={() => {
                            if(regionSlug === 'sp'){
                                return Linking.openURL(`whatsapp://send?&phone=5511992509026`);
                            } else {
                                return Linking.openURL(`whatsapp://send?&phone=5521991143851`);
                            }
                        }}>
                            <View style={styles.boxWhats}>
                                <Whats />
                            </View>
                            <View style={styles.flexWhats}>
                                <Text style={styles.textBlue}>Falar com a Gestão de Autônomos</Text>
                                <ArrowRightGray style={styles.icArrow} />
                            </View>
                        </TouchableOpacity>
                        {/* <TouchableOpacity style={[mainStyles.btnBlue, styles.btnAnswer]} onPress={() => update(true)}>
                            <Text style={mainStyles.btnTextBlue}>SIM</Text>
                        </TouchableOpacity> */}
                    </View>
                </View>
            </ScrollView>
            <View style={mainStyles.contentBottomRegister}>
                <View style={[mainStyles.container, styles.contentButtons]}>              
                    <TouchableOpacity style={[mainStyles.btnOutlineBlue, mainStyles.buttonW48]} onPress={() => navigation.goBack()}>
                    <Text style={mainStyles.btnTextOutlineBlue}>VOLTAR</Text>
                    </TouchableOpacity>
                    <TouchableOpacity style={[mainStyles.btnBlueLight, styles.btnLogin, mainStyles.buttonW48, !loadingManagers && inManager ? null : mainStyles.btnDisabled]} disabled={loadingManagers || !inManager} onPress={() => update(true)}>
                        <Text style={mainStyles.btnTextCenterBlueLight}>AVANÇAR</Text>
                    </TouchableOpacity>
                </View>
            </View>
        </KeyboardAvoidingView>
    );
}

export default RegisterManagerCury;