import { StyleSheet, Dimensions } from 'react-native';

const windowWidth = Dimensions.get('window').width;

const styles = StyleSheet.create({
    container: {
        paddingTop: 50
    },
    btnBack: {
        marginBottom: 30,
        marginTop: 30,
        height: 100,
        justifyContent: "center",
    },
    iconBack: {
        width: 16,
    },
    textQuestion: {
        fontFamily: 'Ubuntu-Regular',
        fontSize: 20,
        color: '#00467F',
    },
    btnAnswer: {
        marginBottom: 15,
        marginTop: 15,
    },
    btnWhats: {
        flexDirection: "row",
        alignItems: "center",
        backgroundColor: "#F2F2F2"
    },
    boxWhats: {
        backgroundColor: "#90B0C0",
        height: 65,
        width: 60,
        alignItems: "center",
        justifyContent: "center"
    },
    flexWhats: {
        height: 65,
        justifyContent: "space-between",
        alignItems: "center",
        flexDirection: "row",
        paddingLeft: 11,
        paddingRight: 11,
        flex: 1
    },
    icArrow: {
        width: 28
    },
    textBlue: {
        fontFamily: 'Ubuntu-Regular',
        fontSize: Platform.OS === 'ios' ? 16 : 14,
        color: "#00467F",
        letterSpacing: 0.46
    },
    divider: {
        borderTopWidth: 1,
        borderTopColor: "#DADADA",
        marginTop: 25,
        marginBottom: 25
    },
    textRegular: {
        fontFamily: 'Ubuntu-Regular',
        fontSize: Platform.OS === 'ios' ? 15 : 13,
        color: "#828282",
        letterSpacing: 1,
        lineHeight: 18
    },
    textBlueRh: {
        fontFamily: 'Ubuntu-Regular',
        fontSize: Platform.OS === 'ios' ? 17 : 15,
        color: "#00467F",
        letterSpacing: 1,
        marginTop: 10,
        marginBottom: 6
    },
    textLight: {
        fontFamily: 'Ubuntu-Light',
        fontSize: Platform.OS === 'ios' ? 15 : 13,
        color: "#828282",
        letterSpacing: 1
    },
    textMedium: {
        fontFamily: 'Ubuntu-Medium',
        fontSize: Platform.OS === 'ios' ? 15 : 13,
        color: "#828282",
        letterSpacing: 1,
        marginTop: 15
    },
    textRegional: {
        fontFamily: 'Ubuntu-Regular',
        fontSize: Platform.OS === 'ios' ? 15 : 13,
        color: "#00467F",
        letterSpacing: 1,
        marginBottom: 10
    },
    textGreen: {
        color: "#1B9C20"
    },
    contentButtons: {
        flexDirection: "row",
        alignItems: "center",
        justifyContent: "space-between"
    },
});

export default styles;