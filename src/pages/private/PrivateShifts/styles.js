import { StyleSheet, Dimensions, Platform } from 'react-native';

const windowWidth = Dimensions.get('window').width;

const styles = StyleSheet.create({
    contentBottom: {
        paddingTop: 20,
        paddingBottom: 40,
        shadowRadius: Platform.OS === 'ios' ? 2 : 16,
        shadowOffset: {
            width: 0,
            height: Platform.OS === 'ios' ? -2 : 12,
        },
        shadowColor: Platform.OS === 'ios' ? "#CCC" : "#000",
        elevation: Platform.OS === 'ios' ? 2 : 24,
        shadowOpacity: 1,
        backgroundColor: "#FFF",
        position: "absolute",
        bottom: 0,
        zIndex: 10
    },
    buttonLarge: {
        width: "100%"
    },
    menu: {
        flexDirection: "row",
        backgroundColor: "#FFF",
        alignItems: 'center',
        justifyContent: 'center',
        borderRadius: 12,
        shadowColor: "#000",
        shadowOffset: {
            width: 0,
            height: 9,
        },
        shadowOpacity: 0.1,
        shadowRadius: 11.95,
        elevation: 12,
        marginTop: 15,
        paddingLeft: 30,
        paddingRight: 30,
        paddingBottom: 15,
        paddingTop: 15
    },
    container: {
        marginTop: 15
    },
    pb: {
        paddingBottom: 130
    },
    menuItem: {
        flex: 1,
        flexDirection: "row",
        justifyContent: "center",
        alignItems: "center"
    },
    separator: {
        marginLeft: 25,
        marginRight: 25,
        width: 1,
        height: '70%',
        backgroundColor: '#BDBDBD'
    },
    icShifts: {
        width: 35,
        marginRight: 12
    },
    icMap: {
        width: 30,
        marginRight: 12
    },
    textShifts: {
        fontFamily: 'Ubuntu-Regular',
        fontSize: Platform.OS === 'ios' ? 16 : 12,
        letterSpacing: 1,
        color: "#4EA1CC"
    },
    textMap: {
        fontFamily: 'Ubuntu-Regular',
        fontSize: Platform.OS === 'ios' ? 16 : 12,
        letterSpacing: 1,
        color: "#828282"
    },
    shift: {
        paddingTop: 10,
        paddingBottom: 0,
    },
    infos: {
        flexDirection: "row",
        backgroundColor: "#F2F2F2",
        alignItems: "center",
        minHeight: 65
    },
    bgIcon: {
        backgroundColor: "#90B0C0",
        minHeight: 65,
        width: 55,
        justifyContent: "center",
        alignItems: "center",
        marginRight: 10
    },
    devTitle: {
        fontFamily: "Roboto-Regular",
        fontSize: Platform.OS === 'ios' ? 16 : 12,
        letterSpacing: 1,
        color: "#4EA1CC"
    },
    devBox: {
        marginRight: 5
    },
    devName: {
        fontFamily: "Ubuntu-Regular",
        fontSize: Platform.OS === 'ios' ? 16 : 14,
        letterSpacing: 1,
        color: "#00467F",
        width: 260
    },
    devAddress: {
        fontFamily: "Ubuntu-Regular",
        fontSize: Platform.OS === 'ios' ? 15 : 13,
        color: "#050505",
        letterSpacing: 0.46,
        marginTop: 2
    },
    distanceTitle: {
        fontFamily: "Roboto-Regular",
        fontSize: Platform.OS === 'ios' ? 16 : 12,
        color: "#00467F",
        letterSpacing: 1.58,
        marginTop: 5
    },
    distance: {
        fontFamily: "Ubuntu-Regular",
        fontSize: Platform.OS === 'ios' ? 16 : 14,
        color: "#00467F",
        textTransform: "lowercase"
    },
    drawTitle: {
        marginTop: 5,
        fontFamily: "Roboto-Regular",
        fontSize: Platform.OS === 'ios' ? 16 : 12,
        color: "#FF312E",
        letterSpacing: 0.5
    },
    draw: {
        fontFamily: "Roboto-Bold",
        fontSize: Platform.OS === 'ios' ? 16 : 12,
        color: "#FF312E"
    },
    actions: {
        flexDirection: "column",
        marginTop: 0
    },
    btnActions: {
        alignItems: "center",
        flexDirection: "row",
        borderBottomColor: "#DADADA",
        borderBottomWidth: 1,
        paddingBottom: 10,
        paddingTop: 10,
        position: "relative"
    },
    btnBorder: {
        alignItems: "center",
        flexDirection: "row",
        justifyContent: "center",
        backgroundColor: "#F2F2F2",
        height: 65,
        width: 55,
        marginRight: 10
    },
    actionText: {
        fontFamily: "Ubuntu-Regular",
        fontSize: Platform.OS === 'ios' ? 14 : 14,
        color: "#00467F",
        letterSpacing: 1
    },
    icAbs: {
        position: "absolute",
        right: 15
    },
    icArrow: {
        transform: [{rotate: "-90deg"}]
    },
    notShifts: {
        fontFamily: "Roboto-Regular",
        fontSize: 16,
        textAlign: "center",
        marginTop: 45
    }
});

export default styles;