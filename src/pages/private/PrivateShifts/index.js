import React, { useEffect, useState } from 'react';
import { Text, View, TouchableOpacity, ScrollView, ActivityIndicator, Linking } from 'react-native';

import Shifts from '../../../assets/svgs/Shifts';
import About from '../../../assets/svgs/About';
import Car from '../../../assets/svgs/Car2';
import Map from '../../../assets/svgs/Map';
import Whats from '../../../assets/svgs/Whats2';
import Plantoes from '../../../assets/svgs/Plantoes';
import Less from '../../../assets/svgs/Less2';
import More from '../../../assets/svgs/More';
import Arrow from '../../../assets/svgs/ArrowDownGrey';

import Dash from 'react-native-dash';

import PrivateHeader from '../../../components/headers/PrivateHeader';

import styles from './styles';
import mainStyles from '../../../mainStyles';

import api from '../../../services/api';

import GetLocation from 'react-native-get-location';

const PrivateShifts = ({navigation}) => {

    const [loading, setLoading] = useState(true);
    const [shifts, setShifts] = useState([]);
    const [notLocation, setNotLocation] = useState(false);
    const [showOptions, setShowOptions] = useState(false);

    useEffect(() => {
        getShifts();
    }, [])

    const getShifts = async () => {
        const geolocation = await GetLocation.getCurrentPosition({
                enableHighAccuracy: true,
                timeout: 15000,
            })
            .then(location => {
                return location;
            })
            .catch(error => {
                // const { code, message } = error;
                // console.warn(code, message);
                setNotLocation(true);
                setLoading(false);
            });
        
        if(geolocation){
            api.get('/plantao', {
                    params: {
                        latitude: geolocation.latitude,
                        longitude: geolocation.longitude
                        // latitude: '-23.624674084647',
                        // longitude: '-46.684935488439'
                    }
                })
                .then(res => {
                    const shifts = res.data.plantoes;
                    setShifts(shifts);
                    setLoading(false);
                })
                .catch(err => {
                    console.log(err.response);
                });
        }
    }
    return (
        <View style={mainStyles.wrapper}>
            <PrivateHeader title={`Plantões`} back={() => navigation.navigate('PrivateMain')} />
            <View style={[mainStyles.privateContainer, {flex: 1}]}>
                
                {/* <View style={styles.menu}>
                    <TouchableOpacity style={styles.menuItem} activeOpacity={1}>
                        <Shifts style={styles.icShifts} />
                        <Text style={styles.textShifts}>{`LISTA DE\nPLANTÕES`}</Text>
                    </TouchableOpacity>
                    {/* <View style={styles.separator}></View>
                    <TouchableOpacity style={styles.menuItem}>
                        <Map style={styles.icMap} />
                        <Text style={styles.textMap}>{`MAPA DE\nPLANTÕES`}</Text>
                    </TouchableOpacity>
                </View> */}
                {loading && 
                    <View style={{flex: 1, justifyContent: "center", alignItems: "center"}}>
                        <ActivityIndicator size="large" color="#00467F" />
                    </View>
                }
                {!loading && shifts.length > 0 &&
                    <ScrollView showsVerticalScrollIndicator={false} style={{paddingTop: 10}}>
                        <View style={styles.pb}>
                            {shifts.map((shift, index) => (
                                <ShiftsDetails
                                    key={index}
                                    shift={shift}
                                    navigation={navigation}
                                />
                            ))}
                        </View>
                    </ScrollView>
                }

                <View style={styles.contentBottom}>
                    <View style={[mainStyles.container, styles.contentButtons]}>
                        <TouchableOpacity style={[mainStyles.btnBlueNew, styles.buttonLarge]} onPress={() => navigation.goBack()}>
                            <Text style={mainStyles.btnTextBlueNew}>VOLTAR</Text>
                        </TouchableOpacity>
                    </View>
                </View>

                {!loading && shifts.length  === 0 && !notLocation &&
                    <View>
                        <Text style={styles.notShifts}>Nenhum plantão disponível no momento.</Text>
                    </View>
                }

                {!loading && notLocation &&
                    <View>
                        <Text style={styles.notShifts}>Não foi possível obter sua localização.</Text>
                    </View>
                }
            </View>
        </View>
    );
}

const ShiftsDetails = ({shift, navigation}) => {
    const [showOptions, setShowOptions] = useState(false);
    return (
        <View style={styles.shift}>
            <TouchableOpacity style={styles.infos} onPress={() => setShowOptions(!showOptions)}>
                <View style={styles.bgIcon}>
                    <Plantoes style={{color: "#FFF", width: 35}} />
                </View>
                <View style={styles.devBox}>
                    <Text style={styles.devName}>{shift.imovel_nome} <Text style={styles.distance}>({shift.distancia})</Text></Text>
                    <Text style={styles.devAddress}>{`${shift.endereco.logradouro}, ${shift.endereco.numero}`}</Text>
                    {/* {shift.horarios_sorteio !== '' && shift.horarios_sorteio !== null &&
                        <>
                            <Text style={styles.drawTitle}>SORTEIO <Text style={styles.draw}>{shift.horarios_sorteio}</Text></Text>
                        </>
                    } */}
                </View>
                <View style={styles.distanceBox}>
                    {showOptions ? <Less /> : <More />}                                     
                </View>
            </TouchableOpacity>
            {showOptions &&
                <View style={[styles.actions]}>
                    <TouchableOpacity style={styles.btnActions} onPress={() => {
                        navigation.navigate('GoTo', {
                            name: shift.imovel_nome,
                            lat: shift.endereco.latitude,
                            long: shift.endereco.longitude,
                            label: `${shift.endereco.logradouro}, ${shift.endereco.numero}`,
                        });
                    }}>
                        <View style={styles.btnBorder}>
                            <Car style={{width: 42}} />
                        </View>
                        <Text style={styles.actionText}>{`Dirigir até lá`}</Text>
                        <View style={styles.icAbs}>
                            <Arrow style={styles.icArrow} />
                        </View>
                    </TouchableOpacity>                     
                    <TouchableOpacity style={styles.btnActions} onPress={() => Linking.openURL(shift.imovel_url) }>
                        <View style={styles.btnBorder}>
                            <About style={{width: 42}}/>
                        </View>
                        <Text style={styles.actionText}>{`Sobre o produto`}</Text>
                        <View style={styles.icAbs}>
                            <Arrow style={styles.icArrow} />
                        </View>
                    </TouchableOpacity>                                    
                    <TouchableOpacity style={styles.btnActions} onPress={() => navigation.navigate('PrivateContacts', { shift: shift })}>
                        <View style={styles.btnBorder}>
                            <Whats style={{width: 38}}/>
                        </View>
                        <Text style={styles.actionText}>{`Contatos`}</Text>
                        <View style={styles.icAbs}>
                            <Arrow style={styles.icArrow} />
                        </View>
                    </TouchableOpacity>                                        
                </View>
            }
            {/* {shifts.length !== (index+1) &&
                <Dash dashThickness={1} dashLength={3}  dashColor="#DADADA" />
            } */}
        </View>
    )
}

export default PrivateShifts;