import {Dimensions, Platform, StyleSheet} from 'react-native';

const windowWidth = Dimensions.get('window').width;

let iconWidth = windowWidth * 0.18;
let optionWidth = '25%';

const styles = StyleSheet.create({
  container: {
    overflow: 'visible',
    maxWidth: 360,
    marginLeft: (windowWidth - 360) / 2,
  },
  containerMarginBottom: {
    paddingBottom: 140
  },
  rowMargin: {
    paddingTop: 30,
  },
  title: {
    color: '#00467F',
    fontFamily: 'Ubuntu-Bold',
    fontSize: 26,
    lineHeight: 26,
    marginLeft: 20,
  },
  subtitle: {
    color: '#00467F',
    fontFamily: 'Ubuntu-Regular',
    fontSize: 26,
    lineHeight: 26,
    marginLeft: 20,
  },
  rowOptionsLeft: {
    justifyContent: 'flex-start',
  },
  rowActiveCheckin: {
    paddingBottom: 140,
  },
  rowOptions: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'flex-start',
    paddingHorizontal: 10 
  },
  contentOption: {
    alignItems: 'center',
    zIndex: 1,
    width: optionWidth
  },
  option: {
    alignItems: 'center',
    backgroundColor: '#F2F2F2',
    alignItems: 'center',
    justifyContent: 'center',
    width: iconWidth,
    height: iconWidth,
    position: 'relative',
  },
  optionFeatured: {
    backgroundColor: '#90B0C0',
  },
  tagAb: {
    position: 'absolute',
    top: -4,
    right: 2,
    zIndex: 9,
  },
  tagCount: {
    width: 22,
    height: 22,
    borderRadius: 25,
    backgroundColor: '#FF6542',
    position: 'absolute',
    top: -10,
    right: -8,
    zIndex: 9,
    alignItems: 'center',
    justifyContent: 'center',
  },
  tagCountNumber: {
    fontFamily: 'Ubuntu-Regular',
    fontSize: 12,
    color: '#fff',
  },
  optionDisabled: {
    borderColor: '#979797',
    width: 72,
    height: 72,
  },
  optionText: {
    fontFamily: 'Ubuntu-Regular',
    color: '#828282',
    marginTop: Platform.OS === 'ios' ? 12 : 8,
    marginBottom: Platform.OS === 'ios' ? 17 : 13,
    fontSize: Platform.OS === 'ios' ? 13 : 13,
    letterSpacing: 0.3,
    textAlign: 'center',
    width: Platform.OS === 'ios' ? 91 : 91,
    lineHeight: 13,
  },
  optionDisabledText: {
    fontFamily: 'Ubuntu-Regular',
    color: '#828282',
    marginTop: Platform.OS === 'ios' ? 16 : 12,
    fontSize: Platform.OS === 'ios' ? 18 : 16,
  },
  boxOptionTitle: {
    position: 'relative',
  },
  dotActive: {
    position: 'absolute',
    width: 14,
    height: 14,
    borderRadius: 14,
    backgroundColor: '#1B9C20',
    left: 15,
    top: 8,
  },
  alertInfo: {
    zIndex: 99,
    position: 'absolute',
    bottom: 0,
    left: 0,
    right: 0,
    width: '100%',
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    borderRadius: 0,
    paddingTop: Platform.OS === 'ios' ? 25 : 15,
    paddingBottom: Platform.OS === 'ios' ? 25 : 15,
    paddingLeft: 25,
    paddingRight: 25,
  },
  bgLightBlue: {
    //backgroundColor: "#4EA1CC"
    backgroundColor: '#90B0C0',
  },
  bgRed: {
    backgroundColor: '#FF6542',
  },
  bgDarkBlue: {
    backgroundColor: '#00467F',
  },
  bgGreen: {
    backgroundColor: '#60BA64',
  },
  alertInfoTextBox: {
    flex: 1,
  },
  alertInfoFirstText: {
    color: '#FFF',
    fontFamily: 'Ubuntu-Light',
    letterSpacing: 1,
    fontSize: 13,
  },
  alertInfoSecondText: {
    color: '#FFF',
    fontFamily: 'Ubuntu-Bold',
    letterSpacing: 1,
    fontSize: 18,
  },
  alertInfoPosition: {
    color: '#FFF',
    fontFamily: 'Ubuntu-Bold',
    letterSpacing: 1,
    fontSize: 48,
  },
  alertInfoIcArrow: {
    width: 10,
    marginLeft: 15,
  },
  alertInfoIcMegaphone: {
    width: 80,
  },
  alertInfoIcLock: {
    width: 40,
    marginLeft: 15,
  },
  boxWebviewHeader: {
    borderTopColor: 'rgba(0, 70, 127, 0.3)',
    borderTopWidth: 1,
    paddingTop: 15,
    marginBottom: 0,
  },
  boxWebviewHeaderText: {
    color: '#00467F',
    letterSpacing: 1,
    fontFamily: 'Ubuntu-Regular',
    textAlign: 'center',
    fontSize: 16,
    marginBottom: 5,
  },
  tag: {
    backgroundColor: '#828282',
    height: 20,
    width: 50,
    alignItems: 'center',
    justifyContent: 'center',
    // borderRadius: 12,
    position: 'absolute',
    top: -12,
    zIndex: 9,
  },
  textTag: {
    color: '#FFF',
    letterSpacing: 0.5,
    fontFamily: 'Ubuntu-Regular',
    textAlign: 'center',
    fontSize: 10.2,
  },
  tagNew: {
    // backgroundColor: "#00467F",
    backgroundColor: '#FF6542',
    top: -10,
    width: 43,
  },
  tagNewGreen: {
    // backgroundColor: "#00467F",
    backgroundColor: '#1B9C20',
    top: -10,
    width: 43,
  },
  viewMarginTop: {
    marginTop: 30,
    marginBottom: 30,
  },
  imgCopaCury: {
    width: 50,
    height: 50,
    resizeMode: 'contain',
  },
  imgSelo: {
    width: 50,
    height: 50,
    resizeMode: 'contain',
  },
  icPlacePresenca: {
    marginLeft: 15,
    color: "#FFF"
  }
});

export default styles;

// import { StyleSheet, Dimensions, Platform } from 'react-native';

// const windowWidth = Dimensions.get('window').width;
// const windowHeight = Dimensions.get('window').height;

// const styles = StyleSheet.create({
//     container: {
//         overflow: "visible",
//         maxWidth: 360,
//         marginLeft: ((windowWidth - 360) /2),
//     },
//     rowMargin: {
//         paddingTop: windowHeight > 700 ? 30 : 30
//     },
//     // containerMarginBottom: {
//     //     marginBottom: 140
//     // },
//     title: {
//         color: '#00467F',
//         fontFamily: 'Ubuntu-Bold',
//         fontSize: 26,
//         lineHeight: 26,
//         marginLeft: 20
//     },
//     subtitle: {
//         color: '#00467F',
//         fontFamily: 'Ubuntu-Regular',
//         fontSize: 26,
//         lineHeight: 26,
//         marginLeft: 20
//     },
//     rowOptions: {
//         flexDirection: "row",
//         justifyContent: "center",
//         flexWrap: 'wrap',
//     },
//     rowOptionsLeft: {
//         justifyContent: "flex-start"
//     },
//     rowActiveCheckin: {
//         paddingBottom: 140
//     },
//     option: {
//         alignItems: 'center',
//         padding: 15,
//         backgroundColor: "#FFF",
//         alignItems: 'center',
//         justifyContent: 'center',
//         borderRadius: 12,
//         borderWidth: 2,
//         borderColor: "#2D719F",
//         marginTop: 10,
//         marginLeft: 5,
//         marginRight: 5,
//         width: 72,
//         height: 72,
//         position: "relative"
//     },
//     optionFeatured: {
//         backgroundColor: "#90B0C0"
//     },
//     tagAb: {
//         position: "absolute",
//         top: -8,
//         right: -8,
//         zIndex: 9
//     },
//     tagCount: {
//         width: 25,
//         height: 25,
//         borderRadius: 25,
//         backgroundColor: "#FF6542",
//         position: "absolute",
//         top: -14,
//         right: -12,
//         zIndex: 9,
//         alignItems: "center",
//         justifyContent: "center"
//     },
//     tagCountNumber: {
//         fontFamily: "Roboto-Regular",
//         fontSize: 12,
//         color: "#fff"
//     },
//     optionDisabled: {
//         borderColor: "#979797",
//         width: 72,
//         height: 72
//     },
//     contentOption: {
//         alignItems: "center",
//         zIndex: 1,
//         width: '25%'
//     },
//     optionText: {
//         fontFamily: "Roboto-Regular",
//         color: "#979797",
//         marginTop: Platform.OS === 'ios' ? 12 : 8,
//         marginBottom: Platform.OS === 'ios' ? 17 : 13,
//         fontSize: Platform.OS === 'ios' ? 13 : 13,
//         letterSpacing: 0.3,
//         textAlign: "center",
//         width: Platform.OS === 'ios' ? 91 : 91,
//         lineHeight: 13
//     },
//     optionDisabledText: {
//         fontFamily: "Roboto-Regular",
//         color: "#979797",
//         marginTop: Platform.OS === 'ios' ? 16 : 12,
//         fontSize: Platform.OS === 'ios' ? 18 : 16
//     },
//     boxOptionTitle: {
//         position: "relative"
//     },
//     dotActive: {
//         position: "absolute",
//         width: 14,
//         height: 14,
//         borderRadius: 14,
//         backgroundColor: "#1B9C20",
//         left: 15,
//         top: 8
//     },
//     alertInfo: {
//         zIndex: 99,
//         position: "absolute",
//         //bottom: windowWidth * 0.075,
//         bottom: Platform.OS === 'ios' ? 0 : 24,
//         //left: windowWidth * 0.075,
//         left: 0,
//         //right: windowWidth * 0.75,
//         right: 0,
//         //width: windowWidth * 0.85,
//         width: '100%',
//         flexDirection: "row",
//         justifyContent: 'space-between',
//         alignItems: "center",
//         //borderRadius: 15,
//         borderRadius: 0,
//         paddingTop:  Platform.OS === 'ios' ? 25 : 15,
//         paddingBottom: Platform.OS === 'ios' ? 25 : 15,
//         paddingLeft: 25,
//         paddingRight: 25
//     },
//     bgLightBlue: {
//         //backgroundColor: "#4EA1CC"
//         backgroundColor: "#90B0C0"
//     },
//     bgRed: {
//         backgroundColor: "#FF6542B2"
//     },
//     bgDarkBlue: {
//         backgroundColor: "#00467F"
//     },
//     bgGreen: {
//         backgroundColor: "#60BA64D3"
//     },
//     alertInfoTextBox: {
//         flex: 1
//     },
//     alertInfoFirstText: {
//         color: "#FFF",
//         fontFamily: "Roboto-Light",
//         letterSpacing: 1,
//         fontSize: 13
//     },
//     alertInfoSecondText: {
//         color: "#FFF",
//         fontFamily: "Roboto-Bold",
//         letterSpacing: 1,
//         fontSize: 18
//     },
//     alertInfoPosition: {
//         color: "#FFF",
//         fontFamily: "Roboto-Bold",
//         letterSpacing: 1,
//         fontSize: 48
//     },
//     alertInfoIcArrow: {
//         width: 10,
//         marginLeft: 15
//     },
//     alertInfoIcMegaphone: {
//         width: 80,
//     },
//     alertInfoIcLock: {
//         width: 40,
//         marginLeft: 15
//     },
//     boxWebviewHeader: {
//         borderTopColor: "rgba(0, 70, 127, 0.3)",
//         borderTopWidth: 1,
//         paddingTop: 15,
//         marginBottom: 0
//     },
//     boxWebviewHeaderText: {
//         color: "#00467F",
//         letterSpacing: 1,
//         fontFamily: "Roboto-Regular",
//         textAlign: "center",
//         fontSize: 16,
//         marginBottom: 5
//     },
//     tag: {
//         backgroundColor: "#828282",
//         height: 20,
//         width: 50,
//         alignItems: "center",
//         justifyContent: "center",
//         borderRadius: 12,
//         position: "absolute",
//         top: -12,
//         zIndex: 9
//     },
//     textTag: {
//         color: "#FFF",
//         letterSpacing: 0.5,
//         fontFamily: "Roboto-Regular",
//         textAlign: "center",
//         fontSize: 10.2
//     },
//     tagNew: {
//         backgroundColor: "#00467F",
//         top: -12,
//         width: 43
//     },
//     viewMarginTop: {
//         marginTop: 30,
//         marginBottom: 30
//     },
//     imgCopaCury: {
//         width: 50,
//         height: 50,
//         resizeMode: 'contain'
//     },
//     imgSelo: {
//         width: 50,
//         height: 50,
//         resizeMode: 'contain'
//     }
// });

// export default styles;
