import React, { useEffect, useState } from 'react';
import {
  ActivityIndicator,
  Alert,
  Linking,
  ScrollView,
  Text,
  TouchableOpacity,
  View,
} from 'react-native';

import GetLocation from 'react-native-get-location';

import Calendar from '../../../assets/svgs/Calendar';
import MegaPhone from '../../../assets/svgs/MegaPhone';

import Tag from '../../../assets/svgs/Tag';

import AlertFooterCheckin from '../../../components/AlertFooterCheckin';
import AlertFooterUnCheckin from '../../../components/AlertFooterUnCheckin';
import LightboxWhats from '../../../components/LightboxWhats';
import PrivateHeader from '../../../components/headers/PrivateHeader';

import { useService } from '../../../context/service';

import mainStyles from '../../../mainStyles';
import styles from './styles';

import { useIsFocused } from '@react-navigation/native';

import { useAuth } from '../../../context/auth';
import { useWoxAi } from '../../../context/woxAi';
import api from '../../../services/api';

import Carousel from '../../../components/Carousel';
import IconRenderer from '../../../components/IconRenderer';
import Place from '../../../assets/svgs/Place';

const AlertInfo = () => {
  const { confirmarPresenca, setTriggerConfirmarPresenca } = useAuth();
  const { queue, redirectTo, activeCheckin } = useService();

  const [showConfirmarPresencaAlert, setShowConfirmarPresencaAlert] = useState(false);
  
  useEffect(() => {
    setShowConfirmarPresencaAlert(confirmarPresenca);
  }, [confirmarPresenca]);

  if(!activeCheckin && !showConfirmarPresencaAlert) return null;

  if(!queue.atendimento_ativo && !queue.atendimento_disponivel && showConfirmarPresencaAlert){
    return (
      <TouchableOpacity
        style={[styles.alertInfo, styles.bgRed, { paddingBottom: 33, paddingTop: 33 }]}
        onPress={() => setTriggerConfirmarPresenca(prev => !prev)}
      >
        <View style={styles.alertInfoTextBox}>
          <Text style={styles.alertInfoFirstText}>{`Fila ${queue.nome}`}</Text>
          <Text style={styles.alertInfoSecondText}>
            Confirmar presença
          </Text>
        </View>
        <Place style={styles.icPlacePresenca} />
      </TouchableOpacity>
    )
  }

  // Fique atento: posição maior que 3
  if (
    !queue.atendimento_ativo &&
    !queue.atendimento_disponivel &&
    queue.posicao > 3 &&
    queue.tipo_atendimento !== 'extra'
  ) {
    return (
      <TouchableOpacity
        style={[styles.alertInfo, styles.bgLightBlue]}
        onPress={() => redirectTo(true)}>
        <View style={styles.alertInfoTextBox}>
          <Text style={styles.alertInfoFirstText}>{`Fila ${queue.nome}`}</Text>
          <Text style={styles.alertInfoSecondText}>
            {queue.status_atendimento}
          </Text>
        </View>
        <Text style={styles.alertInfoPosition}>
          {queue.posicao < 10 ? `0${queue.posicao}` : queue.posicao}
        </Text>
        {/* <ArrowRight style={styles.alertInfoIcArrow} /> */}
      </TouchableOpacity>
    );
  }

  // Fique atento: posição menor que 4
  if (
    !queue.atendimento_ativo &&
    !queue.atendimento_disponivel &&
    queue.posicao < 4 &&
    queue.tipo_atendimento !== 'extra'
  ) {
    return (
      <TouchableOpacity
        style={[styles.alertInfo, styles.bgRed]}
        onPress={() => redirectTo(true)}>
        <View style={styles.alertInfoTextBox}>
          <Text style={styles.alertInfoFirstText}>{`Fila ${queue.nome}`}</Text>
          <Text style={styles.alertInfoSecondText}>
            {queue.status_atendimento}
          </Text>
        </View>
        <Text style={styles.alertInfoPosition}>
          {queue.posicao < 10 ? `0${queue.posicao}` : queue.posicao}
        </Text>
        {/* <ArrowRight style={styles.alertInfoIcArrow} /> */}
      </TouchableOpacity>
    );
  }

  // Fique atento: posição menor que 4
  if (
    !queue.atendimento_ativo &&
    !queue.atendimento_disponivel &&
    queue.posicao < 4 &&
    queue.tipo_atendimento !== 'extra'
  ) {
    return (
      <TouchableOpacity
        style={[styles.alertInfo, styles.bgRed]}
        onPress={() => redirectTo(true)}>
        <View style={styles.alertInfoTextBox}>
          <Text style={styles.alertInfoFirstText}>{`Fila ${queue.nome}`}</Text>
          <Text style={styles.alertInfoSecondText}>
            {queue.status_atendimento}
          </Text>
        </View>
        <Text style={styles.alertInfoPosition}>
          {queue.posicao < 10 ? `0${queue.posicao}` : queue.posicao}
        </Text>
        {/* <ArrowRight style={styles.alertInfoIcArrow} /> */}
      </TouchableOpacity>
    );
  }

  // Atendimento extra: disponível
  if (
    (queue.atendimento_ativo || queue.atendimento_disponivel) &&
    queue.tipo_atendimento === 'extra'
  ) {
    return (
      <TouchableOpacity
        style={[styles.alertInfo, styles.bgGreen]}
        onPress={() => redirectTo(true)}>
        <View style={styles.alertInfoTextBox}>
          <Text style={styles.alertInfoFirstText}>{`Fila ${queue.nome}`}</Text>
          <Text style={styles.alertInfoSecondText}>Atendimento extra</Text>
        </View>
        <MegaPhone style={styles.alertInfoIcMegaphone} />
        {/* <ArrowRight style={styles.alertInfoIcArrow} /> */}
      </TouchableOpacity>
    );
  }

  // Atendimento extra: aguardando
  if (
    !queue.atendimento_ativo &&
    !queue.atendimento_disponivel &&
    queue.tipo_atendimento === 'extra'
  ) {
    return (
      <TouchableOpacity
        style={[styles.alertInfo, styles.bgLightBlue]}
        onPress={() => redirectTo(true)}>
        <View style={styles.alertInfoTextBox}>
          <Text style={styles.alertInfoFirstText}>{`Fila ${queue.nome}`}</Text>
          <Text style={styles.alertInfoSecondText}>
            {'Disponível apenas\npara atendimento extra'}
          </Text>
        </View>
        <Calendar style={styles.alertInfoIcLock} />
      </TouchableOpacity>
    );
  }

  // Atendimento disponível: chegou a sua vez
  if (
    (queue.atendimento_ativo || queue.atendimento_disponivel) &&
    queue.tipo_atendimento !== 'extra'
  ) {
    return (
      <TouchableOpacity
        style={[styles.alertInfo, styles.bgGreen]}
        onPress={() => redirectTo(true)}>
        <View style={styles.alertInfoTextBox}>
          <Text style={styles.alertInfoFirstText}>{`Fila ${queue.nome}`}</Text>
          <Text style={styles.alertInfoSecondText}>
            {queue.status_atendimento}
          </Text>
        </View>
        <MegaPhone style={styles.alertInfoIcMegaphone} />
        {/* <ArrowRight style={styles.alertInfoIcArrow} /> */}
      </TouchableOpacity>
    );
  }

  return (
    <View
      style={{
        flex: 1,
        justifyContent: 'center',
        alignItems: 'center',
        paddingBottom: 50,
      }}>
      <ActivityIndicator size="large" color="#00467F" />
    </View>
  );
};

const PrivateMain = ({ route, navigation }) => {
  const [loading, setLoading] = useState(false);
  const [confirmCheckin, setConfirmCheckin] = useState(false);
  const [productCheckin, setProductCheckin] = useState('');
  const [latitude, setLatitude] = useState('');
  const [longitude, setLongitude] = useState('');
  const [plantaoId, setPlantaoId] = useState('');
  const [alertUnCheckin, setAlertUnCheckin] = useState(false);
  const [messageCheckinError, setMessageCheckinError] = useState('');
  const [lightboxWhats, setLightboxWhats] = useState(false);
  const isFocused = useIsFocused();
  const { crtl } = useWoxAi();
  const { checkin, redirectTo, activeCheckin } = useService();
  const { user, getUser, home, setTriggerConfirmarPresenca } = useAuth();

  useEffect(() => {
    if (crtl) {
      crtl.abort();
    }

    if (isFocused) {
      // getUser();
      setTriggerConfirmarPresenca(prev => !prev);
    }
  }, [isFocused]);


  useEffect(() => {
    const area = route?.params?.area ?? null;
    switch (area) {
      case 'suporte':
        setTimeout(() => {
          setLightboxWhats(true);
        }, 250);
        break;
    }
  }, [route]);

  const handleConfirmCheckin = async () => {
    if (activeCheckin) {
      redirectTo(true);
    } else {
      setLoading(true);

      const geolocation = await GetLocation.getCurrentPosition({
        enableHighAccuracy: true,
        timeout: 15000,
      })
        .then(location => {
          return location;
        })
        .catch(error => {
          console.log(error);
          return false;
        });

      if (geolocation) {
        let long = geolocation.longitude;
        let lat = geolocation.latitude;

        setLatitude(lat);
        setLongitude(long);

        let data = new FormData();
        data.append('latitude', lat);
        data.append('longitude', long);

        api
          .post('/checkin/verificar', data)
          .then(res => {
            setConfirmCheckin(true);
            setProductCheckin(res.data.plantao.imovel_nome);
            setPlantaoId(res.data.plantao.id);
            setLoading(false);
          })
          .catch(err => {
            console.log(err.response);
            if (err.response.status === 422) {
              setAlertUnCheckin(true);
              setMessageCheckinError(err.response.data.message);
              setLoading(false);
            }
          });
      } else {
        Alert.alert(
          'Erro na localização',
          'Não foi possível obter sua localização',
        );
        setLoading(false);
      }
    }
  };

  const handleCheckin = async () => {
    setLoading(true);
    setConfirmCheckin(false);

    const result = await checkin(latitude, longitude, plantaoId);

    if (result) {
      redirectTo(true);
      setLoading(false);
    } else {
      Alert.alert('Erro', 'Não foi possível realizar checkin');
      setLoading(false);
    }
  };

  const openHypnobox = () => {
    if (user.regional === 'Rio de Janeiro') {
      Linking.openURL('https://curyconstrutorarj.hypnobox.com.br/');
    } else {
      Linking.openURL('https://curyconstrutora.hypnobox.com.br/');
    }
  };

  const openCopaCury = () => {
    if (user.regional === 'Rio de Janeiro') {
      Linking.openURL('https://cury.net/copa-cury-rj');
    } else {
      Linking.openURL('https://cury.net/copa-cury');
    }
  };

  const openSalesForce = () => {
    Linking.openURL(
      'https://curyconstrutora.my.salesforce.com',
    );
  };

  const openHub = () => {
    Linking.openURL('https://curyhub.com.br/');
  };

  const openCampaign = () => {
    if (user.regional === 'Rio de Janeiro') {
      Linking.openURL('https://cury.net/cury-60-anos-rj');
    } else {
      Linking.openURL('https://cury.net/cury-60-anos-sp');
    }
  };

  const openStudioC = () => {
    if (user.regional === 'Rio de Janeiro') {
      Linking.openURL('https://calendar.google.com/calendar/appointments/schedules/AcZssZ3iAhRNABFbPPbLNpipMCmHWcD-Lk5Wec0dDusMdgEfjAytcBhc19yy13vZqzO9dohKO3wJKjm6');
    } else {
      Linking.openURL('https://calendar.google.com/calendar/appointments/schedules/AcZssZ1bmA4p8IFs1BhvePrVE998-aE9PoGGMfPV6Fz0VvflIjdSnkltfVTxbVGL3Wf1ZvmUbRAocjuH');
    }
  };

  const openCreditoCury = () => {
    Linking.openURL('https://wa.me/5511930464819');
  };

  const openJogosCury = () => {
    Linking.openURL(
      'https://app.powerbi.com/view?r=eyJrIjoiZWU2YzA3MGYtYzcxNS00YThlLWEyZmMtM2EyNGNkMzZjMTlhIiwidCI6IjEyNzA1NGM3LWRiNDYtNDIzYS05YTZlLTQwZTBhZWMyNjFhYSJ9',
    );
  };

  return (
    <View style={mainStyles.wrapper}>
      {confirmCheckin && (
        <AlertFooterCheckin
          text={
            <Text>
              {`Você deseja realmente\nfazer logout?`}
            </Text>
          }
          product={productCheckin}
          btnBorder={true}
          close={() => setConfirmCheckin(false)}
          action={() => {
            handleCheckin();
          }}
        />
      )}
      {alertUnCheckin && (
        <AlertFooterUnCheckin
          close={() => setAlertUnCheckin(false)}
          message={messageCheckinError}
        />
      )}
      <PrivateHeader title={'Home'} />
      {loading && (
        <View style={{ flex: 1, justifyContent: 'center', alignItems: 'center' }}>
          <ActivityIndicator size="large" color="#00467F" />
        </View>
      )}

      {!loading && (
        <ScrollView
          showsVerticalScrollIndicator={false}>
          <Carousel />
          <View style={[styles.rowOptions, styles.containerMarginBottom]}>
            {home && home.home.icones.map((icone, index) => (
              <View style={styles.contentOption} key={index}>
                <TouchableOpacity
                  style={[
                    styles.option,
                    icone.titulo === 'Visita Virtual' && user.leads_novos > 0 ? styles.optionFeatured : null
                  ]}
                  onPress={() =>
                    icone.titulo === 'Check in'
                      ? handleConfirmCheckin()
                      : Linking.openURL(icone.url)
                  }>
                  {icone.tagNovo == true &&
                    <View style={[styles.tag, styles.tagNewGreen]}>
                      <Text style={styles.textTag}>NOVO</Text>
                    </View>
                  }
                  {icone.titulo == 'Visita Virtual' &&
                    <>
                      {
                        user.leads_novos > 0 && (
                          <View style={styles.tagCount}>
                            <Text style={styles.tagCountNumber}>
                              {user.leads_novos}
                            </Text>
                          </View>
                        )
                      }
                    </>
                  }
                  {
                    icone.titulo == 'Repasse' &&
                    <>
                      {user?.ultima_versao_repasses === null && (
                        <View style={styles.tagAb}>
                          <Tag />
                        </View>
                      )}

                    </>
                  }
                  {
                    icone.titulo == 'Pagadoria' &&
                    <>
                      {user?.ultima_versao_pagadoria === null && (
                        <View style={styles.tagAb}>
                          <Tag />
                        </View>
                      )}

                    </>
                  }
                  <View style={styles.optionIconBg}>
                      <IconRenderer
                        imagem_url={icone.imagem_url}
                        fill={user.leads_novos > 0 ? true : false}
                        titulo={icone.titulo}
                      />
                  </View>
                </TouchableOpacity>
                <View style={styles.boxOptionTitle}>
                  <Text style={styles.optionText}>
                    {icone.titulo == 'Check in' &&
                      <>
                        {activeCheckin ? 'Fila' : 'Check in'}
                      </>
                    }
                    {icone.titulo !== 'Check in' &&
                      <Text style={styles.optionText}>{icone.titulo}</Text>
                    }
                  </Text>
                  {icone.titulo == 'Check in' &&
                    <>
                      {activeCheckin && <View style={styles.dotActive} />}
                    </>
                  }
                </View>
              </View>
            ))}
          </View>
        </ScrollView>
      )}
      <AlertInfo />
      {lightboxWhats && <LightboxWhats close={() => setLightboxWhats(false)} />}
    </View>
  );
};

export default PrivateMain;