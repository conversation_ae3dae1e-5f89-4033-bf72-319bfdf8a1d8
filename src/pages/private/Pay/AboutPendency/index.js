import React, { useEffect, useState } from 'react';
import { TextInput, Text, View, TouchableOpacity, ActivityIndicator, useWindowDimensions, Alert, Image } from 'react-native';
import { useIsFocused, useNavigation } from '@react-navigation/native';
import moment from 'moment';
import Alert2 from '../../../../assets/svgs/Alert3';
import Like from '../../../../assets/svgs/Like';
import Loading from '../../../../assets/svgs/Loading2';
import Check2 from '../../../../assets/svgs/Check3';
import Search2 from '../../../../assets/svgs/Search2';
import Send from '../../../../assets/svgs/Send';
import Arrow from '../../../../assets/svgs/ArrowRightGray';
import ArrowLeft from '../../../../assets/svgs/ArrowLeftSmallWhite';

import PrivateHeader from '../../../../components/headers/PrivateHeader';

import styles from './styles';
import mainStyles from '../../../../mainStyles';
import { ScrollView } from 'react-native-gesture-handler';
import api from '../../../../services/api';
import { useAuth } from '../../../../context/auth';
import { color } from 'react-native/Libraries/Components/View/ReactNativeStyleAttributes';
import RenderHtml from 'react-native-render-html';

const AboutPendency = ({ navigation, route }) => {
    const { pv } = route.params;
    const isFocused = useIsFocused();

    const contentWidth = useWindowDimensions().width;

    const [loading, setLoading] = useState(true);
    const [processo, setProcesso] = useState(null);

    const [title, setTitle] = useState('');
    const [icon, setIcon] = useState('');
    const [color, setColor] = useState('#FFF');

    useEffect(() => {
        if (isFocused) getProcesso();

        return () => {
            setProcesso(null);
        }
    }, [isFocused]);

    const getProcesso = () => {
        setLoading(true);

        const pvArr = pv.split('-');
        const convertedPv = pvArr.length > 1 ? pvArr[1] : pv;

        api.get(`pagadoria/pv/${convertedPv}`).then(res => {
            setProcesso(res.data.processo);

            const items = [
                {
                    status: 'processos-com-pendencia',
                    title: 'Processo com pendência',
                    color: '#FF6542',
                    icon: <Alert2 />
                },
                {
                    status: 'processos-com-pendencias',
                    title: 'Processo com pendência',
                    color: '#FF6542',
                    icon: <Alert2 />
                },
                {
                    status: 'processo-pagamento',
                    title: 'Em processo de pagamento',
                    color: '#4EA1CC',
                    icon: <Loading />
                },
                {
                    status: 'em-processo-de-pagamento',
                    title: 'Em processo de pagamento',
                    color: '#4EA1CC',
                    icon: <Loading />
                },
                {
                    status: 'pronto-envio-adm',
                    title: 'Pronto para enviar',
                    color: '#4EA1CC',
                    icon: <Send />
                },
                {
                    status: 'pronto-para-enviar',
                    title: 'Pronto para enviar',
                    color: '#4EA1CC',
                    icon: <Send />
                },
                {
                    status: 'comissoes-pagas',
                    title: 'Comissão paga',
                    color: '#1B9C20',
                    icon: <Check2 />
                },
                {
                    status: 'em-tratamento',
                    title: 'Em tratamento',
                    color: '#666666',
                    icon: <Alert2 />
                },
                {
                    status: 'novas-vendas',
                    title: 'Em tratamento',
                    color: '#666666',
                    icon: <Alert2 />
                }
            ];

            const item = items.filter(i => i.status === res.data.processo.processo.status)[0];

            setTitle(item.title);
            setColor(item.color);
            setIcon(item.icon);

        }).catch(error => {
            console.log(error);
        }).then(() => setLoading(false));
    }

    return (
        <View style={mainStyles.wrapper}>
            <PrivateHeader title={`Pagadoria`} />
            {loading &&
                <View style={{ flex: 1, justifyContent: "center", alignItems: "center" }}>
                    <ActivityIndicator size="large" color="#00467F" />
                </View>
            }
            {!loading &&
                <>
                    <TouchableOpacity style={styles.buttonDate} onPress={() => navigation.navigate('PaySearch')}>
                        <View style={styles.icArrow}>
                            <ArrowLeft />
                        </View>
                        <Text style={styles.btnTextButtonDate}>{pv.toUpperCase()}</Text>
                    </TouchableOpacity>
                    <ScrollView showsVerticalScrollIndicator={false}>
                        <View style={mainStyles.container}>
                            <View style={styles.borderTop}>
                                {/* <Text style={styles.textUpdate}>ATUALIZADO EM: {moment(processo?.atualizado_em).format('DD/MM/YYYY')}</Text> */}
                                <View>
                                    <Text style={styles.textStatus}>Status</Text>
                                    <View style={styles.boxBoxShadow}>
                                        <View style={styles.boxIconWidth}>
                                            {icon}
                                        </View>
                                        <Text style={[styles.textResult, { color }]}>{title}</Text>
                                    </View>
                                    <Text style={styles.datePay}>{processo?.titulo}</Text>
                                    <Text style={styles.updatePendency}>{processo?.subtitulo}</Text>
                                </View>
                            </View>
                            {processo?.texto &&
                                <RenderHtml
                                    source={{ html: processo.texto }}
                                    contentWidth={contentWidth}
                                />
                            }
                        </View>
                    </ScrollView>
                    <View style={styles.contentBottom}>
                        <View style={[mainStyles.container, styles.contentButtons]}>
                            <TouchableOpacity
                                style={[mainStyles.btnBlueNew, styles.buttonLarge]}
                                onPress={() => navigation.goBack()}>
                                <Text style={mainStyles.btnTextBlueNew}>VOLTAR</Text>
                            </TouchableOpacity>
                        </View>
                    </View>
                </>
            }
        </View>
    );
}

export default AboutPendency;