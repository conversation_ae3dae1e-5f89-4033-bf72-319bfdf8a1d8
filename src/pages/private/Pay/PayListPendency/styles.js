import { StyleSheet, Dimensions, Platform } from 'react-native';

const windowWidth = Dimensions.get('window').width;

const styles = StyleSheet.create({
    wrapper: {
        justifyContent: 'space-between',
    },
    contentBottom: {
      paddingTop: 20,
      paddingBottom: 40,
      shadowRadius: Platform.OS === 'ios' ? 2 : 16,
      shadowOffset: {
        width: 0,
        height: Platform.OS === 'ios' ? -2 : 12,
      },
      shadowColor: Platform.OS === 'ios' ? '#CCC' : '#000',
      elevation: Platform.OS === 'ios' ? 2 : 24,
      shadowOpacity: 1,
      backgroundColor: '#FFF',
    },
    contentButtons: {
      flexDirection: 'row',
      justifyContent: 'space-between',
    },
    button: {
      width: '100%',
    },
    buttonLarge: {
      width: '100%',
    },
    container: {
        maxWidth: 340,
        marginLeft: ((windowWidth - 340) /2),
        marginTop: 10
    },
    contentOption: {
        flexDirection: "row",
        alignItems: "center",
        marginTop: 25,
        marginBottom: 25
    },
    textStatus: {
        fontFamily: "Ubuntu-Regular",
        color: "#00467F",
        fontSize: 15,
        letterSpacing: 1,
    }, 
    textStatusBold: {
        fontFamily: "Roboto-Bold",
        color: "#00467F",
        fontSize: 15,
        letterSpacing: 1,
        marginLeft: 15
    },
    inputText: {
        backgroundColor: "#FFF",
        width: 125,
        paddingLeft: 13,
    },
    buttonCenterBlue: {
        width: 70,
        height: 50,
        marginTop: 10,
        borderRadius: 5,
        marginLeft: 15
    },
    textBtnCenterBlue: {
        fontSize: 20
    }, 
    rowCenter: {
        flexDirection: "row",
        justifyContent: "space-between",
        alignItems: 'center',
        marginBottom: 15,
        marginTop: 10
    },
    rowCenterMargin: {
        flexDirection: "row",
        alignItems: 'center',
        marginVertical: 20
    },
    optionTextBold: {
        fontFamily: "Roboto-Bold",
        fontSize: 12,
        letterSpacing: 0.7,
        color: "#4ea1cc",
        lineHeight: 18
    },
    boxFlex: {
        flexDirection: "column",
        justifyContent: "space-between",
        marginBottom: 10
    },
    boxFlexColumn: {
        flexDirection: "column"
    },
    boxTitle: {
        flexDirection: "row",
        justifyContent: "space-between",
        alignItems: "center",
        paddingLeft: 10,
        width: "80%"
    },
    boxIconWidth: {
        width: 60, 
        minHeight: 65,    
        flexDirection: "row",
        justifyContent: "center",
        alignItems: "center",
        backgroundColor: "#90B0C0"
    },
    boxFlexWithText: {
        flexDirection: "row",
        alignItems: "center",
        backgroundColor: "#F2F2F2"
    },
    textBoxFlex: {
        fontFamily: "Ubuntu-Regular",
        fontSize: 13,
        letterSpacing: 1,
        width: "100%"
    },
    textBoxFlexBold: {
        fontFamily: "Ubuntu-Regular",
        color: "#00467F",
        fontSize: 20,
        letterSpacing: 1,
    },
    textAboutProcessBold: {
        fontFamily: "Ubuntu-Regular",
        color: "#00467F",
        fontSize: 12,
        letterSpacing: 0.5,
        marginTop: 3,
        marginBottom: 3,
        marginLeft: 10
    },
    textAboutProcess: {
        fontFamily: "Ubuntu-Regular",
        color: "#828282",
        fontSize: 12,
        letterSpacing: 0.5,
        marginLeft: 5
    },
    btnTextOutlineBlue: {
        fontFamily: "Roboto-Bold",
        color: "#00467F",
        fontSize: 25,
        fontWeight: "bold",
        letterSpacing: 1,
        textAlign: "center"
    },
    textSearch: {
        fontFamily: "Roboto-Regular",
        color: "#4EA1CC",
        fontSize: 17,
        letterSpacing: 1,
    },
    searchBox: {
        flexDirection: "row",
        justifyContent: "center",
        borderColor: "#DADADA",
        borderBottomWidth: 1,
        paddingBottom: 30
    },
    input: {
        borderWidth: 2,
        borderColor: "#4EA1CC",
        borderRadius: 5,
        width: 150,
        height: 55,
        paddingLeft: 15,
        paddingRight: 15,
        fontSize: 25,
        textAlign: "center"
    },
    btnOk: {
        backgroundColor: "#4EA1CC",
        height: 55,
        width: 55,
        justifyContent: "center",
        alignItems: "center",
        borderRadius: 5,
        marginLeft: 10
    },
    resultPV: {
        borderLeftColor: "#F2F2F2",
        borderLeftWidth: 15,
        paddingTop: 5,
        paddingBottom: 5,
        paddingRight: 5,
        marginTop: 10,
        flexDirection: "row",
        alignItems: "center",
        justifyContent: "space-between"
    },
    divider: {
        height: 1,
        backgroundColor: "#DADADA",
        marginTop: 15,
        marginBottom: 10
    }
});

export default styles;