import React, { useEffect, useState } from 'react';
import { TextInput, Text, View, TouchableOpacity, ActivityIndicator, SafeAreaView, Image } from 'react-native';
import { useIsFocused, useNavigation } from '@react-navigation/native';
import moment from 'moment';
import Alert2 from '../../../../assets/svgs/Alert3';
import Like from '../../../../assets/svgs/Like';
import Loading from '../../../../assets/svgs/Loading2';
import Check2 from '../../../../assets/svgs/Check3';
import Search2 from '../../../../assets/svgs/Search2';
import Send from '../../../../assets/svgs/Send';
import Arrow from '../../../../assets/svgs/ArrowRightGray2';
import ArrowLeft from '../../../../assets/svgs/ArrowLeftSmall';
import More from '../../../../assets/svgs/More';
import Less from '../../../../assets/svgs/Less2';

import PrivateHeader from '../../../../components/headers/PrivateHeader';

import styles from './styles';
import mainStyles from '../../../../mainStyles';
import { ScrollView } from 'react-native-gesture-handler';
import api from '../../../../services/api';
import { useAuth } from '../../../../context/auth';

const PayListPendency = ({ navigation, route }) => {
    const { status, title } = route.params;
    const isFocused = useIsFocused();

    const [loading, setLoading] = useState(true);
    const [showAbout, setShowAbout] = useState(false);
    const [processos, setProcessos] = useState([]);

    useEffect(() => {
        if (isFocused) {
            getProcessos();
        }

        return () => {
            setProcessos([]);
        }
    }, [isFocused]);

    const getProcessos = () => {
        setLoading(true);
        api.get(`pagadoria/status/${status}`).then(res => {
            setProcessos(res.data.processos);
        }).catch(error => {
            console.log(error)
        }).then(() => setLoading(false));
    }

    return (
        <View style={mainStyles.wrapper}>
            <PrivateHeader title={`Pagadoria`} />
            <View style={mainStyles.container}>
                <View style={styles.rowOptions}>
                    {/* <TouchableOpacity onPress={() => navigation.navigate('PaySearch')}>
                        <View style={styles.contentOption}>
                            <ArrowLeft />
                            <Text style={styles.textStatusBold}>{title}</Text>
                        </View>
                    </TouchableOpacity> */}
                    <View style={styles.contentOption}>
                        <Text style={styles.textStatus}>{`Confira abaixo o status das suas comissões. Clique no item para ver mais informações.`}</Text>
                        {/* {atualizadoEm &&
                            <Text style={styles.textStatusBold}>{`ATUALIZADO EM: ${moment(atualizadoEm).format('DD/MM/YYYY')}`}</Text>
                        } */}
                    </View>
                </View>
            </View>
            <ScrollView showsVerticalScrollIndicator={false}>
                <View style={mainStyles.container}>
                    {loading &&
                        <View style={{ flex: 1, justifyContent: "center", alignItems: "center", marginTop: 30 }}>
                            <ActivityIndicator size="large" color="#00467F" />
                        </View>
                    }
                    {!loading && processos.map((processo, index) => (
                        <Pay
                            key={index}
                            processo={processo}
                            navigation={navigation}
                        />
                    ))}
                </View>
            </ScrollView>
            <View style={styles.contentBottom}>
                <View style={[mainStyles.container, styles.contentButtons]}>
                    <TouchableOpacity
                        style={[mainStyles.btnBlueNew, styles.buttonLarge]}
                        onPress={() => navigation.goBack()}>
                        <Text style={mainStyles.btnTextBlueNew}>VOLTAR</Text>
                    </TouchableOpacity>
                </View>
            </View>
        </View>
    );
}

const PayTitle = ({ processo, onPress, icon }) => {
    const items = [
        {
            status: 'processos-com-pendencia',
            title: 'Processo com pendência',
            color: '#FF6542',
            icon: <Alert2 />
        },
        {
            status: 'processos-com-pendencias',
            title: 'Processo com pendência',
            color: '#FF6542',
            icon: <Alert2 />
        },
        {
            status: 'processo-pagamento',
            title: 'Em processo de pagamento',
            color: '#4EA1CC',
            icon: <Loading />
        },
        {
            status: 'em-processo-de-pagamento',
            title: 'Em processo de pagamento',
            color: '#4EA1CC',
            icon: <Loading />
        },
        {
            status: 'pronto-envio-adm',
            title: 'Pronto para enviar',
            color: '#4EA1CC',
            icon: <Send />
        },
        {
            status: 'pronto-para-enviar',
            title: 'Pronto para enviar',
            color: '#4EA1CC',
            icon: <Send />
        },
        {
            status: 'comissoes-pagas',
            title: 'Comissão paga',
            color: '#1B9C20',
            icon: <Check2 />
        },
    ];

    const item = items.filter(i => i.status === processo.processo.status)[0];

    return (
        <TouchableOpacity style={styles.boxFlexWithText} onPress={onPress}>
            <View style={styles.boxIconWidth}>
                {item.icon}
            </View>
            <View style={styles.boxTitle}>
                <View style={styles.boxFlexColumn}>
                    <Text style={styles.textBoxFlexBold}>{processo.pv.toUpperCase()}</Text>
                    <Text style={[styles.textBoxFlex, { color: item.color }]}>{item.title}</Text>
                </View>
                {icon} 
            </View>
        </TouchableOpacity>
    );
}

const Pay = ({ processo, navigation }) => {
    const [showAbout, setShowAbout] = useState(false);
    return (
        <View>
            <View style={styles.boxFlex}>
                <PayTitle processo={processo} onPress={() => setShowAbout(!showAbout)} icon={showAbout ? <Less /> : <More />} />
                {showAbout &&
                <>
                    <TouchableOpacity style={styles.resultPV} onPress={() => navigation.navigate('AboutPendency', { pv: processo.pv })}>
                        <View>
                            <Text style={styles.textAboutProcessBold}>Data da venda: <Text style={styles.textAboutProcess}>{moment(processo?.data).format('DD/MM/YYYY')}</Text></Text>
                            <Text style={styles.textAboutProcessBold}>Nome: <Text style={styles.textAboutProcess}>{processo.nome}</Text></Text>
                            <Text style={styles.textAboutProcessBold}>Produto: <Text style={styles.textAboutProcess}>{processo.produto}</Text></Text>
                        </View>
                        <Arrow />
                    </TouchableOpacity>
                    <View style={styles.divider}></View>
                </>
                }
            </View>
        </View>
    )
}
export default PayListPendency;