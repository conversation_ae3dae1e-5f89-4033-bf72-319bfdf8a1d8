import React, { useEffect, useState } from 'react';
import { TextInput, Text, View, TouchableOpacity, ActivityIndicator, SafeAreaView, Alert, Platform, KeyboardAvoidingView, Image } from 'react-native';
import { useIsFocused, useNavigation } from '@react-navigation/native';
import moment from 'moment';
import Alert2 from '../../../../assets/svgs/Alert2';
import Like from '../../../../assets/svgs/Like';
import Loading from '../../../../assets/svgs/Loading';
import Check2 from '../../../../assets/svgs/Check2';
import Search2 from '../../../../assets/svgs/Search2';
import Send from '../../../../assets/svgs/Send';
import Arrow from '../../../../assets/svgs/ArrowRightGray2';

import PrivateHeader from '../../../../components/headers/PrivateHeader';

import styles from './styles';
import mainStyles from '../../../../mainStyles';
import { ScrollView } from 'react-native-gesture-handler';
import api from '../../../../services/api';
import { useAuth } from '../../../../context/auth';

const PaySearch = ({ navigation }) => {
    const isFocused = useIsFocused();

    const [loading, setLoading] = useState(true);
    const [loadingSearch, setLoadingSearch] = useState(false);

    const [totalPendencias, setTotalPendencias] = useState(0);
    const [totalProntoEnviar, setTotalProntoEnviar] = useState(0);
    const [totalProcessoPagamento, setTotalProcessoPagamento] = useState(0);
    const [totalPagas, setTotalPagas] = useState(0);
    const [atualizadoEm, setAtualizadoEm] = useState(null);

    const [search, setSearch] = useState('');
    
    useEffect(() => {
        if(isFocused) getData();
    }, [isFocused]);

    const getData = () => {
        setLoading(true);
        api.get(`pagadoria`).then(res => {
            const processos = res.data.processos;
            setAtualizadoEm(res.data?.atualizado_em);
            
            processos.map(processo => {
                switch (processo.processo.status) {
                    case 'processos-com-pendencia':
                        setTotalPendencias(processo.total);
                        break;
                    case 'pronto-envio-adm':
                        setTotalProntoEnviar(processo.total);
                        break;
                    case 'processo-pagamento':
                        setTotalProcessoPagamento(processo.total);
                        break;
                    case 'comissoes-pagas':
                        setTotalPagas(processo.total);
                        break;
                }
            });

            setLoading(false);
        }).catch(error => {
            console.log(error)
        }).then(() => setLoading(false));
    }

    const handleSearch = () => {
        setLoadingSearch(true);

        api.get(`pagadoria/pv/${search}`).then(res => {
            navigation.navigate('AboutPendency', { pv: `pv-${search}` });
        }).catch(error => {
            Alert.alert('Não encontrado', 'Não encontramos a PV informada. Por favor, tente novamente.');
            setSearch('');
        }).then(() => setLoadingSearch(false));
        
    }

    const Total = ({total, color}) => {
        return (
            <View>
                {loading &&
                    <View style={{ flex: 1, justifyContent: "center", alignItems: "center", }}>
                        <ActivityIndicator size="large" color="#00467F" />
                    </View>
                }
                {!loading &&
                    <Text style={[styles.textBoxFlexNumber, {color}]}>{total}</Text>
                }
            </View>
        );
    }

    return (
        <KeyboardAvoidingView behavior={Platform.OS === 'ios' ? 'position' : 'none'} style={mainStyles.wrapper}>
            <PrivateHeader title={`Pagadoria`} />
            <View style={mainStyles.container}>
                <View style={styles.rowOptions}>
                    <View style={styles.contentOption}>
                        <Text style={styles.textStatus}>{`Selecione abaixo o status das comissões que deseja pesquisar.`}</Text>
                        {/* {atualizadoEm &&
                            <Text style={styles.textStatusBold}>{`ATUALIZADO EM: ${moment(atualizadoEm).format('DD/MM/YYYY')}`}</Text>
                        } */}
                    </View>
                </View>
                <View style={styles.divider}></View>
            </View>
            <ScrollView keyboardShouldPersistTaps={`handled`} showsVerticalScrollIndicator={false}>
                <View style={mainStyles.container}>
                    <TouchableOpacity 
                        onPress={() => navigation.navigate('PayListPendency', {
                            title: 'PROCESSOS COM PENDÊNCIAS',
                            status: 'processos-com-pendencia' 
                        })}
                    >
                        <View style={styles.boxFlex}>
                            <View style={styles.boxIconWidth}>
                                <Alert2 style={styles.icAlert} />
                            </View> 
                            <View style={styles.boxFlexWithText}>                                 
                                <Text style={[styles.textBoxFlex, { color: "#FF6542" }]}>{`Processos com\npendências`}</Text>
                                <Total total={totalPendencias} color={"#FF6542"} />
                            </View>
                            <Arrow />
                        </View>
                    </TouchableOpacity>
                    <TouchableOpacity 
                        onPress={() => navigation.navigate('PayListPendency', {
                            title: 'PRONTO PARA ENVIAR',
                            status: 'pronto-envio-adm' 
                        })}
                    >
                        <View style={styles.boxFlex}>
                            <View style={styles.boxIconWidth}>
                                <Send />
                            </View> 
                            <View style={styles.boxFlexWithText}>                                 
                                <Text style={[styles.textBoxFlex, { color: "#4EA1CC" }]}>Pronto para enviar</Text>
                                <Total total={totalProntoEnviar} color={"#00467F"} />
                            </View>
                            <Arrow />
                        </View>
                    </TouchableOpacity>
                    <TouchableOpacity 
                        onPress={() => navigation.navigate('PayListPendency', {
                            title: 'EM PROCESSO DE PAGAMENTO',
                            status: 'processo-pagamento' 
                        })}
                    >
                        <View style={styles.boxFlex}>
                            <View style={styles.boxIconWidth}>
                                <Loading style={styles.icLoading} />
                            </View>
                            <View style={styles.boxFlexWithText}>                                  
                                <Text style={[styles.textBoxFlex, { color: "#4EA1CC" }]}>{`Em processo de\npagamento`}</Text>
                                <Total total={totalProcessoPagamento} color={"#00467F"} />
                            </View>                        
                            <Arrow />
                        </View>
                    </TouchableOpacity>
                    <TouchableOpacity 
                        onPress={() => navigation.navigate('PayListPendency', {
                            title: 'COMISSÕES PAGAS',
                            status: 'comissoes-pagas' 
                        })}
                    >
                        <View style={styles.boxFlex}>
                            <View style={styles.boxIconWidth}>
                                <Check2 style={styles.icCheck} />
                            </View> 
                            <View style={styles.boxFlexWithText}>                                                           
                                <Text style={[styles.textBoxFlex, { color: "#1B9C20" }]}>{`Comissões pagas`}</Text>
                                <Total total={totalPagas} color={"#1B9C20"} />
                            </View>
                            <Arrow />
                        </View>
                    </TouchableOpacity>
                    <Text style={styles.textSearch}>Ou busque pela PV:</Text>
                    <SafeAreaView>
                        {!loadingSearch &&
                            <View style={styles.searchBox}>
                                <Text style={[styles.textSearch, styles.textBold]}>PV -</Text>
                                <TextInput 
                                    style={styles.input} 
                                    placeholder="000000"
                                    keyboardType="phone-pad"
                                    value={search}
                                    onChangeText={text => setSearch(text.trim())}
                                /> 
                                <TouchableOpacity style={styles.btnOk} onPress={handleSearch}>
                                    <Search2 />
                                </TouchableOpacity>
                            </View>
                        }
                        {loadingSearch &&
                            <View style={{ flex: 1, justifyContent: "center", alignItems: "center", marginBottom: 20 }}>
                                <ActivityIndicator size="large" color="#00467F" />
                            </View>
                        }
                    </SafeAreaView>
                </View>
            </ScrollView>
            <View style={styles.contentBottom}>
                <View style={[mainStyles.container, styles.contentButtons]}>
                    <TouchableOpacity
                        style={[mainStyles.btnBlueNew, styles.buttonLarge]}
                        onPress={() => navigation.goBack()}>
                        <Text style={mainStyles.btnTextBlueNew}>VOLTAR</Text>
                    </TouchableOpacity>
                </View>
            </View>
        </KeyboardAvoidingView>
    );
}

export default PaySearch;