import Clipboard from '@react-native-clipboard/clipboard';
import {useIsFocused} from '@react-navigation/native';
import React, {useEffect, useRef, useState} from 'react';
import {ActivityIndicator, Text, TouchableOpacity, View} from 'react-native';
import {ScrollView} from 'react-native-gesture-handler';
import RenderHtml from 'react-native-render-html';
import ArrowLeft from '../../../../assets/svgs/ArrowLeftSmallWhite';
import IA from '../../../../assets/svgs/IA';
import PrivateHeader from '../../../../components/headers/PrivateHeader';
import {useWoxAi} from '../../../../context/woxAi';
import mainStyles from '../../../../mainStyles';
import styles from './styles';

const mixedStyle = {
  div: {
    color: '#828282',
    fontFamily: 'Ubuntu-Regular',
    fontSize: 13,
    lineHeight: 22,
    letterSpacing: 1,
  },
  h1: {
    fontFamily: 'Ubuntu-Bold',
    fontSize: 18,
    lineHeight: 26,
  },
};

const IAEstrategiaCriada = ({navigation}) => {
  const {dataAi} = useWoxAi();
  const isFocused = useIsFocused();
  const [loading, setLoading] = useState(true);
  const renderHtmlRef = useRef();
  const getContent = () => {
    setLoading(false);
  };
  let title = '';
  if (dataAi.type === 'IAContentTexto') {
    title = 'Textos';
  } else {
    title = 'Estratégia';
  }
  const source = {
    html: `
          <div>
              ${dataAi.textGeneratedAi}
          </div>`,
  };

  const copyToClipboard = async () => {
    const regex = /<[^>]*>/gm;
    const htmlText = dataAi.textGeneratedAi.replace(regex, '');
    await Clipboard.setString(htmlText);
  };

  useEffect(() => {
    if (isFocused) {
      getContent();
    }
  }, [isFocused]);

  return (
    <View style={mainStyles.wrapper}>
      <PrivateHeader title={'Wox AI'} />
      <ScrollView showsVerticalScrollIndicator={false}>
        <TouchableOpacity
          style={styles.buttonTitle}
          onPress={() => navigation.navigate(dataAi.type)}>
          <View style={styles.icArrow}>
            <ArrowLeft />
          </View>
          <View style={styles.icAi}>
            <IA />
            <Text style={mainStyles.woxAiTitle}>WOX AI</Text>
            <Text style={mainStyles.betaIco}>BETA</Text>
          </View>
        </TouchableOpacity>
        <View style={mainStyles.privateContainer}>
          <Text style={styles.textTitle}>{title}</Text>
          {loading && (
            <View
              style={{
                flex: 1,
                justifyContent: 'center',
                alignItems: 'center',
                marginTop: 30,
              }}>
              <ActivityIndicator size="large" color="#00467F" />
            </View>
          )}

          {!loading && (
            <>
              <View>
                <RenderHtml
                  contentWidth={0}
                  source={source}
                  tagsStyles={mixedStyle}
                  ref={renderHtmlRef}
                />
                <TouchableOpacity
                  style={styles.btnCenterOutlineBlueDarkNew}
                  onPress={copyToClipboard}>
                  <Text style={styles.btnTextOutlineBlue}>Copiar Texto</Text>
                </TouchableOpacity>
              </View>
            </>
          )}
        </View>
      </ScrollView>
      <View style={styles.contentBottom}>
        <View style={[mainStyles.container, styles.contentButtons]}>
          <TouchableOpacity
            style={[
              mainStyles.btnOutlineBlue,
              mainStyles.buttonW48,
              {height: 50},
            ]}
            onPress={() => navigation.navigate('IAList')}>
            <Text style={mainStyles.btnTextOutlineBlue}>VOLTAR</Text>
          </TouchableOpacity>
          <TouchableOpacity
            style={[mainStyles.btnBlueNew, mainStyles.buttonW48]}
            onPress={() => navigation.navigate(dataAi.type)}>
            <Text style={mainStyles.btnTextBlueNew}>REFAZER</Text>
          </TouchableOpacity>
        </View>
      </View>
    </View>
  );
};

export default IAEstrategiaCriada;
