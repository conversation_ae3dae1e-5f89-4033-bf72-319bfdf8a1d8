/* eslint-disable react-native/no-inline-styles */
import {useIsFocused} from '@react-navigation/native';
import React, {useEffect, useState} from 'react';
import {ActivityIndicator, Text, TouchableOpacity, View} from 'react-native';

import PrivateHeader from '../../../../components/headers/PrivateHeader';

import ArrowLeft from '../../../../assets/svgs/ArrowLeftSmallWhite';
import IA from '../../../../assets/svgs/IA';

import {ScrollView} from 'react-native-gesture-handler';
import ArrowRightGray from '../../../../assets/svgs/ArrowRightGray';
import GA from '../../../../assets/svgs/GA';
import MegaPhone from '../../../../assets/svgs/MegaPhoneLight';
import Meta from '../../../../assets/svgs/Meta';
import WhatsApp from '../../../../assets/svgs/Whats4';
import mainStyles from '../../../../mainStyles';
import styles from './styles';

const IATextoCriado = ({navigation}) => {
  const isFocused = useIsFocused();

  const [loading, setLoading] = useState(true);

  useEffect(() => {
    if (isFocused) {
      getContent();
    }
  }, [isFocused]);

  const getContent = () => {
    setLoading(false);
  };

  return (
    <View style={mainStyles.wrapper}>
      <PrivateHeader title={'Wox AI'} />
      <ScrollView showsVerticalScrollIndicator={false}>
        <TouchableOpacity
          style={styles.buttonTitle}
          onPress={() => navigation.navigate('IAContentTexto')}>
          <View style={styles.icArrow}>
            <ArrowLeft />
          </View>
          <View style={styles.icAi}>
            <IA />
            <Text style={mainStyles.woxAiTitle}>WOX AI</Text>
            <Text style={mainStyles.betaIco}>BETA</Text>
          </View>
        </TouchableOpacity>
        <View style={mainStyles.privateContainer}>
          <Text style={styles.textTitle}>Textos</Text>
          {loading && (
            <View
              style={{
                flex: 1,
                justifyContent: 'center',
                alignItems: 'center',
                marginTop: 30,
              }}>
              <ActivityIndicator size="large" color="#00467F" />
            </View>
          )}
          {!loading && (
            <>
              <Text style={[styles.textRegular, {textAlign: 'center'}]}>
                Escolha para qual canal deseja utilizar o texto:
              </Text>
              <View>
                <TouchableOpacity
                  style={styles.boxTotal}
                  onPress={() => navigation.navigate('IATextoWhats')}>
                  <View style={styles.totalIcon}>
                    <WhatsApp />
                  </View>
                  <View style={styles.totalInfos}>
                    <View>
                      <Text style={styles.titleBox}>WhatsApp</Text>
                    </View>
                    <ArrowRightGray style={styles.icArrowRight} />
                  </View>
                </TouchableOpacity>
                <TouchableOpacity
                  style={styles.boxTotal}
                  onPress={() => navigation.navigate('')}>
                  <View style={styles.totalIcon}>
                    <Meta />
                  </View>
                  <View style={styles.totalInfos}>
                    <View>
                      <Text style={styles.titleBox}>Instagram / Facebook</Text>
                    </View>
                    <ArrowRightGray style={styles.icArrowRight} />
                  </View>
                </TouchableOpacity>
                <TouchableOpacity
                  style={styles.boxTotal}
                  onPress={() => navigation.navigate('IATextoGA')}>
                  <View style={styles.totalIcon}>
                    <GA />
                  </View>
                  <View style={styles.totalInfos}>
                    <View>
                      <Text style={styles.titleBox}>Google Adwords</Text>
                    </View>
                    <ArrowRightGray style={styles.icArrowRight} />
                  </View>
                </TouchableOpacity>
                <TouchableOpacity
                  style={styles.boxTotal}
                  onPress={() => navigation.navigate('')}>
                  <View style={styles.totalIcon}>
                    <MegaPhone />
                  </View>
                  <View style={styles.totalInfos}>
                    <View>
                      <Text style={styles.titleBox}>Outros</Text>
                    </View>
                    <ArrowRightGray style={styles.icArrowRight} />
                  </View>
                </TouchableOpacity>
              </View>
              <Text style={[styles.textRegular, {marginTop: 30}]}>
                Lembre-se de adaptar os textos de acordo com as limitações de
                caracteres de cada plataforma, especialmente no caso dos
                anúncios no Google AdWords.
              </Text>
              <Text style={[styles.textRegular, {marginBottom: 30}]}>
                Utilize imagens atraentes que destaquem os diferenciais do
                empreendimento e inclua sempre um link direcionando para o seu
                melhor canal de contato.
              </Text>
            </>
          )}
        </View>
      </ScrollView>
      <View style={styles.contentBottom}>
        <View style={[mainStyles.container, styles.contentButtons]}>
          <TouchableOpacity
            style={[
              mainStyles.btnOutlineBlue,
              mainStyles.buttonW48,
              {height: 50},
            ]}
            onPress={() => navigation.navigate('IAList')}>
            <Text style={mainStyles.btnTextOutlineBlue}>VOLTAR</Text>
          </TouchableOpacity>
          <TouchableOpacity
            style={[mainStyles.btnBlueNew, mainStyles.buttonW48]}
            onPress={() => navigation.navigate('IAContentTexto')}>
            <Text style={mainStyles.btnTextBlueNew}>PEDIR AJUSTES</Text>
          </TouchableOpacity>
        </View>
      </View>
    </View>
  );
};

export default IATextoCriado;
