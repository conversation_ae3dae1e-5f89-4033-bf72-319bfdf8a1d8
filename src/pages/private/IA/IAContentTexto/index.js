/* eslint-disable react-native/no-inline-styles */
/* eslint-disable no-shadow */
import {useIsFocused} from '@react-navigation/native';
import React, {useEffect, useState} from 'react';
import {
  ActivityIndicator,
  Alert,
  Text,
  TouchableOpacity,
  View,
} from 'react-native';
import {ScrollView} from 'react-native-gesture-handler';
import ArrowLeft from '../../../../assets/svgs/ArrowLeftSmallWhite';
import IA from '../../../../assets/svgs/IA';
import Checkbox from '../../../../components/Checkbox';
import SelectBorder from '../../../../components/SelectBorder';
import PrivateHeader from '../../../../components/headers/PrivateHeader';
import {useAuth} from '../../../../context/auth';
import {useWoxAi} from '../../../../context/woxAi';
import mainStyles from '../../../../mainStyles';
import {apisite} from '../../../../services/api';
import styles from './styles';

const IAContentTexto = ({navigation}) => {
  const isFocused = useIsFocused();
  const {user} = useAuth();
  const {dataAi, setForm, crtl} = useWoxAi();
  const [loading, setLoading] = useState(true);
  const [products, setProducts] = useState([]);
  const [details, setDetails] = useState([]);
  const [description, setDescription] = useState([]);
  const [checked, setChecked] = useState({});
  const [checkboxesData, setCheckboxesData] = useState({});

  const toggleCheckbox = name => {
    setChecked(prev => ({
      ...prev,
      [name]: !prev[name],
    }));
  };

  useEffect(() => {
    setForm('diferencial', checked);
  }, [checked]);

  useEffect(() => {
    if (isFocused) {
      getContent();
      getProducts();
      if (crtl) {
        crtl.abort();
      }
      setForm('textGeneratedAi', '');
      setForm('type', 'IAContentTexto');
    }
  }, [isFocused]);

  const getContent = () => {
    setLoading(false);
  };

  const getProducts = async () => {
    apisite
      .post('/products/listAllProducts', {
        regional: user.regional,
      })
      .then(res => {
        let productOptions = [];
        let detailsOptions = [];
        let descriptionOptions = [];

        res.data.data.map(product => {
          productOptions.push({
            label: product.title,
            name: product.title,
            value: product.title,
          });

          descriptionOptions.push({
            description: product.description,
          });

          detailsOptions.push({
            details: product.details,
          });
        });

        setProducts(productOptions);
        setDescription(descriptionOptions);
        setDetails(detailsOptions);
        setLoading(false);
      })
      .catch(err => {
        console.log('err.response');
        console.log(err);
      });
  };

  function setProductValue(value) {
    const index = products.findIndex(product => product.value === value);
    if (index != -1) {
      setCheckboxesData(details[index]);
      console.log('details[index]');
      console.log(details[index]);

      setForm('product', products[index].label);
      setForm('description', description[index].description);
      setForm('details', details[index].details);
    }
  }
  function next() {
    const keys = Object.keys(checked).filter(key => checked[key]);
    if (!dataAi.product) {
      Alert.alert('Você precisa selecionar um produto.');
    } else if (keys.length == 0) {
      Alert.alert('Se precisa selecionar pelo menos 1 item.');
    } else {
      navigation.navigate('IACriandoEstrategia');
    }
  }

  return (
    <View style={mainStyles.wrapper}>
      <PrivateHeader title={'Wox AI'} />
      <ScrollView showsVerticalScrollIndicator={false}>
        <TouchableOpacity
          style={styles.buttonTitle}
          onPress={() => navigation.goBack()}>
          <View style={styles.icArrow}>
            <ArrowLeft />
          </View>
          <View style={styles.icAi}>
            <IA />
            <Text style={mainStyles.woxAiTitle}>WOX AI</Text>
            <Text style={mainStyles.betaIco}>BETA</Text>
          </View>
        </TouchableOpacity>
        <View style={mainStyles.privateContainer}>
          <Text style={styles.textTitle}>Textos</Text>
          {loading && (
            <View
              style={{
                flex: 1,
                justifyContent: 'center',
                alignItems: 'center',
                marginTop: 30,
              }}>
              <ActivityIndicator size="large" color="#00467F" />
            </View>
          )}

          {!loading && (
            <>
              <View style={styles.marginTop}>
                <Text style={mainStyles.labelMargin}>1. ESCOLHA O PRODUTO</Text>
                <SelectBorder
                  options={products}
                  textInputProps={{
                    autoCompleteType: 'name',
                  }}
                  value={products.value}
                  onChange={value => setProductValue(value)}
                  placeholder="SELECIONE"
                />
              </View>
              {checkboxesData?.details &&
              checkboxesData?.details?.length > 0 ? (
                <>
                  <View style={styles.marginTop}>
                    <Text style={mainStyles.labelMargin}>
                      2. QUAIS ITENS VOCÊ GOSTARIA DE ENFATIZAR?
                    </Text>
                  </View>

                  <View style={{marginBottom: 50}}>
                    {checkboxesData?.details &&
                      checkboxesData?.details?.length > 0 &&
                      checkboxesData?.details.map(item => (
                        <Checkbox
                          text={item.label}
                          isChecked={checked[item.label]}
                          onPress={() => toggleCheckbox(item.label)}
                        />
                      ))}
                  </View>
                </>
              ) : (
                <>
                  <View style={styles.marginTop}>
                    <Text style={mainStyles.labelMargin}>
                      Nenhum item encontrado para esse empreendimento.
                    </Text>
                  </View>
                </>
              )}
            </>
          )}
        </View>
      </ScrollView>

      <View style={styles.contentBottom}>
        <View style={[mainStyles.container, styles.contentButtons]}>
          <TouchableOpacity
            style={[mainStyles.btnBlueNew, styles.buttonLarge]}
            onPress={() => next()}>
            <Text style={mainStyles.btnTextBlueNew}>CRIAR TEXTOS</Text>
          </TouchableOpacity>
        </View>
      </View>
    </View>
  );
};

export default IAContentTexto;
