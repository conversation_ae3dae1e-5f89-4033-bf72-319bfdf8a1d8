/* eslint-disable react-native/no-inline-styles */
/* eslint-disable no-shadow */
import {useIsFocused} from '@react-navigation/native';
import React, {useEffect, useState} from 'react';
import {
  ActivityIndicator,
  Alert,
  Text,
  TextInput,
  TouchableOpacity,
  View,
} from 'react-native';
import {ScrollView} from 'react-native-gesture-handler';
import ArrowLeft from '../../../../assets/svgs/ArrowLeftSmallWhite';
import IA from '../../../../assets/svgs/IA';
import Checkbox from '../../../../components/Checkbox';
import SelectBorder from '../../../../components/SelectBorder';
import PrivateHeader from '../../../../components/headers/PrivateHeader';
import {useAuth} from '../../../../context/auth';
import {useWoxAi} from '../../../../context/woxAi';
import mainStyles from '../../../../mainStyles';
import {apisite} from '../../../../services/api';
import styles from './styles';
const checkboxesData = [
  {label: 'Google AdWords'},
  {label: 'YouTube'},
  {label: 'Facebook'},
  {label: 'Instagram'},
  {label: 'TikTok'},
  {label: 'Outros'},
];

const whereData = [
  {label: 'SIM, JÁ SEI ONDE ANUNCIAR', value: true},
  {label: 'NÃO SEI, ACEITO SUGESTÕES', value: false},
];
const IAContentCampanha = ({navigation}) => {
  const {user} = useAuth();
  const isFocused = useIsFocused();
  const {dataAi, setForm, crtl} = useWoxAi();
  const [loading, setLoading] = useState(true);
  const [products, setProducts] = useState([]);
  const [inMoney, setInMoney] = useState('');
  const [description, setDescription] = useState([]);
  const [whereAd, setWhereAd] = useState(true);
  const [checked, setChecked] = useState({});

  const toggleCheckbox = name => {
    setChecked(prev => ({
      ...prev,
      [name]: !prev[name],
    }));
  };

  useEffect(() => {
    setForm('whereAd', checked);
  }, [checked]);

  useEffect(() => {
    if (isFocused) {
      getContent();
      getProducts();
      if (crtl) {
        crtl.abort();
      }
      setForm('textGeneratedAi', '');
      setForm('type', 'IAContentCampanha');
    }
  }, [isFocused]);

  const getContent = () => {
    setLoading(false);
  };

  const getProducts = async () => {
    apisite
      .post('/products/listAllProducts', {
        regional: user.regional,
      })
      .then(res => {
        console.log('product loaded');
        let productOptions = [];
        let descriptionOptions = [];
        res.data.data.map(product => {
          productOptions.push({
            label: product.title,
            name: product.title,
            value: product.title,
          });

          descriptionOptions.push({
            description: product.description,
          });
        });

        setProducts(productOptions);
        setDescription(descriptionOptions);
        setLoading(false);
      })
      .catch(err => {
        console.log('err.response');
        console.log(err);
      });
  };

  function onChangeMoney(text) {
    let cleaned = parseInt(text.replace(/\D/g, '')) / 100;

    if (!cleaned) {
      cleaned = 0;
    }

    const formated = new Intl.NumberFormat('pt-BR', {
      style: 'currency',
      currency: 'BRL',
    }).format(cleaned);
    setInMoney(formated);
    setForm('money', formated);
  }

  function setProductValue(value) {
    const index = products.findIndex(product => product.value === value);

    if (index != -1) {
      setForm('product', products[index].label);
      setForm('description', description[index].description);
    }
    // setForm('product', value);
  }
  function next() {
    const keys = Object.keys(dataAi.whereAd).filter(key => dataAi.whereAd[key]);
    if (!dataAi.product) {
      Alert.alert('Você precisa selecionar um produto.');
    } else if (!dataAi.money) {
      Alert.alert('Você precisa dizer sua verba.');
    } else if (whereAd && keys.length == 0) {
      Alert.alert('Se você sabe aonde anunciar selecione pelo menos 1 item.');
    } else {
      navigation.navigate('IACriandoEstrategia');
    }
  }

  return (
    <View style={mainStyles.wrapper}>
      <PrivateHeader title={'Wox AI'} />
      <ScrollView showsVerticalScrollIndicator={false}>
        <TouchableOpacity
          style={styles.buttonTitle}
          onPress={() => navigation.goBack()}>
          <View style={styles.icArrow}>
            <ArrowLeft />
          </View>
          <View style={styles.icAi}>
            <IA />
            <Text style={mainStyles.woxAiTitle}>WOX AI</Text>
            <Text style={mainStyles.betaIco}>BETA</Text>
          </View>
        </TouchableOpacity>
        <View style={mainStyles.privateContainer}>
          <Text style={styles.textTitle}>Estratégia</Text>
          {loading && (
            <View
              style={{
                flex: 1,
                justifyContent: 'center',
                alignItems: 'center',
                marginTop: 30,
              }}>
              <ActivityIndicator size="large" color="#00467F" />
            </View>
          )}

          {!loading && (
            <>
              <View style={styles.marginTop}>
                <Text style={mainStyles.labelMargin}>1. ESCOLHA O PRODUTO</Text>
                <SelectBorder
                  options={products}
                  value={products.value}
                  onChange={value => setProductValue(value)}
                  placeholder="SELECIONE"
                />
              </View>
              <View style={styles.marginTop}>
                <Text style={mainStyles.labelMargin}>
                  2. DEFINA QUAL A VERBA
                </Text>
                <TextInput
                  underlineColorAndroid="transparent"
                  style={mainStyles.inputText}
                  placeholder="R$ 00.000,00"
                  value={inMoney}
                  onChangeText={onChangeMoney}
                  keyboardType="phone-pad"
                />
              </View>
              <View style={styles.marginTop}>
                <Text style={mainStyles.labelMargin}>
                  3. VOCÊ JÁ SABE ONDE ANUNCIAR?
                </Text>
                <SelectBorder
                  options={whereData}
                  value={whereAd}
                  onChange={value => setWhereAd(value)}
                />
              </View>
              {whereAd && (
                <View style={{marginBottom: 50}}>
                  {checkboxesData.map((item, index) => (
                    <Checkbox
                      key={index}
                      text={item.label}
                      isChecked={checked[item.label]}
                      onPress={() => toggleCheckbox(item.label)}
                    />
                  ))}
                </View>
              )}
            </>
          )}
        </View>
      </ScrollView>

      <View style={styles.contentBottom}>
        <View style={[mainStyles.container, styles.contentButtons]}>
          <TouchableOpacity
            style={[mainStyles.btnBlueNew, styles.buttonLarge]}
            onPress={() => next()}>
            <Text style={mainStyles.btnTextBlueNew}>CRIAR ESTRATÉGIA</Text>
          </TouchableOpacity>
        </View>
      </View>
    </View>
  );
};

export default IAContentCampanha;
