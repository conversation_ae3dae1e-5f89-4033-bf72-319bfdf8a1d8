/* eslint-disable react-hooks/exhaustive-deps */
import React, {useEffect, useRef, useState} from 'react';
import {Animated, Image, Text, TouchableOpacity, View} from 'react-native';
import {ScrollView} from 'react-native-gesture-handler';
import Check2 from '../../../../assets/svgs/Check2';
import IA from '../../../../assets/svgs/IA';
import Timer from '../../../../components/Timer';
import PrivateHeader from '../../../../components/headers/PrivateHeader';
import {useWoxAi} from '../../../../context/woxAi';
import mainStyles from '../../../../mainStyles';
import styles from './styles';

const IACriandoEstrategia = ({navigation}) => {
  const [isGenerating, setIsGenerating] = useState(true);
  const {getai, setForm, dataAi} = useWoxAi();
  const fadeIn = useRef(new Animated.Value(0)).current;
  const icIA = useRef(new Animated.Value(1)).current;
  const [aiRun, setAiRun] = useState(false);

  // const handleAppStateChange = state => {
  //   if (state === 'background') {
  //     console.log('handleAppStateChange');
  //     console.log('++++++++++++++++++++++++++');
  //   }
  //   if (state === 'active' && aiRun) {
  //     console.log('active');
  //     console.log('++++++++++++++++++++++++++');
  //     setTimeout(function () {
  //       startAi();
  //     }, 3000);
  //   }
  // };

  // AppState.addEventListener('change', handleAppStateChange);
  const fadeOut = () => {
    Animated.timing(fadeIn, {
      toValue: 0,
      duration: 600,
      useNativeDriver: true,
    }).start();
  };
  const scaleLarge = () => {
    Animated.timing(icIA, {
      toValue: 2,
      duration: 600,
      useNativeDriver: true,
    }).start();
  };
  const startAnimation = () => {
    Animated.spring(icIA, {
      toValue: 1,
      friction: 3,
      tension: 5,
      useNativeDriver: true,
    }).start(() => {
      Animated.spring(icIA, {
        toValue: 1.12,
        friction: 3,
        tension: 5,
        useNativeDriver: true,
      }).start(() => (isGenerating ? startAnimation() : scaleLarge()));
    });
  };
  useEffect(() => {
    Animated.timing(fadeIn, {
      toValue: 1,
      duration: 600,
      useNativeDriver: true,
    }).start();

    startAi();
  }, [fadeIn]);

  const startAi = async () => {
    console.log('startai ');
    setIsGenerating(true);
    startAnimation();
    setAiRun(true);
    const data = await getai();
    setAiRun(false);
    setForm('textGeneratedAi', data.data.data);
    setIsGenerating(false);
    scaleLarge();
    fadeOut();
    setTimeout(function () {
      navigation.navigate('IAEstrategiaCriada');
    }, 1000);
    //   }, 5000);
  };

  let title = '';
  let text2 = '';
  if (dataAi.type === 'IAContentTexto') {
    title = 'textos';
    text2 = 'Textos criados\ncom sucesso!';
  } else {
    title = 'estratégia';
    text2 = 'Estratégia criada\ncom sucesso!';
  }

  return (
    <View style={[mainStyles.wrapper, {backgroundColor: '#2D719F'}]}>
      <PrivateHeader title={'Wox AI'} />
      <ScrollView style={styles.container} showsVerticalScrollIndicator={false}>
        <Animated.View style={[{opacity: fadeIn}]}>
          <Text style={styles.textTitle}>Criando {title}...</Text>
        </Animated.View>
        <Animated.View
          style={{
            justifyContent: 'center',
            flexDirection: 'row',
            marginTop: 30,
            marginBottom: 30,
            transform: [{scale: icIA}],
          }}>
          <IA style={styles.icIA} />
        </Animated.View>
        <Animated.View style={[{opacity: fadeIn}]}>
          <View style={styles.divider} />
          <View style={styles.boxWhite}>
            <Text style={styles.textAlert}>Fique ligado!</Text>
          </View>
        </Animated.View>
        <View style={styles.animation}>
          {isGenerating ? (
            <>
              <Animated.View style={(styles.boxTexts, [{opacity: fadeIn}])}>
                <Timer
                  open={isGenerating}
                  close={setIsGenerating}
                  startValue={120}
                />
                <Text style={styles.textDontClose}>
                  Não saia desta tela ou feche o aplicativo.
                </Text>
              </Animated.View>
            </>
          ) : (
            <>
              <TouchableOpacity
                style={styles.viewFlex}
                onPress={() => navigation.navigate('IAEstrategiaCriada')}>
                <Check2 style={styles.icCheck} />
                <Text style={styles.textSuccess}>{text2}</Text>
              </TouchableOpacity>
            </>
          )}
        </View>
      </ScrollView>
      <View style={styles.container}>
        <View style={styles.contentBottom}>
          <Text style={styles.textBottom}>Desenvolvido por</Text>
          <Image
            style={styles.logo}
            source={require('../../../../assets/logo-studio.png')}
          />
        </View>
      </View>
    </View>
  );
};

export default IACriandoEstrategia;
