import React, {useEffect, useRef} from 'react';
import {Animated, Image, Text, TouchableOpacity, View} from 'react-native';

import PrivateHeader from '../../../../components/headers/PrivateHeader';

import Check2 from '../../../../assets/svgs/Check2';
import IA from '../../../../assets/svgs/IA';

import {ScrollView} from 'react-native-gesture-handler';
import mainStyles from '../../../../mainStyles';
import styles from './styles';

const IACriandoTexto = ({navigation}) => {
  const fadeIn = useRef(new Animated.Value(0)).current; // Initial value for opacity: 0

  useEffect(() => {
    Animated.timing(fadeIn, {
      toValue: 1,
      duration: 3000,
      useNativeDriver: true,
    }).start();
  }, [fadeIn]);

  return (
    <View style={[mainStyles.wrapper, {backgroundColor: '#2D719F'}]}>
      <PrivateHeader title={'Wox AI'} />
      <ScrollView style={styles.container} showsVerticalScrollIndicator={false}>
        <View style={styles.center}>
          <IA style={styles.icIABig} />
        </View>
        <TouchableOpacity
          style={styles.viewFlex}
          onPress={() => navigation.navigate('IATextoCriado')}>
          <Check2 style={styles.icCheck} />
          <Text style={styles.textSuccess}>
            {'Textos criados\ncom sucesso!'}
          </Text>
        </TouchableOpacity>
      </ScrollView>
      <View style={styles.container}>
        <View style={styles.contentBottom}>
          <Text style={styles.textBottom}>Desenvolvido por</Text>
          <Image
            style={styles.logo}
            source={require('../../../../assets/logo-studio.png')}
          />
        </View>
      </View>
    </View>
  );
};

export default IACriandoTexto;
