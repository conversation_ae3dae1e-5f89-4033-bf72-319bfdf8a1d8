/* eslint-disable react-native/no-inline-styles */
/* eslint-disable no-shadow */
import {useIsFocused} from '@react-navigation/native';
import React, {useEffect, useRef, useState} from 'react';
import {
  ActivityIndicator,
  KeyboardAvoidingView,
  Text,
  TextInput,
  TouchableOpacity,
  View,
} from 'react-native';
import {ScrollView} from 'react-native-gesture-handler';
import ArrowLeft from '../../../../assets/svgs/ArrowLeftSmallWhite';
import ArrowRightChatAi from '../../../../assets/svgs/ArrowRightChatAi';
import IA from '../../../../assets/svgs/IA';
import BoxBot from '../../../../components/BoxesChat/BoxBot';
import BoxBotLoading from '../../../../components/BoxesChat/BoxBotLoading';
import BoxUser from '../../../../components/BoxesChat/BoxUser';
import PrivateHeader from '../../../../components/headers/PrivateHeader';
import {useWoxAi} from '../../../../context/woxAi';
import mainStyles from '../../../../mainStyles';
import useStyle from './styles';

const IATiraDuvidas = ({navigation}) => {
  const {styles} = useStyle();
  const isFocused = useIsFocused();
  const {getai, setForm} = useWoxAi();
  const [loading, setLoading] = useState(true);
  const [isGenerating, setIsGenerating] = useState(false);
  const [textUser, setTextUser] = React.useState('');
  const onChangeText = text => {
    setTextUser(text);
  };
  const startAi = async () => {
    setIsGenerating(true);
    const textUserBefore = textUser;
    setTextUser('');
    const data = await getai(textUserBefore);
    addNewMessageBot(data.data.data);
    setIsGenerating(false);
  };

  function addNewMessageBot(text) {
    setConversation(prevMessages => [
      ...prevMessages,
      {type: 'chat', text: text, showCopy: true},
    ]);
  }
  function addNewMessage() {
    setConversation(prevMessages => [
      ...prevMessages,
      {type: 'user', text: textUser},
    ]);
    startAi();
  }
  useEffect(() => {
    if (isFocused) {
      getContent();
      setForm('type', 'IATiraDuvidas');
    }
  }, [isFocused]);

  const getContent = () => {
    setLoading(false);
  };

  const [conversation, setConversation] = useState([
    {
      type: 'chat',
      text: 'Olá! Pergunte o que quiser sobre o mercado imobiliário, atendimento ao cliente ou marketing digital. Minha resposta pode demorar um pouquinho, isso é porque eu estou pensando bastante :)',
      showCopy: false,
    },
  ]);

  const scrollViewRef = useRef();

  return (
    <View style={(mainStyles.wrapper, styles.wrapperChat)}>
      <PrivateHeader title={'Wox AI'} />
      <TouchableOpacity
        style={styles.buttonTitle}
        onPress={() => navigation.goBack()}>
        <View style={styles.icArrow}>
          <ArrowLeft />
        </View>
        <View style={styles.icAi}>
          <IA />
          <Text style={mainStyles.woxAiTitle}>WOX AI</Text>
          <Text style={mainStyles.betaIco}>BETA</Text>
        </View>
      </TouchableOpacity>
      <KeyboardAvoidingView behavior="position">
        <View style={styles.chatArea}>
          {loading && (
            <View
              style={{
                flex: 1,
                justifyContent: 'center',
                alignItems: 'center',
              }}>
              <ActivityIndicator size="large" color="#00467F" />
            </View>
          )}

          {!loading && (
            <>
              <View style={styles.boxChatArea}>
                <ScrollView
                  ref={scrollViewRef}
                  onContentSizeChange={() =>
                    scrollViewRef.current.scrollToEnd({animated: true})
                  }
                  style={
                    {
                      // height: '88%',
                      // backgroundColor: '#00f',
                    }
                  }
                  showsVerticalScrollIndicator={false}>
                  <Text style={styles.textTitle}>Converse comigo</Text>
                  <View style={styles.textChatArea}>
                    {conversation.map(message => {
                      if (message.type === 'chat') {
                        return (
                          <BoxBot
                            key={message.text}
                            text={message.text}
                            showCopy={message.showCopy}
                          />
                        );
                      } else if (message.type === 'user') {
                        return (
                          <BoxUser key={message.text} text={message.text} />
                        );
                      }
                    })}
                    {isGenerating && <BoxBotLoading />}
                  </View>
                </ScrollView>
              </View>
            </>
          )}

          <View style={styles.contentBottom}>
            <View style={[mainStyles.container, styles.contentButtons]}>
              <TextInput
                multiline
                editable
                numberOfLines={3}
                style={styles.boxChatTextInput}
                value={textUser}
                placeholder="Tire suas dúvidas"
                onChangeText={text => onChangeText(text)}
                underlineColorAndroid="transparent"
              />
              <TouchableOpacity
                style={[mainStyles.btnBlueNew, styles.contentButtons]}
                onPress={() => addNewMessage()}>
                <ArrowRightChatAi
                  style={{
                    height: 25,
                  }}
                />
              </TouchableOpacity>
            </View>
          </View>
        </View>
      </KeyboardAvoidingView>
    </View>
  );
};

export default IATiraDuvidas;
