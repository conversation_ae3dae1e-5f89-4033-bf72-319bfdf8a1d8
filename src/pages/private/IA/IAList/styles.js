import {Dimensions, Platform, StyleSheet} from 'react-native';

const windowWidth = Dimensions.get('window').width;

const styles = StyleSheet.create({
  wrapper: {
    justifyContent: 'space-between',
  },
  contentBottom: {
    paddingTop: 20,
    paddingBottom: 30,
    shadowRadius: Platform.OS === 'ios' ? 2 : 16,
    shadowOffset: {
      width: 0,
      height: Platform.OS === 'ios' ? -2 : 12,
    },
    shadowColor: Platform.OS === 'ios' ? '#CCC' : '#000',
    elevation: Platform.OS === 'ios' ? 2 : 24,
    shadowOpacity: 1,
    backgroundColor: '#FFF',
  },
  contentButtons: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  button: {
    width: '100%',
  },
  buttonLarge: {
    width: '100%',
  },
  container: {
    maxWidth: 340,
    marginLeft: (windowWidth - 340) / 2,
    marginTop: 10,
  },
  contentOption: {
    flexDirection: 'row',
    alignItems: 'center',
    marginTop: 25,
    marginBottom: 25,
  },
  textStatusBold: {
    fontFamily: 'Ubuntu-Bold',
    color: '#00467F',
    fontSize: 15,
    letterSpacing: 1,
    marginLeft: 15,
  },
  inputText: {
    backgroundColor: '#FFF',
    width: 125,
    paddingLeft: 13,
  },
  buttonCenterBlue: {
    width: 70,
    height: 50,
    marginTop: 10,
    borderRadius: 5,
    marginLeft: 15,
  },
  textBtnCenterBlue: {
    fontSize: 20,
  },
  rowCenter: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 15,
    marginTop: 10,
  },
  rowCenterMargin: {
    flexDirection: 'row',
    alignItems: 'center',
    marginVertical: 20,
  },
  optionTextBold: {
    fontFamily: 'Ubuntu-Bold',
    fontSize: 12,
    letterSpacing: 0.7,
    color: '#4ea1cc',
    lineHeight: 18,
  },
  boxFlex: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    borderColor: '#DADADA',
    borderTopWidth: 1,
    paddingTop: 20,
    paddingBottom: 20,
  },
  boxFlexColumn: {
    flexDirection: 'column',
  },
  boxIconWidth: {
    width: 40,
    flexDirection: 'row',
    justifyContent: 'center',
  },
  boxFlexWithText: {
    flexDirection: 'row',
  },
  textBoxFlex: {
    fontSize: 14,
    letterSpacing: 1,
    width: 230,
    marginLeft: 10,
  },
  textBoxFlexBold: {
    fontWeight: 'bold',
    color: '#00467F',
    fontSize: 20,
    letterSpacing: 1,
    width: 230,
    marginLeft: 10,
    marginTop: -3,
    marginBottom: 10,
  },
  textAboutProcessBold: {
    fontWeight: 'bold',
    color: '#828282',
    fontSize: 12,
    letterSpacing: 0.5,
    width: 280,
    marginTop: 5,
    marginBottom: 5,
  },
  textAboutProcess: {
    fontWeight: 'normal',
    color: '#828282',
    fontSize: 12,
    letterSpacing: 0.5,
    marginLeft: 5,
  },
  btnTextOutlineBlue: {
    fontFamily: 'Ubuntu-Bold',
    color: '#00467F',
    fontSize: 25,
    fontWeight: 'bold',
    letterSpacing: 1,
    textAlign: 'center',
  },
  boxFlexColumn: {
    flexDirection: 'column',
  },
  textSearch: {
    fontFamily: 'Ubuntu-Regular',
    color: '#4EA1CC',
    fontSize: 17,
    letterSpacing: 1,
  },
  searchBox: {
    flexDirection: 'row',
    justifyContent: 'center',
    borderColor: '#DADADA',
    borderBottomWidth: 1,
    paddingBottom: 30,
  },
  input: {
    borderWidth: 2,
    borderColor: '#4EA1CC',
    borderRadius: 5,
    width: 150,
    height: 55,
    paddingLeft: 15,
    paddingRight: 15,
    fontSize: 25,
    textAlign: 'center',
  },
  buttonTitle: {
    backgroundColor: '#2D719F',
    height: 70,
    width: '100%',
    alignItems: 'center',
    justifyContent: 'center',
    flexDirection: 'row',
  },
  icArrow: {
    position: 'absolute',
    left: 20,
  },
  icAi: {
    flexDirection: 'row',
    // alignItems: "center"
  },
  btnTextButtonDate: {
    fontFamily: 'Ubuntu-Regular',
    color: '#FFF',
    fontSize: 15,
    letterSpacing: 0.46,
    textTransform: 'uppercase',
    marginLeft: 12,
  },
  divider: {
    borderBottomWidth: 1,
    borderBottomColor: '#DADADA',
    marginTop: 15,
    marginBottom: 15,
  },
  boxTotal: {
    flexDirection: 'row',
    minHeight: 70,
    marginTop: 10,
    marginBottom: 25,
  },
  totalIcon: {
    backgroundColor: '#90B0C0',
    width: 60,
    alignItems: 'center',
    justifyContent: 'center',
  },
  totalInfos: {
    backgroundColor: '#F2F2F2',
    flex: 1,
    paddingLeft: 10,
    paddingRight: 20,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  textTitle: {
    fontFamily: 'Ubuntu-Regular',
    fontSize: 14,
    color: '#00467F',
    letterSpacing: 1,
    textTransform: 'uppercase',
    textAlign: 'center',
    marginTop: 30,
    marginBottom: 20,
  },
  titleBox: {
    fontFamily: 'Ubuntu-Regular',
    fontSize: 16,
    color: '#00467F',
    letterSpacing: 0.46,
    marginTop: 2,
    marginBottom: 4,
    textTransform: 'uppercase',
    lineHeight: 17.32,
  },
  subtitleBox: {
    fontFamily: 'Ubuntu-Light',
    fontSize: 14,
    color: '#000',
    letterSpacing: 0.46,
    lineHeight: 17.32,
    width: 230,
  },
  boxOption: {
    flexDirection: 'row',
    minHeight: 70,
  },
  icArrowRight: {
    color: '#90B0C0',
    width: 10,
  },
  icEdit: {
    color: '#62829A',
  },
});

export default styles;
