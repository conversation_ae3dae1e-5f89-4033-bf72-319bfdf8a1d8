import {useIsFocused} from '@react-navigation/native';
import React, {useEffect, useState} from 'react';
import {
  ActivityIndicator,
  Image,
  Text,
  TouchableOpacity,
  View,
} from 'react-native';

import PrivateHeader from '../../../../components/headers/PrivateHeader';

import ArrowLeft from '../../../../assets/svgs/ArrowLeftSmallWhite';
import Campanha from '../../../../assets/svgs/Campanha';
import Edit from '../../../../assets/svgs/Edit';
import IA from '../../../../assets/svgs/IA';
import MegaPhone from '../../../../assets/svgs/MegaPhoneLight';

import {ScrollView} from 'react-native-gesture-handler';
import ArrowRightGray from '../../../../assets/svgs/ArrowRight';
import Less2 from '../../../../assets/svgs/Less2';
import More from '../../../../assets/svgs/More';
import mainStyles from '../../../../mainStyles';
import styles from './styles';

const IAList = ({navigation}) => {
  const [showOptions, setShowOptions] = useState(true);

  const isFocused = useIsFocused();

  const [loading, setLoading] = useState(true);

  useEffect(() => {
    if (isFocused) {
      getContent();
    }
  }, [isFocused]);

  const getContent = () => {
    setLoading(false);
  };

  return (
    <View style={mainStyles.wrapper}>
      <PrivateHeader title={'Wox AI'} />
      <ScrollView showsVerticalScrollIndicator={false}>
        <TouchableOpacity
          style={styles.buttonTitle}
          onPress={() => navigation.goBack()}>
          <View style={styles.icArrow}>
            <ArrowLeft />
          </View>
          <View style={styles.icAi}>
            <IA />
            <Text style={mainStyles.woxAiTitle}>WOX AI</Text>
            <Text style={mainStyles.betaIco}>BETA</Text>
          </View>
        </TouchableOpacity>
        <View style={mainStyles.privateContainer}>
          <Text style={styles.textTitle}>O que deseja fazer?</Text>
          {loading && (
            <View
              style={{
                flex: 1,
                justifyContent: 'center',
                alignItems: 'center',
                marginTop: 30,
              }}>
              <ActivityIndicator size="large" color="#00467F" />
            </View>
          )}

          {!loading && (
            <>
              <TouchableOpacity
                style={styles.boxTotal}
                onPress={() => setShowOptions(!showOptions)}>
                <View style={styles.totalIcon}>
                  <MegaPhone />
                </View>
                <View style={styles.totalInfos}>
                  <View>
                    <Text style={styles.titleBox}>Marketing</Text>
                    <Text style={styles.subtitleBox}>03 ferramentas</Text>
                  </View>
                  {showOptions ? <Less2 /> : <More />}
                </View>
              </TouchableOpacity>
              {showOptions && (
                <>
                  <TouchableOpacity
                    style={styles.boxOption}
                    onPress={() => navigation.navigate('IAContentCampanha')}>
                    <View
                      style={[styles.totalIcon, {backgroundColor: '#F2F2F2'}]}>
                      <Campanha />
                    </View>
                    <View
                      style={[styles.totalInfos, {backgroundColor: '#FFF'}]}>
                      <View>
                        <Text
                          style={[styles.titleBox, {textTransform: 'none'}]}>
                          Estratégia
                        </Text>
                        <Text style={styles.subtitleBox}>
                          Criar plano tático para divulgação de mídia digital
                        </Text>
                      </View>
                      <ArrowRightGray style={styles.icArrowRight} />
                    </View>
                  </TouchableOpacity>
                  <View style={styles.divider} />
                  <TouchableOpacity
                    style={styles.boxOption}
                    onPress={() => navigation.navigate('IAContentTexto')}>
                    <View
                      style={[styles.totalIcon, {backgroundColor: '#F2F2F2'}]}>
                      <Edit style={styles.icEdit} />
                    </View>
                    <View
                      style={[styles.totalInfos, {backgroundColor: '#FFF'}]}>
                      <View>
                        <Text
                          style={[styles.titleBox, {textTransform: 'none'}]}>
                          Texto
                        </Text>
                        <Text style={styles.subtitleBox}>
                          Criar textos para minhas peças publicitárias
                        </Text>
                      </View>
                      <ArrowRightGray style={styles.icArrowRight} />
                    </View>
                  </TouchableOpacity>
                  <View style={styles.divider} />
                  <TouchableOpacity
                    style={styles.boxOption}
                    onPress={() => navigation.navigate('IATiraDuvidas')}>
                    <View
                      style={[styles.totalIcon, {backgroundColor: '#F2F2F2'}]}>
                      <Image
                        style={styles.logo}
                        source={require('../../../../assets/iconConverseAi.png')}
                      />
                    </View>
                    <View
                      style={[styles.totalInfos, {backgroundColor: '#FFF'}]}>
                      <View>
                        <Text
                          style={[styles.titleBox, {textTransform: 'none'}]}>
                          Converse comigo
                        </Text>
                        <Text style={styles.subtitleBox}>
                          Pergunte o que quiser sobre o mercado imobiliário
                        </Text>
                      </View>
                      <ArrowRightGray style={styles.icArrowRight} />
                    </View>
                  </TouchableOpacity>
                  <View style={styles.divider} />
                </>
              )}
            </>
          )}
        </View>
      </ScrollView>
      <View style={styles.contentBottom}>
        <View style={[mainStyles.container, styles.contentButtons]}>
          <TouchableOpacity
            style={[mainStyles.btnBlueNew, styles.buttonLarge]}
            onPress={() => navigation.goBack()}>
            <Text style={mainStyles.btnTextBlueNew}>VOLTAR</Text>
          </TouchableOpacity>
        </View>
      </View>
    </View>
  );
};

export default IAList;
