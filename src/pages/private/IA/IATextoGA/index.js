import {useIsFocused} from '@react-navigation/native';
import React, {useEffect, useState} from 'react';
import {ActivityIndicator, Text, TouchableOpacity, View} from 'react-native';
import RenderHtml from 'react-native-render-html';

import PrivateHeader from '../../../../components/headers/PrivateHeader';

import ArrowLeft from '../../../../assets/svgs/ArrowLeftSmallWhite';
import IA from '../../../../assets/svgs/IA';

import {ScrollView} from 'react-native-gesture-handler';
import mainStyles from '../../../../mainStyles';
import styles from './styles';

const IATextoGA = ({navigation}) => {
  const isFocused = useIsFocused();

  const [loading, setLoading] = useState(true);

  useEffect(() => {
    if (isFocused) {
      getContent();
    }
  }, [isFocused]);

  const getContent = () => {
    setLoading(false);
  };

  const source = {
    html: `
        <p>
            Título: Conheça o Guedala Park <br /><br /> Descrição: Descubra o Guedala Park, apartamentos com 2 e 3 dormitórios e vaga na garagem. Lazer completo com salão de festas, churrasqueira, piscina e espaço de coworking. Aproveite essa oportunidade única!
        </p>`,
  };

  const mixedStyle = {
    p: {
      color: '#828282',
      fontFamily: 'Ubuntu-Regular',
      fontSize: 13,
      lineHeight: 18,
      letterSpacing: 1,
    },
  };

  return (
    <View style={mainStyles.wrapper}>
      <PrivateHeader title={'Wox AI'} />
      <ScrollView showsVerticalScrollIndicator={false}>
        <TouchableOpacity
          style={styles.buttonTitle}
          onPress={() => navigation.goBack()}>
          <View style={styles.icArrow}>
            <ArrowLeft />
          </View>
          <View style={styles.icAi}>
            <IA />
            <Text style={mainStyles.woxAiTitle}>WOX AI</Text>
            <Text style={mainStyles.betaIco}>BETA</Text>
          </View>
        </TouchableOpacity>
        <View style={mainStyles.privateContainer}>
          <Text style={styles.textTitle}>Google Adwords</Text>
          {loading && (
            <View
              style={{
                flex: 1,
                justifyContent: 'center',
                alignItems: 'center',
                marginTop: 30,
              }}>
              <ActivityIndicator size="large" color="#00467F" />
            </View>
          )}
          {/* {!loading && leads.length === 0 &&
                        <Text style={styles.textWarningScheduling}>Você não recebeu leads neste dia.</Text>
                    } */}
          {!loading && (
            <>
              <View>
                <RenderHtml
                  contentWidth={0}
                  source={source}
                  tagsStyles={mixedStyle}
                />
                <TouchableOpacity style={styles.btnCenterOutlineBlueDarkNew}>
                  <Text style={styles.btnTextOutlineBlue}>Copiar Texto</Text>
                </TouchableOpacity>
              </View>
            </>
          )}
        </View>
      </ScrollView>
      <View style={styles.contentBottom}>
        <View style={[mainStyles.container, styles.contentButtons]}>
          <TouchableOpacity
            style={[mainStyles.btnBlueNew, styles.buttonLarge]}
            onPress={() => navigation.navigate('IATextoCriado')}>
            <Text style={mainStyles.btnTextBlueNew}>VOLTAR</Text>
          </TouchableOpacity>
        </View>
      </View>
    </View>
  );
};

export default IATextoGA;
