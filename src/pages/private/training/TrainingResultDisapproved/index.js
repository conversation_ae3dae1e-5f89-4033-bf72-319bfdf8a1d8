import React, { useState } from 'react';
import { TextInput, Text, View, TouchableOpacity, Touchable } from 'react-native';

import TrainingWhite from '../../../../assets/svgs/TrainingWhite';
import Bad from '../../../../assets/svgs/BadRed';
import YT from '../../../../assets/svgs/YTWhite';
import ArrowRight from '../../../../assets/svgs/ArrowDownGrey';

import PrivateHeader from '../../../../components/headers/PrivateHeader';

import styles from './styles';
import mainStyles from '../../../../mainStyles';
import { ScrollView } from 'react-native-gesture-handler';

const TrainingResultDisapproved = ({route, navigation}) => {

    const { response } = route.params;

    return (
        <View style={mainStyles.wrapper}>
            <PrivateHeader title={`Treinamentos`} />
            <ScrollView showsVerticalScrollIndicator={false}>
                <View style={styles.container}>
                    <Text style={styles.textTitle}>{response.titulo}</Text>
                    <View style={styles.divider}></View>
                    {/* <View style={styles.boxBorder}>
                        <TouchableOpacity style={styles.rowCenterMargin}>
                            <View style={styles.viewFlex}>
                                <TrainingWhite />
                                <View style={styles.marginIc}>
                                    <Text style={[mainStyles.textService, styles.textService]}>REPROVADO</Text>
                                </View>
                            </View>
                        </TouchableOpacity>
                    </View> */}
                    <View style={styles.boxContent}>
                        <View style={styles.icon}>
                            <Bad />
                        </View>
                        <View style={styles.boxWidth}>
                            <Text style={styles.textBold}>{`Reprovado`}</Text>
                            <Text style={[styles.textRegular, {fontFamily: "Ubuntu-Medium"}]}>{`Não foi dessa vez!`}</Text>
                            <Text style={styles.textRegular}>Você teve acerto de {response.porcentagem_acertos}, <Text style={{fontFamily: "Ubuntu-Medium"}}>{`\nque tal tentar de novo?`}</Text></Text>
                        </View>
                    </View>
                    <View style={styles.divider}></View>
                    {/* <Text style={styles.textBlue}>Revise o material de apoio novamente:</Text>
                    <TouchableOpacity style={styles.rowCenterMargin} onPress={() => navigation.navigate('')}>
                        <View style={styles.viewFlex}>
                            <View style={styles.boxIcon}>
                                <YT />
                            </View>
                            <View style={styles.marginIc}>
                                <View>
                                    <Text style={styles.titleTraining}>Campanha100 Digital - Análise de Crédito</Text>
                                </View>
                                <ArrowRight style={styles.icArrowBtn} />
                            </View>
                        </View>
                    </TouchableOpacity> */}
                </View>
            </ScrollView>
            <View style={styles.contentBottom}>
                <View style={[styles.container, styles.contentButtons]}>
                    <TouchableOpacity style={[mainStyles.btnOutlineBlue, mainStyles.buttonW48]} onPress={() => navigation.navigate('PrivateMain')}>
                        <Text style={mainStyles.btnTextOutlineBlue}>PÁGINA INICIAL</Text>
                    </TouchableOpacity>
                    <TouchableOpacity style={[mainStyles.btnBlueLight, styles.btnRed, mainStyles.buttonW48]} onPress={() => navigation.navigate('TrainingQuestion', { id: response.id })}>
                        <Text style={mainStyles.btnTextCenterBlueLight}>REFAZER TESTE</Text>
                    </TouchableOpacity>
                </View>
            </View>
        </View>
    );
}

export default TrainingResultDisapproved;