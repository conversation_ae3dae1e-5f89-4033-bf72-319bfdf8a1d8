import { StyleSheet, Dimensions, Platform } from 'react-native';

const windowWidth = Dimensions.get('window').width;

const styles = StyleSheet.create({
    wrapper: {
        justifyContent: "space-between"
    },
    contentBottom: {
        paddingTop: 25,      
        paddingBottom: 40,
        shadowRadius: Platform.OS === 'ios' ? 2 : 16,
        shadowOffset: {
            width: 0,
            height: Platform.OS === 'ios' ? -2 : 12,
        },
        shadowColor: Platform.OS === 'ios' ? '#CCC' : "#000",
        elevation: Platform.OS === 'ios' ? 2 : 24,
        shadowOpacity: 1,
        backgroundColor: "#FFF"
    },
    contentButtons: {
        flexDirection: "row",
        justifyContent: "space-between"
    },
    button: {
        width: '100%',
    },
    container: {
        maxWidth: 340,
        marginLeft: ((windowWidth - 340) /2),
        marginTop: 10
    },
    textStatus: {
        fontFamily: "Roboto-Regular",
        color: "#4F4F4F",
        fontSize: 17,
        letterSpacing: 1,
        marginTop: 25,
        marginBottom: 10
    },
    textStatusBold: {
        fontFamily: "Roboto-Bold",
        color: "#979797",
        letterSpacing: 1
    },
    inputText: {
        backgroundColor: "#FFF",
        width: 125,
        paddingLeft: 13,
    },
    buttonCenterBlue: {
        width: 70,
        height: 50,
        marginTop: 10,
        borderRadius: 5,
        marginLeft: 15
    },
    textBtnCenterBlue: {
        fontSize: 20
    },
    marginIc: {
        marginLeft: 20
    }, 
    marginArrow: {
        marginLeft: 50
    },
    viewFlex: {
        flexDirection: "row",
        alignItems: 'center'
    },
    rowCenter: {
        flexDirection: "row",
        justifyContent: "space-between",
        alignItems: 'center',
        marginBottom: 15,
        marginTop: 10
    },
    rowCenterMargin: {
        flexDirection: "row",
        alignItems: 'center',
        justifyContent: "space-between",
        marginVertical: 20
    },
    optionTextBold: {
        fontFamily: "Roboto-Bold",
        fontSize: 12,
        letterSpacing: 0.7,
        color: "#4ea1cc",
        lineHeight: 18
    },
    nameNotice: {
        fontFamily: "Roboto-Regular",
        maxWidth: 280,
        marginBottom: 5,
        fontSize: 18
    }, 
    nameNoticeNoRead: {
        maxWidth: 280,
        marginBottom: 5,
        fontSize: 18,
        color: "#CCC"
    },  
    borderBottom: {
        borderBottomColor: "#BDBDBD",
        borderBottomWidth: 1
    },
    textServiceActive: {
        color: "#1B9C20"
    },
    textNames: {
        fontFamily: "Roboto-Regular",
        fontSize: 13,
        letterSpacing: 0.5,
        color: "#828282"
    },
    textNameBold: {
        fontFamily: "Roboto-Bold"
    },
    textService: {
        color: "#FFF",
        textTransform: "uppercase",
        marginBottom: 0
    },
    boxBorder: {
        borderTopLeftRadius: 12,
        borderTopRightRadius: 12,
        shadowColor: "#000",
        shadowOffset: {
            width: 0,
            height: 12,
        },
        shadowOpacity: 0.58,
        shadowRadius: 16.00,
        elevation: 50,
        backgroundColor: "#00467F",
        paddingHorizontal: 20,
        marginTop: 30
    },
    boxContent: {
        flexDirection: "row",
        alignItems: "center"
    },
    btnLogin: {
        marginBottom: 25
    },  
    textBtn: {
        fontSize: Platform.OS === 'ios' ? 24 : 20
    },
    textTitle: {
        fontFamily: "Ubuntu-Regular",
        color: "#00467F",
        fontSize: Platform.OS === 'ios' ? 19 : 15,
        letterSpacing: 1,
        marginTop: 20
    },
    divider: {
        borderColor: "#DADADA",
        borderBottomWidth: 1,
        marginTop: 25,
        marginBottom: 25
    },
    textBold: {
        color: "#1B9C20",
        fontFamily: "Ubuntu-Medium",
        fontSize: Platform.OS === 'ios' ? 25 : 21,
        letterSpacing: 1,
        marginBottom: 7
    },
    textRegular: {
        color: "#828282",
        fontFamily: "Ubuntu-Regular",
        fontSize: Platform.OS === 'ios' ? 18 : 14,
        letterSpacing: 1,
        lineHeight: 18
    },
    boxWidth: {
        marginLeft: 15,
        maxWidth: 165
    },
    btnRed: {
        backgroundColor: "#FF8689",
        borderColor: "#FF8689"
    },
    textBlue: {
        color: "#00467F",
        fontFamily: "Ubuntu-Regular",
        fontSize: Platform.OS === 'ios' ? 18 : 15,
        marginBottom: 20
    },
    rowCenterMargin: {
        flexDirection: "row",
        alignItems: 'center',
        justifyContent: "space-between",
        marginVertical: 5,
        backgroundColor: "#F2F2F2",
        minHeight: 65
    },
    boxIcon: {
        backgroundColor: "#90B0C0",
        minHeight: 65,
        justifyContent: "center",
        alignItems: "center",
        width: 55    
    },
    boxIconPending: {
        backgroundColor: "#F2F2F2",
        minHeight: 65,
        justifyContent: "center",
        alignItems: "center",
        width: 55 
    },
    marginIc: {
        borderLeftWidth: 2,
        borderLeftColor: "#90B0C0",
        minHeight: 65,
        justifyContent: "space-between",
        paddingHorizontal: 10,
        alignItems: "center",
        flexDirection: "row",
        width: 270
    },
    icArrowBtn: {
        transform: [{ rotate: "-90deg" }]
    },
    titleTraining: {
        fontFamily: "Ubuntu-Regular",
        color: "#00467F",
        fontSize: 15,
        width: 245,
        letterSpacing: 0.46,
        marginRight: 5
    },
    textServiceNoRead: {
        fontFamily: "Ubuntu-Light",
        color: "#FF6542",
        fontSize: 13,
        marginBottom: 0
    },
    buttonLarge: {
        width: "100%"
    }
});

export default styles;