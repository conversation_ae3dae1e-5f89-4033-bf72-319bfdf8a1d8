import React from 'react';
import { TextInput, Text, View, TouchableOpacity, Touchable, Image } from 'react-native';

import TrainingWhite from '../../../../assets/svgs/TrainingWhite';
import EditRed from '../../../../assets/svgs/EditRed';

import PrivateHeader from '../../../../components/headers/PrivateHeader';

import styles from './styles';
import mainStyles from '../../../../mainStyles';
import { ScrollView } from 'react-native-gesture-handler';

const TrainingResultApproved = ({route, navigation}) => {

    const { response } = route.params;

    return (
        <View style={mainStyles.wrapper}>
            <PrivateHeader title={`Treinamentos`} />
            <ScrollView showsVerticalScrollIndicator={false}>
                <View style={styles.container}>
                    <Text style={styles.textTitle}>{response.titulo}</Text>
                    <View style={styles.divider}></View>
                    {/* <View style={styles.boxBorder}>
                        <TouchableOpacity style={styles.rowCenterMargin} onPress={() => navigation.navigate('Notice')}>
                            <View style={styles.viewFlex}>
                                <TrainingWhite />
                                <View style={styles.marginIc}>
                                    <Text style={[mainStyles.textBlueBold, styles.textWhite]}>{response.titulo}</Text>
                                    <Text style={[mainStyles.textService, styles.textService]}>APROVADO</Text>
                                </View>
                            </View>
                        </TouchableOpacity>
                    </View> */}
                    <View style={styles.boxContent}>
                        <View style={styles.icon}>
                        <Image
                            style={styles.profile}
                            source={require('../../../../assets/svgs/medalGreen.png')}
                        />
                        </View>
                        <View style={styles.boxWidth}>
                            <Text style={styles.textBold}>Aprovado</Text>
                            <Text style={[styles.textRegular, {fontFamily: "Ubuntu-Medium"}]}>Parabéns!</Text>
                            <Text style={styles.textRegular}>Você foi aprovado com <Text style={{fontFamily: "Ubuntu-Medium"}}>{response.porcentagem_acertos} de acerto.</Text></Text>
                        </View>
                    </View>
                    <View style={styles.divider}></View>
                    {/* <Text style={styles.textBlue}>Veja outros cursos que ainda estão pendentes:</Text> */}
                    {/* <TouchableOpacity style={styles.rowCenterMargin} onPress={() => navigation.navigate('AboutTrainingStart', { id: item.id })}>
                        <View style={styles.viewFlex}>
                            <View style={styles.boxIcon}>
                                {item.status_andamento === 'Pendente' &&
                                    <View style={styles.boxIconPending}>
                                        <EditRed color="#CCC" style={styles.icBell} />
                                    </View>
                                }
                            </View>
                            <View style={styles.marginIc}>
                                <View>
                                    <Text style={styles.titleTraining}>{item.titulo}</Text>
                                    {item.status_andamento === 'Pendente' &&
                                        <Text style={[mainStyles.textService, styles.textServiceNoRead]}>{item.status_andamento}</Text>
                                    }
                                </View>
                                <ArrowRight style={styles.icArrowBtn} />
                            </View>
                        </View>
                    </TouchableOpacity> */}

                    
                    {/* <TouchableOpacity style={[mainStyles.btnOutlineBlue, styles.btnLogin]} onPress={() => navigation.navigate('TrainingList')}>
                        <Text style={[mainStyles.btnTextOutlineBlue, styles.textBtn]}>OUTROS TREINAMENTOS</Text>
                    </TouchableOpacity> */}
                </View>
            </ScrollView>
            <View style={styles.contentBottom}>
                <View style={styles.container}>
                    <TouchableOpacity style={[mainStyles.btnBlueLight, styles.buttonLarge]} onPress={() => navigation.navigate('PrivateMain')}>
                        <Text style={mainStyles.btnTextCenterBlueLight}>PÁGINA INICIAL</Text>
                    </TouchableOpacity>
                </View>
            </View>
        </View>
    );
}

export default TrainingResultApproved;