import { StyleSheet, Dimensions, Platform } from 'react-native';

const windowWidth = Dimensions.get('window').width;

const styles = StyleSheet.create({
    wrapper: {
        justifyContent: "space-between"
    },
    contentBottom: {
        paddingTop: 20,      
        paddingBottom: 20,
        shadowRadius: Platform.OS === 'ios' ? 2 : 16,
        shadowOffset: {
            width: 0,
            height: Platform.OS === 'ios' ? -2 : 12,
        },
        shadowColor: Platform.OS === 'ios' ? '#CCC' : "#000",
        elevation: Platform.OS === 'ios' ? 2 : 24,
        shadowOpacity: 1,
        backgroundColor: "#FFF"
    },
    contentButtons: {
        flexDirection: "row",
        justifyContent: "space-between"
    },
    button: {
        width: '45%',
    },
    container: {
        maxWidth: 340,
        marginHorizontal: ((windowWidth - 340) /2),
        marginTop: 10
    },
    textStatus: {
        fontFamily: "Roboto-Regular",
        color: "#4F4F4F",
        fontSize: 17,
        letterSpacing: 1,
        marginTop: 25,
        marginBottom: 10
    },
    textStatusBold: {
        fontFamily: "Roboto-Bold",
        color: "#979797",
        letterSpacing: 1
    },
    inputText: {
        backgroundColor: "#FFF",
        width: 125,
        paddingLeft: 13,
    },
    buttonCenterBlue: {
        width: 70,
        height: 50,
        marginTop: 10,
        borderRadius: 5,
        marginLeft: 15
    },
    textBtnCenterBlue: {
        fontSize: 20
    },
    marginIc: {
        marginLeft: 20
    }, 
    marginArrow: {
        marginLeft: 50
    },
    viewFlex: {
        flexDirection: "row",
        alignItems: 'center',
        marginBottom: 20
    },
    rowCenter: {
        flexDirection: "row",
        justifyContent: "space-between",
        alignItems: 'center',
        marginBottom: 15,
        marginTop: 10
    },
    rowCenterMargin: {
        flexDirection: "column",
        marginVertical: 20
    },
    optionTextBold: {
        fontFamily: "Roboto-Bold",
        fontSize: 12,
        letterSpacing: 0.7,
        color: "#4ea1cc",
        lineHeight: 18
    },
    nameNotice: {
        fontFamily: "Roboto-Regular",
        maxWidth: 280,
        marginBottom: 5,
        fontSize: 18
    }, 
    nameNoticeNoRead: {
        maxWidth: 280,
        marginBottom: 5,
        fontSize: 18,
        color: "#CCC"
    },  
    borderBottom: {
        borderBottomColor: "#BDBDBD",
        borderBottomWidth: 1
    },
    textServiceActive: {
        color: "#1B9C20"
    },
    textService: {
        fontFamily: "Ubuntu-Regular",
        color: "#00467F",
        fontSize: Platform.OS === 'ios' ? 19 : 15,
        letterSpacing: 1,
        marginBottom: 15,
        marginTop: 15
    },
    textBold: {
        fontFamily: "Roboto-Bold"
    },
    boxBorder: {
        borderRadius: 12,
        shadowColor: "#000",
        shadowOffset: {
            width: 0,
            height: 9,
        },
        shadowOpacity: 0.48,
        shadowRadius: 11.95,
        elevation: 12,
        backgroundColor: "#FFF",
        paddingHorizontal: 20,
        paddingVertical: 10,
        marginTop: 30
    },
    question: {
        fontFamily: 'Ubuntu-Regular',
        fontSize: Platform.OS === 'ios' ? 16 : 14,
        marginTop: 30,
        marginBottom: 30,
        flexShrink: 1,
        color: "#828282"
    },
    btnLogin: {
        marginBottom: 25
    },  
    textBtn: {
        fontSize: Platform.OS === 'ios' ? 24 : 20
    },
    icTraining: {
        width: 40
    },
    nameTraining: {
        fontFamily: 'Ubuntu-Bold',
        letterSpacing: 0.8,
        color: "#00467F",
        fontSize: Platform.OS === 'ios' ? 20 : 16,
        maxWidth: 230
    },
    buttonFinish: {
        backgroundColor: '#1B9C20',
        borderWidth: 1,
        borderColor: '#1B9C20',
        borderRadius: 5,
        height: 60,
        justifyContent: "center",
        alignItems: "center"
    },
    buttonDate: {
        backgroundColor: "#90B0C0",
        height: 58,
        width: '100%',
        alignItems: "center",
        justifyContent: "center"
    },
    icArrow: {
        position: "absolute",
        top: 23,
        left: 25
    },
    btnTextButtonDate: {
        fontFamily: "Ubuntu-Regular",
        color: "#FFF",
        fontSize: 15,
        letterSpacing: 0.46,
        textAlign: "center",
        width: "80%"
    },
    buttonLarge: {
        width: '100%'
    },
});

export default styles;