import React, { useState, useEffect } from 'react';
import { TextInput, Text, View, TouchableOpacity, ActivityIndicator, Alert, ScrollView } from 'react-native';

import TrainingBlue from '../../../../assets/svgs/TrainingBlue';

import ArrowLeft from '../../../../assets/svgs/ArrowLeftSmallWhite';

import AlertFooterRed from '../../../../components/AlertFooterRed';
import AlertFooterBlue from '../../../../components/AlertFooterBlue';
import PrivateHeader from '../../../../components/headers/PrivateHeader';

import styles from './styles';
import mainStyles from '../../../../mainStyles';

import { useTraining } from '../../../../context/training';

import api from '../../../../services/api';
import AlertBlue from '../../../../components/AlertBlue';
import AlertRed from '../../../../components/AlertRed';

const TrainingQuestion = ({route, navigation}) => {
    const [showConfirmLogout, setShowConfirmLogout] = useState(false);
    const [showConfirmFinish, setShowConfirmFinish] = useState(false);
    const [training, setTraining] = useState(null);
    const [questions, setQuestions] = useState([]);
    const [current, setCurrent] = useState(0);
    const [answers, setAnswers] = useState([]);
    const [loading, setLoading] = useState(true);

    // const { updateQuestions } = useTraining();

    const { id } = route.params;

    useEffect(() => {
        api(`/treinamentos/${id}/avaliacao`).then(res => {
            setTraining(res.data.treinamento);
            setQuestions(res.data.treinamento.avaliacao);
            setLoading(false);
        });
    }, []);

    const getProgressPercent = () => {
        let total = questions.length;
        let compelte = current + 1;
        let percent = (compelte / total) * 100;
        return `${percent}%`;
    }

    const handleNextQuestion = () => {

        if(!answers[current]){
            Alert.alert('Escolha uma opção', 'Para seguir, você precisa escolher uma opção');
            return;
        }

        let next = current + 1;

        if(next === questions.length){
            setShowConfirmFinish(true);
        } else {
            setCurrent(next);
        }
    }

    const handlePrevQuestion = () => {
        let prev = current - 1;
        if(prev >= 0){
            setCurrent(prev);
        } else {
            setShowConfirmLogout(true);
        }
    }

    const handleCheckQuestion = (value) => {
        let toAnswers = [...answers];

        questions.map((q, index) => {
            if(index === current){
                toAnswers[current] = value;
            } else {
                toAnswers[current] = toAnswers[current] ? toAnswers[current] : '';
            }
        });

        setAnswers(toAnswers);
    }

    const handleFinish = () => {
        setLoading(true);

        let data = new FormData();

        questions.map((q, index) => {
            data.append(`avaliacao[${q.id}]`, answers[index]);
        });

        api.post(`/treinamentos/${id}/avaliacao`, data)
            .then(res => {
                if(res.data.status === 'reprovado'){
                    navigation.navigate('TrainingResultDisapproved', { response: res.data });
                } else {
                    navigation.navigate('TrainingResultApproved', { response: res.data });
                }

                clean();
                setLoading(false);
            })
            .catch(err => {
                Alert.alert('Por favor, verifique', err.response.data.message);
            });
    }

    const clean = () => {
        setCurrent(0);
        setAnswers([]);
        setShowConfirmLogout(false);
        setShowConfirmFinish(false);
    }

    return (
        <>
            {showConfirmLogout && 
                <AlertRed
                    title={training.titulo}
                    close={() => setShowConfirmLogout(false)}
                    route={() => navigation.navigate('TrainingList')}
                />
            }
            {showConfirmFinish && 
                <AlertBlue
                    title={training.titulo}
                    close={() => setShowConfirmFinish(false)}
                    route={() => handleFinish()}
                />
            }
            <View style={mainStyles.wrapper}>
                <PrivateHeader title={`Treinamentos`} />
                {loading &&
                    <View style={{flex: 1, justifyContent: "center", alignItems: "center"}}>
                        <ActivityIndicator size="large" color="#00467F" />
                    </View>
                }
                {!loading &&
                <>
                    <ScrollView scrollIndicatorInsets={{ right: 1 }}>
                        <TouchableOpacity style={styles.buttonDate} onPress={() => navigation.goBack()}>
                            <View style={styles.icArrow}>
                                <ArrowLeft />
                            </View>
                            <Text style={styles.btnTextButtonDate}>{training.titulo}</Text>
                        </TouchableOpacity>
                        <View style={styles.container}>
                            <View>
                                <Text style={styles.textService}>Pergunta <Text style={styles.textBold}>{current + 1}</Text> de <Text style={styles.textBold}>{questions.length}</Text></Text>
                                <View style={mainStyles.progressBarNew}>
                                    <View style={[mainStyles.progressNew, { width: getProgressPercent() }]}></View>
                                </View>
                            </View>
                            {/* <View style={styles.boxBorder}>
                                <View style={styles.rowCenterMargin}>
                                    <View style={styles.viewFlex}>
                                        <TrainingBlue style={styles.icTraining} />
                                        <View style={styles.marginIc}>
                                            <Text style={styles.nameTraining}>{training.titulo}</Text>
                                        </View>
                                    </View>
                                </View>
                            </View> */}
                            <View>
                                <Text style={styles.question}>
                                    {current + 1}. {questions[current].titulo}
                                </Text>
                            </View>
                            {questions[current].alternativas.map((a, index) => (
                                <TouchableOpacity key={index} onPress={() => handleCheckQuestion(a.id)}>
                                    {answers[current] === a.id &&
                                        <View style={[mainStyles.questionOptionsNew, mainStyles.optionSelectedNew]}>
                                            <Text style={[mainStyles.textQuestion, mainStyles.textMargin, mainStyles.textSelectedNew]}>{a.titulo}</Text>
                                        </View>
                                    }
                                    {answers[current] !== a.id &&
                                        <View style={mainStyles.questionOptionsNew}>
                                            <Text style={[mainStyles.textQuestion, mainStyles.textMargin]}>{a.titulo}</Text>
                                        </View>
                                    }
                                </TouchableOpacity>
                            ))}
                        </View>
                    </ScrollView>
                    <View style={styles.contentBottom}>
                        <View style={[mainStyles.container, styles.contentButtons]}>              
                            <TouchableOpacity style={[mainStyles.btnOutlineBlue, mainStyles.buttonW48]} onPress={() => handlePrevQuestion()}>
                                <Text style={mainStyles.btnTextOutlineBlue}>VOLTAR</Text>
                            </TouchableOpacity>
                            {(current + 1) < questions.length &&
                                <TouchableOpacity style={[mainStyles.btnBlueLight, styles.btnLogin, mainStyles.buttonW48]} onPress={() => handleNextQuestion()}>
                                    <Text style={mainStyles.btnTextCenterBlueLight}>PRÓXIMA</Text>
                                </TouchableOpacity>
                            }
                            {(current + 1)  === questions.length &&
                                <TouchableOpacity style={[mainStyles.btnBlueLight, styles.btnLogin, mainStyles.buttonW48]} onPress={() => handleNextQuestion()}>
                                    <Text style={mainStyles.btnTextCenterBlueLight}>FINALIZAR</Text>
                                </TouchableOpacity>
                            }
                        </View>
                    </View>
                </>
                }
            </View>
        </>
    );
}

export default TrainingQuestion;