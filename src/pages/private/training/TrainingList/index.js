import React, { useEffect, useRef, useState } from 'react';
import { Text, View, TouchableOpacity, ActivityIndicator, FlatList, Linking } from 'react-native';
import { useIsFocused } from '@react-navigation/core';

import ArrowRight from '../../../../assets/svgs/ArrowDownGrey';

import EditWhite from '../../../../assets/svgs/EditWhite';
import EditRed from '../../../../assets/svgs/EditRed';

import PrivateHeader from '../../../../components/headers/PrivateHeader';
import Search from '../../../../components/Search';

import styles from './styles';
import mainStyles from '../../../../mainStyles';

import api from '../../../../services/api';
import SalesAcademy from "../../../../assets/svgs/SalesAcademy";

const TrainingList = ({ navigation }) => {
    const { isFocused } = useIsFocused();
    
    const [trainings, setTrainings] = useState([]);
    const [page, setPage] = useState(1);
    const [searchText, setSearchText] = useState('');
    const [loading, setLoading] = useState(false);
    const [hasMore, setHasMore] = useState(true);
    const isFirstRender = useRef(true);

    useEffect(() => {
        if (isFocused) getTraingns();
    }, [isFocused]);

    useEffect(() => {
        getTraingns();
    }, [page])

    useEffect(() => {
        console.log('alterada: ' + searchText);
        if (!isFirstRender.current) {
            getTraingns(true);
        } else {
            isFirstRender.current = false;
        }
    }, [searchText])

    const getTraingns = (fromSearch = false) => {
        if (loading) return;
        
        if (!hasMore && !fromSearch) return;

        if(fromSearch){
            setTrainings([]);
            setPage(1);
            setHasMore(true);
        };

        setLoading(true);
        api('/treinamentos', {
            params: {
                page: fromSearch ? 1 : page,
                searchText
            }
        }).then(res => {
            let toTrainings = fromSearch ? [] : trainings;
            toTrainings = [...toTrainings, ...res.data.treinamentos];
            setTrainings(toTrainings);
            setHasMore(res.data._pagination.has_next_page);
            setLoading(false);            
        }).catch((err) => {
            setLoading(false);
            setHasMore(false);
        });
    }

    const nextPage = () => {
        if (!loading) {
            setPage(prevPage => prevPage + 1);
        }
    }

    const renderTraining = ({ item }) => {
        return (
            <View style={styles.borderBottom}>
                <TouchableOpacity style={styles.rowCenterMargin} onPress={() => navigation.navigate('AboutTrainingStart', { id: item.id })}>
                    <View style={styles.viewFlex}>
                        <View style={styles.boxIcon}>
                            {item.status_andamento === 'Pendente' &&
                                <View style={styles.boxIconPending}>
                                    <EditRed color="#CCC" style={styles.icBell} />
                                </View>
                            }
                            {item.status_andamento !== 'Pendente' &&
                                <EditWhite style={styles.icBell} />
                            }
                        </View>
                        <View style={styles.marginIc}>
                            <View>
                                <Text style={styles.titleTraining}>{item.titulo}</Text>
                                {item.status_andamento === 'Pendente' &&
                                    <Text style={[mainStyles.textService, styles.textServiceNoRead]}>{item.status_andamento}</Text>
                                }
                                {item.status_andamento !== 'Pendente' &&
                                    <Text style={[mainStyles.textService, styles.textService]}>{item.status_andamento}</Text>
                                }
                            </View>
                            <ArrowRight style={styles.icArrowBtn} />
                        </View>
                    </View>
                </TouchableOpacity>
            </View>
        )

    }

    const LoadingComponent = () => {
        if (!loading || !hasMore) return <></>;
        return (
            <View style={{ flex: 1, justifyContent: "center", alignItems: "center", paddingVertical: 50  }}>
                <ActivityIndicator size="large" color="#00467F" />
            </View>
        );
    };

    const HeaderComponent = () => (
        <View style={{ marginTop: 15 }}>
            <TouchableOpacity style={styles.rowCenterMargin} onPress={() => Linking.openURL('https://cury.aceflix.com.br/')}>
                <View style={styles.viewFlex}>
                    <View style={[styles.boxIcon, { backgroundColor: "#2D719F" }]}>
                        <SalesAcademy />
                    </View>
                    <View style={[styles.marginIc, { borderLeftColor: "#2D719F"}]}>
                        <View>
                            <Text style={styles.titleTraining}>Sales Academy</Text>
                            <Text style={[mainStyles.textService, styles.textService]}>Clique para conferir</Text>
                        </View>
                        <ArrowRight style={styles.icArrowBtn} />
                    </View>
                </View>
            </TouchableOpacity>
            <View style={styles.divider} />
            <Text style={styles.title}>Outros treinamentos:</Text>
        </View>
    )

    return (
        <View style={mainStyles.wrapper}>
            {searchText === '' && !loading &&
                <Search 
                    searchText={searchText} 
                    setSearchText={setSearchText}
                />
            }
            {searchText !== '' && !loading &&
                <View style={styles.contentBottom}>
                    <View style={[mainStyles.container, styles.contentButtons]}>
                        <TouchableOpacity style={[mainStyles.btnBlueNew, styles.buttonLarge]} onPress={() => setSearchText('')}>
                            <Text style={mainStyles.btnTextBlueNew}>VOLTAR</Text>
                        </TouchableOpacity>
                    </View>
                </View>
            }
            <PrivateHeader title={`Treinamentos`} />
            {loading && trainings.length === 0 &&
                <LoadingComponent />
            }
            {trainings.length > 0 &&
                <FlatList
                    // ListHeaderComponent={HeaderComponent}
                    contentContainerStyle={styles.container}
                    data={trainings}
                    onEndReached={nextPage}
                    renderItem={renderTraining}
                    onEndReachedThreshold={0.1}
                    ListFooterComponent={LoadingComponent}
                    keyExtractor={(item, index) => index.toString()}
                />
            }
            {trainings.length === 0 && !hasMore &&
                <Text style={{ textAlign: 'center', marginTop: 40}}>Nenhum treinamento encontrado</Text>
            }
        </View>
    );
}

export default TrainingList;