import React, { useEffect, useState } from 'react';
import { TextInput, Text, View, TouchableOpacity, ActivityIndicator, ScrollView } from 'react-native';
import { useIsFocused } from '@react-navigation/core';

import TrainingRed from '../../../../assets/svgs/TrainingRed';
import TrainingBlue from '../../../../assets/svgs/TrainingBlue';

import PrivateHeader from '../../../../components/headers/PrivateHeader';

import styles from './styles';
import mainStyles from '../../../../mainStyles';
import api from '../../../../services/api';

import Pdf from 'react-native-pdf';

const TrainingPdf = ({route, navigation}) => {
    const [training, setTraining] = useState(null);
    const [loading, setLoading] = useState(true);

    const { isFocused } = useIsFocused();
    const { id } = route.params;

    useEffect(() => {
        const unsubscribe = navigation.addListener('focus', () => {
            getContents();
        });
      
        return unsubscribe;
    }, [navigation]);
    
    useEffect(() => {
        if(isFocused) getContents();
    }, [isFocused]);

    const getContents = () => {
        api.get(`/treinamentos/${id}/conteudo`);

        api.get(`/treinamentos/${id}`).then(res => {
            setTraining(res.data.treinamento);
            setLoading(false);
        });
    }

    return (
        <View style={mainStyles.wrapper}>
            <PrivateHeader title={`Treinamentos`} />
            {loading &&
                <View style={{flex: 1, justifyContent: "center", alignItems: "center"}}>
                    <ActivityIndicator size="large" color="#00467F" />
                </View>
            }
            {!loading && training === null &&
                <View>
                    <Text>Nenhuma informação encontrada</Text>
                </View>
            }
            {!loading && training !== null &&
            <>
                <ScrollView showsVerticalScrollIndicator={false}>
                    <View style={styles.container}>
                        {/* <View style={styles.boxBorder}>
                            <View style={styles.rowCenterMargin} onPress={() => navigation.navigate('Notice')}>
                                <View style={styles.viewFlex}>
                                    {training.status_andamento === 'Pendente' &&
                                        <TrainingRed style={styles.icBell} />
                                    }
                                    {training.status_andamento !== 'Pendente' &&
                                        <TrainingBlue style={styles.icBell} />
                                    }
                                    <View style={styles.marginIc}>
                                        <Text style={[mainStyles.textBlueBold]}>{training.titulo}</Text>
                                        {training.status_andamento === 'Pendente' &&
                                            <Text style={[mainStyles.textService, styles.textServiceNoRead]}>{training.status_andamento}</Text>
                                        }
                                        {training.status_andamento !== 'Pendente' &&
                                            <Text style={[mainStyles.textService, styles.textService]}>{training.status_andamento}</Text>
                                        }
                                    </View>
                                </View>
                            </View>
                        </View> */}
                        <View>
                            <Pdf
                                trustAllCerts={false}
                                source={{uri: training.conteudo.pdf_url, cache:true}}
                                onLoadComplete={(numberOfPages,filePath)=>{
                                    console.log(`number of pages: ${numberOfPages}`);
                                }}
                                onPageChanged={(page,numberOfPages)=>{
                                    console.log(`current page: ${page}`);
                                }}
                                onError={(error)=>{
                                    console.log(error);
                                }}
                                onPressLink={(uri)=>{
                                    console.log(`Link presse: ${uri}`)
                                }}
                                style={styles.pdf}/>
                            {/* <Text style={styles.aboutTraining}>{training.descricao}</Text> training.conteudo.pdf_url */}
                        </View>
                        {training.has_avaliacao && training.status_andamento === 'Pendente' &&
                            <TouchableOpacity style={mainStyles.btnOutlineBlue} onPress={() => navigation.navigate('TrainingQuestion', { id: training.id })}>
                                <Text style={[mainStyles.btnTextOutlineBlue, styles.textBtn]}>FAZER O TESTE</Text>
                            </TouchableOpacity>
                        }
                    </View>
                </ScrollView>
                <View style={styles.contentBottom}>
                    <View style={[mainStyles.container, styles.contentButtons]}>
                        <TouchableOpacity style={[mainStyles.btnBlueNew, styles.buttonLarge]} onPress={() => navigation.goBack()}>
                            <Text style={mainStyles.btnTextBlueNew}>VOLTAR</Text>
                        </TouchableOpacity>
                    </View>
                </View>
            </>
            }
        </View>
    );
}

export default TrainingPdf;