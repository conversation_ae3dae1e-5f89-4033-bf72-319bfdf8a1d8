import React, { useEffect, useState } from 'react';
import { TextInput, Text, View, TouchableOpacity, ActivityIndicator, Linking } from 'react-native';
import { useIsFocused } from '@react-navigation/core';

import TrainingRed from '../../../../assets/svgs/TrainingRed';
import TrainingBlue from '../../../../assets/svgs/TrainingBlue';
import ArrowLeft from '../../../../assets/svgs/ArrowLeftSmallWhite';

import PrivateHeader from '../../../../components/headers/PrivateHeader';

import styles from './styles';
import mainStyles from '../../../../mainStyles';
import { ScrollView } from 'react-native-gesture-handler';
import api from '../../../../services/api';

const AboutTrainingStart = ({route, navigation}) => {
    const [training, setTraining] = useState(null);
    const [loading, setLoading] = useState(true);

    const { isFocused } = useIsFocused();
    const { id } = route.params;

    console.log(route.params);
    
    useEffect(() => {
        if(isFocused) getTraingn();
    }, [isFocused]);

    useEffect(() => {
        const unsubscribe = navigation.addListener('focus', () => {
            getTraingn();
        });
      
        return unsubscribe;
    }, [navigation]);

    const getTraingn = () => {
        api(`/treinamentos/${id}`).then(res => {
            setTraining(res.data.treinamento);
            console.log(res.data.treinamento)
            setLoading(false);
        });
    }

    return (
        <View style={mainStyles.wrapper}>
            <PrivateHeader title={`Treinamentos`} />
            {loading &&
                <View style={{flex: 1, justifyContent: "center", alignItems: "center"}}>
                    <ActivityIndicator size="large" color="#00467F" />
                </View>
            }
            {!loading && training === null &&
                <View>
                    <Text>Nenhuma informação encontrada</Text>
                </View>
            }
            {!loading && training !== null &&
            <>
                <ScrollView showsVerticalScrollIndicator={false}>
                    <TouchableOpacity style={styles.buttonDate} onPress={() => navigation.goBack()}>
                        <View style={styles.icArrow}>
                            <ArrowLeft />
                        </View>
                        <Text style={styles.btnTextButtonDate}>{training.titulo}</Text>
                    </TouchableOpacity>
                    <View style={styles.container}>
                        {/* <View style={styles.boxBorder}>
                            <View style={styles.rowCenterMargin} onPress={() => navigation.navigate('Notice')}>
                                <View style={styles.viewFlex}>
                                    {training.status_andamento === 'Pendente' &&
                                        <TrainingRed style={styles.icBell} />
                                    }
                                    {training.status_andamento !== 'Pendente' &&
                                        <TrainingBlue style={styles.icBell} />
                                    }
                                    <View style={styles.marginIc}>
                                        <Text style={[mainStyles.textBlueBold]}>{training.titulo}</Text>
                                        {training.status_andamento === 'Pendente' &&
                                            <Text style={[mainStyles.textService, styles.textServiceNoRead]}>{training.status_andamento}</Text>
                                        }
                                        {training.status_andamento !== 'Pendente' &&
                                            <Text style={[mainStyles.textService, styles.textService]}>{training.status_andamento}</Text>
                                        }
                                    </View>
                                </View>
                            </View>
                        </View> */}
                        <View>
                            <Text style={styles.aboutTraining}>{training.descricao}</Text>
                        </View>
                        {training.conteudo.tipo === 'youtube' &&
                            <TouchableOpacity style={[mainStyles.btnBlueNew, styles.btnLogin]} onPress={() => navigation.navigate('TrainingVideo', { training: training })}>
                                <Text style={mainStyles.btnTextBlueNew}>{training.status_andamento === 'Pendente' ? 'INICIAR TREINAMENTO' : 'REVER TREINAMENTO'}</Text>
                            </TouchableOpacity>
                        }
                        {training.conteudo.tipo === 'pdf' &&
                            <TouchableOpacity style={[mainStyles.btnBlueNew, styles.btnLogin]} onPress={() => navigation.navigate('TrainingPdf', { id: training.id })}>
                                <Text style={mainStyles.btnTextBlueNew}>{training.status_andamento === 'Pendente' ? 'INICIAR TREINAMENTO' : 'REVER TREINAMENTO'}</Text>
                            </TouchableOpacity>
                        }
                        {training.has_avaliacao && training.status_andamento === 'Pendente' &&
                            <TouchableOpacity style={[mainStyles.btnBlueNew, styles.btnLogin]} onPress={() => navigation.navigate('TrainingQuestion', { id: training.id })}>
                                <Text style={mainStyles.btnTextBlueNew}>FAZER O TESTE</Text>
                            </TouchableOpacity>
                        }
                        {/* {training.status_andamento !== 'Pendente' &&
                            <TouchableOpacity style={mainStyles.btnOutlineBlue} onPress={() => navigation.navigate('TrainingList')}>
                                <Text style={[mainStyles.btnTextOutlineBlue, styles.textBtn]}>VOLTAR</Text>
                            </TouchableOpacity>
                        } */}
                    </View>
                </ScrollView>
                <View style={styles.contentBottom}>
                    <View style={[mainStyles.container, styles.contentButtons]}>
                        <TouchableOpacity style={[mainStyles.btnBlueNew, styles.buttonLarge]} onPress={() => navigation.goBack()}>
                            <Text style={mainStyles.btnTextBlueNew}>VOLTAR</Text>
                        </TouchableOpacity>
                    </View>
                </View>
            </>
            }
        </View>
    );
}

export default AboutTrainingStart;