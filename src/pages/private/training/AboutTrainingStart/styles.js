import { StyleSheet, Dimensions, Platform } from 'react-native';

const windowWidth = Dimensions.get('window').width;

const styles = StyleSheet.create({
    wrapper: {
        justifyContent: "space-between"
    },
    contentBottom: {
        paddingTop: 25,      
        paddingBottom: 40,
        shadowRadius: Platform.OS === 'ios' ? 2 : 16,
        shadowOffset: {
            width: 0,
            height: Platform.OS === 'ios' ? -2 : 12,
        },
        shadowColor: Platform.OS === 'ios' ? '#CCC' : "#000",
        elevation: Platform.OS === 'ios' ? 2 : 24,
        shadowOpacity: 1,
        backgroundColor: "#FFF"
    },
    contentButtons: {
        flexDirection: "row",
        justifyContent: "space-between"
    },
    button: {
        width: '100%',
    },
    container: {
        maxWidth: 340,
        marginLeft: ((windowWidth - 340) /2),
        marginTop: 10
    },
    textStatus: {
        fontFamily: "Roboto-Regular",
        color: "#4F4F4F",
        fontSize: 17,
        letterSpacing: 1,
        marginTop: 25,
        marginBottom: 10
    },
    textStatusBold: {
        fontFamily: "Roboto-Bold",
        color: "#979797",
        letterSpacing: 1
    },
    inputText: {
        backgroundColor: "#FFF",
        width: 125,
        paddingLeft: 13,
    },
    buttonCenterBlue: {
        width: 70,
        height: 50,
        marginTop: 10,
        borderRadius: 5,
        marginLeft: 15
    },
    textBtnCenterBlue: {
        fontSize: 20
    },
    marginIc: {
        marginLeft: 20
    }, 
    marginArrow: {
        marginLeft: 50
    },
    viewFlex: {
        flexDirection: "row",
        alignItems: 'center'
    },
    rowCenter: {
        flexDirection: "row",
        justifyContent: "space-between",
        alignItems: 'center',
        marginBottom: 15,
        marginTop: 10
    },
    rowCenterMargin: {
        flexDirection: "row",
        alignItems: 'center',
        justifyContent: "space-between",
        marginVertical: 20
    },
    optionTextBold: {
        fontFamily: "Roboto-Bold",
        fontSize: 12,
        letterSpacing: 0.7,
        color: "#4ea1cc",
        lineHeight: 18
    },
    nameNotice: {
        fontFamily: "Roboto-Regular",
        maxWidth: 280,
        marginBottom: 5,
        fontSize: 18
    }, 
    nameNoticeNoRead: {
        maxWidth: 280,
        marginBottom: 5,
        fontSize: 18,
        color: "#CCC"
    },  
    borderBottom: {
        borderBottomColor: "#BDBDBD",
        borderBottomWidth: 1
    },
    textServiceActive: {
        color: "#1B9C20"
    },
    textNames: {
        fontFamily: "Roboto-Regular",
        fontSize: 13,
        letterSpacing: 0.5,
        color: "#828282"
    },
    textNameBold: {
        fontFamily: "Roboto-Bold"
    },
    textService: {
        color: "#4EA1CC",
        textTransform: "uppercase",
        marginBottom: 0
    },
    textServiceNoRead: {
        color: "#FF312E",
        textTransform: "uppercase",
        marginBottom: 0
    },
    boxBorder: {
        borderRadius: 12,
        shadowColor: "#000",
        shadowOffset: {
            width: 0,
            height: 9,
        },
        shadowOpacity: 0.48,
        shadowRadius: 11.95,
        elevation: 12,
        backgroundColor: "#FFF",
        paddingHorizontal: 20,
        paddingVertical: 10,
        marginTop: 30
    },
    aboutTraining: {
        fontFamily: 'Ubuntu-Regular',
        fontSize: Platform.OS === 'ios' ? 17 : 13,
        marginTop: 15,
        marginBottom: 35,
        color: "#666",
        letterSpacing: 1,
        lineHeight: 16
    },
    btnLogin: {
        marginBottom: 25
    },  
    textBtn: {
        fontSize: Platform.OS === 'ios' ? 24 : 20
    },
    buttonDate: {
        backgroundColor: "#90B0C0",
        height: 58,
        width: '100%',
        alignItems: "center",
        justifyContent: "center"
    },
    icArrow: {
        position: "absolute",
        top: 23,
        left: 25
    },
    btnTextButtonDate: {
        fontFamily: "Ubuntu-Regular",
        color: "#FFF",
        fontSize: 15,
        letterSpacing: 0.46,
        textAlign: "center",
        width: "80%"
    },
    buttonLarge: {
        width: '100%'
    },
});

export default styles;