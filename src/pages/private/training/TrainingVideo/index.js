import React, { useCallback, useEffect, useState } from 'react';
import { TextInput, Text, View, TouchableOpacity, ScrollView, Linking } from 'react-native';
import { useIsFocused } from '@react-navigation/core';

import YoutubePlayer from "react-native-youtube-iframe";

import TrainingRed from '../../../../assets/svgs/TrainingRed';
import TrainingBlue from '../../../../assets/svgs/TrainingBlue';

import ArrowLeft from '../../../../assets/svgs/ArrowLeftSmallWhite';

import PrivateHeader from '../../../../components/headers/PrivateHeader';

import styles from './styles';
import mainStyles from '../../../../mainStyles';

import api from '../../../../services/api';

const TrainingVideo = ({route, navigation}) => {
    const { training } = route.params;

    const { isFocused } = useIsFocused();

    const [playing, setPlaying] = useState(true);

    useEffect(() => {
        const unsubscribe = navigation.addListener('focus', () => {
            getContents();
        });
      
        return unsubscribe;
    }, [navigation]);

    useEffect(() => {
        if(isFocused) getContents();
    }, [isFocused]);

    const getContents = () => {
        api.get(`/treinamentos/${training.id}/conteudo`);
    }

    return (
        <View style={mainStyles.wrapper}>
            <PrivateHeader title={`Treinamentos`} />
            <ScrollView showsVerticalScrollIndicator={false}>
                <TouchableOpacity style={styles.buttonDate} onPress={() => navigation.goBack()}>
                    <View style={styles.icArrow}>
                        <ArrowLeft />
                    </View>
                    <Text style={styles.btnTextButtonDate}>{training.titulo}</Text>
                </TouchableOpacity>
                <View style={styles.container}>
                    {/* <View style={styles.boxBorder}>
                        <View style={styles.rowCenterMargin} onPress={() => navigation.navigate('Notice')}>
                            <View style={styles.viewFlex}>
                                {training.status_andamento === 'Pendente' &&
                                    <TrainingRed style={styles.icBell} />
                                }
                                {training.status_andamento !== 'Pendente' &&
                                    <TrainingBlue style={styles.icBell} />
                                }
                                <View style={styles.marginIc}>
                                    <Text style={[mainStyles.textBlueBold]}>{training.titulo}</Text>
                                    {training.status_andamento === 'Pendente' &&
                                        <Text style={[mainStyles.textService, styles.textServiceNoRead]}>{training.status_andamento}</Text>
                                    }
                                    {training.status_andamento !== 'Pendente' &&
                                        <Text style={[mainStyles.textService, styles.textService]}>{training.status_andamento}</Text>
                                    }
                                </View>
                            </View>
                        </View>
                    </View> */}
                    <View style={styles.viewVideo}>
                        <YoutubePlayer
                            height={240}
                            play={playing}
                            videoId={training.conteudo.youtube_id}
                        />
                        <TouchableOpacity style={styles.textVideoBtn} onPress={() => Linking.openURL(`https://www.youtube.com/watch?v=${training.conteudo.youtube_id}`)}>
                            <Text style={styles.textVideo}>Caso não consiga visualizar o vídeo, <Text style={styles.textVideoLink}>clique aqui.</Text></Text>
                        </TouchableOpacity>
                    </View>
                    {training.has_avaliacao && training.status_andamento === 'Pendente' &&
                        <TouchableOpacity style={mainStyles.btnOutlineBlue} onPress={() => navigation.navigate('TrainingQuestion', { id: training.id })}>
                            <Text style={[mainStyles.btnTextOutlineBlue, styles.textBtn]}>FAZER O TESTE</Text>
                        </TouchableOpacity>
                    }
                    {/* {(!training.has_avaliacao || training.status_andamento !== 'Pendente') &&
                        <TouchableOpacity style={mainStyles.btnOutlineBlue} onPress={() => navigation.goBack()}>
                            <Text style={[mainStyles.btnTextOutlineBlue, styles.textBtn]}>VOLTAR</Text>
                        </TouchableOpacity>
                    } */}
                </View>
            </ScrollView>
            <View style={styles.contentBottom}>
                <View style={[mainStyles.container, styles.contentButtons]}>
                    <TouchableOpacity style={[mainStyles.btnBlueNew, styles.buttonLarge]} onPress={() => navigation.goBack()}>
                        <Text style={mainStyles.btnTextBlueNew}>VOLTAR</Text>
                    </TouchableOpacity>
                </View>
            </View>
        </View>
    );
}

export default TrainingVideo;