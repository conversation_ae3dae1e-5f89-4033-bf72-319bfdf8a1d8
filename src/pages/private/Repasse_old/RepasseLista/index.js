import React, { useEffect, useState } from 'react';
import { TextInput, Text, View, TouchableOpacity, ActivityIndicator, SafeAreaView, Alert } from 'react-native';
import { useIsFocused, useNavigation } from '@react-navigation/native';
import moment from 'moment';
import Alert2 from '../../../../assets/svgs/Alert2';
import Like from '../../../../assets/svgs/Like';
import Loading from '../../../../assets/svgs/Loading';
import Check2 from '../../../../assets/svgs/Check2';
import Search2 from '../../../../assets/svgs/Search2';
import Arrow from '../../../../assets/svgs/ArrowRightGray2';
import ArrowLeft from '../../../../assets/svgs/ArrowLeftSmallWhite';
import More from '../../../../assets/svgs/More';
import Less from '../../../../assets/svgs/Less2';

import PrivateHeader from '../../../../components/headers/PrivateHeader';

import styles from './styles';
import mainStyles from '../../../../mainStyles';
import { ScrollView } from 'react-native-gesture-handler';
import api from '../../../../services/api';
import { useAuth } from '../../../../context/auth';

const RepasseLista = ({ navigation, route }) => {
    const { status, title } = route.params;
    const isFocused = useIsFocused();

    const [loading, setLoading] = useState(true);
    const [processos, setProcessos] = useState([]);
    const [search, setSearch] = useState('');
    const [loadingSearch, setLoadingSearch] = useState(false);

    useEffect(() => {
        if(isFocused) getProcessos();

        return () => {
            setProcessos([]);
        }
    }, [isFocused]);

    const getProcessos = () => {
        setLoading(true);
        api.get(`repasses/status/${status}`).then(res => {
            console.log('processos', res.data.processos)
            setProcessos(res.data.processos);
        }).catch(error => {
            console.log(error)
        }).then(() => setLoading(false));
    }

    const handleSearch = () => {
        setLoadingSearch(true);
        setSearch('');
        api.get(`repasses/cvc/${search}`).then(res => {
            navigation.navigate('RepasseDetalhes', { cvc: `CVC-${search}` });
        }).catch(error => {
            Alert.alert('Não encontrado', 'Não encontramos o CVC informado. Por favor, tente novamente.');
            setSearch('');
        }).then(() => setLoadingSearch(false));
        
    }

    return (
        <View style={mainStyles.wrapper}>
            <PrivateHeader title={`Repasse`} />
            <View style={styles.rowOptions}>
                <TouchableOpacity style={styles.buttonDate} onPress={() => navigation.navigate('RepasseBusca')}>
                    <View style={styles.icArrow}>
                        <ArrowLeft />
                    </View>
                    <Text style={styles.btnTextButtonDate}>{title}</Text>
                </TouchableOpacity>
                {/* <TouchableOpacity onPress={() => navigation.navigate('RepasseBusca')}>
                    <View style={styles.contentOption}>
                        <ArrowLeft />
                        <Text style={styles.textStatusBold}>{title}</Text>
                    </View>
                </TouchableOpacity> */}
            </View>
            <ScrollView showsVerticalScrollIndicator={false}>
                <View style={mainStyles.container}>
                    {loading &&
                        <View style={{ flex: 1, justifyContent: "center", alignItems: "center", marginTop: 30 }}>
                            <ActivityIndicator size="large" color="#00467F" />
                        </View>
                    }
                    <Text style={styles.textSearch}>Busque pelo PV:</Text>
                    <View style={styles.searchBox}>
                        <Text style={[styles.textSearch, styles.textBold]}>PV -</Text>
                        <TextInput 
                            style={styles.input} 
                            placeholder="00000"
                            keyboardType="phone-pad"
                            value={search}
                            onChangeText={text => setSearch(text.trim())}
                        /> 
                        <TouchableOpacity style={styles.btnOk} onPress={handleSearch}>
                            <Search2 />
                        </TouchableOpacity>
                    </View>
                    <View style={[styles.divider, {marginBottom: 25}]}></View>
                    {!loading && processos.map((processo, index) => (
                        <Repasse
                            key={index}
                            processo={processo}
                            navigation={navigation}
                        />
                    ))}
                </View>
            </ScrollView>
            <View style={styles.contentBottom}>
                <View style={[mainStyles.container, styles.contentButtons]}>
                    <TouchableOpacity
                        style={[mainStyles.btnBlueNew, styles.buttonLarge]}
                        onPress={() => navigation.goBack()}>
                        <Text style={mainStyles.btnTextBlueNew}>VOLTAR</Text>
                    </TouchableOpacity>
                </View>
            </View>
        </View>
    );
}

const Repasse = ({processo, navigation}) => {
    const [showAbout, setShowAbout] = useState(false);
    return (
        <TouchableOpacity onPress={() => setShowAbout(!showAbout)}>
            <View style={styles.boxFlex}>
                <View style={styles.boxFlexColumn}>
                    <View style={styles.boxFlexWithText}>
                        <View style={styles.boxFlexColumn}>
                            {/* <Text style={styles.textBoxFlex}>{processo.processo.titulo}</Text> */}
                            <View style={styles.boxTitle}>
                                <Text style={styles.textBoxFlexBold}>{processo.cvc}</Text>
                                <View style={styles.label}>
                                    <Text style={styles.textLabel}>{processo.processo.status !== 'futuro-vencido' ? 'Há 18 dias' : 'Até 12/12/12'}</Text>
                                </View>
                            </View>
                            <Text style={styles.textBoxFlex}>Em assinatura de contrato - finan...</Text>
                        </View>
                        {showAbout ? <Less /> : <More />}
                    </View>
                    {showAbout &&
                        <>
                        <TouchableOpacity style={styles.resultPV} onPress={() => navigation.navigate('RepasseDetalhes', { cvc: processo.cvc })}>
                            <View>
                                {/* <Text style={styles.textAboutProcessBold}>Data da venda: <Text style={styles.textAboutProcess}>{moment(processo?.data).format('DD/MM/YYYY')}</Text></Text> */}
                                <Text style={styles.textAboutProcessBold}>Corretor: <Text style={styles.textAboutProcess}>{processo.corretor}</Text></Text>
                                <Text style={styles.textAboutProcessBold}>Produto: <Text style={styles.textAboutProcess}>{processo.produto}</Text></Text>
                                <Text style={styles.textAboutProcessBold}>Pendência: <Text style={styles.textAboutProcess}>{processo.pendencia}</Text></Text>
                            </View>
                            <Arrow />
                        </TouchableOpacity>
                        <View style={styles.divider}></View>
                        </>
                    }
                </View>
            </View>
        </TouchableOpacity>
    )
}
export default RepasseLista;