import React, { useEffect, useState } from 'react';
import { TextInput, Text, View, TouchableOpacity, ActivityIndicator, useWindowDimensions, Alert } from 'react-native';
import { useIsFocused, useNavigation } from '@react-navigation/native';
import moment from 'moment';
import Like from '../../../../assets/svgs/Like';
import Loading from '../../../../assets/svgs/Loading';
import Check from '../../../../assets/svgs/Check3';
import ArrowLeft from '../../../../assets/svgs/ArrowLeftSmallWhite';
import Alert2 from '../../../../assets/svgs/Alert3';
import FGTS from '../../../../assets/svgs/FGTS';

import PrivateHeader from '../../../../components/headers/PrivateHeader';

import styles from './styles';
import mainStyles from '../../../../mainStyles';
import { ScrollView } from 'react-native-gesture-handler';
import api from '../../../../services/api';
import { useAuth } from '../../../../context/auth';
import { color } from 'react-native/Libraries/Components/View/ReactNativeStyleAttributes';
import RenderHtml from 'react-native-render-html';

const RepasseDetalhes = ({ navigation, route }) => {
    const { cvc } = route.params;
    const isFocused = useIsFocused();

    const contentWidth = useWindowDimensions().width;

    const [loading, setLoading] = useState(true);
    const [processo, setProcesso] = useState(null);
    
    useEffect(() => {
        if(isFocused){
            getProcesso();
        }

        return () => {
            setProcesso(null);
        }
    }, [isFocused]);

    const getProcesso = () => {
        setLoading(true);

        const cvcArr = cvc.split('-');
        const convertedCvc = cvcArr.length > 1 ? cvcArr[1] : cvc;

        api.get(`repasses/cvc/${convertedCvc}`).then(res => {
            setProcesso(res.data.processo);
        }).catch(error => {
            console.log(error);
        }).then(() => setLoading(false));
    }

    return (
        <View style={mainStyles.wrapper}>
            <PrivateHeader title={`Repasse`} />
            {loading &&
                <View style={{ flex: 1, justifyContent: "center", alignItems: "center" }}>
                    <ActivityIndicator size="large" color="#00467F" />
                </View>
            }
            {!loading &&
            <>
                <TouchableOpacity style={styles.buttonDate} onPress={() => navigation.navigate('RepasseBusca')}>
                    <View style={styles.icArrow}>
                        <ArrowLeft />
                    </View>
                    <Text style={styles.btnTextButtonDate}>{cvc}</Text>
                </TouchableOpacity>
                <ScrollView showsVerticalScrollIndicator={false}>
                    <View style={mainStyles.container}>
                        <View style={styles.borderTop}>
                            {/* <Text style={styles.textUpdate}>ATUALIZADO EM: {moment(processo?.atualizado_em).format('DD/MM/YYYY')}</Text> */}
                            <View>
                                <View style={styles.boxBoxShadow}>
                                    <View style={styles.boxIconWidth}>
                                        <Alert2 style={{width: 38}} />
                                        {/* <Check style={{width: 35}} /> */}
                                        {/* <FGTS /> */}
                                    </View>
                                    <View>
                                        <Text style={styles.textResult}>{processo?.processo?.titulo}</Text>
                                        <Text style={styles.textLabel}>{processo?.processo?.status !== 'futuro-vencido' ? 'Nesse status há 18 dias' : 'Resolver até: 12/12/12'}</Text>
                                    </View>
                                </View>
                                {/* <Text style={styles.datePay}>{processo?.titulo}</Text>
                                <Text style={styles.updatePendency}>{processo?.subtitulo}</Text> */}
                            </View>
                        </View>
                        {processo?.texto &&
                            <RenderHtml 
                            source={{ html: processo.texto }} 
                            contentWidth={contentWidth} 
                            />
                        }
                    </View>
                </ScrollView>
                <View style={styles.contentBottom}>
                    <View style={[mainStyles.container, styles.contentButtons]}>
                        <TouchableOpacity
                            style={[mainStyles.btnBlueNew, styles.buttonLarge]}
                            onPress={() => navigation.goBack()}>
                            <Text style={mainStyles.btnTextBlueNew}>VOLTAR</Text>
                        </TouchableOpacity>
                    </View>
                </View>
            </>
            }
        </View>
    );
}

export default RepasseDetalhes;