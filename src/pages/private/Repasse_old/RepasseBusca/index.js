import React, { useEffect, useState } from 'react';
import { TextInput, Text, View, TouchableOpacity, ActivityIndicator, SafeAreaView, Alert, Platform, KeyboardAvoidingView } from 'react-native';
import { useIsFocused, useNavigation } from '@react-navigation/native';
import moment from 'moment';
import Alert2 from '../../../../assets/svgs/Alert2';
import Like from '../../../../assets/svgs/Like';
import Loading from '../../../../assets/svgs/Loading';
import Check2 from '../../../../assets/svgs/Check2';
import Search2 from '../../../../assets/svgs/Search2';
import Arrow from '../../../../assets/svgs/ArrowRightGray2';

import PrivateHeader from '../../../../components/headers/PrivateHeader';

import styles from './styles';
import mainStyles from '../../../../mainStyles';
import { ScrollView } from 'react-native-gesture-handler';
import api from '../../../../services/api';
import { useAuth } from '../../../../context/auth';

const RepasseBusca = ({ navigation }) => {
    const isFocused = useIsFocused();

    const [loading, setLoading] = useState(true);
    const [loadingSearch, setLoadingSearch] = useState(false);

    const [processos, setProcessos] = useState([]);
    const [atualizadoEm, setAtualizadoEm] = useState(null);

    const [search, setSearch] = useState('');
    
    useEffect(() => {
        if(isFocused) getData();
    }, []);
    
    useEffect(() => {
        getData();
    }, [isFocused]);

    const getData = () => {
        setLoading(true);
        api.get(`repasses`).then(res => {
            setProcessos(res.data?.processos ?? []);
            setAtualizadoEm(res.data?.atualizado_em ?? '');

            setLoading(false);
        }).catch(error => {
            console.log(error)
        }).then(() => setLoading(false));
    }

    const handleSearch = () => {
        setLoadingSearch(true);
        setSearch('');
        api.get(`repasses/cvc/${search}`).then(res => {
            navigation.navigate('RepasseDetalhes', { cvc: `CVC-${search}` });
        }).catch(error => {
            Alert.alert('Não encontrado', 'Não encontramos o CVC informado. Por favor, tente novamente.');
            setSearch('');
        }).then(() => setLoadingSearch(false));
        
    }

    const Total = ({total}) => {
        return (
            <View>
                {loading &&
                    <View style={{ flex: 1, justifyContent: "center", alignItems: "center", }}>
                        <ActivityIndicator size="large" color="#00467F" />
                    </View>
                }
                {!loading &&
                    <Text style={styles.textBoxFlexNumber}>{total}</Text>
                }
            </View>
        );
    }

    return (
        <KeyboardAvoidingView behavior={Platform.OS === 'ios' ? 'position' : 'none'} style={mainStyles.wrapper}>
            <PrivateHeader title={`Repasse`} />
            <View style={mainStyles.container}>
                <Text style={styles.textSearch}>Busque pelo PV:</Text>
                    <SafeAreaView>
                        {!loadingSearch &&
                            <View style={styles.searchBox}>
                                <Text style={[styles.textSearch, styles.textBold]}>PV -</Text>
                                <TextInput 
                                    style={styles.input} 
                                    placeholder="00000"
                                    keyboardType="phone-pad"
                                    value={search}
                                    onChangeText={text => setSearch(text.trim())}
                                /> 
                                <TouchableOpacity style={styles.btnOk} onPress={handleSearch}>
                                    <Search2 />
                                </TouchableOpacity>
                            </View>
                        }
                        {loadingSearch &&
                            <View style={{ flex: 1, justifyContent: "center", alignItems: "center", marginBottom: 20 }}>
                                <ActivityIndicator size="large" color="#00467F" />
                            </View>
                        }
                    </SafeAreaView>
                <View style={styles.divider}></View>
                <View style={styles.rowOptions}>
                    <View style={styles.contentOption}>
                        {atualizadoEm &&
                            <Text style={styles.textStatus}>{`Atualizado em ${moment(atualizadoEm).format('DD/MM/YYYY')}`}</Text>
                        }
                        <Text style={styles.textStatus}>{`Selecione a etapa desejada:`}</Text>
                    </View>
                </View>
            </View>
            <ScrollView keyboardShouldPersistTaps={`handled`} showsVerticalScrollIndicator={false}>
                <View style={mainStyles.container}>
                    {processos.map((processo, index) => (
                        <TouchableOpacity 
                            key={index}
                            onPress={() => navigation.navigate('RepasseLista', {
                                title: processo.processo.titulo,
                                status: processo.processo.status
                            })}
                        >
                            <View style={styles.boxFlex}>
                                <View style={styles.boxFlexWithText}>
                                    <Text style={styles.textBoxFlex}>{processo.processo.titulo}</Text>
                                </View>
                                <Total total={processo.total} />
                                <Arrow />
                            </View>
                        </TouchableOpacity>
                    ))}
                </View>
            </ScrollView>
            <View style={styles.contentBottom}>
                <View style={[mainStyles.container, styles.contentButtons]}>
                    <TouchableOpacity
                        style={[mainStyles.btnBlueNew, styles.buttonLarge]}
                        onPress={() => navigation.goBack()}>
                        <Text style={mainStyles.btnTextBlueNew}>VOLTAR</Text>
                    </TouchableOpacity>
                </View>
            </View>
        </KeyboardAvoidingView>
    );
}

export default RepasseBusca;