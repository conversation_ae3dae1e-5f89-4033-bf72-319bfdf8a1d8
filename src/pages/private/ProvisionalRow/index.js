import React, { useState, useEffect } from 'react';
import { Text, View, TouchableOpacity, Alert, ActivityIndicator } from 'react-native';

import { useService } from '../../../context/service';

import Info from '../../../assets/svgs/Info';
import Products from '../../../assets/svgs/Products';
import TrainingBlue from '../../../assets/svgs/TrainingBlue';

import PrivateHeader from '../../../components/headers/PrivateHeader';

import styles from './styles';
import mainStyles from '../../../mainStyles';
import AlertFooter from '../../../components/AlertFooter';
import RowCheckin from '../../../components/RowCheckin';

import api from '../../../services/api';
import { ScrollView } from 'react-native-gesture-handler';
import { useAuth } from '../../../context/auth';

const ProvisionalRow = ({ navigation }) => {

    const [loading, setLoading] = useState(false);
    const [confirmExit, setConfirmExit] = useState(false);
    const [row, setRow] = useState(false);


    const { exit, queue } = useService();
    const { user, hasPermission } = useAuth();


    const handleExit = async () => {
        setLoading(true);
        setConfirmExit(false);
        const result = await exit();
        if (result) {
            navigation.navigate('PrivateMain');
        } else {
            Alert.alert('Erro', 'Não foi possível sair da fila, por favor, tente novamente.');
            setLoading(false);
        }
    }

    return (
        <>
            <View style={mainStyles.wrapper}>
                {row &&
                    <RowCheckin close={() => setRow(false)} />
                }
                {confirmExit &&
                    <AlertFooter
                        text={
                            <Text>
                                {`Você deseja realmente\nsair da fila?`}
                            </Text>
                        }
                        btnText={`SIM, SAIR DA FILA`}
                        btnBorder={true}
                        close={() => setConfirmExit(false)}
                        action={() => handleExit()}
                    />
                }
                <PrivateHeader title={`Fila`} back={() => navigation.navigate('PrivateMain')} />
                {loading &&
                    <View style={{ flex: 1, justifyContent: "center", alignItems: "center" }}>
                        <ActivityIndicator size="large" color="#00467F" />
                    </View>
                }
                {!loading &&
                    <>
                        <ScrollView showsVerticalScrollIndicator={false}>
                            <View style={styles.container}>
                                <View style={mainStyles.bgHourCheckin}>
                                    <Text style={mainStyles.textHourCheckin}>Horário do seu check in: {user.fila.checkin_horario}</Text>
                                </View>
                                {user.fila.sorteio_horario &&
                                    <View>
                                        <Text style={mainStyles.sorteioText}>Horário do sorteio: {user.fila.sorteio_horario}</Text>
                                        <View style={styles.divider}></View>
                                    </View>
                                }
                                {queue.tipo_fila === 'provisoria' &&
                                    <>
                                        <View style={styles.provisionalRow}>
                                            <Text style={styles.provisionalText}><Text style={styles.spanColor}>Lembre-se:</Text> essa é uma fila provisória.</Text>
                                            <Text style={styles.provisionalText}>Sua posição irá <Text style={styles.spanColor}>MUDAR APÓS O SORTEIO.</Text> </Text>
                                        </View>
                                        <View style={styles.divider}></View>
                                    </>
                                }
                                <View style={styles.rowOptions}>
                                    <View style={styles.contentOption}>
                                        <Text style={styles.textStatus}>Status e posição na fila:</Text>
                                        <Text style={styles.textWait}>{`Aguarde ser anunciado`}</Text>
                                    </View>
                                </View>
                                <View style={styles.contentOption}>
                                    <View style={[styles.option, queue.posicao > 3 ? styles.optionWhite : styles.optionRed]}>
                                        {/* <View style={[styles.optionBgColor, queue.posicao > 3 ? styles.optionBgGrey : styles.optionBgRed]}></View> */}
                                        <View>
                                            <Text style={[styles.optionNumber, queue.posicao > 3 ? styles.optionNumberBlue : styles.optionNumberWhite]}>{queue.posicao < 10 ? `0${queue.posicao}` : queue.posicao}</Text>
                                        </View>
                                    </View>
                                </View>
                                <View style={styles.contentTop}>
                                    <View style={styles.contentButtons}>
                                        {hasPermission('checkin/ver-fila') &&
                                            <TouchableOpacity style={[mainStyles.btnCenterBlue, styles.buttonCenterBlue]} onPress={() => {
                                                navigation.navigate('RowCheckin')
                                            }}>
                                                <Text style={[mainStyles.btnTextCenterBlue, styles.textBtnCenterBlue]}>VER FILA</Text>
                                            </TouchableOpacity>
                                        }
                                        <TouchableOpacity style={[mainStyles.btnCenterOutlineBlue, styles.buttonCenterOutlineBlue, !hasPermission('checkin/ver-fila') ? { width: '100%' } : null]} onPress={() => setConfirmExit(true)}>
                                            <Text style={[mainStyles.btnTextCenterOutlineBlue, styles.textBtnCenterOutlineBlue]}>SAIR DA FILA</Text>
                                        </TouchableOpacity>
                                    </View>
                                </View>
                            </View>
                        </ScrollView>
                    </>
                }
                <View style={styles.contentBottom}>
                    <View style={styles.contentButton}>
                        <TouchableOpacity style={[mainStyles.btnBlueNew, styles.buttonLarge]} onPress={() => navigation.navigate('PrivateMain')}>
                            <Text style={mainStyles.btnTextBlueNew}>VOLTAR</Text>
                        </TouchableOpacity>
                    </View>
                </View>
            </View>
        </>
    );
}

export default ProvisionalRow;