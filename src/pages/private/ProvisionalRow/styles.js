import { StyleSheet, Dimensions, Platform } from 'react-native';

const windowWidth = Dimensions.get('window').width;

const styles = StyleSheet.create({
    wrapper: {
        justifyContent: "space-between"
    },
    container: {
        width: windowWidth * 0.9,
        marginLeft: windowWidth * 0.05,
        marginTop: 30,
        zIndex: 1
    },
    boxShadow: {
        height: 20,
        backgroundColor: "#FFF",
        elevation: 5,
        shadowColor: "#000",
        marginBottom: 0,
        position: "absolute",
        top: -20,
        right: 0,
        left: 0
    },
    contentBottom: {
        paddingTop: 25,      
        paddingBottom: 40,
        shadowRadius: Platform.OS === 'ios' ? 2 : 16,
        shadowOffset: {
            width: 0,
            height: Platform.OS === 'ios' ? -2 : 12,
        },
        shadowColor: Platform.OS === 'ios' ? '#CCC' : "#000",
        elevation: Platform.OS === 'ios' ? 2 : 24,
        shadowOpacity: 1,
        backgroundColor: "#FFF",
        position: "relative"
    },
    divider: {
        borderTopColor: "#DADADA",
        borderTopWidth: 1,
        marginTop: 30
    },
    rowOptions: {
        flexDirection: "row",
        justifyContent: "space-between"
    },
    provisionalText: {
        fontFamily: "Ubuntu-Regular",
        color: "#452323",
        marginTop: Platform.OS === 'ios' ? 8 : 4,
        fontSize: Platform.OS === 'ios' ? 18 : 16,
        lineHeight: 15,
        letterSpacing: 1,
        fontSize: 12
    },
    spanColor: {
        color: "#FF6542"
    },
    textStatus: {
        fontFamily: "Ubuntu-Regular",
        color: "#2D719F",
        letterSpacing: 1,
        marginTop: 30,
        fontSize: Platform.OS === 'ios' ? 16 : 14
    },
    textWait: {
        color: '#2D719F',
        fontFamily: 'Ubuntu-Medium',
        fontSize: 22,
        letterSpacing: 1,
        marginTop: 5
    },
    option: {
        alignItems: 'center',
        height: 160,
        backgroundColor: "#FFF",
        alignItems: 'center',
        justifyContent: 'center',
        borderRadius: 12,
        // shadowColor: "#000",
        // shadowOffset: {
        //     width: 0,
        //     height: 9,
        // },
        // shadowOpacity: 0.1,
        // shadowRadius: 11.95,
        // elevation: 12,
        marginTop: 20,
        position: 'relative'
    },
    optionWhite: {
        backgroundColor: "#90B0C0",
    },
    optionRed: {
        backgroundColor: "#FF6542B3",
    },
    optionBgGrey: {
        backgroundColor: "#F3F3F3",
    },
    optionBgRed: {
        backgroundColor: "#E02523",
    },
    // optionBgColor: {
    //     height: 70,
    //     position: 'absolute',
    //     bottom: 0,
    //     left: 0,
    //     right: 0,
    //     borderBottomRightRadius: 12,
    //     borderBottomLeftRadius: 12
    // },
    optionNumber: {
        fontSize: 85,
        fontFamily: 'Ubuntu-Regular',
        color: "#00467F"
    },
    optionNumberBlue: {
        color: "#FFF"
    },
    optionNumberWhite: {
        color: "#FFF"
    },
    contentTop: {        
        marginTop: 30
    },
    contentButtons: {
        flexDirection: "row",
        justifyContent: "space-between"
    },
    buttonCenterBlue: {
        width: '46%',
        backgroundColor: "#2D719F",
        borderRadius: 0
    },
    buttonCenterOutlineBlue: {
        width: '46%',
        borderColor: "#000",
        borderRadius: 0
    },
    textBtnCenterBlue: {
        fontSize: 14,
        fontFamily: "Ubuntu-Regular"
    },
    textBtnCenterOutlineBlue: {
        fontSize: 14,
        color: "#000",
        fontFamily: "Ubuntu-Regular"
    },
    boxWaitingRow: {
        flexDirection: "row",
        backgroundColor: "#FFF",
        justifyContent: "space-between",
        shadowColor: "#000",
        shadowOffset:{
        width: 0,
        height: 2,
        },
        shadowOpacity: 0.23,
        shadowRadius: 2.62,
        elevation: 4,
        borderRadius: 14,
        paddingTop: 20,
        paddingBottom: 14,
        paddingLeft: 26,
        paddingRight: 26,
        height: 150,
        marginTop: 60,
    },
    contentWaiting: {
        marginTop: 17
    },
    textWaiting: {
        fontFamily: "Roboto-Regular",
        color: "#979797",
        letterSpacing: 1,
    },
    textWaitingBold: {
        fontFamily: "Roboto-Bold",
        color: "#979797",
        letterSpacing: 1,
        marginTop: -4
    },
    optionWaiting: {
        alignItems: "center"
    },
    optionText: {
       fontSize: 12,
       letterSpacing: 0.7,
       color: "#4ea1cc",
       marginTop: 5 
    },
    contentButton: {
        flexDirection: "row",
        justifyContent: "center"
    },
    buttonLarge: {
        width: '90%',
        elevation: 0,
        height: 50
    }
});

export default styles;