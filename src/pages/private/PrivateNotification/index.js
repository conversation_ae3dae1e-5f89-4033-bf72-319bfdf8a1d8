import React, { useEffect, useState } from 'react';
import { Text, View, ScrollView, TouchableOpacity, useWindowDimensions, Image, ActivityIndicator, Alert, Linking } from 'react-native';

import RenderHtml from 'react-native-render-html';

import PrivateHeader from '../../../components/headers/PrivateHeader';

import styles from './styles';
import mainStyles from '../../../mainStyles';
import ImageLoad from 'react-native-image-placeholder';

import { API_HOMOLOG_BASE_STORAGE, API_PRODUCTION_BASE_STORAGE } from '@env';
import { useAuth } from '../../../context/auth';
import api from '../../../services/api';
import { useIsFocused } from '@react-navigation/native';

import Phone from '../../../assets/svgs/Celphone';
import Email from '../../../assets/svgs/Email';
import Whats from '../../../assets/svgs/Whats2';

const PrivateNotification = ({route, navigation}) => {
    const isFocused = useIsFocused();
    const [loading, setLoading] = useState(true);
    const [notification, setNotification] = useState(null);

    const { production } = useAuth();
    let baseImageUrl = production ? API_PRODUCTION_BASE_STORAGE : API_HOMOLOG_BASE_STORAGE;

    const { id } = route.params;

    const contentWidth = useWindowDimensions().width;
    const [imageWidth, setImageWidth] = useState(contentWidth * 0.9);
    const [imageHeight, setImageHeight] = useState(contentWidth * 0.9);

    useEffect(() => {
        if(isFocused) getNotification();
    }, [isFocused, id]);

    const getNotification = () => {
        setLoading(true);
        api.get(`/notificacoes/${id}`).then(res => {
            console.log(res);
            const toNotification = createLinks(res.data.notificacao);
            setNotification(toNotification);
        }).catch(error => {
            console.log(error);
        }).then(() => setLoading(false));
    }

    const ativarCliente = type => {
        setLoading(true);

        api.post(`/crm/ativar-cliente/${notification.corretor_cliente_id}/${type}`).then(res => {
            console.log(res);

            switch (type) {
                case 'whatsapp':
                    Linking.openURL(`https://wa.me/${res.data.whatsapp_phone}?text=${res.data.whatsapp_message}`);
                    break;
                case 'phone':
                    Linking.openURL(`tel:${res.data.phone_number}`);
                    break;
                case 'email':
                    Linking.openURL(`mailto:${res.data.email_address}`);
                    break;
            }
            
        }).catch(error => {
            console.log(error);
            console.log(error?.response);
            Alert.alert('Não foi possível chamar', 'Por favor, tente novamente.');
        }).then(() => setLoading(false));
    }

    useEffect(() => {
        if(notification){
            getImageSize();
        }
    }, [notification]);

    const createLinks = toNotification => {
        var Rexp = /((http|https|ftp):\/\/[\w?=&.\/-;#~%-]+(?![\w\s?&.\/;#~%"=-]*>))/g;
        toNotification.body = toNotification.body.replace(Rexp, "<a href='$1'>$1</a>");
        return toNotification;
    }

    const geImageUrl = url => {
        if(url.includes('https://') || url.includes('http://')){
            return url;
        }
        return `${baseImageUrl}${notification.imagem_url}`;
    }

    const getImageSize = () => {
        if(!notification.imagem_url) return;
        Image.getSize(notification.imagem_url, (width, height) => {
            let toImageWidth = contentWidth * 0.9;
            let ratio = toImageWidth / width;
            let toImageHeight = height * ratio;
            setImageWidth(toImageWidth);
            setImageHeight(toImageHeight);
        });
    }

    return (
        <View style={mainStyles.wrapper}>
            {loading &&
                <View style={{ flex: 1, justifyContent: "center", alignItems: "center" }}>
                    <ActivityIndicator size="large" color="#00467F" />
                </View>
            }
            {!loading && notification === null &&
                <Text>Não foi possível encontrar a notificação</Text>
            }
            {!loading && notification !== null &&
                <>
                    <PrivateHeader title={`Notificações`} />
                    <ScrollView style={styles.container} showsVerticalScrollIndicator={false}>
                        <View style={[mainStyles.privateContainer, styles.pdBottom]}>
                            <View style={styles.notificationContainer}>
                                <View>
                                    {notification.from !== null &&
                                        <Text style={styles.from}>Mensagem de {notification.from.gestor.apelido}</Text>
                                    }
                                    <Text style={styles.title}>{notification.subject}</Text>
                                    <RenderHtml 
                                        source={{ html: notification.body }} 
                                        contentWidth={contentWidth} 
                                    />
                                    {notification.imagem_url &&
                                        <ImageLoad 
                                            source={{ uri: geImageUrl(notification.imagem_url) }}
                                            loadingStyle={{ size: 'large', color: 'gray' }}
                                            style={{ width: imageWidth, height: imageHeight, marginTop: 20}}
                                            customImagePlaceholderDefaultStyle={{ display: 'none' }}
                                            resizeMode="contain"
                                        />
                                    }
                                </View>
                                {/* {notification.is_lead_ativo === true && notification.show_btn_whatsapp === true &&
                                    <TouchableOpacity style={[styles.btn, styles.btnWhats]} onPress={ativarCliente}>
                                        <Text style={styles.btnText}>Chamar no Whatsapp</Text>
                                    </TouchableOpacity>
                                } */}

                                {notification.is_lead_ativo === true && notification.show_btn_whatsapp === true &&
                                    <>
                                        <View style={styles.divider}></View>
                                        <View style={styles.m20}>
                                            <Text style={[mainStyles.labelMargin, styles.textTitle]}>Contatos:</Text>
                                            <View style={styles.actions}>
                                                <View style={styles.boxBtn}>
                                                    <TouchableOpacity style={styles.btnBorder} onPress={() => ativarCliente('phone')}>
                                                        <Phone style={styles.icCel}/>
                                                    </TouchableOpacity>
                                                    <Text style={styles.typeBtn}>Telefone</Text>
                                                </View>
                                                <View style={styles.boxBtn}>
                                                    <TouchableOpacity style={styles.btnBorder} onPress={() => ativarCliente('whatsapp') }>
                                                        <Whats style={styles.icContact}/>
                                                    </TouchableOpacity>
                                                    <Text style={styles.typeBtn}>WhatsApp</Text>
                                                </View>
                                                <View style={styles.boxBtn}>
                                                    <TouchableOpacity style={styles.btnBorder} onPress={() => ativarCliente('email') }>
                                                        <Email style={styles.icEmail}/>
                                                    </TouchableOpacity>
                                                    <Text style={styles.typeBtn}>E-mail</Text>
                                                </View>
                                            </View>
                                        </View>
                                        <View style={styles.divider}></View>
                                    </>
                                }
                                {/* <Text style={styles.textAlert}><Text style={styles.spanColor}>Lembre-se</Text>: Você deve ativar o lead por um dos botões acima em, no máximo, 1h para que ele não seja expirado.</Text> */}
                                {/* <TouchableOpacity style={styles.btn} onPress={() => navigation.navigate('PrivateNotifications')}>
                                    <Text style={styles.btnText}>Voltar</Text>
                                </TouchableOpacity> */}
                            </View>
                        </View>
                    </ScrollView>
                    <View style={styles.contentBottom}>
                        <View style={[mainStyles.container, styles.contentButtons]}>
                        <TouchableOpacity
                            style={[mainStyles.btnBlueNew, styles.buttonLarge]}
                            onPress={() => navigation.navigate('PrivateNotifications')}>
                            <Text style={mainStyles.btnTextBlueNew}>VOLTAR</Text>
                        </TouchableOpacity>
                        </View>
                    </View>
                </>
            }
        </View>
    );
}

export default PrivateNotification;