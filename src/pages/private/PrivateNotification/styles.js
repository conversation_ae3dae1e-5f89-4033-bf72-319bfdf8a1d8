import { StyleSheet, Dimensions, Platform } from 'react-native';

const styles = StyleSheet.create({
    contentBottom: {
        paddingTop: 20,
        paddingBottom: 40,
        shadowRadius: Platform.OS === 'ios' ? 2 : 16,
        shadowOffset: {
            width: 0,
            height: Platform.OS === 'ios' ? -2 : 12,
        },
        shadowColor: Platform.OS === 'ios' ? "#CCC" : "#000",
        elevation: Platform.OS === 'ios' ? 2 : 24,
        shadowOpacity: 1,
        backgroundColor: "#FFF",
        position: "absolute",
        bottom: 0,
        zIndex: 10
    },
    buttonLarge: {
        width: "100%"
    },
    pdBottom: {
        paddingBottom: 100
    },
    from: {
        fontFamily: "Ubuntu-Regular",
        fontSize: 16,
        color: "#00467F",
        marginTop: 30,
        letterSpacing: 1
    },
    title: {
        fontFamily: "Ubuntu-Medium",
        fontSize: 20,
        color: "#00467F",
        marginTop: 15,
        marginBottom: 20,
        letterSpacing: 1
    },
    body: {
        color: "#828282",
        fontSize: 18,
        fontFamily: "Ubuntu-Light",
        lineHeight: 28,
    },
    // notificationContainer: {
    //     minHeight: "100%",
    //     justifyContent: "space-between"
    // },
    btn: {
        backgroundColor: '#00467F',
        borderWidth: 1,
        borderColor: '#00467F',
        borderRadius: 5,
        height: 60,
        justifyContent: "center",
        alignItems: "center",
        marginTop: 17, //17
        marginBottom: 50
    },
    btnWhats: {
        backgroundColor: "#1B9C20",
        borderWidth: 1,
        borderColor: '#1B9C20',
        marginTop: 50,
        marginBottom: 0
    },
    btnText: {
        fontFamily: 'Ubuntu-Medium',
        color: '#FFF',
        fontSize: 18,
        textTransform: "uppercase",
        letterSpacing: 1.58
    },
    divider: {
        borderBottomWidth: 1,
        borderBottomColor: '#DADADA',
        marginTop: 20,
    },
    m20: {
        marginTop: 15
    },
    textTitle: {
        color: "#2D719F",
        fontSize: Platform.OS === 'ios' ? 16 : 14,
        letterSpacing: 1
    },
    actions: {
        flexDirection: "row",
        marginTop: 10,
        marginBottom: 0,
        justifyContent: "flex-start",
    },
    boxBtn: {
        marginRight: 20
    },
    btnBorder: {
        alignItems: "center",
        justifyContent: "center",
        borderWidth: 1,
        borderColor: "#00467F",
        height: 65,
        width: 75,
        borderRadius: 10
    },
    btnBorderDisabled: {
        borderColor: "#979797"
    },
    icUser: {
        width: 55,
        marginTop: 5
    },
    icCel: {
        width: 30,
    },
    icContact: {
        width: 45,
    },
    typeBtn: {
        fontFamily: "Ubuntu-Regular",
        color: "#828282",
        fontSize: 11,
        letterSpacing: 1,
        textAlign: "center",
        marginTop: 5
    },
    textAlert: {
        fontFamily: "Roboto-Light",
        fontSize: 13,
        letterSpacing: 1,
        color: "#828282",
        lineHeight: 18,
        marginTop: 12,
        marginBottom: 12
    },
    spanColor: {
        fontFamily: "Roboto-Regular",
        color: "#FF6542"
    }
});

export default styles;