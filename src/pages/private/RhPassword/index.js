import React, { useState } from 'react';
import { Text, View, TouchableOpacity, ScrollView, ActivityIndicator, Keyboard, Alert, TextInput, KeyboardAvoidingView } from 'react-native';

import ArrowLeft from '../../../assets/svgs/ArrowLeftSmallWhite';
import Dash from 'react-native-dash';

import PrivateHeader from '../../../components/headers/PrivateHeader';

import styles from './styles';
import mainStyles from '../../../mainStyles';

import api from '../../../services/api';
import AsyncStorage from "@react-native-async-storage/async-storage";

const RhPersonalData = ({navigation}) => {
    const [loading, setLoading] = useState(false);

    const [inNewPassword, setInNewPassword] = useState('');
    const [inNewPasswordConfirm, setInNewPasswordConfirm] = useState('');

    const update = () => {
        setLoading(true);

        let data = {
            password: inNewPassword,
            password_confirm: inNewPasswordConfirm
        }

        api.put(`/rh/cadastro/senha`, data, {
            headers: {
                // 'content-type': 'application/x-www-form-urlencoded'
            }
        }).then(res => {
            Alert.alert('Senha atualizada com sucesso.');
            AsyncStorage.setItem('@CuryCorretor:password', inNewPassword);

            navigation.navigate('RhMyData')
        }).catch(err => {
            console.log(err.response);
            if(err?.response?.data?.errors){
                let errors = err.response.data.errors;
                let text = '';
                Object.keys(errors).map(error => {
                    text += `\n${errors[error][0]}`;
                });
                Alert.alert('Verifique os campos', text);
            } else {
                Alert.alert('Erro ao atualizar os dados.', 'Por favor, verifique os dados e tente novamente.');
            }
        }).then(() => {
            setLoading(false);
        });
    }


    return (
        <>
        <KeyboardAvoidingView style={{ flex: 1 }} behavior="padding">
            <PrivateHeader title={`Meu Perfil`} back={() => navigation.navigate('PrivateMain')} />
            <TouchableOpacity style={styles.shift} onPress={() => navigation.navigate('RhMyData')}>
                <View style={styles.icAbs}>
                    <ArrowLeft style={styles.icArrow} />
                </View>
                <Text style={[mainStyles.textWhite]}>ALTERAR SENHA DO APP</Text>
            </TouchableOpacity>
            <View style={[mainStyles.privateContainer, {flex: 1}]}>
                {loading && 
                    <View style={{flex: 1, justifyContent: "center", alignItems: "center"}}>
                        <ActivityIndicator size="large" color="#00467F" />
                    </View>
                }
                {!loading &&
                <>
                    <ScrollView showsVerticalScrollIndicator={false}>
                        <View style={styles.marginTop}>
                            {/* <View style={styles.marginTop}>
                                <Text style={mainStyles.labelBlue}>DIGITE A SENHA ATUAL</Text>                                
                                <TextInput
                                    autoCapitalize="none"
                                    underlineColorAndroid="transparent"  
                                    style={mainStyles.inputEditable}
                                    value={inPassword}
                                    onChangeText={setInPassword}
                                    secureTextEntry={true}
                                />
                                <Dash dashThickness={1} dashLength={3} dashColor="#DADADA" />
                            </View> */}
                            <View>
                                <Text style={mainStyles.label}>Escolha uma nova senha:</Text>                                
                                <TextInput
                                    autoCapitalize="none"
                                    underlineColorAndroid="transparent"  
                                    style={mainStyles.inputText}
                                    value={inNewPassword}
                                    onChangeText={setInNewPassword}                   
                                    secureTextEntry={true}
                                />
                                <Text style={mainStyles.label}>Confirmar nova senha:</Text>                                
                                <TextInput
                                    autoCapitalize="none"
                                    underlineColorAndroid="transparent"  
                                    style={mainStyles.inputText}
                                    value={inNewPasswordConfirm}
                                    onChangeText={setInNewPasswordConfirm}                      
                                    secureTextEntry={true}
                                />
                                <Text style={styles.textPass}>A senha deve ter ao menos 8 caracteres.</Text>
                            </View>                           
                        </View>
                    </ScrollView>
                    <View style={styles.contentBottom}>
                        <View style={[mainStyles.container, styles.contentButtons]}>              
                            <TouchableOpacity style={[mainStyles.btnOutlineBlue, mainStyles.buttonW48]} onPress={() => navigation.navigate('RhMyData')}>
                                <Text style={mainStyles.btnTextOutlineBlue}>VOLTAR</Text>
                            </TouchableOpacity>
                            <TouchableOpacity style={[mainStyles.btnBlueLight, styles.btnLogin, mainStyles.buttonW48]} onPress={update}>
                                <Text style={mainStyles.btnTextCenterBlueLight}>SALVAR</Text>
                            </TouchableOpacity>
                        </View>
                    </View>
                </>
                }
            </View>
        </KeyboardAvoidingView>
    </>
    );
}

export default RhPersonalData;