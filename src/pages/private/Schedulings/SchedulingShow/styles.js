import { StyleSheet, Dimensions, Platform } from 'react-native';

const windowWidth = Dimensions.get('window').width;

const styles = StyleSheet.create({
    wrapper: {
        justifyContent: "space-between"
    },
    contentBottom: {
        paddingTop: 25,      
        paddingBottom: 40,
        shadowRadius: Platform.OS === 'ios' ? 2 : 16,
        shadowOffset: {
            width: 0,
            height: Platform.OS === 'ios' ? -2 : 12,
        },
        shadowColor: Platform.OS === 'ios' ? '#CCC' : "#000",
        elevation: Platform.OS === 'ios' ? 2 : 24,
        shadowOpacity: 1,
        backgroundColor: "#FFF"
    },
    ptop: {
        paddingTop: 25,
        paddingLeft: 20,
        paddingRight: 20
    },
    contentButtons: {
        flexDirection: "row",
        justifyContent: "space-between",
        paddingLeft: 20,
        paddingRight: 20
    },
    input: {
        width: '75%',
        height: 50
    },
    button: {
        width: '46%',
    },
    buttonLarge: {
        width: '100%',
    },
    buttonDrop: {
        backgroundColor: "rgba(255, 104, 107, 0.8)"
    },
    container: {
        maxWidth: 340,
        marginLeft: ((windowWidth - 340) /2),
        marginTop: 10
    },
    buttonDate: {
        backgroundColor: "#90B0C0",
        height: 58,
        width: '100%',
        alignItems: "center",
        justifyContent: "center"
    },
    icArrow: {
        position: "absolute",
        top: 23,
        left: 25
    },
    btnTextButtonDate: {
        fontFamily: "Ubuntu-Regular",
        color: "#FFF",
        fontSize: 15,
        letterSpacing: 0.46,
        textTransform: "uppercase"
    },
    divider: {
        borderBottomWidth: 1,
        borderBottomColor: '#DADADA',
        marginTop: 10,
    },
    marginTop: {
        marginTop: 25
    },
    w90: {
        paddingLeft: 20,
        paddingRight: 20
    },
    customer: {
        flexDirection: "row",
        justifyContent: "space-between",
        alignItems: "center",
        paddingTop: 20,
        paddingBottom: 20,
        borderBottomColor: "#DADADA",
        borderBottomWidth: 1
    },
    dFlex: {
        flexDirection: "row",
        alignItems: "center"
    },
    iconCustomer: {
        backgroundColor: "#F2F2F2",
        height: 70,
        width: 56,
        alignItems: "center",
        justifyContent: "center",
        marginRight: 6
    },
    iconUser: {
        width: 40
    },
    icArrowBtn: {
        transform: [{ rotate: "-90deg" }]
    },
    dataCustomer: {
        width: '78%'
    },
    nameCustomer: {
        fontFamily: 'Ubuntu-Regular',
        color: "#00467F",
        fontSize: 16,
        letterSpacing: 0.46
    },
    productCustomer: {
        fontFamily: 'Ubuntu-Light',
        color: "#000",
        fontSize: 14,
        letterSpacing: 0.46,
        marginTop: 3,
        marginBottom: 3
    },
    refCustomer: {
        fontFamily: 'Ubuntu-Light',
        color: "#2D719F",
        fontSize: 14,
        letterSpacing: 0.46
    },
    pdlr: {
        paddingRight: 20,
        paddingLeft: 20,
        marginTop: 25,
        marginBottom: 30
    },
    qrcode: {
        paddingTop: 30,
        alignItems: 'center',
        justifyContent: 'center'
    },
    textWarning: {
        fontFamily: 'Ubuntu-Regular',
        fontSize: 14,
        letterSpacing: 1,
        color: "#828282",
        lineHeight: 17.5,
    },
    btnOutlineBlue: {
        marginTop: 25,
        height: 44,
        borderRadius: 0,
        textAlign: "center"
    },
    textBtnOutlineBlue: {
        fontFamily: 'Ubuntu-Regular',
        fontSize: 13,
        color: "#00467F",
        letterSpacing: 1,
        textAlign: "center"
    },
    warningSuccess: {
        fontFamily: 'Ubuntu-Regular',
        fontSize: 15,
        color: "#00467F",
        letterSpacing: 1,
        textAlign: "center",
        borderBottomWidth: 1,
        borderBottomColor: "rgba(0, 70, 127, 0.3)",
        paddingBottom: 15,
        marginBottom: 10
    },
    dFlexData: {
        flexDirection: "row",
        justifyContent: "center"
    },
    dataScheduling: {
        justifyContent: "center",
        width: '49%',
        marginTop: 10,
        marginBottom: 10,
        paddingTop: 5,
        paddingBottom: 5
    },
    boxBorder: {
        borderRadius : 1,
        borderRightWidth: 1,
        borderRightColor: '#E0E0E0'
    },
    titleScheduling: {
        fontFamily: 'Ubuntu-Regular',
        fontSize: 14,
        color: "#00467F",
        letterSpacing: 1,
        textAlign: "center"
    },
    infoScheduling: {
        fontFamily: 'Ubuntu-Regular',
        fontSize: 15,
        color: "#828282",
        letterSpacing: 1,
        textAlign: "center",
        marginTop: 3
    },
    textSuccess: {
        fontFamily: "Ubuntu-Regular",
        fontSize: 15,
        letterSpacing: 1,
        color: "#00467F",
        marginBottom: 15,
        marginTop: 10
    },
    textWarningLightbox: {
        fontFamily: "Ubuntu-Regular",
        fontSize: 14,
        letterSpacing: 1,
        color: "#828282",
        marginBottom: 10
    },
    link: {
        fontFamily: 'Ubuntu-Regular',
        fontSize: 15,
        color: "#00467F",
        letterSpacing: 0.4,
        marginTop: 5,
        marginBottom: 5
    },
    btnMargin: {
        marginTop: 20
    },
    btnCancel: {
        height: 0,
        width: 0
    },
    textConfirmed: {
        fontFamily: 'Ubuntu-Regular',
        fontSize: 14,
        color: "#1B9C20",
        letterSpacing: 1.58,
        marginLeft: 10
    }
});

export default styles;