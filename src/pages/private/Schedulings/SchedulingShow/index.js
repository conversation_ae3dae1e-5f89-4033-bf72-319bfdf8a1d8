import React, { useEffect, useState } from 'react';
import { TextInput, Text, View, TouchableOpacity, ActivityIndicator, SafeAreaView, Linking, Share } from 'react-native';
import { useIsFocused, useNavigation } from '@react-navigation/native';
import moment from 'moment';
import { Calendar, CalendarList, Agenda } from 'react-native-calendars';

import PrivateHeader from '../../../../components/headers/PrivateHeader';
import Select from '../../../../components/Select';

import User from '../../../../assets/svgs/Users3';
import Check from '../../../../assets/svgs/Check2';
import ArrowLeft from '../../../../assets/svgs/ArrowLeftSmallWhite';

import styles from './styles';
import mainStyles from '../../../../mainStyles';
import { ScrollView } from 'react-native-gesture-handler';
import api from '../../../../services/api';
import { useAuth } from '../../../../context/auth';
import SelectBorder from '../../../../components/SelectBorder';
import LightboxScheduling from '../../../../components/LightboxScheduling';
import { convertDateToBrazil } from '../../../../useful/conversions';
import { SvgUri } from 'react-native-svg';
import Clipboard from '@react-native-clipboard/clipboard';

import { PLANTAO_HOMOLOG_URL, PLANTAO_PRODUCTION_URL  } from '@env';

const SchedulingShow = ({ navigation, route }) => {
    const isFocused = useIsFocused();

    const { production } = useAuth();

    const [loading, setLoading] = useState(true);
    const [agendamento, setAgendamento] = useState(null);
    const [showLightbox, setShowLightbox] = useState(false);
    const [showLightboxDrop, setShowLightboxDrop] = useState(false);
    const [showLightboxDropSuccess, setShowLightboxDropSuccess] = useState(false);
    const [linkCopied, setLinkCopied] = useState(false);
    const [nome, setNome] = useState('');
    const { id, alterado, criado } = route.params;

    const linkConfirmacao = `${production ? PLANTAO_PRODUCTION_URL : PLANTAO_HOMOLOG_URL}/agendamento/${id}`;
    console.log(linkConfirmacao)
    useEffect(() => {
        if(isFocused) getAgendamento();
    }, [isFocused]);

    const getAgendamento = () => {
        setLoading(true);

        api.get(`/crm/agendamentos/${id}`).then(res => {
            setAgendamento(res.data);
            setNome(res.data.cliente?.nome);
            console.log(res.data);
        }).catch(err => {
            console.log(err.response);
        }).then(() => setLoading(false));
    }

    const deleteAppointment = () => {
        setLoading(true);
        api.delete(`/crm/agendamentos/${id}`)
            .then(res => {
                setShowLightboxDrop(false) & setShowLightboxDropSuccess(true)
            })
            .catch(err => {
                console.log(err);
            })
            .then(() => setLoading(false));
    }

    const editScheduling = () => {
        navigation.navigate('SchedulingEdit', { agendamento })
    }

    const copyLink = () => {
        Clipboard.setString(linkConfirmacao);
        setLinkCopied(true);
    }

    const isAtendimento = () => {
        if(!agendamento){
            return false;
        }
        return moment(agendamento.data).isBefore(moment().format('YYYY-MM-DD')) || agendamento.atendimento !== null;
    }

    const onShare = async () => {
        try {
            const message = `Oi ${nome},\n\nEstamos empolgados para a sua visita ao nosso stand e para ajudar a tornar o seu sonho de um novo apartamento realidade! Para garantir que tudo esteja certinho, por favor, confirme suas informações acessando este link: ${linkConfirmacao}.\n\nEstamos à disposição para tornar esse processo o mais tranquilo possível. Até breve!`;
    
            const result = await Share.share({
                message,
            });
    
            if (result.action === Share.sharedAction) {
                if (result.activityType) {
                    console.log('Compartilhado via:', result.activityType);
                } else {
                    console.log('Compartilhado');
                }
            } else if (result.action === Share.dismissedAction) {
                console.log('Compartilhamento cancelado');
            }
        } catch (error) {
            console.error('Erro ao compartilhar:', error.message);
        }
    };
    

    return (
        <View style={mainStyles.wrapper}>
            <PrivateHeader title={`Atendimentos`} />
            <TouchableOpacity style={styles.buttonDate} onPress={() => navigation.navigate('SchedulingList')}>
                <View style={styles.icArrow}>
                    <ArrowLeft />
                </View>
                <Text style={styles.btnTextButtonDate}>{isAtendimento() ? 'ATENDIMENTO' : 'AGENDAMENTO'}</Text>
            </TouchableOpacity>
            <ScrollView showsVerticalScrollIndicator={false}>
                {loading &&
                    <View style={{ flex: 1, justifyContent: "center", alignItems: "center", marginTop: 30 }}>
                        <ActivityIndicator size="large" color="#00467F" />
                    </View>
                }
                {!loading && agendamento &&
                    <View style={[mainStyles.privateContainer, styles.ptop]}>
                        {(criado || alterado) &&
                            <Text style={styles.warningSuccess}>Agendamento {alterado ? 'alterado' :'feito'} com sucesso!</Text>
                        }
                        <View style={styles.dFlexData}>
                            <View style={[styles.dataScheduling, styles.boxBorder]}>
                                <Text style={styles.titleScheduling}>Data</Text>
                                <Text style={styles.infoScheduling}>{convertDateToBrazil(agendamento?.data)}</Text>
                            </View>
                            <View style={styles.dataScheduling}>
                                <Text style={styles.titleScheduling}>Horário</Text>
                                <Text style={styles.infoScheduling}>{agendamento?.horario}</Text>
                            </View>
                        </View>
                        <View style={styles.divider}></View>
                        <View style={styles.customer}>
                            <View style={styles.dFlex}>
                                <View style={styles.iconCustomer}>
                                    <User style={styles.iconUser} />
                                </View>
                                <View style={styles.dataCustomer}>
                                    <Text style={styles.nameCustomer}>{agendamento?.cliente?.nome}</Text>
                                    <Text style={styles.productCustomer}>{agendamento?.plantao?.imovel_nome}</Text>
                                    <Text style={styles.refCustomer}>{agendamento?.cliente?.origem_txt}</Text>
                                </View>
                            </View>
                        </View>
                        {isAtendimento() && agendamento.atendimento === null &&
                            <View style={styles.pdlr}>
                                <Text style={[styles.textWarning, { textAlign: "center" }]}>
                                    Atendimento não realizado.
                                </Text>
                            </View>
                        }
                        {agendamento?.status === 'aguardando-confirmacao' &&
                            <View style={styles.pdlr}>
                                {agendamento.precisa_reconfirmar &&
                                    <Text style={styles.textWarning}>
                                        Este agendamento foi alterado e, portanto, precisa ser confirmado novamente. Envie esse link para o cliente para que ele possa confirmar o agendamento.
                                    </Text>
                                }
                                {!agendamento.precisa_reconfirmar &&
                                    <Text style={styles.textWarning}>
                                        Este agendamento ainda não foi confirmado. Envie esse link para o cliente para que ele possa confirmar o agendamento.
                                    </Text>
                                }
                                <TouchableOpacity style={[mainStyles.btnOutlineBlue, styles.btnOutlineBlue]} onPress={() => setShowLightbox(true)}>
                                    <Text style={styles.textBtnOutlineBlue}>LINK DE CONFIRMAÇÃO</Text>
                                </TouchableOpacity>
                            </View>
                        }
                        {agendamento?.status === 'agendamento-confirmado' &&
                            <View style={[styles.pdlr, styles.dFlex]}>
                                <Check />
                                <Text style={styles.textConfirmed}>AGENDAMENTO CONFIRMADO PELO CLIENTE EM {moment(agendamento?.confirmado_em).format('DD/MM/YYYY')}</Text>
                            </View>
                        }
                        {(agendamento?.status === 'agendamento-confirmado' || agendamento?.status === 'aguardando-confirmacao') &&
                            <View style={[mainStyles.privateContainer, styles.contentButtons]}>
                                <TouchableOpacity style={[mainStyles.btnBlueNew, styles.button]} onPress={() => editScheduling()}>
                                    <Text style={mainStyles.btnTextBlueNew}>EDITAR</Text>
                                </TouchableOpacity>
                                <TouchableOpacity style={[mainStyles.btnBlueNew, styles.button, styles.buttonDrop]} onPress={() => setShowLightboxDrop(true)}>
                                    <Text style={mainStyles.btnTextBlueNew}>EXCLUIR</Text>
                                </TouchableOpacity>
                            </View>
                        }
                        {/* {loading &&
                            <View style={{ flex: 1, justifyContent: "center", alignItems: "center", marginTop: 30 }}>
                                <ActivityIndicator size="large" color="#00467F" />
                            </View>
                        }
                        {!loading &&
                            
                        } */}
                    </View>
                }
            </ScrollView>
            <View style={[styles.contentBottom, mainStyles.privateContainer]}>
                <View style={[mainStyles.privateContainer, styles.contentButtons]}>
                    <TouchableOpacity style={[mainStyles.btnBlueNew, styles.buttonLarge]} onPress={() => navigation.navigate('SchedulingList')}>
                        <Text style={mainStyles.btnTextBlueNew}>VOLTAR</Text>
                    </TouchableOpacity>
                </View>
            </View>
            {showLightbox &&
                <LightboxScheduling
                    close={() => setShowLightbox(false)}
                    title="CONFIRMAR AGENDAMENTO">
                    <Text style={styles.textSuccess}>Link gerado com sucesso!</Text>
                    <Text style={styles.textWarningLightbox}>Envie esse link para o cliente para que ele possa confirmar o agendamento.</Text>
                    <Text style={styles.link}>{linkConfirmacao}</Text>
                    <TouchableOpacity style={[mainStyles.btnBlueNew, styles.btnMargin]} onPress={copyLink}>
                        <Text style={mainStyles.btnTextBlueNew}>{linkCopied ? 'COPIADO' : 'COPIAR'}</Text>
                    </TouchableOpacity>
                    {/* <TouchableOpacity style={[mainStyles.btnBlueNew, styles.btnMargin]} onPress={() => Linking.openURL(`https://wa.me/?text=${linkConfirmacao}`)}>
                        <Text style={mainStyles.btnTextBlueNew}>COMPARTILHAR</Text>
                    </TouchableOpacity> */}
                    <TouchableOpacity style={[mainStyles.btnBlueNew, styles.btnMargin]} onPress={onShare}>
                        <Text style={mainStyles.btnTextBlueNew}>COMPARTILHAR</Text>
                    </TouchableOpacity>
                </LightboxScheduling>
            }

            {showLightboxDrop &&
                <LightboxScheduling
                    close={() => setShowLightboxDrop(false)}
                    title="EXCLUIR AGENDAMENTO">
                    <Text style={styles.textWarningLightbox}>Deseja mesmo excluir este agendamento? Essa ação não poderá ser desfeita.</Text>
                    <TouchableOpacity style={[mainStyles.btnBlueNew, styles.btnMargin, styles.buttonDrop]} onPress={() => deleteAppointment()}>
                        <Text style={mainStyles.btnTextBlueNew}>SIM, EXCLUIR</Text>
                    </TouchableOpacity>
                    <TouchableOpacity style={[mainStyles.btnBlueNew, styles.btnMargin]} onPress={() => setShowLightboxDrop(false)}>
                        <Text style={mainStyles.btnTextBlueNew}>CANCELAR</Text>
                    </TouchableOpacity>
                </LightboxScheduling>
            }

            {showLightboxDropSuccess &&
                <LightboxScheduling
                    style={styles.btnCancel}
                    title="EXCLUIR AGENDAMENTO"
                    close={() => navigation.navigate('SchedulingList')}
                >
                    <Text style={styles.textWarningLightbox}>Agendamento excluído com sucesso!</Text>
                    <TouchableOpacity style={[mainStyles.btnBlueNew, styles.btnMargin]} onPress={() => navigation.navigate('SchedulingList')}>
                        <Text style={mainStyles.btnTextBlueNew}>VOLTAR</Text>
                    </TouchableOpacity>
                </LightboxScheduling>
            }

        </View>
    );
}

export default SchedulingShow;