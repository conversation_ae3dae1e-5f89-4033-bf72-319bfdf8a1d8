import React, { useEffect, useState } from 'react';
import { TextInput, Text, View, TouchableOpacity, Alert, ActivityIndicator, SafeAreaView } from 'react-native';
import { useIsFocused, useNavigation } from '@react-navigation/native';
import moment from 'moment';
import { Calendar, CalendarList, Agenda } from 'react-native-calendars';

import PrivateHeader from '../../../../components/headers/PrivateHeader';
import Select from '../../../../components/Select';

import CalendarLight from '../../../../assets/svgs/CalendarLight';
import ArrowDownGrey from '../../../../assets/svgs/ArrowDownGrey';
import ArrowLeft from '../../../../assets/svgs/ArrowLeftSmallWhite';
import User from '../../../../assets/svgs/Users3';

import styles from './styles';
import mainStyles from '../../../../mainStyles';
import { ScrollView } from 'react-native-gesture-handler';
import api from '../../../../services/api';
import { useSchedule } from '../../../../context/cliente';
import SelectBorder from '../../../../components/SelectBorder';
import LightboxCalendar from '../../../../components/LightboxCalendar';
import { dateMask, timeMask } from '../../../../useful/masks';
import { convertDateToAmerican } from '../../../../useful/conversions';
import { validateDate, validateHour } from '../../../../useful/validate';
import CustomerList from '../CustomerList';

const SchedulingNew = ({ navigation, route }) => {
    useEffect(() => {
        loadProducts();
    }, [])

    const selectedDate = route.params?.selectedDate ?? null;

    const [products, setProducts] = useState([]);
    
    const { id,
        inNome,
        imovelNome,
        origemTxt,
        inHorario,
        inData,
        inProduto,
        setInHorario,
        setInData,
        setInProduto,
        setId,
        setInNome,
        setImovelNome,
        setOrigemTxt,
        clearFields
    } = useSchedule();

    useEffect(() => {
        if(selectedDate){
            setInData(moment(selectedDate).format('DD/MM/YYYY'));
        }
    }, [selectedDate]);

    const [loading, setLoading] = useState(true);

    const loadProducts = () => {
        api.get('/crm/plantoes')
            .then(res => {
                let products = res.data.plantoes;
                let productOptions = [];
                products.map(product => productOptions.push({ label: product.imovel_nome, value: product.id }))
                setProducts(productOptions);

            }).catch(err => {
                Alert.alert('Ops, ocorreu algum erro!');
            })
            .then(() => {
                setLoading(false);
            });
    }

    const excludeCustomer = () => {
        setId('');
    }

    const makeAppointment = () => {
        let validation = validate();

        if (!validation) return;

        setLoading(true);
        let americanDate = convertDateToAmerican(inData);

        api.post('/crm/agendamentos', {
            data: americanDate,
            horario: inHorario,
            cliente_id: id,
            plantao_id: inProduto,
        }).then(res => {
            navigation.navigate('SchedulingShow', { id: res.data.id, criado: true });
            emptyFields();
        }).catch(err => {
            console.log(err.response);
            if (err?.response?.data?.errors) {
                let errors = err.response.data.errors;
                let text = '';
                Object.keys(errors).map(error => {
                    text += `\n${errors[error][0]}`;
                });
                Alert.alert('Verifique os campos', text);
            }
        }).then(() => {
            setLoading(false);
        })
    }

    const emptyFields = () => {
        setId('');
        setInData('');
        setInHorario('');
        setInNome('');
        setImovelNome('');
        setOrigemTxt('');
    }

    const customerList = () => {
        if (!inProduto) {
            return Alert.alert('Selecione um produto primeiro!');
        }
        
        navigation.navigate('CustomerList', { routeFrom: "SchedulingNew",  plantao: inProduto });
    }

    const validate = () => {
        if (inData.length === 0) {
            Alert.alert('Por favor, informe a data!');
            return false;
        }

        if (!validateDate(inData)) {
            Alert.alert('O formato da data é inválido');
            return false;
        }

        if (!validateHour(inHorario)) {
            Alert.alert('O formato do horário é inválido');
            return false;
        }

        return true;
    }

    return (
        <View style={mainStyles.wrapper}>
            {!loading &&
                <>
                    <PrivateHeader title={`Atendimentos`} />
                    <ScrollView showsVerticalScrollIndicator={false}>
                        <TouchableOpacity style={styles.buttonDate} onPress={() => navigation.goBack()}>
                            <View style={styles.icArrow}>
                                <ArrowLeft />
                            </View>
                            <Text style={styles.btnTextButtonDate}>NOVO AGENDAMENTO</Text>
                        </TouchableOpacity>
                        <View style={[mainStyles.privateContainer, styles.ptop]}>
                            <Text style={mainStyles.labelMargin}>DATA:</Text>
                            <TextInput
                                placeholderTextColor="#BDBDBD"
                                underlineColorAndroid="transparent"
                                style={mainStyles.inputBorder}
                                value={inData}
                                placeholder={'DD/MM/AAAA'}
                                keyboardType="phone-pad"
                                onChangeText={text => setInData(dateMask(text))}
                            />
                            <Text style={mainStyles.labelMargin}>HORÁRIO:</Text>
                            <TextInput
                                placeholderTextColor="#BDBDBD"
                                underlineColorAndroid="transparent"
                                style={mainStyles.inputBorder}
                                value={inHorario}
                                placeholder={'HH:MM'}
                                keyboardType="phone-pad"
                                onChangeText={text => setInHorario(timeMask(text))}
                            />
                            <Text style={mainStyles.labelMargin}>PRODUTO</Text>
                            <SelectBorder placeholder="ESCOLHER PRODUTO" options={products} value={inProduto} onChange={value => setInProduto(value)} />
                            {!id &&
                                <>
                                    <Text style={[mainStyles.labelMargin, styles.labelCustomer]}>CLIENTE:</Text>
                                    <View style={styles.customerContentButtons}>
                                        <TouchableOpacity style={[mainStyles.btnBlueNew, styles.customerButton]} onPress={() => customerList()}>
                                            <Text style={mainStyles.btnTextBlueNew}>BUSCAR</Text>
                                        </TouchableOpacity>
                                        {/* <TouchableOpacity style={[mainStyles.btnBlueNew, styles.customerButton]} onPress={() => createCustomer()}>
                                            <Text style={mainStyles.btnTextBlueNew}>NOVO</Text>
                                        </TouchableOpacity> */}
                                    </View>
                                </>
                            }
                            {id !== '' &&
                                <>
                                    <Text style={[mainStyles.labelMargin, styles.labelCustomer]}>CLIENTE:</Text>
                                    <View style={styles.divider}></View>
                                    <View style={styles.customer}>
                                        <View style={styles.dFlex}>
                                            <View style={styles.iconCustomer}>
                                                <User style={styles.iconUser} />
                                            </View>
                                            <View style={styles.dataCustomer}>
                                                <Text style={styles.nameCustomer}>{inNome}</Text>
                                                <Text style={styles.productCustomer}>{imovelNome}</Text>
                                                <Text style={styles.refCustomer}>{origemTxt}</Text>
                                            </View>
                                        </View>
                                    </View>
                                    <TouchableOpacity style={styles.drop} onPress={() => excludeCustomer()}>
                                        <Text style={styles.textDrop}>EXCLUIR</Text>
                                    </TouchableOpacity>
                                </>
                            }
                        </View>
                    </ScrollView>
                    {id !== '' &&
                        <>
                            <View style={styles.contentBottom}>
                                <View style={[mainStyles.privateContainer, styles.contentButtons]}>
                                    <TouchableOpacity style={[mainStyles.btnBlueNew, styles.button]} onPress={() => makeAppointment()}>
                                        <Text style={mainStyles.btnTextBlueNew}>SALVAR</Text>
                                    </TouchableOpacity>
                                </View>
                            </View>
                        </>
                    }
                </>
            }
            {loading &&
                <View style={{ flex: 1, justifyContent: "center", alignItems: "center", marginTop: 30 }}>
                    <ActivityIndicator size="large" color="#00467F" />
                </View>
            }
        </View>
    );
}

export default SchedulingNew;