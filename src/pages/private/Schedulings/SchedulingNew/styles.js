import { StyleSheet, Dimensions, Platform } from 'react-native';

const windowWidth = Dimensions.get('window').width;

const styles = StyleSheet.create({
    wrapper: {
        justifyContent: "space-between"
    },
    contentBottom: {
        backgroundColor: '#FFF',
        paddingTop: 25,      
        paddingBottom: 40,
        shadowRadius: Platform.OS === 'ios' ? 2 : 16,
        shadowOffset: {
            width: 0,
            height: Platform.OS === 'ios' ? -2 : 12,
        },
        shadowColor: Platform.OS === 'ios' ? '#CCC' : "#000",
        elevation: Platform.OS === 'ios' ? 2 : 24,
        shadowOpacity: 1,
    },
    ptop: {
        paddingTop: 25,
        paddingLeft: 20,
        paddingRight: 20
    },
    contentButtons: {
        flexDirection: "row",
        justifyContent: "space-between",
        marginTop: 0,
        marginBottom: 0
    },
    customerContentButtons: {
        flexDirection: "row",
        justifyContent: "space-between",
        marginTop: 10,
    },
    button: {
        width: '100%',
    },
    customerButton: {
        width: '100%',
    },
    container: {
        maxWidth: 340,
        marginLeft: ((windowWidth - 340) /2),
        marginTop: 10
    },
    contentOption: {
        flexDirection: "row",
        alignItems: "center",
        marginTop: 25,
        marginBottom: 25
    },  
    textStatusBold: {
        fontFamily: "Roboto-Bold",
        color: "#00467F",
        fontSize: 15,
        letterSpacing: 1,
        marginLeft: 15
    },
    inputText: {
        backgroundColor: "#FFF",
        width: 125,
        paddingLeft: 13,
    },
    buttonCenterBlue: {
        width: 70,
        height: 50,
        marginTop: 10,
        borderRadius: 5,
        marginLeft: 15
    },
    textBtnCenterBlue: {
        fontSize: 20
    }, 
    rowCenter: {
        flexDirection: "row",
        justifyContent: "space-between",
        alignItems: 'center',
        marginBottom: 15,
        marginTop: 10
    },
    rowCenterMargin: {
        flexDirection: "row",
        alignItems: 'center',
        marginVertical: 20
    },
    optionTextBold: {
        fontFamily: "Roboto-Bold",
        fontSize: 12,
        letterSpacing: 0.7,
        color: "#4ea1cc",
        lineHeight: 18
    },
    boxFlex: {
        flexDirection: "row",
        justifyContent: "space-between",
        alignItems: "center",
        borderColor: "#DADADA",
        borderTopWidth: 1,
        paddingTop: 20,
        paddingBottom: 20
    },
    boxFlexColumn: {
        flexDirection: "column"
    },
    boxIconWidth: {
        width: 40,
        flexDirection: "row",
        justifyContent: "center"
    },
    boxFlexWithText: {
        flexDirection: "row"
    },
    textBoxFlex: {
        fontSize: 14,
        letterSpacing: 1,
        width: 230,
        marginLeft: 10
    },
    textBoxFlexBold: {
        fontWeight: "bold",
        color: "#00467F",
        fontSize: 20,
        letterSpacing: 1,
        width: 230,
        marginLeft: 10,
        marginTop: -3,
        marginBottom: 10
    },
    textAboutProcessBold: {
        fontWeight: "bold",
        color: "#828282",
        fontSize: 12,
        letterSpacing: 0.5,
        width: 280,
        marginTop: 5,
        marginBottom: 5
    },
    textAboutProcess: {
        fontWeight: "normal",
        color: "#828282",
        fontSize: 12,
        letterSpacing: 0.5,
        marginLeft: 5
    },
    btnTextOutlineBlue: {
        fontFamily: "Roboto-Bold",
        color: "#00467F",
        fontSize: 25,
        fontWeight: "bold",
        letterSpacing: 1,
        textAlign: "center"
    },
    boxFlexColumn: {
        flexDirection: "column"
    },
    textSearch: {
        fontFamily: "Roboto-Regular",
        color: "#4EA1CC",
        fontSize: 17,
        letterSpacing: 1,
    },
    searchBox: {
        flexDirection: "row",
        justifyContent: "center",
        borderColor: "#DADADA",
        borderBottomWidth: 1,
        paddingBottom: 30
    },
    input: {
        borderWidth: 2,
        borderColor: "#4EA1CC",
        borderRadius: 5,
        width: 150,
        height: 55,
        paddingLeft: 15,
        paddingRight: 15,
        fontSize: 25,
        textAlign: "center"
    },
    buttonDate: {
        backgroundColor: "#90B0C0",
        height: 58,
        width: '100%',
        alignItems: "center",
        justifyContent: "center"
    },
    icArrow: {
        position: "absolute",
        top: 23,
        left: 25
    },
    btnTextButtonDate: {
        fontFamily: "Ubuntu-Regular",
        color: "#FFF",
        fontSize: 15,
        letterSpacing: 0.46,
        textTransform: "uppercase"
    },
    carouselDays: {
        marginTop: 20,
        marginBottom: 10,
        flexDirection: 'row'
    },
    boxDays: {
        backgroundColor: "#F2F2F2",
        height: 67,
        width: 67,
        alignItems: "center",
        justifyContent: "center",
        marginLeft: 7,
        marginRight: 7,
        overflow: "scroll",
    },
    boxDaysActive: {
      backgroundColor: "#90B0C0"  
    },
    day: {
        fontFamily: "Ubuntu-Regular",
        color: "#00467F",
        fontSize: 14,
        textTransform: "uppercase",
        letterSpacing: 0.46
    },
    dayActive: {
        color: "#FFF"
    },
    numberDay: {
        fontFamily: "Ubuntu-Light",
        color: "#000",
        fontSize: 27
    },
    numberDayActive: {
        color: "#FFF"
    },
    marginTop: {
        marginTop: 25
    },
    w90: {
        paddingLeft: 20,
        paddingRight: 20
    },
    boxTotal: {
        flexDirection: "row",
        minHeight: 74,
        marginTop: 10,
        marginBottom: 25
    },
    totalIcon: {
        backgroundColor: "#2D719F",
        width: 50,
        alignItems: "center",
        justifyContent: "center"
    },
    totalInfos: {
        backgroundColor: "#F2F2F2",
        flex: 1,
        paddingLeft: 10,
        paddingRight: 20,
        flexDirection: "row",
        alignItems: "center",
        justifyContent: "space-between"
    },
    textTitle: {
        fontFamily: "Ubuntu-Regular",
        fontSize: 14,
        color: "#00467F",
        letterSpacing: 0.46
    },
    textQtdTotal: {
        fontFamily: "Ubuntu-Light",
        fontSize: 13,
        color: "#000",
        letterSpacing: 0.46
    },
    textQtd: {
        fontFamily: "Ubuntu-Light",
        fontSize: 13,
        color: "#1B9C20",
        letterSpacing: 0.46
    },
    boxScheduling: {
        flexDirection: "row",
        minHeight: 60
    },
    schedulingHour: {
        width: 50,
        alignItems: "center",
        justifyContent: "center"
    },
    schedulinglInfos: {
        flex: 1,
        paddingLeft: 10,
        paddingRight: 20,
        flexDirection: "row",
        alignItems: "center",
        justifyContent: "space-between"
    },
    schedulingProduct: {
        fontFamily: "Ubuntu-Regular",
        fontSize: 14,
        color: "#00467F",
        letterSpacing: 0.46
    },
    schedulingName: {
        fontFamily: "Ubuntu-Light",
        fontSize: 13,
        color: "#000",
        letterSpacing: 0.46
    },
    schedulingStatus: {
        fontFamily: "Ubuntu-Light",
        fontSize: 13,
        letterSpacing: 0.46
    },
    divider: {
        borderBottomWidth: 1,
        borderBottomColor: '#DADADA',
        marginTop: 10,
        marginBottom: 0
    },
    customer: {
        flexDirection: "row",
        justifyContent: "space-between",
        alignItems: "center",
        paddingTop: 15,
        paddingBottom: 15,
        borderBottomColor: "#DADADA",
        borderBottomWidth: 1
    },
    dFlex: {
        flexDirection: "row",
        alignItems: "center"
    },
    iconCustomer: {
        backgroundColor: "#F2F2F2",
        height: 70,
        width: 56,
        alignItems: "center",
        justifyContent: "center",
        marginRight: 6
    },
    iconUser: {
        width: 40
    },
    dataCustomer: {
        width: '78%'
    },
    nameCustomer: {
        fontFamily: 'Ubuntu-Regular',
        color: "#00467F",
        fontSize: 16,
        letterSpacing: 0.46
    },
    productCustomer: {
        fontFamily: 'Ubuntu-Light',
        color: "#000",
        fontSize: 14,
        letterSpacing: 0.46,
        marginTop: 3,
        marginBottom: 3
    },
    refCustomer: {
        fontFamily: 'Ubuntu-Light',
        color: "#2D719F",
        fontSize: 14,
        letterSpacing: 0.46
    },
    drop: {
        marginTop: 20
    },
    textDrop: {
        fontFamily: "Ubuntu-Light",
        fontSize: 14,
        color: "#FF6542",
        letterSpacing: 1.25,
        textDecorationLine: "underline"
    },
    labelCustomer: {
        marginTop: 15,
        marginBottom: 0,
        lineHeight: 0
    }
});

export default styles;