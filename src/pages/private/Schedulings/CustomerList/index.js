import React, { useEffect, useState } from 'react';
import { TextInput, Text, View, TouchableOpacity, ActivityIndicator, Alert } from 'react-native';
import { useIsFocused } from '@react-navigation/native';

import PrivateHeader from '../../../../components/headers/PrivateHeader';

import User from '../../../../assets/svgs/Users3';
import ArrowDownGrey from '../../../../assets/svgs/ArrowDownGrey';
import ArrowLeft from '../../../../assets/svgs/ArrowLeftSmallWhite';

import styles from './styles';
import mainStyles from '../../../../mainStyles';
import { ScrollView } from 'react-native-gesture-handler';
import api from '../../../../services/api';
import { useSchedule } from '../../../../context/cliente';

const CustomerList = ({ navigation, route }) => {
    const isFocused = useIsFocused();

    useEffect(() => {
        if(isFocused){
            loadClients()
        }
    }, [isFocused])

    const plantao = route.params?.plantao ?? '';
    const routeFrom = route.params?.routeFrom ?? '';
    
    const [search, setSearch] = useState('');

    const [loading, setLoading] = useState(true);
    const [clientes, setClientes] = useState([]);
    const [filteredClientes, setFilteredClientes] = useState([]);

    const { setId, setInNome, setImovelNome, setOrigemTxt } = useSchedule();

    useEffect(() => {
        filterClientes();
    }, [clientes]);

    const filterClientes = () => {
        let toFilteredClientes = [];
        clientes.map(cliente => {
            if (cliente.nome == null) {
                return;
            }
            let nome = cliente.nome.toLowerCase();
            let term = search.toLocaleLowerCase().trim();
            if(nome.indexOf(term) > -1) toFilteredClientes.push(cliente);
        });
        setFilteredClientes(toFilteredClientes);
    }

    const loadClients = () => {
        setLoading(true);
        api.get('/crm/clientes')
            .then(res => {
                setClientes(res.data.clientes);
            })
            .catch(err => {
                console.log(err.response);
                Alert.alert('Ops, ocorreu algum erro!');
            })
            .then(() => {
                setLoading(false);
            });
    }

    const goToAppointment = (cliente) => {
        setId(cliente.id);
        setInNome(cliente.nome);
        setImovelNome(cliente?.plantao?.imovel_nome);
        setOrigemTxt(cliente.origem_txt);

        if (routeFrom == 'SchedulingEdit') {
            navigation.navigate('SchedulingEdit');
        } else if (routeFrom == 'SchedulingNew') {
            navigation.navigate('SchedulingNew');
        } else {
            navigation.navigate('MyCustomerData', { clienteId: cliente.id });
        }
    }

    return (
        <View style={mainStyles.wrapper}>
            {!loading &&
                <>
                    <PrivateHeader title={`MEUS CLIENTES`} />
                    <TouchableOpacity style={styles.buttonDate} onPress={() => navigation.goBack()}>
                        <View style={styles.icArrow}>
                            <ArrowLeft />
                        </View>
                        <Text style={styles.btnTextButtonDate}>MEUS CLIENTES</Text>
                    </TouchableOpacity>
                    <View style={[mainStyles.privateContainer, styles.ptop]}>
                        <Text style={mainStyles.labelMargin}>CLIENTE:</Text>
                        <View style={styles.contentButtons}>
                            <TextInput
                                placeholderTextColor="#BDBDBD"
                                placeholder="BUSCAR"
                                underlineColorAndroid="transparent"
                                style={[mainStyles.inputBorder, styles.input]}
                                value={search}
                                onChangeText={value => setSearch(value)}
                                autoCapitalize="words"
                            />
                            <TouchableOpacity style={[mainStyles.btnBlueNew, styles.button]} onPress={filterClientes}>
                                <Text style={mainStyles.btnTextBlueNew}>OK</Text>
                            </TouchableOpacity>
                        </View>
                    </View>
                        <ScrollView showsVerticalScrollIndicator={false}>
                            <View style={mainStyles.privateContainer}>
                                <View style={styles.divider}></View>
                                {filteredClientes.length === 0 &&
                                    <Text style={{ marginTop: 30 }}>Nenhum cliente encontrado</Text>
                                }
                                {filteredClientes.map((item, index) =>
                                    <TouchableOpacity style={styles.customer} key={index} onPress={() => goToAppointment(item)}>
                                        <View style={styles.dFlex}>
                                            <View style={styles.iconCustomer}>
                                                <User style={styles.iconUser} />
                                            </View>

                                            <View style={styles.dataCustomer}>
                                                <Text style={styles.nameCustomer}>{item.nome}</Text>
                                                <Text style={styles.productCustomer}>{item?.plantao?.imovel_nome}</Text>
                                                <Text style={styles.refCustomer}>{item.origem_txt ?? ''}</Text>
                                            </View>


                                        </View>
                                        <ArrowDownGrey style={styles.icArrowBtn} />
                                    </TouchableOpacity>
                                )}
                            </View>
                        </ScrollView>
                    <View style={styles.contentBottom}>
                        <View style={[mainStyles.container, styles.contentButtons]}>
                            <TouchableOpacity style={[mainStyles.btnBlueNew, styles.buttonLarge]} onPress={() => navigation.navigate('CustomerNew', { routeFrom, plantao }) }>
                                <Text style={mainStyles.btnTextBlueNew}>NOVO CLIENTE</Text>
                            </TouchableOpacity>
                        </View>
                    </View>
                </>
            }
            {loading &&
                <View style={{ flex: 1, justifyContent: "center", alignItems: "center", marginTop: 30 }}>
                    <ActivityIndicator size="large" color="#00467F" />
                </View>
            }

        </View>
    );
}

export default CustomerList;