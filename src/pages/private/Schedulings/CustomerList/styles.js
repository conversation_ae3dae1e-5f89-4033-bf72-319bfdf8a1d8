import { StyleSheet, Dimensions, Platform } from 'react-native';

const windowWidth = Dimensions.get('window').width;

const styles = StyleSheet.create({
    wrapper: {
        justifyContent: "space-between"
    },
    contentBottom: {
        paddingTop: 25,      
        paddingBottom: 40,
        shadowRadius: Platform.OS === 'ios' ? 2 : 16,
        shadowOffset: {
            width: 0,
            height: Platform.OS === 'ios' ? -2 : 12,
        },
        shadowColor: Platform.OS === 'ios' ? '#CCC' : "#000",
        elevation: Platform.OS === 'ios' ? 2 : 24,
        shadowOpacity: 1,
        backgroundColor: "#FFF"
    },
    ptop: {
        paddingTop: 25,
        paddingLeft: 20,
        paddingRight: 20
    },
    buttonLarge: {
        width: '100%'
    },
    contentButtons: {
        flexDirection: "row",
        justifyContent: "space-between"
    },
    input: {
        width: '75%',
        height: 50
    },
    button: {
        width: '20%',
    },
    container: {
        maxWidth: 340,
        marginLeft: ((windowWidth - 340) /2),
        marginTop: 10
    },
    buttonDate: {
        backgroundColor: "#90B0C0",
        height: 58,
        width: '100%',
        alignItems: "center",
        justifyContent: "center"
    },
    icArrow: {
        position: "absolute",
        top: 23,
        left: 25
    },
    btnTextButtonDate: {
        fontFamily: "Ubuntu-Regular",
        color: "#FFF",
        fontSize: 15,
        letterSpacing: 0.46,
        textTransform: "uppercase"
    },
    divider: {
        borderBottomWidth: 1,
        borderBottomColor: '#DADADA',
        marginTop: 10,
    },
    marginTop: {
        marginTop: 25
    },
    w90: {
        paddingLeft: 20,
        paddingRight: 20
    },
    customer: {
        flexDirection: "row",
        justifyContent: "space-between",
        alignItems: "center",
        paddingTop: 20,
        paddingBottom: 20,
        borderBottomColor: "#DADADA",
        borderBottomWidth: 1
    },
    dFlex: {
        flexDirection: "row",
        alignItems: "center"
    },
    iconCustomer: {
        backgroundColor: "#F2F2F2",
        height: 70,
        width: 56,
        alignItems: "center",
        justifyContent: "center",
        marginRight: 6
    },
    iconUser: {
        width: 40
    },
    icArrowBtn: {
        transform: [{ rotate: "-90deg" }]
    },
    dataCustomer: {
        width: '78%'
    },
    nameCustomer: {
        fontFamily: 'Ubuntu-Regular',
        color: "#00467F",
        fontSize: 16,
        letterSpacing: 0.46
    },
    productCustomer: {
        fontFamily: 'Ubuntu-Light',
        color: "#000",
        fontSize: 14,
        letterSpacing: 0.46,
        marginTop: 3,
        marginBottom: 3
    },
    refCustomer: {
        fontFamily: 'Ubuntu-Light',
        color: "#2D719F",
        fontSize: 14,
        letterSpacing: 0.46
    }
});

export default styles;