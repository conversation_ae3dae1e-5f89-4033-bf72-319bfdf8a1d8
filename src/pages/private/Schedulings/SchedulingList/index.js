import React, { useEffect, useRef, useState } from 'react';
import { Text, View, TouchableOpacity, ActivityIndicator } from 'react-native';
import { useIsFocused, useNavigation } from '@react-navigation/native';
import moment from 'moment';

import PrivateHeader from '../../../../components/headers/PrivateHeader';

import CalendarLight from '../../../../assets/svgs/CalendarLight';
import ArrowDownGrey from '../../../../assets/svgs/ArrowDownGrey';

import styles from './styles';
import mainStyles from '../../../../mainStyles';
import { ScrollView } from 'react-native-gesture-handler';
import api from '../../../../services/api';
import SelectBorder from '../../../../components/SelectBorder';
import LightboxCalendar from '../../../../components/LightboxCalendar';
import ArrowRightGray from '../../../../assets/svgs/ArrowRightGray';
import { useSchedule } from '../../../../context/cliente';
import CalendarHorizontal from '../../../../components/CalendarHorizontal';
import CalendarMonth from '../../../../components/CalendarMonth';

const StatusText = ({status}) => {
    return (
        <>
            {status === 'aguardando-confirmacao' &&
                <Text style={[styles.schedulingStatus, { color: "#FF6542" }]}>Aguardando confirmação</Text>
            }
            {status === 'agendamento-confirmado' &&
                <Text style={[styles.schedulingStatus, { color: "#1B9C20" }]}>Agendamento confirmado</Text>
            }
            {status === 'atendimento-agendado' &&
                <Text style={[styles.schedulingStatus, { color: "#1B9C20" }]}>Atendimento agendado</Text>
            }
            {status === 'atendimento-nao-agendado' &&
                <Text style={[styles.schedulingStatus, { color: "#4EA1CC" }]}>Atendimento não agendado</Text>
            }
            {status === 'atendimento-nao-realizado' &&
                <Text style={[styles.schedulingStatus, { color: "#FF6542" }]}>Atendimento não realizado</Text>
            }
        </>
    );
}

const SchedulingList = ({ navigation, route }) => {
    const isFocused = useIsFocused();
    const flatListRef = useRef();

    const { clearFields } = useSchedule();
    const [loading, setLoading] = useState(true);
    const [calendar, setCalendar] = useState(false);

    const [days, setDays] = useState([]);
    const [selectedDate, setSelectedDate] = useState(moment().format('YYYY-MM-DD'));
    const [selectedPlantao, setSelectedPlantao] = useState(null);
    const [agendamentos, setAgendamentos] = useState([]);
    const [plantoes, setPlantoes] = useState([]);

    const [totalAtendimentos, setTotalAtendimentos] = useState(0);
    const [totalAtendimentosAgendados, setTotalAtendimentosAgendados] = useState(0);

    const [totalAgendamentos, setTotalAgendamentos] = useState(0);
    const [totalAgendamentosConfirmados, setTotalAgendamentosConfirmados] = useState(0);

    useEffect(() => {
        getPlantoes();
    }, []);

    useEffect(() => {
        getDays();
    }, [selectedDate])

    useEffect(() => {
        goToCurrentDay();
    }, [days, selectedDate, isFocused]);

    useEffect(() => {
        if(isFocused){
            getAgendamentos();
        }
    }, [isFocused, selectedDate, selectedPlantao]);

    useEffect(() => {
        getTotais();
    }, [agendamentos]);

    const getTotais = () => {
        let toTotalAgendamentos = 0;
        let toTotalAgendamentosConfirmados = 0;
        let toTotalAtendimentos = 0;
        let toTotalAtendimentosAgendados = 0;

        agendamentos.map(agendamento => {
            if(['aguardando-confirmacao', 'agendamento-confirmado', 'atendimento-agendado'].includes(agendamento.status)){
                toTotalAgendamentos++;
            }
            if(['agendamento-confirmado'].includes(agendamento.status)){
                toTotalAgendamentosConfirmados++;
            }
            if(['atendimento-agendado', 'atendimento-nao-agendado'].includes(agendamento.status)){
                toTotalAtendimentos++;
            }
            if(['atendimento-agendado'].includes(agendamento.status)){
                toTotalAtendimentosAgendados++;
            }
        });

        setTotalAgendamentos(toTotalAgendamentos);
        setTotalAgendamentosConfirmados(toTotalAgendamentosConfirmados);
        setTotalAtendimentos(toTotalAtendimentos);
        setTotalAtendimentosAgendados(toTotalAtendimentosAgendados)
    }

    const getAgendamentos = () => {
        setLoading(true);

        api.get(`/crm/agendamentos`, {
            params: {
                data: selectedDate,
                plantao_id: selectedPlantao
            }
        }).then(res => {
            setAgendamentos(res.data.data);

        }).catch(err => {
            console.log(err.response);
        }).then(() => setLoading(false));
    }

    const getPlantoes = () => {
        api.get('/crm/plantoes')
            .then(res => {
                let toPlantoes = [];
                res.data.plantoes.map(plantao => toPlantoes.push({ label: plantao.imovel_nome, value: plantao.id }))
                setPlantoes(toPlantoes);
            }).catch(err => {
                Alert.alert('Ops, ocorreu algum erro!');
            })
            .then(() => {
                setLoading(false);
            });
    }

    const getDays = () => {
        let startOfMonth = moment(selectedDate).startOf('month');
        let endOfMonth = moment(selectedDate).endOf('month');

        let toDays = [];
        let currentDay = startOfMonth;
        let i = 1;

        while (currentDay <= endOfMonth) {
            toDays.push({
                id: i,
                date: currentDay.format('YYYY-MM-DD'),
                number: currentDay.format('DD'),
                text: currentDay.format('ddd')
            })
            currentDay = currentDay.clone().add(1, 'd');
            i++;
        }

        setDays(toDays);
    }

    const goToCurrentDay = () => {
        let currentDay = parseInt(selectedDate.split('-')[2]);
        let position = currentDay - 3;
        position = position < 0 ? 0 : position;
        setTimeout(() => {
            if (flatListRef.current) {
                flatListRef.current.scrollToIndex({ animated: true, index: position })
            }
        }, 200)
    }

    const newScheduling = () => {
        clearFields();
        navigation.navigate('SchedulingNew', { selectedDate });
    }

    return (
        <View style={mainStyles.wrapper}>
            <PrivateHeader title={`Atendimentos`} />
            <ScrollView showsVerticalScrollIndicator={false}>
                <CalendarMonth
                    selectedDate={selectedDate}
                    setSelectedDate={setSelectedDate}
                    setCalendar={setCalendar}
                />
                <CalendarHorizontal
                    selectedDate={selectedDate}
                    setSelectedDate={setSelectedDate}
                />

                <View style={mainStyles.privateContainer}>
                    <View style={styles.divider}></View>
                    <View style={styles.w90}>
                        <Text style={mainStyles.labelMargin}>PRODUTO</Text>
                        <SelectBorder options={plantoes} value={selectedPlantao} onChange={setSelectedPlantao} placeholder="TODOS" />
                    </View>
                    <View style={styles.divider}></View>
                    {loading &&
                        <View style={{ flex: 1, justifyContent: "center", alignItems: "center", marginTop: 30 }}>
                            <ActivityIndicator size="large" color="#00467F" />
                        </View>
                    }
                    {!loading && agendamentos.length === 0 &&
                        <Text style={styles.textWarningScheduling}>Você ainda não tem atendimentos agendados para este dia.</Text>
                    }
                    {!loading && agendamentos.length > 0 &&
                        <>
                            <View style={styles.boxTotal}>
                                <View style={styles.totalIcon}>
                                    <CalendarLight />
                                </View>
                                <View style={styles.totalInfos}>
                                    <View>
                                        <Text style={styles.textTitle}>Total</Text>
                                        {moment(selectedDate).isAfter(moment().format('YYYY-MM-DD')) &&
                                            <>
                                                <Text style={styles.textQtdTotal}>{totalAgendamentos} agendamentos</Text>
                                                <Text style={styles.textQtd}>{totalAgendamentosConfirmados} confirmados</Text>
                                            </>
                                        }
                                        {moment(selectedDate).isSame(moment().format('YYYY-MM-DD')) &&
                                            <>
                                                <Text style={styles.textQtdTotal}>{totalAgendamentos} agendamentos</Text>
                                                <Text style={styles.textQtd}>{totalAtendimentos} atendimentos realizados</Text>
                                            </>
                                        }
                                        {moment(selectedDate).isBefore(moment().format('YYYY-MM-DD')) &&
                                            <>
                                                <Text style={styles.textQtdTotal}>{totalAtendimentos} atendimentos realizados</Text>
                                                <Text style={styles.textQtd}>{totalAtendimentosAgendados} por agendamento</Text>
                                            </>
                                        }
                                    </View>
                                    <ArrowDownGrey />
                                </View>
                            </View>
                            {agendamentos.map((agendamento, index) => (
                                <View key={index}>
                                    <TouchableOpacity
                                        style={styles.boxScheduling}
                                        onPress={() => navigation.navigate('SchedulingShow', { id: agendamento.id })}
                                    >
                                        <View style={[styles.schedulingHour, { backgroundColor: "rgba(27, 156, 32, 0.2);" }]}>
                                            <Text style={styles.hour}>{agendamento.horario}</Text>
                                        </View>
                                        <View style={styles.schedulinglInfos}>
                                            <View style={styles.schedulingInfosText}>
                                                <Text style={styles.schedulingProduct}>{agendamento?.plantao?.imovel_nome}</Text>
                                                <Text style={styles.schedulingName}>{agendamento?.cliente?.nome}</Text>
                                                <StatusText status={agendamento?.status} />
                                            </View>
                                            <ArrowRightGray style={{ width: 24 }} />
                                        </View>
                                    </TouchableOpacity>
                                    <View style={styles.divider}></View>
                                </View>
                            ))}
                        </>
                    }
                </View>
            </ScrollView>
            {calendar &&
                <LightboxCalendar
                    initialDate={selectedDate}
                    setSelectedDate={setSelectedDate}
                    close={() => setCalendar(false)}
                />
            }
            <View style={styles.contentBottom}>
                <View style={[mainStyles.container, styles.contentButtons]}>
                    <TouchableOpacity style={[mainStyles.btnBlueNew, styles.button]} onPress={() => newScheduling()}>
                        <Text style={mainStyles.btnTextBlueNew}>NOVO AGENDAMENTO</Text>
                    </TouchableOpacity>
                </View>
            </View>
        </View>
    );
}

export default SchedulingList;