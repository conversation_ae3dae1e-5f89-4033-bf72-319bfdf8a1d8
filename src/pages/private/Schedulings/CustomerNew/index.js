import React, { useEffect, useRef, useState } from 'react';
import { TextInput, Text, View, TouchableOpacity, Alert, ActivityIndicator, KeyboardAvoidingView } from 'react-native';
import { useIsFocused, useNavigation } from '@react-navigation/native';
import moment from 'moment';
import { Calendar, CalendarList, Agenda } from 'react-native-calendars';

import PrivateHeader from '../../../../components/headers/PrivateHeader';
import Select from '../../../../components/Select';

import CalendarLight from '../../../../assets/svgs/CalendarLight';
import ArrowDownGrey from '../../../../assets/svgs/ArrowDownGrey';
import ArrowLeft from '../../../../assets/svgs/ArrowLeftSmallWhite';

import styles from './styles';
import mainStyles from '../../../../mainStyles';
import { ScrollView } from 'react-native-gesture-handler';
import api from '../../../../services/api';
import { useAuth } from '../../../../context/auth';
import SelectBorder from '../../../../components/SelectBorder';
import LightboxCalendar from '../../../../components/LightboxCalendar';
import { cpfMask, phoneMask } from '../../../../useful/masks';
import { useSchedule } from '../../../../context/cliente';

const CustomerNew = ({ navigation, route }) => {
    const scrollViewRef = useRef();
    const params = route.params;
    const routeFrom = params.routeFrom;

    const [loading, setLoading] = useState(false);

    const [nome, setNome] = useState('');
    const [telefone, setTelefone] = useState('');
    const [email, setEmail] = useState('');
    const [cpf, setCpf] = useState('');
    const [anotacoes, setAnotacoes] = useState('');
    const {setId, setInNome, setImovelNome, setOrigemTxt} = useSchedule();

    const [plantao, setPlantao] = useState(params?.plantao ?? '');
    const [products, setProducts] = useState([]);

    useEffect(() => {
        getProducts();
    }, []);

    const getProducts = () => {
        setLoading(true);

        api.get('/crm/plantoes')
            .then(res => {
                let productOptions = [];
                res.data.plantoes.map(plantao => productOptions.push({ label: plantao.imovel_nome, value: plantao.id }));
                setProducts(productOptions);
            })
            .catch(err => {
                Alert.alert('Ops, ocorreu algum erro!');
            })
            .then(() => setLoading(false));
    }

    const saveClient = () => {
        setLoading(true);

        const data = { nome, cpf, celular: telefone, email, anotacoes };
        api.post(`/crm/clientes/${plantao}`, data)
            .then(res => {
                setId(res.data.cliente.id);
                setInNome(res.data.cliente.nome);
                setImovelNome(res.data.cliente.plantao.imovel_nome);
                setOrigemTxt(res.data.cliente.origem_txt);
                
                if (routeFrom == 'SchedulingEdit') {
                    navigation.navigate('SchedulingEdit');
                } else if (routeFrom == 'SchedulingNew') {
                    navigation.navigate('SchedulingNew');
                } else {
                    navigation.navigate('CustomerList');
                }
            }).catch(err => {
                console.log(err.response);
                if (err?.response?.data?.errors) {
                    let errors = err.response.data.errors;
                    let text = '';
                    Object.keys(errors).map(error => {
                        text += `\n${errors[error][0]}`;
                    });
                    Alert.alert('Verifique os campos', text);
                }
            })
            .then(() => setLoading(false));
    }

    return (
        <KeyboardAvoidingView style={{ flex: 1 }} behavior="padding" >
            <PrivateHeader title={`Atendimentos`} />
            <ScrollView ref={scrollViewRef} contentContainerStyle={{ flexGrow: 1 }} keyboardShouldPersistTaps="handled" showsVerticalScrollIndicator={false}>
                <TouchableOpacity style={styles.buttonDate} onPress={() => navigation.goBack()}>
                    <View style={styles.icArrow}>
                        <ArrowLeft />
                    </View>
                    <Text style={styles.btnTextButtonDate}>NOVO CLIENTE</Text>
                </TouchableOpacity>
                {loading &&
                    <View style={{ flex: 1, justifyContent: "center", alignItems: "center", marginTop: 100 }}>
                        <ActivityIndicator size="large" color="#00467F" />
                    </View>
                }
                {!loading &&
                    <View style={[mainStyles.privateContainer, styles.customerContainer]}>
                        <Text style={mainStyles.labelMargin}>NOME:</Text>
                        <TextInput
                            placeholderTextColor="#BDBDBD"
                            underlineColorAndroid="transparent"
                            style={mainStyles.inputBorder}
                            value={nome}
                            autoCapitalize="words"
                            onChangeText={text => setNome(text)}
                        />
                        <Text style={mainStyles.labelMargin}>CELULAR:</Text>
                        <TextInput
                            placeholderTextColor="#BDBDBD"
                            underlineColorAndroid="transparent"
                            style={mainStyles.inputBorder}
                            value={telefone}
                            keyboardType="phone-pad"
                            onChangeText={text => setTelefone(phoneMask(text))}
                        />
                        <Text style={mainStyles.labelMargin}>E-MAIL:</Text>
                        <TextInput
                            placeholderTextColor="#BDBDBD"
                            underlineColorAndroid="transparent"
                            style={mainStyles.inputBorder}
                            value={email}
                            keyboardType="email-address"
                            autoCapitalize="none"
                            onChangeText={text => setEmail(text)}
                        />
                        <Text style={mainStyles.labelMargin}>CPF:</Text>
                        <TextInput
                            placeholderTextColor="#BDBDBD"
                            underlineColorAndroid="transparent"
                            style={mainStyles.inputBorder}
                            value={cpf}
                            keyboardType="phone-pad"
                            onChangeText={text => setCpf(cpfMask(text))}
                        />
                        <View style={styles.selectMargin}>
                            <Text style={mainStyles.labelMargin}>PRODUTO</Text>
                            <SelectBorder 
                                disabled={params?.plantao ? true : false}
                                options={products} 
                                value={plantao} 
                                onChange={value => setPlantao(value)} 
                                placeholder="Selecione..." 
                            />
                        </View>
                        <Text style={mainStyles.labelMargin}>INFORMAÇÕES ADICIONAIS:</Text>
                        <TextInput
                            placeholderTextColor="#BDBDBD"
                            underlineColorAndroid="transparent"
                            style={[mainStyles.inputBorder, { height: 170, textAlignVertical: 'top', paddingTop: 15 }]}
                            multiline={true}
                            numberOfLines={10}
                            onFocus={() => scrollViewRef.current.scrollTo({ x: 0, y: 300, animated: true })}
                            onChangeText={text => setAnotacoes(text)}
                            value={anotacoes}
                        />
                        <TouchableOpacity style={[mainStyles.btnBlueNew, styles.button]} disabled={loading} onPress={() => saveClient()}>
                            <Text style={mainStyles.btnTextBlueNew}>SALVAR CLIENTE</Text>
                        </TouchableOpacity>
                    </View>
                }
            </ScrollView>
        </KeyboardAvoidingView>
    );
}

export default CustomerNew;