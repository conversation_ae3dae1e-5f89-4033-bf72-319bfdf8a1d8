import { StyleSheet, Dimensions, Platform } from 'react-native';

const windowWidth = Dimensions.get('window').width;

const styles = StyleSheet.create({
    wrapper: {
        justifyContent: "space-between"
    },
    contentBottom: {
        paddingTop: 25,      
        paddingBottom: 40,
        shadowRadius: 2,
        shadowOffset: {
            width: 0,
            height: -2,
        },
        shadowColor: '#000000',
        elevation: 2,
        shadowOpacity: 1
    },
    ptop: {
        paddingTop: 25,
        paddingLeft: 20,
        paddingRight: 20
    },
    contentButtons: {
        flexDirection: "row",
        justifyContent: "space-between",
        paddingLeft: 20,
        paddingRight: 20
    },
    input: {
        width: '75%',
        height: 50
    },
    button: {
        width: '100%',
    },
    container: {
        maxWidth: 340,
        marginLeft: ((windowWidth - 340) /2),
        marginTop: 10
    },
    buttonDate: {
        backgroundColor: "#90B0C0",
        height: 58,
        width: '100%',
        alignItems: "center",
        justifyContent: "center"
    },
    icArrow: {
        position: "absolute",
        top: 23,
        left: 25
    },
    btnTextButtonDate: {
        fontFamily: "Ubuntu-Regular",
        color: "#FFF",
        fontSize: 15,
        letterSpacing: 0.46,
        textTransform: "uppercase"
    },
    divider: {
        borderBottomWidth: 1,
        borderBottomColor: '#DADADA',
        marginTop: 10,
    },
    marginTop: {
        marginTop: 25
    },
    w90: {
        paddingLeft: 20,
        paddingRight: 20
    },
    customer: {
        flexDirection: "row",
        justifyContent: "space-between",
        alignItems: "center",
        paddingTop: 20,
        paddingBottom: 20,
        borderBottomColor: "#DADADA",
        borderBottomWidth: 1
    },
    dFlex: {
        flexDirection: "row",
        alignItems: "center"
    },
    iconCustomer: {
        backgroundColor: "#F2F2F2",
        height: 70,
        width: 56,
        alignItems: "center",
        justifyContent: "center",
        marginRight: 6
    },
    iconUser: {
        width: 40
    },
    icArrowBtn: {
        transform: [{ rotate: "-90deg" }]
    },
    dataCustomer: {
        width: '76%'
    },
    nameCustomer: {
        fontFamily: 'Ubuntu-Regular',
        color: "#00467F",
        fontSize: 16,
        letterSpacing: 0.46
    },
    productCustomer: {
        fontFamily: 'Ubuntu-Light',
        color: "#000",
        fontSize: 14,
        letterSpacing: 0.46,
        marginTop: 3,
        marginBottom: 3
    },
    refCustomer: {
        fontFamily: 'Ubuntu-Light',
        color: "#2D719F",
        fontSize: 14,
        letterSpacing: 0.46
    },
    boxAbout: {
        marginTop: 20
    },
    boxText: {
        height: 47,
        borderWidth: 1,
        borderColor: "#E0E0E0",
        backgroundColor: "#EEE",
        justifyContent: "center",
        alignItems: "center"
    },
    textDisabled: {
        fontFamily: "Ubuntu-Regular",
        color: "#828282",
        fontSize: 13,
        letterSpacing: 1
    },
    dFlexSB: {
        flexDirection: "row",
        justifyContent: "space-between",
        marginBottom: 20
    },
    w33: {
        width: "30%"
    },
    txtLeft: {
        alignItems: "flex-start",
        paddingLeft: 15
    },
    m20: {
        marginTop: 20
    },
    actions: {
        flexDirection: "row",
        marginTop: 10,
        marginBottom: 20,
        justifyContent: "flex-start",
    },
    boxBtn: {
        marginRight: 20
    },
    btnBorder: {
        alignItems: "center",
        justifyContent: "center",
        borderWidth: 1,
        borderColor: "#00467F",
        height: 65,
        width: 75,
        borderRadius: 10
    },
    btnBorderDisabled: {
        borderColor: "#979797"
    },
    icUser: {
        width: 55,
        marginTop: 5
    },
    icCel: {
        width: 30,
    },
    icContact: {
        width: 45,
    },
    typeBtn: {
        fontFamily: "Ubuntu-Regular",
        color: "#828282",
        fontSize: 11,
        letterSpacing: 1,
        textAlign: "center",
        marginTop: 5
    }
});

export default styles;