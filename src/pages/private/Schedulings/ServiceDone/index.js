import React, { useEffect, useState } from 'react';
import { TextInput, Text, View, TouchableOpacity, ActivityIndicator, SafeAreaView } from 'react-native';
import { useIsFocused, useNavigation } from '@react-navigation/native';
import moment from 'moment';

import PrivateHeader from '../../../../components/headers/PrivateHeader';

import User from '../../../../assets/svgs/Users3';
import ArrowDownGrey from '../../../../assets/svgs/ArrowDownGrey';
import ArrowLeft from '../../../../assets/svgs/ArrowLeftSmallWhite';
import Phone from '../../../../assets/svgs/Celphone';
import Email from '../../../../assets/svgs/EmailDisabled';
import Whats from '../../../../assets/svgs/Whats2';

import styles from './styles';
import mainStyles from '../../../../mainStyles';
import { ScrollView } from 'react-native-gesture-handler';
import api from '../../../../services/api';

const ServiceDone = ({ navigation, route }) => {
    const isFocused = useIsFocused();
    const { id } = route.params;
    const [loading, setLoading] = useState(true);

    useEffect(() => {
        if(isFocused) getAgendamento();
    }, [isFocused]);

    const getAgendamento = () => {
        api.get(`/crm/agendamentos/${id}`).then(res => {
            console.log('ag', res);
        });
    }

    return (
        <View style={mainStyles.wrapper}>
            <PrivateHeader title={`Atendimentos`} />
            <TouchableOpacity style={styles.buttonDate} onPress={() => navigation.goBack()}>
                <View style={styles.icArrow}>
                    <ArrowLeft />
                </View>
                <Text style={styles.btnTextButtonDate}>ATENDIMENTO REALIZADO</Text>
            </TouchableOpacity>
            <ScrollView showsVerticalScrollIndicator={false}>                
                <View style={[mainStyles.privateContainer, styles.ptop]}>
                    <View style={styles.divider}></View>
                    <View style={styles.customer}>
                        <View style={styles.dFlex}>
                            <View style={styles.iconCustomer}>
                                <User style={styles.iconUser} />
                            </View>
                            <View style={styles.dataCustomer}>
                                <Text style={styles.nameCustomer}>Ronaldo Castro Barbosa Se...</Text>
                                <Text style={styles.refCustomer}>Agendamento</Text>
                            </View>
                            <ArrowDownGrey style={{transform: [{ rotate: "-90deg" }]}} />
                        </View>
                    </View>
                    <View style={styles.boxAbout}>
                        <Text style={mainStyles.labelMargin}>PRODUTO:</Text>
                        <View style={[styles.boxText, styles.txtLeft]}>
                            <Text style={styles.textDisabled}>Urban Tatuapé</Text>
                        </View>
                    </View>
                    <View style={styles.dFlexSB}>
                        <View style={styles.w33}>
                            <View style={styles.boxAbout}>
                                <Text style={mainStyles.labelMargin}>DIA:</Text>
                                <View style={styles.boxText}>
                                    <Text style={styles.textDisabled}>15/10/2022</Text>
                                </View>
                            </View>
                        </View>
                        <View style={styles.w33}>
                            <View style={styles.boxAbout}>
                                <Text style={mainStyles.labelMargin}>CHEGADA:</Text>
                                <View style={styles.boxText}>
                                    <Text style={styles.textDisabled}>10:55</Text>
                                </View>
                            </View>
                        </View>
                        <View style={styles.w33}>
                            <View style={styles.boxAbout}>
                                <Text style={mainStyles.labelMargin}>SAÍDA:</Text>
                                <View style={styles.boxText}>
                                    <Text style={styles.textDisabled}>12:22</Text>
                                </View>
                            </View>
                        </View>
                    </View>
                    <View style={styles.divider}></View>
                    <View style={styles.m20}>
                        <Text style={mainStyles.labelMargin}>Contato</Text>
                        <View style={styles.actions}>
                            <View style={styles.boxBtn}>
                                <TouchableOpacity style={styles.btnBorder} onPress={() => '' }>
                                    <Phone style={styles.icCel}/>
                                </TouchableOpacity>
                                <Text style={styles.typeBtn}>Telefone</Text>
                            </View>
                            <View style={styles.boxBtn}>
                                <TouchableOpacity style={styles.btnBorder} onPress={() => '' }>
                                    <Whats style={styles.icContact}/>
                                </TouchableOpacity>
                                <Text style={styles.typeBtn}>WhatsApp</Text>
                            </View>
                            <View style={styles.boxBtn}>
                                <TouchableOpacity style={[styles.btnBorder, styles.btnBorderDisabled]} disabled>
                                    <Email style={styles.icEmail}/>
                                </TouchableOpacity>
                                <Text style={styles.typeBtn}>E-mail</Text>
                            </View>
                        </View>
                    </View>
                    <View style={styles.divider}></View>
                    
                    {/* {loading &&
                        <View style={{ flex: 1, justifyContent: "center", alignItems: "center", marginTop: 30 }}>
                            <ActivityIndicator size="large" color="#00467F" />
                        </View>
                    }
                    {!loading &&
                        
                    } */}
                </View>
            </ScrollView>
        </View>
    );
}

export default ServiceDone;