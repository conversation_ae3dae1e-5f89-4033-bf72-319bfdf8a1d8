import React, { useEffect, useState } from 'react';
import { Text, View, ScrollView, TouchableOpacity, Alert, Image } from 'react-native';

import PrivateHeader from '../../../components/headers/PrivateHeader';

import styles from './styles';
import mainStyles from '../../../mainStyles';

import { useService } from '../../../context/service';

import api from '../../../services/api';

import AlertFooter from '../../../components/AlertFooter';
import { useAuth } from '../../../context/auth';

const Turn = ({navigation}) => {
    const [loading, setLoading] = useState(true);
    const [name, setName] = useState('');
    const [confirmStartService, setConfirmStartService] = useState(false);
    const [confirmExit, setConfirmExit] = useState(false);

    const { exit, refreshQueue } = useService();
    const {user} = useAuth();

    useEffect(() => {
        api.get('/fila').then(res => {
            let name = res.data.fila.atendimento.cliente.nome;
            if(name){
                setName(name);
            }
        })
        .catch(err => {
            console.log(err.response);
        });
    }, []);

    const startService = () => {
        api.post('/atendimento/iniciar').then(res => {
            refreshQueue();
            navigation.navigate('TurnActive');
        })
        .catch(err => {
            Alert.alert('Erro', err.response.data.message);
            console.log(err.response);
        });
    }

    const handleExit = async () => {
        const res = await exit();
        if(res){
            navigation.navigate('PrivateMain');
        }
    }

    return (
        <View style={mainStyles.wrapper}>
            {confirmStartService &&
                <AlertFooter 
                    text={
                        <Text>
                            {`Você deseja realmente `}<Text style={{fontFamily: 'Ubuntu-Bold'}}>{`iniciar o atendimento?`}</Text>
                        </Text>
                    }
                    btnText={`INICIAR ATENDIMENTO`}
                    btnBorder={true}
                    close={() => setConfirmStartService(false)}
                    action={() => startService()}
                />
            }
            {confirmExit &&
                <AlertFooter 
                    text={
                        <Text>
                            {`Você deseja realmente\nsair da fila?`}
                        </Text>
                    }
                    btnText={`SIM, SAIR DA FILA`}
                    btnBorder={true}
                    close={() => setConfirmExit(false)}
                    action={() => handleExit()}
                />
            }
            <PrivateHeader title={`Fila`} />
            <ScrollView style={styles.container} showsVerticalScrollIndicator={false}>
                <View style={mainStyles.bgHourCheckin}>
                    <Text style={mainStyles.textHourCheckin}>Horário do seu check in:  {user.fila.checkin_horario}</Text>
                </View>
                {user.fila.sorteio_horario &&
                    <View>
                        <Text style={mainStyles.sorteioText}>Horário do sorteio: {user.fila.sorteio_horario}</Text>
                        <View style={styles.divider}></View>
                    </View>
                }
                <View style={styles.rowOptions}>
                    <View style={styles.contentOption}>
                        <Text style={styles.textStatus}>Status e posição na fila:</Text>
                        <Text style={styles.textWait}>Chegou a sua vez!</Text>
                    </View>
                </View>
                <View style={styles.contentOption}>
                    <View style={styles.option}>
                        <View style={styles.optionBgColor}></View>
                        <View>
                            <Image source={require('../../../assets/svgs/MegaPhoneLight.png')} style={{width: 140}} />
                        </View>
                    </View>
                </View>
                <View style={styles.boxWaiting}>
                    <View style={styles.boxWaitingRow}>
                        <View style={styles.contentWaiting}>
                            <Text style={styles.textClient}>{`Quem é o seu cliente:`}</Text>                           
                            <Text style={styles.nameClient}>{name}</Text>                           
                        </View>
                    </View>
                </View>
                <View style={styles.divider}></View>
                <View style={styles.contentTop}>
                    <View style={ styles.contentButtons}>    
                        <TouchableOpacity style={[mainStyles.btnCenterBlue, styles.buttonCenterBlue]} onPress={() => setConfirmStartService(true)}>
                            <Text style={[mainStyles.btnTextCenterBlue, styles.textBtnCenterBlue]}>{`INICIAR\nATENDIMENTO`}</Text>
                        </TouchableOpacity>          
                        <TouchableOpacity style={[mainStyles.btnCenterOutlineBlue, styles.buttonCenterOutlineBlue]} onPress={() => setConfirmExit(true)}>
                            <Text style={[mainStyles.btnTextCenterOutlineBlue, styles.textBtnCenterOutlineBlue]}>DESISTIR E SAIR</Text>
                        </TouchableOpacity>
                    </View>
                </View>
            </ScrollView>
        </View>
    );
}

export default Turn;