import React, { useState, useEffect } from 'react';
import { TouchableOpacity, View, Text, ScrollView, Alert, ActivityIndicator } from 'react-native';
import styles from './styles';
import Close from '../../../assets/svgs/Close';
import Calendar from '../../../assets/svgs/Calendar';
import mainStyles from '../../../mainStyles';

import PrivateHeader from '../../../components/headers/PrivateHeader';

import { useService } from '../../../context/service';
import { useAuth } from '../../../context/auth';
import api from '../../../services/api';

const RowCheckin = ({ navigation }) => {

    const [loading, setLoading] = useState(true);
    const [brokers, setBrokers] = useState([]);


    const { queue } = useService();
    const { user } = useAuth();

    useEffect(() => {
        getBrokers();
    }, []);

    const getBrokers = () => {
        api.get('/fila/lista')
            .then(res => {
                console.log(res.data.fila);
                setBrokers(res.data.corretores);
                setLoading(false);
            })
            .catch(err => {
                Alert.alert('Erro', 'Não foi possível obter a lista');
                setLoading(false);
            });
    }

    return (
        <>
            <View style={mainStyles.wrapper}>
                <PrivateHeader title={`Fila`} back={() => navigation.navigate('PrivateMain')} />

                <View style={styles.container}>
                    <View style={mainStyles.bgHourCheckin}>
                        <Text style={mainStyles.textHourCheckin}>Horário do seu check in: {user.fila.checkin_horario}</Text>
                    </View>
                    {user.fila.sorteio_horario &&
                        <View>
                            <Text style={mainStyles.sorteioText}>Horário do sorteio: {user.fila.sorteio_horario}</Text>
                            <View style={styles.divider}></View>
                        </View>
                    }
                    {queue.tipo_fila === 'provisoria' &&
                        <>
                            <View style={styles.provisionalRow}>
                                <Text style={styles.provisionalText}><Text style={styles.spanColor}>Lembre-se:</Text> essa é uma fila provisória.</Text>
                                <Text style={styles.provisionalText}>Sua posição irá <Text style={styles.spanColor}>MUDAR APÓS O SORTEIO.</Text> </Text>
                            </View>
                            <View style={styles.divider}></View>
                        </>
                    }
                </View>
                <ScrollView showsVerticalScrollIndicator={false}>
                    <View style={[styles.container, styles.withoutMargin]}>
                        {/* <Text style={styles.title}>{`Fila por ordem\nde`} <Text style={styles.titleCheckin}>{queue.tipo_fila === 'sorteio' ? 'sorteio' : 'Check In'}</Text></Text> */}
                        {loading &&
                            <View style={{ flex: 1, justifyContent: "center", alignItems: "center" }}>
                                <ActivityIndicator size="large" color="#00467F" />
                            </View>
                        }
                        {!loading &&
                            <ScrollView showsVerticalScrollIndicator={false}>
                                <View>
                                    {brokers.map((broker, index) => {
                                        if (broker.is_user_logado) {
                                            return (
                                                <View key={index}>
                                                    <View style={styles.rowActive}>
                                                        {broker.posicao &&
                                                            <Text style={styles.countActive} maxFontSizeMultiplier={1}>{broker.posicao}</Text>
                                                        }
                                                        {!broker.posicao &&
                                                            <Calendar style={{ color: "#00467F", width: 20, marginLeft: 38, marginRight: 40 }} />
                                                        }
                                                        <View>
                                                            <Text style={styles.personNameActive}>{broker.apelido}</Text>
                                                            {broker.superintendente &&
                                                                <Text style={styles.super}>
                                                                    <Text style={styles.superTitle}>Sup. </Text>
                                                                    <Text style={styles.superName}>{broker.superintendente.apelido}</Text>
                                                                </Text>
                                                            }
                                                            {broker.status_atendimento !== 'Disponível' &&
                                                                <Text style={styles.personStatusInactive}>{broker.status_atendimento}</Text>
                                                            }
                                                            {broker.status_atendimento === 'Disponível' &&
                                                                <Text style={styles.personStatusActive}>{broker.status_atendimento}</Text>
                                                            }
                                                        </View>
                                                    </View>
                                                    <View style={styles.borderBottomActive}></View>
                                                </View>
                                            );
                                        } else {
                                            return (
                                                <View key={index} style={[styles.row, styles.borderBottom]}>
                                                    {broker.posicao &&
                                                        <Text style={styles.count} maxFontSizeMultiplier={1}>{broker.posicao}</Text>
                                                    }
                                                    {!broker.posicao &&
                                                        <Calendar style={{ color: "#CCC", width: 20, marginRight: 32 }} />
                                                    }
                                                    <View>
                                                        <Text style={styles.personName}>{broker.apelido}</Text>

                                                        {broker.superintendente &&
                                                            <Text style={styles.super}>
                                                                <Text style={styles.superTitle}>Sup. </Text>
                                                                <Text style={styles.superName}>{broker.superintendente.apelido}</Text>
                                                            </Text>
                                                        }

                                                        {broker.status_atendimento !== 'Disponível' &&
                                                            <Text style={styles.personStatusInactive}>{broker.status_atendimento}</Text>
                                                        }
                                                        {broker.status_atendimento === 'Disponível' &&
                                                            <Text style={styles.personStatusActive}>{broker.status_atendimento}</Text>
                                                        }
                                                    </View>
                                                </View>
                                            );
                                        }
                                    })}
                                </View>
                            </ScrollView>
                        }
                    </View>
                </ScrollView>
                <View style={styles.contentBottom}>
                    <View style={styles.contentButton}>
                        <TouchableOpacity style={[mainStyles.btnBlueNew, styles.buttonLarge]} onPress={() => navigation.goBack()}>
                            <Text style={mainStyles.btnTextBlueNew}>VOLTAR</Text>
                        </TouchableOpacity>
                    </View>
                </View>
            </View>
        </>
    );
}

export default RowCheckin;