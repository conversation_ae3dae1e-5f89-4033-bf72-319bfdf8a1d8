import { StyleSheet, Dimensions, Platform } from 'react-native';

const windowWidth = Dimensions.get('window').width;
const windowHeight = Dimensions.get('window').height;

const styles = StyleSheet.create({
    wrapper: {
        justifyContent: "space-between"
    },
    container: {
        width: windowWidth * 0.9,
        marginLeft: windowWidth * 0.05,
        marginTop: 30,
        backgroundColor: "#FFF"
    },
    withoutMargin: {
        marginTop: 0,
        marginBottom: 5
    },
    provisionalText: {
        fontFamily: "Ubuntu-Regular",
        color: "#452323",
        marginTop: Platform.OS === 'ios' ? 8 : 4,
        lineHeight: 15,
        letterSpacing: 1,
        fontSize: 12
    },
    spanColor: {
        color: "#FF6542"
    },
    divider: {
        height: 1,
        backgroundColor: "#00467F4D",
        marginTop: 30
    },
    title: {
        fontFamily: "Ubuntu-Light",
        fontSize: 24,
        letterSpacing: 1,
        color: "#00467F",
        marginBottom: 25,
        marginLeft: 25
    },
    titleCheckin: {
        fontFamily: "Ubuntu-Bold",
    },
    count: {
        color: "#00467F",
        fontFamily: "Ubuntu-Medium",
        fontSize: 35,
        marginRight: 10,
        width: 90,
        textAlign: "center",
    },
    countActive: {
        color: "#00467F",
        fontFamily: "Ubuntu-Medium",
        fontSize: 35,
        marginRight: 10,
        width: 70,
        textAlign: "center",
    },
    personName: {
        color: "#00467F",
        fontFamily: "Ubuntu-Regular",
        letterSpacing: 1,
        fontSize: 14
    },
    personStatus: {
        color: "#828282",
        fontFamily: "Ubuntu-Regular",
        letterSpacing: 1
    },
    row: {
        flexDirection: "row",
        marginBottom: 5,
        alignItems: "center",
        borderWidth: 1,
        borderColor: "#FFF",
        paddingVertical: 10,
        paddingTop: 15,
        paddingBottom: 20
    },
    rowActive: {
        flexDirection: "row",
        marginBottom: 5,
        alignItems: "center",
        borderWidth: 1,
        borderColor: "#00467F",
        paddingVertical: 10,
        marginTop: 15
    },
    borderBottom: {
        borderBottomColor: "#DADADA",
        borderBottomWidth: 1
    },
    borderBottomActive: {
        backgroundColor: "#CCC",
        height: 1,
        marginTop: 15
    },
    personNameActive: {
        color: "#00467F",
        fontFamily: "Ubuntu-Regular",
        letterSpacing: 1,
        fontSize: 14
    },
    superTitle: {
        color: "#000",
        fontFamily: "Ubuntu-Bold",
        letterSpacing: 0.46
    },
    super: {
        marginVertical: 3
    },
    superName: {
        color: "#000",
        fontFamily: "Ubuntu-Regular",
        letterSpacing: 0.46,
    },
    personStatusActive: {
        color: "#1B9C20",
        fontFamily: "Ubuntu-Medium",
        letterSpacing: 1
    },
    personStatusInactive: {
        color: "#FF6542",
        fontFamily: "Ubuntu-Medium",
        letterSpacing: 1
    },
    icSmile: {
        position: "absolute",
        right: 20,
        width: 30
    },
    contentBottom: {
        paddingTop: 25,      
        paddingBottom: 40,
        shadowRadius: Platform.OS === 'ios' ? 2 : 16,
        shadowOffset: {
            width: 0,
            height: Platform.OS === 'ios' ? -2 : 12,
        },
        shadowColor: Platform.OS === 'ios' ? '#CCC' : "#000",
        elevation: Platform.OS === 'ios' ? 2 : 24,
        shadowOpacity: 1,
        backgroundColor: "#FFF",
        position: "relative"
    },
    boxShadow: {
        height: 20,
        backgroundColor: "#FFF",
        elevation: 5,
        shadowColor: "#000",
        marginBottom: 0,
        position: "absolute",
        top: -20,
        right: 0,
        left: 0
    },
    contentButton: {
        flexDirection: "row",
        justifyContent: "center"
    },
    buttonLarge: {
        width: '80%'
    }
});

export default styles;