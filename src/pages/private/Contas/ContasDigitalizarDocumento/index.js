import React, { useState, useRef, useEffect } from 'react';
import { Platform, Text, TouchableOpacity, View, Image, Alert, ActivityIndicator, TextInput, PermissionsAndroid, KeyboardAvoidingView, ScrollView, Keyboard } from 'react-native';

import PrivateHeader from '../../../../components/headers/PrivateHeader';
import { useRegister } from '../../../../context/register';

import mainStyles from '../../../../mainStyles';
import styles from './styles';

import Camera from '../../../../assets/svgs/Camera2';
import ArrowLeft from '../../../../assets/svgs/ArrowLeftSmallWhite';

import * as ImagePicker from 'react-native-image-picker';
import DocumentPicker from 'react-native-document-picker';
import { requestCameraPermission } from "../../../../useful/permissions";
import api from "../../../../services/api";
import CustomAlert from '../components/CustomAlert';

const DigitalizarDocumento = ({ route, navigation }) => {
    const image = route.params?.image ?? null;
    const id = route.params?.id ?? null;

    const [alertVisible, setAlertVisible] = useState(false);
    const [alertOptions, setAlertOptions] = useState([]);

    const showCustomAlert = () => {
        setAlertVisible(true);
    };

    const closeCustomAlert = () => {
        setAlertVisible(false);
    };

    const chooseImage = (tipo) => {
        let alertProperties = [];

        if (Platform.OS === 'ios') {
            alertProperties = [
                { text: "Tirar foto", onPress: () => openCamera(tipo) },
                { text: "Galeria", onPress: () => openImageLibrary(tipo) },
                { text: "Escolher arquivo", onPress: () => openDocumentPicker(tipo) },
                { text: "Fechar" }
            ];

            Alert.alert(
                "Enviar documento",
                "Como deseja enviar o documento?",
                alertProperties,
                { cancelable: true }
            );
        } else {
            /* alertProperties = [
                { text: "Fechar" },
                { text: "Galeria", onPress: () => openImageLibrary(tipo) },
                { text: "Tirar foto", onPress: () => openCamera(tipo) },
                { text: "Escolher arquivo", onPress: () => openDocumentPicker(tipo) }
            ]; */

            const alertOptions = [
                { text: "FECHAR", onPress: () => closeCustomAlert() },
                { text: "GALERIA", onPress: () => { openImageLibrary(tipo); closeCustomAlert(); } },
                { text: "TIRAR FOTO", onPress: () => { openCamera(tipo); closeCustomAlert(); } },
                { text: "ESCOLHER ARQUIVO", onPress: () => { openDocumentPicker(tipo); closeCustomAlert(); } },
            ];

            setAlertOptions(alertOptions);

            setAlertVisible(true);
        }
    }

    const imageOptions = {
        mediaType: 'photo',
        includeBase64: false,
        maxHeight: 1024,
        maxWidth: 1024
    };

    const openCamera = async (tipo) => {
        const hasPermission = await requestCameraPermission();
        if (hasPermission) {
            ImagePicker.launchCamera(imageOptions, res => {
                if (res.errorCode) {
                    Alert.alert('Erro', 'Câmera não disponível');
                    return;
                }
                if (res?.assets) {
                    upload(res.assets[0], tipo);
                }
            });
        }
    }

    const openImageLibrary = (tipo) => {
        ImagePicker.launchImageLibrary(imageOptions, res => {
            if (res?.assets) {
                upload(res.assets[0], tipo);
            }

        });
    }

    const openDocumentPicker = async (tipo) => {
        try {
            const res = await DocumentPicker.pick({
                type: [DocumentPicker.types.allFiles],
            });
            if (res) {
                upload(res[0], tipo);
            }
        } catch (err) {
            if (DocumentPicker.isCancel(err)) {
                console.log('User canceled document picker');
            } else {
                throw err;
            }
        }
    };

    const upload = (image, tipo) => {
        navigation.navigate('ContasAnalisandoDocumento', { image, tipo, id });
    }

    return (
        <>
            <KeyboardAvoidingView style={{ flex: 1 }} behavior="padding" >
                <ScrollView contentContainerStyle={{ flexGrow: 1, backgroundColor: "#FFF" }}>

                    <PrivateHeader title={'Contas'} />
                    <TouchableOpacity
                        style={styles.buttonTitle}
                        onPress={() => navigation.goBack()}>
                        <View style={styles.icArrow}>
                            <ArrowLeft />
                        </View>
                        <Text style={styles.titlePag}>Digitalizar Documento</Text>
                    </TouchableOpacity>
                    <View style={mainStyles.wrapperRegister}>
                        <View>
                            <View style={[mainStyles.container, styles.container]}>
                                {/* {inDocument.length > 0 && ''} */}
                                <Text style={[mainStyles.label, { textAlign: "center" }]}>Tire uma foto do documento do cliente</Text>
                                <Text style={[mainStyles.label, { textAlign: "center", color: "#979797" }]}>RG, CNH ou RNE</Text>
                                <View style={styles.divider}></View>
                                <View style={mainStyles.rowUploads}>
                                    {image === null &&
                                        <TouchableOpacity style={mainStyles.btnUploadCol2} onPress={() => chooseImage('frente')}>
                                            <Camera style={[mainStyles.btnIconUpload, mainStyles.btnIconUploadM]} />
                                            <Text style={[mainStyles.btnTextUpload2, mainStyles.btnTextUploadCol, mainStyles.btnTextUploadCol2]}>{`FOTO DO SEU\nDOCUMENTO\n`}<Text style={mainStyles.bold}>FRENTE</Text></Text>
                                        </TouchableOpacity>
                                    }
                                    {image !== null &&
                                        <TouchableOpacity style={mainStyles.boxPreviewOcr} onPress={() => chooseImage('frente')}>
                                            <View style={mainStyles.boxPreviewImageContentOcr}>
                                                <Image source={{ uri: image.uri }} style={mainStyles.previewCol} />
                                            </View>
                                        </TouchableOpacity>
                                    }


                                    <TouchableOpacity disabled={image === null} style={[mainStyles.btnUploadCol2, { opacity: image === null ? 0.3 : 1 }]} onPress={() => chooseImage('verso')}>
                                        <Camera style={[mainStyles.btnIconUpload, mainStyles.btnIconUploadM]} />
                                        <Text style={[mainStyles.btnTextUpload2, mainStyles.btnTextUploadCol, mainStyles.btnTextUploadCol2]}>{`FOTO DO SEU\nDOCUMENTO\n`}<Text style={mainStyles.bold}>VERSO</Text></Text>
                                    </TouchableOpacity>
                                </View>
                            </View>
                        </View>
                        <CustomAlert
                            visible={alertVisible}
                            onClose={closeCustomAlert}
                            title="Enviar documento"
                            message="Como deseja enviar o documento?"
                            options={alertOptions}
                        />
                    </View>
                    {/* <View style={mainStyles.contentBottomRegister}>
                        <View style={[mainStyles.container, styles.contentButtons]}>              
                            <TouchableOpacity style={[mainStyles.btnBlueLight, styles.btnLarge, image === null || backImage === null ? styles.btnDisabled : '']} onPress={() => update()}>
                                <Text style={mainStyles.btnTextCenterBlueLight}>AVANÇAR</Text>
                            </TouchableOpacity>
                        </View>
                    </View> */}
                </ScrollView>
            </KeyboardAvoidingView>
        </>
    );
}

export default DigitalizarDocumento;