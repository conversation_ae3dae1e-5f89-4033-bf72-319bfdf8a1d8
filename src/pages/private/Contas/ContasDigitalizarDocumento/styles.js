import { StyleSheet, Dimensions } from 'react-native';

const windowWidth = Dimensions.get('window').width;
const windowHeight = Dimensions.get('window').height;

const styles = StyleSheet.create({
    container: {
        paddingTop: 20
    },
    wrapper: {
        justifyContent: "space-between"
    },
    input: {
        marginBottom: 35
    },
    contentBottom: {        
        marginBottom: 30
    },
    contentButtons: {
        flexDirection: "row",
        justifyContent: "space-between"
    },
    button: {
        width: '45%',
    },
    btnLarge: {
        width: '100%',
    },
    camera: {
        width: windowWidth,
        height: windowHeight
    },
    capture: {
        position: "absolute",
        bottom: 40,
        width: 80,
        height: 80,
        borderRadius: 40,
        borderWidth: 2,
        borderColor: '#FFF',
        justifyContent: "center",

    },
    caputreIn: {
        position: "absolute",
        bottom: 3,
        left: 3,
        width: 70,
        height: 70,
        borderRadius: 35,
        backgroundColor: '#FFF'
    },
    preview: {
        width: '100%',
        height: 150,
        resizeMode: "contain",
        borderRadius: 5,
        marginTop: 20
    },
    divider: {
        borderBottomWidth: 1,
        borderBottomColor: '#DADADA',
        marginTop: 20,
        marginBottom: 30
    },
    buttonTitle: {
      backgroundColor: '#90B0C0',
      height: 70,
      width: '100%',
      alignItems: 'center',
      justifyContent: 'center',
      flexDirection: 'row',
      marginBottom: 20
    },
    icArrow: {
      position: 'absolute',
      left: 20,
    },
    titlePag: {
      fontFamily: 'Ubuntu-Regular',
      color: '#FFF',
      fontSize: 15,
      letterSpacing: 0.46,
      textTransform: 'uppercase'
    },
    btnDisabled: {
        backgroundColor: "#979797",
        borderColor: "#979797"
    }
});

export default styles;