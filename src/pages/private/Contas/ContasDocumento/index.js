import React, { useState, useRef, useEffect } from 'react';
import { Platform, Text, TouchableOpacity, View, Image, Alert, ActivityIndicator, TextInput, PermissionsAndroid, KeyboardAvoidingView, ScrollView, Modal } from 'react-native';
import * as ImagePicker from 'react-native-image-picker';
import DocumentPicker from 'react-native-document-picker';

import PrivateHeader from '../../../../components/headers/PrivateHeader';
import Select from '../../../../components/Select';

import { dateMask } from '../../../../useful/masks';

import mainStyles from '../../../../mainStyles';
import styles from './styles';

import ArrowLeft from '../../../../assets/svgs/ArrowLeftSmallWhite';
import { useIsFocused } from "@react-navigation/native";
import { ContasClienteBox } from "../components/ContasClienteBox";
import { createSelectOptions } from "../helpers/functions";
import api from "../../../../services/api";
import moment from "moment";
import { convertDateToAmerican } from "../../../../useful/conversions";
import ContasCompartilharBox from "../components/ContasCompartilharBox";

import Camera from '../../../../assets/svgs/Camera';
import { requestCameraPermission } from '../../../../useful/permissions';
import { useAuth } from '../../../../context/auth';
import CustomAlert from '../components/CustomAlert';
import PDF from '../../../../assets/svgs/PDF';

const ContasDocumento = ({ route, navigation }) => {
    const { id, conta } = route.params;
    const { token, user } = useAuth();
    const image = route.params?.image ?? null;
    const editable = conta.itens.autorizacoes.pendencias > 0;

    const isFocused = useIsFocused();

    const [cliente, setCliente] = useState(null);

    const [loading, setLoading] = useState(false);

    const [inTipo, setInTipo] = useState('');
    const [inNumero, setInNumero] = useState('');
    const [inOrgaoEmissor, setInOrgaoEmissor] = useState('');
    const [inEstadoEmissor, setInEstadoEmissor] = useState('');
    const [inDataEmissao, setInDataEmissao] = useState('');
    const [inNacionalidade, setInNacionalidade] = useState('');

    const [tipoOptions, setTipoOptions] = useState([]);
    const [orgaoEmissorOptions, setOrgaoEmissorOptions] = useState([]);
    const [estadoEmissorOptions, setEstadoEmissorOptions] = useState([]);
    const [documentoFrentePath, setDocumentoFrentePath] = useState('');
    const [documentoVersoPath, setDocumentoVersoPath] = useState('');

    const [alertVisible, setAlertVisible] = useState(false);
    const [alertOptions, setAlertOptions] = useState([]);

    const [mimeTypeFrente, setMimeTypeFrente] = useState('');
    const [mimeTypeVerso, setMimeTypeVerso] = useState('');

    useEffect(() => {
        if (isFocused) {
            getDocumento();
        }
    }, [isFocused]);

    const showCustomAlert = () => {
        setAlertVisible(true);
    };

    const closeCustomAlert = () => {
        setAlertVisible(false);
    };

    const getDocumento = () => {
        setLoading(true);
        console.log(id)
        api.get(`/crm/contas/${id}/documento`).then(res => {
            console.log(res.data);
            const { itens, selects } = res.data;

            setCliente(res.data.cliente);

            setDocumentoFrentePath(res.data.dados.documento_arquivo.documento_frente_url);
            setDocumentoVersoPath(res.data.dados.documento_arquivo.documento_verso_url);
            setMimeTypeFrente(res.data.dados.documento_arquivo.mime_type_frente);
            setMimeTypeVerso(res.data.dados.documento_arquivo.mime_type_verso);
            setInTipo(itens.DocumentoIdentificacao.Tipo ?? '');
            setInNumero(itens.DocumentoIdentificacao.Numero ?? '');
            setInOrgaoEmissor(itens.DocumentoIdentificacao.OrgaoEmissor ?? '');
            setInEstadoEmissor(itens.DocumentoIdentificacao.EstadoEmissor ?? '');
            setInDataEmissao(itens.DocumentoIdentificacao.DataEmissao ? moment(itens.DocumentoIdentificacao.DataEmissao).format('DD/MM/YYYY') : '');
            setInNacionalidade(itens.Nacionalidade ?? '');

            setTipoOptions(createSelectOptions(selects.Tipo));
            setOrgaoEmissorOptions(createSelectOptions(selects.OrgaoEmissor));
            setEstadoEmissorOptions(createSelectOptions(selects.EstadoEmissor));
        }).catch(error => {
            Alert.alert('Erro ao obter o documento');
            console.log(error);
        }).then(() => setLoading(false));
    }

    const update = () => {
        setLoading(true);

        api.put(`/crm/contas/${id}/documento`, {
            DocumentoIdentificacao: {
                Tipo: inTipo,
                Numero: inNumero,
                OrgaoEmissor: inOrgaoEmissor,
                EstadoEmissor: inEstadoEmissor,
                DataEmissao: inDataEmissao ? convertDateToAmerican(inDataEmissao) : null,
            },
            Nacionalidade: inNacionalidade,
            DocumentoFrentePath: documentoFrentePath ?? null,
            DocumentoVersoPath: documentoVersoPath ?? null
        }).then(res => {
            navigation.goBack();
        }).catch(error => {
            let errors = error?.response?.data?.errors ?? null;
            let errorText = '';
            if (errors) {
                Object.keys(errors).map(key => {
                    errors[key].map(error => {
                        errorText += `- ${error}\n`;
                    });
                });
            }
            console.log('error save document', error);
            Alert.alert('Não foi possível salvar', errorText,);
        }).then(() => setLoading(false));
    }

    const chooseImage = (tipo) => {
        let alertProperties = [];

        if (Platform.OS === 'ios') {
            alertProperties = [
                { text: "Tirar foto", onPress: () => openCamera(tipo) },
                { text: "Galeria", onPress: () => openImageLibrary(tipo) },
                { text: "Escolher arquivo", onPress: () => openDocumentPicker(tipo) },
                { text: "Fechar" }
            ];

            Alert.alert(
                "Enviar documento",
                "Como deseja enviar o documento?",
                alertProperties,
                { cancelable: true }
            );
        } else {
            /* alertProperties = [
                { text: "Fechar" },
                { text: "Galeria", onPress: () => openImageLibrary(tipo) },
                { text: "Tirar foto", onPress: () => openCamera(tipo) },
                { text: "Escolher arquivo", onPress: () => openDocumentPicker(tipo) }
            ]; */

            const alertOptions = [
                { text: "FECHAR", onPress: () => closeCustomAlert() },
                { text: "GALERIA", onPress: () => { openImageLibrary(tipo); closeCustomAlert(); } },
                { text: "TIRAR FOTO", onPress: () => { openCamera(tipo); closeCustomAlert(); } },
                { text: "ESCOLHER ARQUIVO", onPress: () => { openDocumentPicker(tipo); closeCustomAlert(); } },
            ];

            setAlertOptions(alertOptions);

            setAlertVisible(true);
        }
    }

    const imageOptions = {
        mediaType: 'photo',
        includeBase64: false,
        maxHeight: 1024,
        maxWidth: 1024
    };

    const openCamera = async (tipo) => {
        const hasPermission = await requestCameraPermission();
        if (hasPermission) {
            ImagePicker.launchCamera(imageOptions, res => {
                if (res.errorCode) {
                    Alert.alert('Erro', 'Câmera não disponível');
                    return;
                }
                if (res?.assets) {
                    upload(res.assets[0], tipo);
                }
            });
        }
    }

    const openImageLibrary = (tipo) => {
        ImagePicker.launchImageLibrary(imageOptions, res => {
            if (res?.assets) {
                upload(res.assets[0], tipo);
            }

        });
    }

    const openDocumentPicker = async (tipo) => {
        try {
            const res = await DocumentPicker.pick({
                type: [DocumentPicker.types.allFiles],
            });
            if (res) {
                upload(res[0], tipo);
            }
        } catch (err) {
            if (DocumentPicker.isCancel(err)) {
                console.log('User canceled document picker');
            } else {
                throw err;
            }
        }
    };

    const upload = (image, tipo) => {
        navigation.navigate('ContasAnalisandoDocumento', { image, tipo, id, conta });
    }

    const deleteDocumentoFrente = () => {
        setDocumentoFrentePath('');
    }

    const deleteDocumentoVerso = () => {
        setDocumentoVersoPath('');
    }

    return (
        <>
            <View style={mainStyles.wrapper}>
                <PrivateHeader title={'Contas'} />
                <TouchableOpacity
                    style={styles.buttonTitle}
                    onPress={() => navigation.goBack()}>
                    <View style={styles.icArrow}>
                        <ArrowLeft />
                    </View>
                    <Text style={styles.titlePag}>Documento</Text>
                </TouchableOpacity>
                <KeyboardAvoidingView style={{ flex: 1 }} behavior="padding" >
                    <ScrollView>
                        {loading &&
                            <View style={{ flex: 1, marginTop: 50, justifyContent: "center", alignItems: "center" }}>
                                <ActivityIndicator size="large" color="#00467F" />
                            </View>
                        }
                        {!loading &&
                            <>
                                <View style={mainStyles.wrapperRegister}>
                                    <View>
                                        <View style={[mainStyles.container, styles.container]}>
                                            <ContasClienteBox cliente={cliente} />
                                            <View style={styles.divider}></View>
                                            {/* {inDocument.length > 0 && ''} */}
                                            {documentoFrentePath === '' &&
                                                <Text style={[styles.textInfo, { textAlign: "center" }]}>Não perca tempo! Digitalize o documento e tenha os dados preenchidos automaticamente.</Text>
                                            }
                                            <View style={mainStyles.rowUploads}>
                                                {documentoFrentePath === '' &&
                                                    <TouchableOpacity style={mainStyles.btnUploadCol2} onPress={() => chooseImage('frente')}>
                                                        <Camera style={[mainStyles.btnIconUpload, mainStyles.btnIconUploadM]} />
                                                        <Text style={[mainStyles.btnTextUpload2, mainStyles.btnTextUploadCol, mainStyles.btnTextUploadCol2]}>{`FOTO DO SEU\nDOCUMENTO\n`}<Text style={mainStyles.bold}>FRENTE</Text></Text>
                                                    </TouchableOpacity>
                                                }
                                                {documentoFrentePath !== '' &&
                                                    <TouchableOpacity style={mainStyles.btnDocCol2} >
                                                        {mimeTypeFrente !== "application/pdf" &&
                                                            <View style={mainStyles.boxPreviewImageContentOcrRed}>

                                                                <Image source={{
                                                                    uri: documentoFrentePath, headers: {
                                                                        Authorization: `Bearer ${token}`
                                                                    }
                                                                }} style={mainStyles.previewCol} />
                                                            </View>
                                                        }
                                                        {mimeTypeFrente == "application/pdf" &&
                                                            <PDF />
                                                        }
                                                        <Text style={mainStyles.deletePreview} onPress={deleteDocumentoFrente} >EXCLUIR</Text>
                                                    </TouchableOpacity>
                                                }

                                                {
                                                    documentoVersoPath !== '' &&
                                                    <TouchableOpacity disabled={image === null} style={[mainStyles.btnDocCol2]} onPress={() => chooseImage('verso')}>
                                                        {mimeTypeVerso !== "application/pdf" &&
                                                            <View style={mainStyles.boxPreviewImageContentOcrRed}>
                                                                <Image source={{
                                                                    uri: documentoVersoPath, headers: {
                                                                        Authorization: `Bearer ${token}`
                                                                    }
                                                                }} style={mainStyles.previewCol} />
                                                            </View>
                                                        }
                                                        {mimeTypeVerso == "application/pdf" &&
                                                            <PDF />
                                                        }
                                                        <Text style={mainStyles.deletePreview} onPress={deleteDocumentoVerso} >EXCLUIR</Text>
                                                    </TouchableOpacity>
                                                }

                                                {
                                                    documentoVersoPath === '' &&
                                                    <TouchableOpacity disabled={image === null} style={[mainStyles.btnUploadCol2, { opacity: image === null ? 0.3 : 1 }]} onPress={() => chooseImage('verso')}>
                                                        <Camera style={[mainStyles.btnIconUpload, mainStyles.btnIconUploadM]} />
                                                        <Text style={[mainStyles.btnTextUpload2, mainStyles.btnTextUploadCol, mainStyles.btnTextUploadCol2]}>{`FOTO DO SEU\nDOCUMENTO\n`}<Text style={mainStyles.bold}>VERSO</Text></Text>
                                                    </TouchableOpacity>
                                                }

                                            </View>
                                            <View>
                                                <Text style={mainStyles.label}>TIPO DE DOCUMENTO:</Text>
                                                <Select
                                                    disabled={!editable}
                                                    removeBorder={true}
                                                    placeholder="SELECIONE"
                                                    options={tipoOptions}
                                                    value={inTipo}
                                                    onChange={setInTipo}
                                                />
                                            </View>
                                            <View>
                                                <Text style={mainStyles.label}>NÚMERO:</Text>
                                                <TextInput
                                                    editable={editable}
                                                    underlineColorAndroid="transparent"
                                                    style={[mainStyles.inputText, editable ? null : mainStyles.inputTextDisabled]}
                                                    keyboardType="phone-pad"
                                                    value={inNumero}
                                                    onChangeText={setInNumero}
                                                />
                                            </View>
                                            <View>
                                                <Text style={mainStyles.label}>ÓRGÃO EMISSOR:</Text>
                                                <Select
                                                    disabled={!editable}
                                                    placeholder="SELECIONE"
                                                    options={orgaoEmissorOptions}
                                                    value={inOrgaoEmissor}
                                                    onChange={value => setInOrgaoEmissor(value)}
                                                />
                                            </View>
                                            <View>
                                                <Text style={mainStyles.label}>UF EMISSOR:</Text>
                                                <Select
                                                    disabled={!editable}
                                                    placeholder="UF"
                                                    options={estadoEmissorOptions}
                                                    value={inEstadoEmissor}
                                                    onChange={value => setInEstadoEmissor(value)}
                                                />
                                            </View>
                                            <View>
                                                <Text style={mainStyles.label}>DATA DE EMISSÃO:</Text>
                                                <TextInput
                                                    editable={editable}
                                                    underlineColorAndroid="transparent"
                                                    style={[mainStyles.inputText, editable ? null : mainStyles.inputTextDisabled]}
                                                    value={inDataEmissao}
                                                    onChangeText={value => setInDataEmissao(dateMask(value))}
                                                    placeholder="dd/mm/aaaa"
                                                    keyboardType="phone-pad"
                                                />
                                            </View>
                                            <View>
                                                <Text style={mainStyles.label}>NACIONALIDADE:</Text>
                                                <TextInput
                                                    editable={editable}
                                                    underlineColorAndroid="transparent"
                                                    style={[mainStyles.inputText, editable ? null : mainStyles.inputTextDisabled]}
                                                    value={inNacionalidade}
                                                    onChangeText={text => setInNacionalidade(text)}
                                                    autoCapitalize="words"
                                                />
                                            </View>
                                            {!editable &&
                                                <View style={{ marginBottom: 40 }}>
                                                    <View style={styles.divider}></View>
                                                    <ContasCompartilharBox conta={conta} />
                                                </View>
                                            }
                                        </View>
                                    </View>
                                </View>
                                <View style={[mainStyles.container, styles.contentButtons]}>
                                    <TouchableOpacity
                                        style={[mainStyles.btnBlueNew, styles.buttonLarge]}
                                        onPress={editable ? update : () => navigation.goBack()}>
                                        <Text style={mainStyles.btnTextBlueNew}>{loading ? 'CARREGANDO' : (editable ? 'SALVAR' : 'VOLTAR')}</Text>
                                    </TouchableOpacity>
                                </View>
                            </>
                        }
                    </ScrollView>
                </KeyboardAvoidingView>

                <CustomAlert
                    visible={alertVisible}
                    onClose={closeCustomAlert}
                    title="Enviar documento"
                    message="Como deseja enviar o documento?"
                    options={alertOptions}
                />
            </View>
        </>
    );
}

export default ContasDocumento;