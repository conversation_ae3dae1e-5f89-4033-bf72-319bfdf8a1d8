import React, { useState, useRef, useEffect } from 'react';
import { Platform, Text, TouchableOpacity, View, Image, Alert, ActivityIndicator, TextInput, PermissionsAndroid, KeyboardAvoidingView, ScrollView, Keyboard } from 'react-native';

import PrivateHeader from '../../../../components/headers/PrivateHeader';
import Select from '../../../../components/Select';

import { cpfMask, phoneMask, dateMask } from '../../../../useful/masks';

import mainStyles from '../../../../mainStyles';
import styles from './styles';

import ArrowLeft from '../../../../assets/svgs/ArrowLeftSmallWhite';
import User from '../../../../assets/svgs/Users3';
import LightboxSearch from '../../../../components/LightboxSearch';
import { useIsFocused } from "@react-navigation/native";
import { ContasClienteBox } from "../components/ContasClienteBox";
import api from "../../../../services/api";

import Clipboard from "@react-native-clipboard/clipboard";
import Share from 'react-native-share';
import ContasCompartilharBox from "../components/ContasCompartilharBox";

const ContasAutorizacoes = ({route, navigation}) => {
    const { id, conta } = route.params;

    const isFocused = useIsFocused();

    const [loading, setLoading] = useState(false);

    const [cliente, setCliente] = useState(null);

    const [autorizacaoLgpd, setAutorizacaoLgpd] = useState(false);
    const [autorizacaoSerasa, setAutorizacaoSerasa] = useState(false);
    const [autorizacaoSelfie, setAutorizacaoSelfie] = useState(false);

    useEffect(() => {
        if(isFocused){
            getAutorizacoes();
        }
    }, [isFocused]);

    const getAutorizacoes = () => {
        setLoading(true);

        api.get(`/crm/contas/${id}/autorizacoes`).then(res => {
            const { itens } = res.data;

            console.log(res.data);
            
            setCliente(res.data.cliente);

            setAutorizacaoLgpd(itens.autorizacao_lgpd);
            setAutorizacaoSerasa(itens.autorizacao_serasa);
            setAutorizacaoSelfie(itens.autorizacao_selfie);
        }).catch(error => {
            Alert.alert('Erro ao obter autorizações');
            console.log(error);
        }).then(() => setLoading(false));
    }

    return (
        <>
        <KeyboardAvoidingView style={{ flex: 1 }} behavior="padding" >
            <View style={mainStyles.wrapper}>
            <PrivateHeader title={'Contas'} />
                <TouchableOpacity
                    style={styles.buttonTitle}
                    onPress={() => navigation.goBack()}>
                    <View style={styles.icArrow}>
                        <ArrowLeft />
                    </View>
                    <Text style={styles.titlePag}>Autorizações</Text>
                </TouchableOpacity>
                <ScrollView>
                        {loading && 
                            <View style={{flex: 1, marginTop: 50, justifyContent: "center", alignItems: "center"}}>
                                <ActivityIndicator size="large" color="#00467F" />
                            </View>
                        }
                        {!loading && 
                            <>
                            <View style={mainStyles.wrapperRegister}>
                                <View>
                                    <View style={[mainStyles.container, styles.container]}>
                                        <ContasClienteBox cliente={cliente} />
                                        <View style={styles.divider}></View>
                                        <View>
                                            <Text style={mainStyles.label}>AUTORIZAÇÃO LGPD:</Text>                                
                                            <TextInput
                                                editable={false}
                                                underlineColorAndroid="transparent"  
                                                style={[
                                                    mainStyles.inputText, 
                                                    mainStyles.inputTextDisabled, 
                                                    autorizacaoLgpd ? null : styles.pendingInput
                                                ]}
                                                value={autorizacaoLgpd ? "Não há pendências" : "Pendente"}
                                            />
                                        </View>
                                        <View>
                                            <Text style={mainStyles.label}>AUTORIZAÇÃO SERASA:</Text>                                
                                            <TextInput
                                                editable={false}
                                                underlineColorAndroid="transparent"  
                                                style={[
                                                    mainStyles.inputText, 
                                                    mainStyles.inputTextDisabled, 
                                                    autorizacaoSerasa ? null : styles.pendingInput
                                                ]}
                                                value={autorizacaoSerasa ? "Não há pendências" : "Pendente"}
                                            />
                                        </View>
                                        <View>
                                            <Text style={mainStyles.label}>SELFIE:</Text>                                
                                            <TextInput
                                                underlineColorAndroid="transparent"  
                                                style={[
                                                    mainStyles.inputText, 
                                                    mainStyles.inputTextDisabled, 
                                                    autorizacaoSelfie ? null : styles.pendingInput
                                                ]}
                                                value={autorizacaoSelfie ? "Não há pendências" : "Pendente"}
                                            />
                                        </View>
                                        {!autorizacaoLgpd && !autorizacaoSelfie && !autorizacaoSerasa &&
                                            <>
                                                <View style={styles.divider}></View>
                                                <ContasCompartilharBox conta={conta} />
                                                <View style={[styles.divider, {marginBottom: 50}]}></View>
                                            </>
                                        }
                                    </View>
                                </View>
                            </View>
                            </>
                        }
                </ScrollView>
                <View style={styles.contentBottom}>
                    <View style={[mainStyles.container, styles.contentButtons]}>
                    <TouchableOpacity
                        style={[mainStyles.btnBlueNew, styles.buttonLarge]}
                        onPress={() => navigation.goBack()}>
                        <Text style={mainStyles.btnTextBlueNew}>VOLTAR</Text>
                    </TouchableOpacity>
                    </View>
                </View>
            </View>
        </KeyboardAvoidingView>
        </>
    );
}

export default ContasAutorizacoes;