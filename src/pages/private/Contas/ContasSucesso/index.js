import React, { useState, useRef, useEffect } from 'react';
import { Platform, Text, TouchableOpacity, View, Image, Alert, ActivityIndicator, TextInput, PermissionsAndroid, KeyboardAvoidingView, ScrollView, Keyboard } from 'react-native';

import PrivateHeader from '../../../../components/headers/PrivateHeader';

import mainStyles from '../../../../mainStyles';
import styles from './styles';

import Bad from '../../../../assets/svgs/Alert2';
import ArrowLeft from '../../../../assets/svgs/ArrowLeftSmallWhite';
import CheckBlue from '../../../../assets/svgs/CheckBlue';
import User from '../../../../assets/svgs/Users3';
import { ContasClienteBox } from "../components/ContasClienteBox";

const ContasSucesso = ({route, navigation}) => {
    const { cliente, status } = route.params;

    return (
        <>
        <KeyboardAvoidingView style={{ flex: 1 }} behavior="padding" >
            <View style={mainStyles.wrapper}>
            <PrivateHeader title={'Contas'} />
                <TouchableOpacity
                    style={styles.buttonTitle}
                    onPress={() => navigation.goBack()}>
                    <View style={styles.icArrow}>
                        <ArrowLeft />
                    </View>
                    <Text style={styles.titlePag}>ENVIO SALESFORCE</Text>
                </TouchableOpacity>
                <ScrollView>
                    <View style={mainStyles.wrapperRegister}>
                        <View>
                            <View style={[mainStyles.container, styles.container]}>
                                <View style={styles.divider}></View>

                                {status === 'error' &&
                                    <>
                                        <View style={{flexDirection: "row", justifyContent: "center", marginBottom: 15}}>
                                            <Bad />                                       
                                        </View> 
                                        <Text style={[mainStyles.label, {textAlign: "center"}]}>{`Ops, não conseguimos criar a conta no\nSalesforce. Fale com seu gestor antes de\ntentar novamente.`}</Text>
                                    </>
                                }
                                
                                {status === 'success' &&
                                    <>
                                        <View style={{flexDirection: "row", justifyContent: "center", marginBottom: 15, marginTop: 10}}>
                                            <CheckBlue />                                       
                                        </View> 
                                        <Text style={[mainStyles.label, {textAlign: "center"}]}>{`Conta criada com sucesso\nno Salesforce`}</Text>
                                    </>
                                }
                                <View style={styles.divider}></View>
                                <ContasClienteBox cliente={cliente} />
                            </View>
                        </View>
                    </View>
                </ScrollView>
                <View style={styles.contentBottom}>
                    <View style={[mainStyles.container, styles.contentButtons]}>
                    <TouchableOpacity
                        style={[mainStyles.btnBlueNew, styles.buttonLarge]}
                        onPress={() => navigation.navigate('ContasMain')}>
                        <Text style={mainStyles.btnTextBlueNew}>VOLTAR</Text>
                    </TouchableOpacity>
                    </View>
                </View>
            </View>
        </KeyboardAvoidingView>
        </>
    );
}

export default ContasSucesso;