import { GOOGLE_MAPS_API_KEY } from '@env';
import React, { useState, useRef, useEffect, useCallback } from 'react';
import debounce from "lodash.debounce";
import { Text, TouchableOpacity, View, Alert, ActivityIndicator, TextInput, KeyboardAvoidingView, ScrollView } from 'react-native';
import { GooglePlacesAutocomplete } from 'react-native-google-places-autocomplete';

import PrivateHeader from '../../../../components/headers/PrivateHeader';

import api from '../../../../services/api';

import { cepMask } from '../../../../useful/masks';

import mainStyles from '../../../../mainStyles';
import styles from './styles';

import ArrowLeft from '../../../../assets/svgs/ArrowLeftSmallWhite';
import { useIsFocused } from "@react-navigation/native";
import { ContasClienteBox } from "../components/ContasClienteBox";
import ContasCompartilharBox from "../components/ContasCompartilharBox";

import Check2 from '../../../../assets/svgs/CheckBox';


const ContasEnderecoComercial = ({ route, navigation }) => {
    const inputRefs = useRef([]);
    const inputRefsGoogle = useRef([]);
    const { id, conta } = route.params;

    const editable = conta.itens.autorizacoes.pendencias > 0;

    const isFocused = useIsFocused();

    const [cliente, setCliente] = useState(null);

    const [loading, setLoading] = useState(false);
    const [loadingBusca, setLoadingBusca] = useState(false);
    const [gettingEndereco, setGettingEndereco] = useState(false);

    const [inCep, setInCep] = useState('');
    const [inEndereco, setInEndereco] = useState('');
    const [inBairro, setInBairro] = useState('');
    const [inCidade, setInCidade] = useState('');
    const [inEstado, setInEstado] = useState('');
    const [inEnderecoNumero, setInEnderecoNumero] = useState('');
    const [inEnderecoComplemento, setInEnderecoComplemento] = useState('');
    const [inPais, setInPais] = useState('');

    const [isChecked, setIsChecked] = useState(conta.cliente.EnderecoResidencialIgualComercial ?? false);
    const [hideAll, setHideAll] = useState(conta.cliente.possuiEnderecoComercial ? false : true);
    const [isFieldFocused, setIsFieldFocused] = useState(false);

    useEffect(() => {
        if (isFocused) {
            getEnderecoComercial();
        }
    }, [isFocused]);

    useEffect(() => {
        console.log('campo google focado')
    }, [isFieldFocused]);

    const handleIsChecked = () => {
        if (isChecked) {
            setIsChecked(false);
            getEnderecoComercial();
        } else {
            setIsChecked(true);
            getEndereco();
        }
    }

    const handlePlaceSelected = (data, details) => {
        const addressComponents = details.address_components;
        const getComponent = (type) => {
            const component = addressComponents.find((comp) =>
                comp.types.includes(type)
            );

            if (type == 'administrative_area_level_1') {
                return component ? component.short_name : '';
            }

            return component ? component.long_name : '';
        };

        const endereco = getComponent('route');
        const cepApi = getComponent('postal_code');
        const cidade = getComponent('administrative_area_level_2');
        const estado = getComponent('administrative_area_level_1');
        const bairro = getComponent('sublocality');
        const numero = getComponent('street_number');

        setInCep(cepApi);
        setInBairro(bairro);
        setInCidade(cidade);
        setInEstado(estado);
        setInEndereco(endereco);
        setInEnderecoNumero(numero);

        if (inputRefsGoogle.current) {
            inputRefsGoogle.current.setAddressText(endereco);
        }
    };

    const handleNumeroChange = (text) => {
        setLoadingBusca(true);
        setInEnderecoNumero(text); 
        debouncedBuscarCep(text); 
    };

    const buscarCep = (text) => {
        setInEnderecoNumero(text);

        fetch(`https://maps.googleapis.com/maps/api/geocode/json?address=${inEndereco},${text},${inCidade},${inEstado}&key=${GOOGLE_MAPS_API_KEY}&language=pt`)
            .then(response => response.json())
            .then(geoData => {
                if (geoData.status === "OK") {
                    const address = geoData.results[0].address_components;
                    const novoCep = address.find(comp => comp.types.includes("postal_code"))?.long_name;

                    if (novoCep) {
                        setInCep(novoCep);
                    }
                }
            })
            .catch(error => console.error("Erro ao buscar CEP:", error))
            .finally(() => {
                setLoadingBusca(false);
            });
    };

    const debouncedBuscarCep = useCallback(debounce(buscarCep, 800), [inEndereco]);

    const getEnderecoComercial = () => {
        setLoading(true);

        api.get(`/crm/contas/${id}/endereco-comercial`).then(res => {
            const { itens, selects } = res.data;
            setCliente(res.data.cliente);

            setInCep(itens.EnderecoComercial.CEP ?? '');
            setInEndereco(itens.EnderecoComercial.Rua ?? '');
            setInEnderecoNumero(itens.EnderecoComercial.Numero ?? '');
            setInEnderecoComplemento(itens.EnderecoComercial.Complemento ?? '');
            setInBairro(itens.EnderecoComercial.Bairro ?? '');
            setInCidade(itens.EnderecoComercial.Cidade ?? '');
            setInEstado(itens.EnderecoComercial.Estado ?? '');
            setInPais(itens.EnderecoComercial.Pais ?? '');

        }).catch(error => {
            Alert.alert('Erro ao obter o endereço');
            console.log(error);
        }).then(() => setLoading(false));
    }

    const getEndereco = () => {

        setLoading(true);

        api.get(`/crm/contas/${id}/endereco`).then(res => {
            const { itens, selects } = res.data;
            console.log(res.data);
            setCliente(res.data.cliente);

            setInCep(itens.Endereco.CEP ?? '');
            setInEndereco(itens.Endereco.Rua ?? '');
            setInEnderecoNumero(itens.Endereco.Numero ?? '');
            setInEnderecoComplemento(itens.Endereco.Complemento ?? '');
            setInBairro(itens.Endereco.Bairro ?? '');
            setInCidade(itens.Endereco.Cidade ?? '');
            setInEstado(itens.Endereco.Estado ?? '');
            setInPais(itens.Endereco.Pais ?? '');

        }).catch(error => {
            Alert.alert('Erro ao obter o endereço');
            console.log(error);
        }).then(() => setLoading(false));
    }

    const update = () => {
        setLoading(true);

        const payload = hideAll
            ? {
                possuiEnderecoComercial: false,
                EnderecoComercial: null,
                EnderecoResidencialIgualComercial: false
            }
            : {
                EnderecoComercial: {
                    CEP: inCep,
                    Rua: inEndereco,
                    Numero: inEnderecoNumero,
                    Complemento: inEnderecoComplemento,
                    Bairro: inBairro,
                    Cidade: inCidade,
                    Estado: inEstado,
                    Pais: inPais
                },
                EnderecoResidencialIgualComercial: isChecked,
                possuiEnderecoComercial: true
            };

        api.put(`/crm/contas/${id}/endereco-comercial`, payload).then(res => {
            navigation.goBack();
        }).catch(error => {
            let errors = error?.response?.data?.errors ?? null;
            let errorText = '';
            if (errors) {
                Object.keys(errors).map(key => {
                    errors[key].map(error => {
                        errorText += `- ${error}\n`;
                    });
                });
            }
            Alert.alert('Não foi possível salvar', errorText,);
        }).then(() => setLoading(false));
    }

    const focusNextField = (index) => {
        if (inputRefs.current[index + 1]) {
            inputRefs.current[index + 1].focus();
        }
    };

    const handleFocus = () => {
        setIsFieldFocused(true);
    };

    const handleBlur = () => {
        setIsFieldFocused(false);
    };

    return (
        <>
            <View style={mainStyles.wrapper}>
                <PrivateHeader title={'Contas'} />
                <TouchableOpacity
                    style={styles.buttonTitle}
                    onPress={() => navigation.goBack()}>
                    <View style={styles.icArrow}>
                        <ArrowLeft />
                    </View>
                    <Text style={styles.titlePag}>Endereço comercial</Text>
                </TouchableOpacity>
                <KeyboardAvoidingView style={{ flex: 1 }} behavior="padding" >
                    <ScrollView keyboardShouldPersistTaps="handled">
                        {loading &&
                            <View style={{ flex: 1, marginTop: 50, justifyContent: "center", alignItems: "center" }}>
                                <ActivityIndicator size="large" color="#00467F" />
                            </View>
                        }
                        {!loading &&
                            <>
                                <View style={mainStyles.wrapperRegister}>
                                    <View>
                                        <View style={[mainStyles.container, styles.container]}>
                                            {!isFieldFocused &&
                                                <>
                                                    <ContasClienteBox cliente={cliente} />
                                                    <View style={styles.divider}></View>
                                                </>
                                            }
                                            {!hideAll && (
                                                <>
                                                    {!isFieldFocused &&
                                                        <View style={styles.checkboxContainer}>
                                                            <TouchableOpacity
                                                                disabled={!editable}
                                                                style={[
                                                                    styles.checkbox,
                                                                    isChecked && styles.checkedCheckbox,
                                                                    !editable && { backgroundColor: '#ccc', borderColor: '#ccc'}
                                                                ]}
                                                                onPress={handleIsChecked}
                                                            >
                                                                {isChecked &&
                                                                    <Check2 style={styles.icCheck} />
                                                                }
                                                            </TouchableOpacity>
                                                            <TouchableOpacity disabled={!editable} onPress={handleIsChecked}>
                                                                <Text style={styles.label}>{`Endereço comercial é o mesmo que o \nresidencial`}</Text>
                                                            </TouchableOpacity>
                                                        </View>
                                                    }

                                                    <View>
                                                        <Text style={[mainStyles.label, isFieldFocused ? { marginTop: 19 } : null]}>ENDEREÇO:</Text>
                                                        <GooglePlacesAutocomplete
                                                            ref={inputRefsGoogle}
                                                            placeholder={isFieldFocused ? '' : inEndereco}
                                                            onPress={(data, details) => handlePlaceSelected(data, details)}
                                                            fetchDetails={true}
                                                            query={{
                                                                key: GOOGLE_MAPS_API_KEY,
                                                                language: 'pt',
                                                            }}
                                                            onFocus={() => setInputFocused(false)}
                                                            onBlur={() => setInputFocused(true)}
                                                            textInputProps={{
                                                                onFocus: handleFocus,
                                                                onBlur: handleBlur,
                                                                fontFamily: 'Ubuntu-Regular',
                                                                placeholderTextColor: '#828282',
                                                                editable: !isChecked,
                                                            }}
                                                            styles={{
                                                                textInput: [mainStyles.inputText, (isChecked || !editable) && mainStyles.inputTextDisabled],
                                                                listView: { zIndex: 10 },
                                                            }}
                                                            listViewDisplayed="auto"
                                                            disableScroll={true}
                                                        />
                                                    </View>
                                                    {!isFieldFocused &&
                                                        <>
                                                            <View>
                                                                <Text style={[mainStyles.label]}>NÚMERO:</Text>
                                                                <TextInput
                                                                    returnKeyType='next'
                                                                    ref={el => inputRefs.current[1] = el}
                                                                    onSubmitEditing={() => focusNextField(1)}
                                                                    blurOnSubmit={false}
                                                                    underlineColorAndroid="transparent"
                                                                    style={[mainStyles.inputText, (isChecked || !editable) && mainStyles.inputTextDisabled]}
                                                                    value={inEnderecoNumero}
                                                                    onChangeText={handleNumeroChange}
                                                                    editable={!isChecked && editable}
                                                                />
                                                            </View>
                                                            <View>
                                                                <Text style={[mainStyles.label]}>COMPLEMENTO:</Text>
                                                                <TextInput
                                                                    ref={el => inputRefs.current[2] = el}
                                                                    returnKeyType='done'
                                                                    underlineColorAndroid="transparent"
                                                                    editable={!isChecked && editable}
                                                                    style={[mainStyles.inputText, (isChecked || !editable) && mainStyles.inputTextDisabled]}
                                                                    value={inEnderecoComplemento}
                                                                    onChangeText={text => setInEnderecoComplemento(text)}
                                                                />
                                                            </View>
                                                            <View>
                                                                <Text style={[mainStyles.label]}>CEP:</Text>
                                                                <TextInput
                                                                    returnKeyType='next'
                                                                    ref={el => inputRefs.current[0] = el}
                                                                    onSubmitEditing={() => focusNextField(0)}
                                                                    blurOnSubmit={false}
                                                                    editable={!isChecked && editable}
                                                                    underlineColorAndroid="transparent"
                                                                    // style={[mainStyles.inputText, editable ? null : mainStyles.inputTextDisabled]}
                                                                    style={[mainStyles.inputText, (isChecked || !editable) && mainStyles.inputTextDisabled]}
                                                                    keyboardType="phone-pad"
                                                                    value={loadingBusca ? 'Buscando...' : inCep}
                                                                    onChangeText={text => setInCep(cepMask(text))}

                                                                />
                                                            </View>
                                                            <View>
                                                                <Text style={mainStyles.label}>BAIRRO:</Text>
                                                                <TextInput
                                                                    editable={false}
                                                                    underlineColorAndroid="transparent"
                                                                    style={[mainStyles.inputText, mainStyles.inputTextDisabled]}
                                                                    value={gettingEndereco ? 'Buscando...' : inBairro}
                                                                    onChangeText={text => setInBairro(text)}
                                                                />
                                                            </View>
                                                            <View>
                                                                <Text style={mainStyles.label}>CIDADE:</Text>
                                                                <TextInput
                                                                    editable={false}
                                                                    underlineColorAndroid="transparent"
                                                                    style={[mainStyles.inputText, mainStyles.inputTextDisabled]}
                                                                    value={gettingEndereco ? 'Buscando...' : inCidade}
                                                                    onChangeText={text => setInCidade(text)}
                                                                />
                                                            </View>
                                                            <View>
                                                                <Text style={mainStyles.label}>ESTADO:</Text>
                                                                <TextInput
                                                                    editable={false}
                                                                    underlineColorAndroid="transparent"
                                                                    style={[mainStyles.inputText, mainStyles.inputTextDisabled]}
                                                                    value={gettingEndereco ? 'Buscando...' : inEstado}
                                                                    onChangeText={text => setInEstado(text)}
                                                                />
                                                            </View>
                                                        </>
                                                    }

                                                    {!editable &&
                                                        <View style={{ marginBottom: 40 }}>
                                                            <View style={styles.divider}></View>
                                                            <ContasCompartilharBox conta={conta} />
                                                        </View>
                                                    }
                                                </>
                                            )}
                                            {!isFieldFocused &&
                                                <View style={styles.checkboxContainer}>
                                                    <TouchableOpacity
                                                        disabled={!editable}
                                                        style={[
                                                            styles.checkbox,
                                                            hideAll && styles.checkedCheckbox,
                                                            !editable && { backgroundColor: '#ccc', borderColor: '#ccc'}
                                                        ]}
                                                        onPress={() => setHideAll(!hideAll)}
                                                    >
                                                        {hideAll &&
                                                            <Check2 style={styles.icCheck} />
                                                        }
                                                    </TouchableOpacity>
                                                    <TouchableOpacity disabled={!editable} onPress={() => setHideAll(!hideAll)}>
                                                        <Text style={styles.label}>Não possuo endereço comercial</Text>
                                                    </TouchableOpacity>
                                                </View>
                                            }
                                        </View>
                                    </View>
                                </View>
                                {!isFieldFocused &&
                                    <View style={[mainStyles.container, styles.contentButtons]}>
                                        <TouchableOpacity
                                            disabled={loading}
                                            style={[mainStyles.btnBlueNew, styles.buttonLarge]}
                                            onPress={editable ? update : () => navigation.goBack()}>
                                            <Text style={mainStyles.btnTextBlueNew}>{loading ? 'CARREGANDO' : (editable ? 'SALVAR' : 'VOLTAR')}</Text>
                                        </TouchableOpacity>
                                    </View>
                                }
                            </>
                        }
                    </ScrollView>
                </KeyboardAvoidingView>
            </View>
        </>
    );
}

export default ContasEnderecoComercial;