import {Dimensions, Platform, StyleSheet} from 'react-native';

const windowWidth = Dimensions.get('window').width;

const styles = StyleSheet.create({
  wrapper: {
    justifyContent: 'space-between',
  },
  contentBottom: {
    paddingTop: 20,
    paddingBottom: 30,
    shadowRadius: Platform.OS === 'ios' ? 2 : 16,
    shadowOffset: {
        width: 0,
        height: Platform.OS === 'ios' ? -2 : 12,
    },
    shadowColor: Platform.OS === 'ios' ? '#CCC' : "#000",
    elevation: Platform.OS === 'ios' ? 2 : 24,
    shadowOpacity: 1,
    backgroundColor: "#FFF"
  },
  contentButtons: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginTop: 15,
    marginBottom: 50
  },
  button: {
    width: '100%',
  },
  buttonLarge: {
    width: '100%',
  },
  container: {
    paddingTop: 0
  },
  inputText: {
    backgroundColor: '#FFF',
    width: 125,
    paddingLeft: 13,
  },
  input: {
    borderWidth: 2,
    borderColor: '#4EA1CC',
    borderRadius: 5,
    width: 150,
    height: 55,
    paddingLeft: 15,
    paddingRight: 15,
    fontSize: 25,
    textAlign: 'center',
  },
  buttonTitle: {
    backgroundColor: '#90B0C0',
    height: 70,
    width: '100%',
    alignItems: 'center',
    justifyContent: 'center',
    flexDirection: 'row'
  },
  icArrow: {
    position: 'absolute',
    left: 20,
  },
  divider: {
    borderBottomWidth: 1,
    borderBottomColor: '#DADADA',
    marginTop: 15,
    marginBottom: 15,
  },
  totalIcon: {
    backgroundColor: '#90B0C0',
    width: 60,
    alignItems: 'center',
    justifyContent: 'center',
  },
  totalInfos: {
    backgroundColor: '#F2F2F2',
    flex: 1,
    paddingLeft: 10,
    paddingRight: 20,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  titleBox: {
    fontFamily: 'Ubuntu-Regular',
    fontSize: 16,
    color: '#00467F',
    letterSpacing: 0.46,
    marginTop: 2,
    marginBottom: 4,
    lineHeight: 17.32,
  },
  subtitleBox: {
    fontFamily: 'Ubuntu-Light',
    fontSize: 14,
    color: '#000',
    letterSpacing: 0.46,
    lineHeight: 17.32,
    width: 230,
  },
  status: {
    fontFamily: 'Ubuntu-Light',
    fontSize: 14,
    letterSpacing: 0.46,
    lineHeight: 17.32,
    width: 230,
    marginTop: 4
  },
  boxOption: {
    flexDirection: 'row',
    minHeight: 70,
    marginTop: 15
  },
  titlePag: {
    fontFamily: 'Ubuntu-Regular',
    color: '#FFF',
    fontSize: 15,
    letterSpacing: 0.46,
    textTransform: 'uppercase'
  },
  // checkbox: {
  //   borderColor: "#CCC",
  //   borderWidth: 1,
  //   transform: [{ scaleX: 2 }, { scaleY: 2 }]
  // }
  checkboxContainer: {
    flexDirection: "row",
    alignItems: "center",
    marginBottom: 20
  },
  checkbox: {
    width: 30, 
    height: 30,
    borderWidth: 1,
    borderColor: '#DADADA',
    justifyContent: 'center',
    alignItems: 'center',
  },
  checkedCheckbox: {
    borderColor: '#00467F',
    backgroundColor: '#00467F',
  },
  icCheck: {
    width: 20,
    color: "#FFF"
  },
  label: {
    fontFamily: "Ubuntu-Regular",
    fontSize: 13,
    letterSpacing: 0.46,
    lineHeight: 17.3,
    color: "#00467F",
    marginLeft: 10
  }
});

export default styles;
