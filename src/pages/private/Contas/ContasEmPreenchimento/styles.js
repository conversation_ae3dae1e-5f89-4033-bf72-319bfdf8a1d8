import { Dimensions, Platform, StyleSheet } from 'react-native';

const windowWidth = Dimensions.get('window').width;

const styles = StyleSheet.create({
  wrapper: {
    justifyContent: 'space-between',
  },
  contentBottom: {
    paddingTop: 15,
    paddingBottom: 15,
    shadowRadius: Platform.OS === 'ios' ? 2 : 16,
    shadowOffset: {
      width: 0,
      height: Platform.OS === 'ios' ? -2 : 12,
    },
    shadowColor: Platform.OS === 'ios' ? '#CCC' : '#000',
    elevation: Platform.OS === 'ios' ? 2 : 24,
    shadowOpacity: 1,
    backgroundColor: '#FFF',
  },
  contentButtons: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  button: {
    width: '100%',
  },
  buttonLarge: {
    width: '100%',
  },
  container: {
    paddingTop: 0
  },
  buttonTitle: {
    backgroundColor: '#90B0C0',
    height: 70,
    width: '100%',
    alignItems: 'center',
    justifyContent: 'center',
    flexDirection: 'row',
  },
  icArrow: {
    position: 'absolute',
    left: 20,
  },
  divider: {
    borderBottomWidth: 1,
    borderBottomColor: '#DADADA',
    marginTop: 15,
    marginBottom: 15,
  },
  boxTotal: {
    flexDirection: 'row',
    minHeight: 70,
    marginTop: 5,
    marginBottom: 5,
  },
  totalIcon: {
    backgroundColor: '#90B0C0',
    width: 60,
    alignItems: 'center',
    justifyContent: 'center',
  },
  totalInfos: {
    backgroundColor: '#F2F2F2',
    flex: 1,
    paddingLeft: 10,
    paddingRight: 20,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  titleBox: {
    fontFamily: 'Ubuntu-Regular',
    fontSize: 16,
    color: '#00467F',
    letterSpacing: 0.46,
    marginTop: 2,
    marginBottom: 4,
    lineHeight: 17.32,
  },
  subtitleBox: {
    fontFamily: 'Ubuntu-Light',
    fontSize: 14,
    color: '#000',
    letterSpacing: 0.46,
    lineHeight: 17.32,
    width: 230,
  },
  status: {
    fontFamily: 'Ubuntu-Light',
    fontSize: 14,
    letterSpacing: 0.46,
    lineHeight: 17.32,
    width: 230,
    marginTop: 4
  },
  boxOption: {
    flexDirection: 'row',
    minHeight: 70,
    marginTop: 15
  },
  icArrowBottom: {
    color: '#90B0C0',
    width: 10,
  },
  titlePag: {
    fontFamily: 'Ubuntu-Regular',
    color: '#FFF',
    fontSize: 15,
    letterSpacing: 0.46,
    textTransform: 'uppercase'
  },
  textAlert: {
    fontFamily: 'Ubuntu-Regular',
    color: '#828282',
    fontSize: 13,
    letterSpacing: 1,
    lineHeight: 17.32,
    marginBottom: 10
  },
  btnDelete: {
    alignItems: 'center',
    justifyContent: 'center',
    height: 40,
    width: '100%',
    marginBottom: 30,
    marginTop: 10
  },
  btnDeleteText: {
    fontFamily: 'Ubuntu-Regular',
    color: '#FF6542',
    textDecorationLine: 'underline',
    letterSpacing: 1
  },
  containerFicha: {
    backgroundColor: '#F2F2F2',
    padding: 20,
    alignItems: 'flex-start',
    margin: 2,
    marginBottom: 15
  },
  containerFichaTitulo: {
    color: "#00467F",
    fontSize: 15,
  },
  btnGerarFicha: {
    backgroundColor: '#2D719F',
    borderWidth: 2,
    borderColor: '#2D719F',
    height: 50,
    width: 253,
    textAlign: 'center',
    lineHeight: 50,
    color: 'white',
  },
  btnVerFicha: {
    backgroundColor: '#FFF',
    borderWidth: 2,
    borderColor: '#2D719F',
    height: 50,
    width: '100%',
    textAlign: 'center',
    lineHeight: 50,
    color: '#2D719F',
    alignItems: 'center',
  },
  textStatus: {
    fontFamily: 'Ubuntu-Regular',
    color: '#00467F',
    fontSize: Platform.OS === 'ios' ? 18 : 16,
    height: 30,
    letterSpacing: 1,
  },
  textStatusGreen: {
    fontFamily: 'Ubuntu-Regular',
    color: '#1B9C20',
    fontSize: Platform.OS === 'ios' ? 18 : 16,
    height: 30,
    letterSpacing: 1,
  },
  textItem: {
    marginLeft: -10,
    flex: 1,
  },
  textInfo: {
    fontSize: Platform.OS === 'ios' ? 14 : 12,
    paddingLeft: 15,
    letterSpacing: 1,
    marginTop: 4,
    width: 270
  },
  
  textView: {
    marginTop: 10,
  },
  textInfoFicha: {
    fontFamily: 'Ubuntu-Light',
    fontSize: Platform.OS === 'ios' ? 14 : 12,
    marginTop: 10,
  },
  icon: {
    width: 11,
    height: 11,
  },
  greenIcon: {
    width: 11,
    height: 11,
  },
});

export default styles;
