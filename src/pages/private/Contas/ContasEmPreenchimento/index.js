import React, { useState, useEffect } from 'react';
import { Text, TouchableOpacity, View, ActivityIndicator, ScrollView, Alert, BackHandler, PermissionsAndroid } from 'react-native';

import PrivateHeader from '../../../../components/headers/PrivateHeader';

import mainStyles from '../../../../mainStyles';
import styles from './styles';

import ArrowLeft from '../../../../assets/svgs/ArrowLeftSmallWhite';
import ArrowRight from '../../../../assets/svgs/ArrowRight';
import Data from '../../../../assets/svgs/Data';
import Files from '../../../../assets/svgs/Files';
import Check2 from '../../../../assets/svgs/Check2';
import Address from '../../../../assets/svgs/Address2';
import AddressComercial from '../../../../assets/svgs/AddressComercial';
import api from "../../../../services/api";
import { useIsFocused } from "@react-navigation/native";
import statuses from "../helpers/statuses";
import * as Progress from 'react-native-progress';
import SelectBorder from "../../../../components/SelectBorder";
import { ContasClienteBox } from "../components/ContasClienteBox";
import Bad from '../../../../assets/svgs/Alert2';
import Clipboard from "@react-native-clipboard/clipboard";
import Share from 'react-native-share';
import ContasCompartilharBox from "../components/ContasCompartilharBox";
import GreenIcon from '../../../../assets/svgs/GreenIcon';
import RNBlobUtil from 'react-native-blob-util';

const colors = {
    success: '#070707',
    warning: '#FF6542',
    danger: '#FF6542',
};

const EmPreenchimento = ({ route, navigation }) => {
    const { id, progresso, porcentagem, preenchidos = null } = route.params;
    const isFocused = useIsFocused();
    const [loading, setLoading] = useState(true);
    const [progress, setProgress] = useState(0);
    const [conta, setConta] = useState(null);
    const [origens, setOrigens] = useState([]);

    const [origem, setOrigem] = useState(null);

    const back = () => {
        navigation.navigate('ContasMain');
    }

    useEffect(() => {
        const backHandler = BackHandler.addEventListener(
            "hardwareBackPress",
            back
        );

        return () => backHandler.remove();
    }, []);

    useEffect(() => {
        if (isFocused) {
            getConta();
        }
    }, [isFocused]);

    useEffect(() => {
        if (origem) {
            updateOrigem();
        }
    }, [origem]);

    const updateOrigem = () => {
        api.put(`/crm/contas/${id}/origem`, {
            Origem: origem
        }).then(res => {
            console.log('success', res);
            setConta(res.data);
        }).catch(error => {
            Alert.alert('Erro ao atualizar a origem');
            console.log(error);
        });
    }

    const getConta = () => {
        setLoading(true);

        api.get(`/crm/contas/${id}`).then(res => {
            let toOrigens = [];
            console.log(res);
            Object.entries(res.data.selects.Origem).map(([index, origem]) => {
                toOrigens.push({ label: index, value: origem });
            });
            setOrigens(toOrigens);
            setOrigem(res.data.Origem);
            setProgress(res.data.cliente.conta.progresso);
            setConta(res.data);
        }).catch(error => {
            Alert.alert('Não foi possível obter a conta');
            console.log(error);
        }).then(() => setTimeout(() => {
            setLoading(false)
        }, 200));
    }

    const openConfirmDelete = () => {
        Alert.alert('Excluir conta', 'Tem certeza que deseja excluir essa conta?', [
            { text: "Cancelar" },
            { text: "Excluir", onPress: () => remove() }
        ])
    }

    const generatePDF = () => {
        setLoading(true);

        api.post(`/crm/contas/${id}/generate-pdf`).then(() => {
            navigation.reset({
                index: 0,
                routes: [{ name: 'ContasEmPreenchimento', params: { id } }],
            });
        }).finally(() => {
            setLoading(false);
        });
    }

    const remove = () => {
        setLoading(true);

        api.delete(`/crm/contas/${id}`).then(() => {
            navigation.navigate('ContasMain');
        }).catch(error => {
            let message = error?.response?.data?.message ?? '';
            Alert.alert('Não foi possível excluir', message);
        }).then(() => setLoading(false));
    }

    const send = () => {
        setLoading(true);

        api.post(`/crm/contas/${id}/salesforce`).then(res => {
            navigation.navigate('ContasSucesso', { status: 'success', cliente: res.data.conta });
        }).catch(error => {
            let message = error?.response?.data?.message ?? '';
            console.log(error);
            Alert.alert('Não foi possível enviar', message);
        }).then(() => setLoading(false));
    }

    const downloadPDF = async () => {
        setLoading(true);
        const { config, fs } = RNBlobUtil;
        const date = new Date();
        const pdf_URL = conta.cliente.pdf_path;
        const file_path = `${fs.dirs.DownloadDir}/sample_${Math.floor(date.getTime() + date.getSeconds() / 2)}.pdf`;

        if (Platform.OS === 'android') {
            try {
                const granted = await PermissionsAndroid.request(
                    PermissionsAndroid.PERMISSIONS.WRITE_EXTERNAL_STORAGE,
                    {
                        title: 'Permissão para salvar arquivos',
                        message: 'Este aplicativo precisa acessar o armazenamento para baixar arquivos',
                    }
                );
                if (granted !== PermissionsAndroid.RESULTS.GRANTED) {
                    Alert.alert('Permissão negada', 'Não foi possível salvar o arquivo. Permissão negada.');
                    setLoading(false);
                    return;
                }
            } catch (err) {
                console.warn(err);
                setLoading(false);
                return;
            }
        }

        config({
            fileCache: true,
            path: file_path,
        })
            .fetch('GET', pdf_URL)
            .then((res) => {
                setLoading(false);
                Alert.alert('Download Concluído', `O arquivo foi salvo em: ${res.path()}`);
            })
            .catch((error) => {
                setLoading(false);
                console.error(error);
                Alert.alert('Erro', 'Não foi possível baixar o arquivo.');
            });
    };

    return (
        <View style={mainStyles.wrapper}>
            <PrivateHeader title={'Contas'} />
            {loading &&
                <View style={{ flex: 1, justifyContent: "center", alignItems: "center" }}>
                    <ActivityIndicator size="large" color="#00467F" />
                </View>
            }
            {!loading && conta &&
                <>
                    <TouchableOpacity
                        style={styles.buttonTitle}
                        onPress={() => navigation.navigate('ContasMain')}>
                        <View style={styles.icArrow}>
                            <ArrowLeft />
                        </View>
                        <Text style={styles.titlePag}>{statuses[conta.cliente.status]}</Text>
                    </TouchableOpacity>
                    <ScrollView>
                        <View style={mainStyles.wrapperRegister}>
                            <View>
                                <View style={[mainStyles.container, styles.container]}>
                                    <ContasClienteBox cliente={conta.cliente} />
                                    <View style={styles.divider}></View>
                                    <View style={styles.containerFicha}>
                                        <Text style={[conta.cliente.conta.porcentagem == '100%' ? styles.textStatusGreen : styles.textStatus]}>Status do preenchimento: {conta.cliente.conta.porcentagem}</Text>
                                        <Progress.Bar
                                            progress={progress}
                                            width={null}
                                            height={15}
                                            color={conta.cliente.conta.porcentagem == '100%' ? "#1B9C20" : "#2D719F"}
                                            unfilledColor="#D9D9D9"
                                            borderWidth={1}
                                            borderColor="#D9D9D9"
                                            style={{ width: '100%' }}
                                        />
                                        <View style={styles.textView}>
                                            <Text style={styles.textInfoFicha}>Fique atento! Itens importantes para a ficha:</Text>
                                            <Text style={styles.textInfoFicha}>{conta.cliente.conta.preenchidos?.cpf ? <GreenIcon style={styles.greenIcon} /> : <Bad style={styles.icon} />} CPF do cliente</Text>
                                            <Text style={styles.textInfoFicha}>{conta.cliente.conta.preenchidos?.foto_documento ? <GreenIcon style={styles.greenIcon} /> : <Bad style={styles.icon} />} Foto do documento</Text>
                                            <Text style={styles.textInfoFicha}>{conta.cliente.conta.preenchidos?.dados_selfie ? <GreenIcon style={styles.greenIcon} /> : <Bad style={styles.icon} />} Confirmação dos dados e selfie</Text>
                                            <Text style={styles.textInfoFicha}>{conta.cliente.conta.preenchidos?.endereco_comercial ? <GreenIcon style={styles.greenIcon} /> : <Bad style={styles.icon} />} Endereço comercial</Text>
                                        </View>
                                        <TouchableOpacity style={{ marginTop: 20, width: '100%' }} onPress={() => navigation.navigate('ContaPDF', { pdf_url: conta.cliente.pdf_path.pdf_url, conta: conta })}>
                                            <Text style={styles.btnVerFicha}>VER FICHA DIGITAL</Text>
                                        </TouchableOpacity>
                                    </View>
                                    <Text style={[mainStyles.label, { color: colors[conta.itens.origem.status] }]}>ORIGEM:</Text>
                                    <SelectBorder options={origens} value={origem} onChange={setOrigem} placeholder="SELECIONE" />
                                    <View style={styles.divider}></View>
                                    <TouchableOpacity style={styles.boxTotal} onPress={() => navigation.navigate('ContasDadosPessoais', { id, conta })}>
                                        <View style={styles.totalIcon}>
                                            <Data style={{ color: "#FFF" }} />
                                        </View>
                                        <View style={styles.totalInfos}>
                                            <View>
                                                <Text style={styles.titleBox}>Dados pessoais</Text>
                                                <Text style={[styles.subtitleBox, { color: colors[conta.itens['dados-pessoais'].status] }]}>{conta.itens['dados-pessoais'].texto}</Text>
                                            </View>
                                            <ArrowRight style={styles.icArrowBottom} />
                                        </View>
                                    </TouchableOpacity>
                                    <View style={styles.divider}></View>
                                    <TouchableOpacity style={styles.boxTotal} onPress={() => navigation.navigate('ContasEndereco', { id, conta })}>
                                        <View style={styles.totalIcon}>
                                            <Address style={{ color: "#FFF" }} />
                                        </View>
                                        <View style={styles.totalInfos}>
                                            <View>
                                                <Text style={styles.titleBox}>Endereço Residencial</Text>
                                                <Text style={[styles.subtitleBox, { color: colors[conta.itens['endereco'].status] }]}>{conta.itens['endereco'].texto}</Text>
                                            </View>
                                            <ArrowRight style={styles.icArrowBottom} />
                                        </View>
                                    </TouchableOpacity>
                                    <View style={styles.divider}></View>
                                    <TouchableOpacity style={styles.boxTotal} onPress={() => navigation.navigate('ContasEnderecoComercial', { id, conta })}>
                                        <View style={styles.totalIcon}>
                                            <AddressComercial style={{ color: "#FFF" }} />
                                        </View>
                                        <View style={styles.totalInfos}>
                                            <View>
                                                <Text style={styles.titleBox}>Endereço Comercial</Text>
                                                <Text style={[styles.subtitleBox, { color: colors[conta.itens['endereco-comercial'].status] }]}>{conta.itens['endereco-comercial'].texto}</Text>
                                            </View>
                                            <ArrowRight style={styles.icArrowBottom} />
                                        </View>
                                    </TouchableOpacity>
                                    <View style={styles.divider}></View>
                                    <TouchableOpacity style={styles.boxTotal} onPress={() => navigation.navigate('ContasDocumento', { id, conta })}>
                                        <View style={styles.totalIcon}>
                                            <Files style={{ color: "#FFF" }} />
                                        </View>
                                        <View style={styles.totalInfos}>
                                            <View>
                                                <Text style={styles.titleBox}>Documento</Text>
                                                <Text style={[styles.subtitleBox, { color: colors[conta.itens['documento'].status] }]}>{conta.itens['documento'].texto}</Text>
                                            </View>
                                            <ArrowRight style={styles.icArrowBottom} />
                                        </View>
                                    </TouchableOpacity>
                                    <View style={styles.divider}></View>
                                    <TouchableOpacity style={styles.boxTotal} onPress={() => navigation.navigate('ContasAutorizacoes', { id, conta })}>
                                        <View style={styles.totalIcon}>
                                            <Check2 style={{ color: "#FFF" }} />
                                        </View>
                                        <View style={styles.totalInfos}>
                                            <View>
                                                <Text style={styles.titleBox}>Autorizações</Text>
                                                <Text style={[styles.subtitleBox, { color: colors[conta.itens['autorizacoes'].status] }]}>{conta.itens['autorizacoes'].texto}</Text>
                                            </View>
                                            <ArrowRight style={styles.icArrowBottom} />
                                        </View>
                                    </TouchableOpacity>
                                    <View style={styles.divider}></View>
                                    <ContasCompartilharBox conta={conta} />
                                    <View style={styles.divider}></View>
                                    {conta.cliente.status === 'em-preenchimento' &&
                                        <TouchableOpacity style={styles.btnDelete} onPress={openConfirmDelete}>
                                            <Text style={styles.btnDeleteText}>EXCLUIR CONTA</Text>
                                        </TouchableOpacity>
                                    }

                                </View>
                            </View>
                        </View>
                    </ScrollView>
                    <View style={styles.contentBottom}>
                        <View style={[mainStyles.container, styles.contentButtons]}>
                            <TouchableOpacity
                                style={[mainStyles.btnBlueNew, styles.buttonLarge, conta.cliente.salesforce_can_send !== true ? { backgroundColor: "#979797" } : null]}
                                disabled={conta.cliente.salesforce_can_send !== true}
                                onPress={send}>
                                <Text style={mainStyles.btnTextBlueNew}>{conta.cliente.status === 'enviada' ? 'ENVIADA PARA SALESFORCE' : 'ENVIAR PARA SALESFORCE'}</Text>
                            </TouchableOpacity>
                        </View>
                    </View>
                </>
            }
        </View>
    );
}

export default EmPreenchimento;