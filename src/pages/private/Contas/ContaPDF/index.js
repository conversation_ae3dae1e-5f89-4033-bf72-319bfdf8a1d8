import React, { useState } from 'react';
import { StyleSheet, Dimensions, Text, View, PermissionsAndroid, Alert, TouchableOpacity, ActivityIndicator, ScrollView, Platform } from 'react-native';

import PrivateHeader from '../../../../components/headers/PrivateHeader';

import styles from './styles';
import mainStyles from '../../../../mainStyles';
import RNBlobUtil from 'react-native-blob-util';
import ReactNativeBlobUtil from 'react-native-blob-util';

import Pdf from 'react-native-pdf';
import Share from 'react-native-share';
import ArrowLeft from '../../../../assets/svgs/ArrowLeftSmallWhite';
import { useAuth } from '../../../../context/auth';

const ContaPDF = ({ route, navigation }) => {
    const [loading, setLoading] = useState(false);

    const [scrollViewHeight, setScrollViewHeight] = useState(0);

    const { token } = useAuth();

    const { pdf_url, conta } = route.params;

    console.log('pdf url', pdf_url);

    const shareFile = async () => {
        try {
            setLoading(true);
            const dirs = ReactNativeBlobUtil.fs.dirs;


            const filePath = `${dirs.DocumentDir}/ficha-cadastral-${conta.cliente.nome}.pdf`; 
            const configOptions = {
                fileCache: true,
                path: filePath
            };

            const res = await ReactNativeBlobUtil.config(configOptions)
                .fetch('GET', pdf_url, {
                    Authorization: `Bearer ${token}`
                });

            setLoading(false);

            const fetchedFilePath = res.path();
            const mimeType = 'application/pdf'; 

            await Share.open({
                type: mimeType,
                url: Platform.OS === 'ios' ? fetchedFilePath : `file://${fetchedFilePath}`,
                title: 'Compartilhar Ficha Cadastral',
                subject: 'Ficha Cadastral',
                message: 'Segue em anexo a ficha cadastral.'
            }).catch((err) => {
                console.log(err);
            });

            ReactNativeBlobUtil.fs.unlink(fetchedFilePath);
        } catch (error) {
            console.log(error);
            setLoading(false);
        }
    };

    return (
        <View style={mainStyles.wrapper}>
            <PrivateHeader title={`Ficha Cadastral`} />
            <TouchableOpacity
                style={styles.buttonTitle}
                onPress={() => navigation.goBack()}>
                <View style={styles.icArrow}>
                    <ArrowLeft />
                </View>
                <Text style={styles.titlePag}>FICHA DE CADASTRO DIGITAL</Text>
            </TouchableOpacity>
            {loading &&
                <View style={{ flex: 1, marginTop: 40, justifyContent: "center", alignItems: "center" }}>
                    <ActivityIndicator size="large" color="#00467F" />
                </View>
            }
            {!loading === null &&
                <View>
                    <Text>Nenhuma informação encontrada</Text>
                </View>
            }
            {!loading !== null &&
                <>
                    <ScrollView 
                        contentContainerStyle={styles.scrollView}
                        onLayout={(event) => {
                            const { height } = event.nativeEvent.layout;
                            setScrollViewHeight(height);
                        }}
                    >
                        <View style={styles.containerView}>
                            <Pdf
                                trustAllCerts={false}
                                source={{
                                    uri: `${pdf_url}?v=${Date.now()}`, cache: false, headers: {
                                        Authorization: `Bearer ${token}`
                                    }
                                }}
                                onLoadComplete={(numberOfPages, filePath) => {
                                    console.log(`number of pages: ${numberOfPages}`);
                                }}
                                onPageChanged={(page, numberOfPages) => {
                                    console.log(`current page: ${page}`);
                                }}
                                onError={(error) => {
                                    console.log(error);
                                }}
                                onPressLink={(uri) => {
                                    console.log(`Link presse: ${uri}`)
                                }}
                                style={{...styles.containerPdf, height: scrollViewHeight}} />
                        </View>
                    </ScrollView>
                    <View style={styles.contentBottom}>
                        <View style={[mainStyles.container, styles.contentButtons]}>
                            <TouchableOpacity disabled={loading} style={[mainStyles.btnBlueNew, styles.buttonLarge]} onPress={() => shareFile(pdf_url)}>
                                <View style={styles.btnAct}>
                                    <Text style={styles.btnWhite}>{loading ? 'CARREGANDO...' : 'COMPARTILHAR'}</Text>
                                </View>
                            </TouchableOpacity>
                        </View>
                    </View>
                </>
            }
        </View>
    );
}

export default ContaPDF;