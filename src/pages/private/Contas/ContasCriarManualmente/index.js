import React, { useState, useRef, useEffect } from 'react';
import { Platform, Text, TouchableOpacity, View, Image, Alert, ActivityIndicator, TextInput, PermissionsAndroid, KeyboardAvoidingView, ScrollView, Keyboard, FlatList } from 'react-native';

import PrivateHeader from '../../../../components/headers/PrivateHeader';
import Select from '../../../../components/Select';

import { cpfMask, phoneMask, dateMask } from '../../../../useful/masks';

import mainStyles from '../../../../mainStyles';
import styles from './styles';

import ArrowLeft from '../../../../assets/svgs/ArrowLeftSmallWhite';
import User from '../../../../assets/svgs/Users3';
import LightboxSearch from '../../../../components/LightboxSearch';
import { useIsFocused } from "@react-navigation/native";
import api from "../../../../services/api";
import moment from "moment";
import { createSelectOptions } from "../helpers/functions";
import SelectButton from "../../../../components/SelectButton";
import { convertDateToAmerican } from "../../../../useful/conversions";
import { ContasClienteBox } from "../components/ContasClienteBox";

const CriarManualmente = ({route, navigation}) => {
    const isFocused = useIsFocused();

    const [loading, setLoading] = useState(false);

    const [inNome, setInNome] = useState('');
    const [inSobrenome, setInSobrenome] = useState('');
    const [inCelular, setInCelular] = useState('');
    const [inEmail, setInEmail] = useState('');
    const [inCpf, setInCpf] = useState('');
    const [inDataNascimento, setInDataNascimento] = useState('');
    const [inSexo, setInSexo] = useState('');
    const [inEstadoCivil, setInEstadoCivil] = useState('');
    const [inRegime, setInRegime] = useState('');
    const [inProfissao, setInProfissao] = useState('');
    const [inOutraProfissao, setInOutraProfissao] = useState('');

    const [showModalProfissao, setShowModalProfissao] = useState(false);

    const [estadoCivilOptions, setEstadoCivilOptions] = useState([]);
    const [profissaoOptions, setProfissaoOptions] = useState([]);
    const [regimeBensOptions, setRegimeBensOptions] = useState([]);
    const [sexoOptions, setSexoOptions] = useState([]);

    useEffect(() => {
        if(isFocused){
            getSelects();
        }
    }, [isFocused]);

    const getSelects = () => {
        api.get(`/crm/contas/criar/dados-pessoais`).then(res => {
            const { selects } = res.data;

            setEstadoCivilOptions(createSelectOptions(selects.EstadoCivil));
            setProfissaoOptions(createSelectOptions(selects.Profissao));
            setRegimeBensOptions(createSelectOptions(selects.RegimeBens));
            setSexoOptions(createSelectOptions(selects.Sexo));
        });
    }

    const store = () => {
        setLoading(true);

        api.post(`/crm/contas/criar`, {
            tipo: 'manual',
            Nome: inNome,
            Sobrenome: inSobrenome,
            Celular: inCelular,
            Email: inEmail,
            cpf_cnpj: inCpf,
            DataNascimento: convertDateToAmerican(inDataNascimento),
            Sexo: inSexo,
            EstadoCivil: inEstadoCivil,
            Profissao: inProfissao,
            RegimeBens: inRegime,
            ProfissaoOutra: inOutraProfissao
        }).then(res => {
            navigation.navigate('ContasEmPreenchimento', { id: res.data.conta.id });
        }).catch(error => {
            console.log(error);
            let errors = error?.response?.data?.errors ?? null;
            let errorText = '';
            if(errors){
                Object.keys(errors).map(key => {
                    errors[key].map(error => {
                        errorText += `- ${error}\n`;
                    });
                });
            }
            Alert.alert('Não foi possível salvar', errorText);
        }).then(() => setLoading(false));
    }

    return (
        <>
            <View style={mainStyles.wrapper}>
                <PrivateHeader title={'Contas'} />
                <TouchableOpacity
                    style={styles.buttonTitle}
                    onPress={() => navigation.goBack()}>
                    <View style={styles.icArrow}>
                        <ArrowLeft />
                    </View>
                    <Text style={styles.titlePag}>Dados pessoais</Text>
                </TouchableOpacity>
                <KeyboardAvoidingView style={{ flex: 1 }} behavior="padding">
                    <ScrollView keyboardShouldPersistTaps='handled'>
                        {loading && 
                            <View style={{flex: 1, marginTop: 50, justifyContent: "center", alignItems: "center"}}>
                                <ActivityIndicator size="large" color="#00467F" />
                            </View>
                        }
                        {!loading && 
                            <>
                                <View style={mainStyles.wrapperRegister}>
                                    <View style={[mainStyles.container, styles.container]}>
                                        <View style={{ marginTop: 20 }}>
                                            <Text style={mainStyles.label}>NOME:</Text>                                
                                            <TextInput
                                                underlineColorAndroid="transparent"  
                                                style={mainStyles.inputText}
                                                value={inNome}
                                                onChangeText={text => setInNome(text)}
                                                autoCapitalize="words"
                                            />
                                        </View>
                                        <View>
                                            <Text style={mainStyles.label}>SOBRENOME:</Text>                                
                                            <TextInput
                                                underlineColorAndroid="transparent"  
                                                style={mainStyles.inputText}
                                                value={inSobrenome}
                                                onChangeText={text => setInSobrenome(text)}
                                                autoCapitalize="words"
                                            />
                                        </View>
                                        <View>
                                            <Text style={[mainStyles.label]}>TELEFONE:</Text>
                                            <TextInput
                                                underlineColorAndroid="transparent"  
                                                style={mainStyles.inputText}
                                                keyboardType="phone-pad"
                                                value={inCelular}
                                                onChangeText={text => setInCelular( phoneMask(text) )}
                                            />
                                        </View>
                                        <View>
                                            <Text style={[mainStyles.label]}>E-MAIL:</Text>
                                            <TextInput
                                                autoCapitalize="none"  
                                                underlineColorAndroid="transparent"  
                                                style={mainStyles.inputText}
                                                keyboardType="email-address"
                                                value={inEmail}
                                                onChangeText={text => setInEmail(text.trim())}
                                            />
                                        </View>
                                        <View>
                                            <Text style={mainStyles.label}>CPF:</Text>
                                            <TextInput
                                                underlineColorAndroid="transparent"  
                                                style={mainStyles.inputText}
                                                value={inCpf}
                                                onChangeText={text => setInCpf( cpfMask(text) )}
                                                keyboardType="phone-pad"
                                            />
                                        </View>
                                        <View>
                                            <Text style={mainStyles.label}>DATA DE NASCIMENTO</Text>                                
                                            <TextInput  
                                                underlineColorAndroid="transparent"  
                                                style={mainStyles.inputText}
                                                value={inDataNascimento}
                                                onChangeText={value => setInDataNascimento( dateMask(value))}
                                                placeholder="dd/mm/aaaa"
                                                keyboardType="phone-pad"
                                            />
                                        </View>
                                        <View>
                                            <Text style={mainStyles.label}>SEXO:</Text>                                
                                            <Select 
                                                removeBorder={true}
                                                placeholder="SELECIONE"
                                                options={sexoOptions} 
                                                value={inSexo} 
                                                onChange={setInSexo} 
                                            />
                                        </View>
                                        <View>
                                            <Text style={mainStyles.label}>ESTADO CIVIL:</Text>                                
                                            <Select 
                                                removeBorder={true}
                                                placeholder="SELECIONE"
                                                options={estadoCivilOptions} 
                                                value={inEstadoCivil} 
                                                onChange={setInEstadoCivil} 
                                            />
                                        </View>
                                        {inEstadoCivil == 'Casado (a)' &&
                                            <View>
                                                <Text style={mainStyles.label}>REGIME DE BENS:</Text>                                
                                                <Select 
                                                    removeBorder={true}
                                                    placeholder="SELECIONE"
                                                    options={regimeBensOptions} 
                                                    value={inRegime} 
                                                    onChange={setInRegime} 
                                                />
                                            </View>
                                        }
                                        <View>
                                            <Text style={mainStyles.label}>PROFISSÃO:</Text> 
                                            <SelectButton 
                                                onPress={() => setShowModalProfissao(true)}
                                                placeholder="SELECIONE"
                                                value={inProfissao}
                                            />
                                        </View>
                                        <View>
                                            <Text style={mainStyles.label}>OUTRA PROFISSÃO:</Text>                                
                                            <TextInput
                                                underlineColorAndroid="transparent"  
                                                style={mainStyles.inputText}
                                                value={inOutraProfissao}
                                                onChangeText={text => setInOutraProfissao(text)}
                                                autoCapitalize="words"
                                            />
                                        </View>
                                    </View>
                                </View>
                            </>
                        }
                    </ScrollView>
                    <View style={styles.contentBottom}>
                        <View style={[mainStyles.container, styles.contentButtons]}>
                        <TouchableOpacity
                            disabled={loading}
                            style={[mainStyles.btnBlueNew, styles.buttonLarge]}
                            onPress={store}>
                            <Text style={mainStyles.btnTextBlueNew}>{loading ? 'CARREGANDO' : 'CADASTRAR'}</Text>
                        </TouchableOpacity>
                        </View>
                    </View>
                </KeyboardAvoidingView>
            </View>
            {showModalProfissao && (
                <LightboxSearch
                    close={() => setShowModalProfissao(false)}
                    title="Profissão"
                    options={profissaoOptions}
                    value={inProfissao}
                    setValue={setInProfissao}
                />
            )}
        </>
    );
}

export default CriarManualmente;