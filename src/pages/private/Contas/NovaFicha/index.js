import React, { useState, useEffect, useRef } from 'react';
import { Text, TouchableOpacity, View, Alert, ActivityIndicator, TextInput, KeyboardAvoidingView, ScrollView } from 'react-native';

import PrivateHeader from '../../../../components/headers/PrivateHeader';

import { phoneMask } from '../../../../useful/masks';

import mainStyles from '../../../../mainStyles';
import styles from './styles';

import ArrowLeft from '../../../../assets/svgs/ArrowLeftSmallWhite';

import api from "../../../../services/api";


const CriarManualmente = ({ route, navigation }) => {
    const inputRefs = useRef([]);


    const [loading, setLoading] = useState(false);

    const [inNome, setInNome] = useState('');
    const [inSobrenome, setInSobrenome] = useState('');
    const [inCelular, setInCelular] = useState('');
    const [inEmail, setInEmail] = useState('');

    const store = () => {
        setLoading(true);

        api.post(`/crm/contas/criar`, {
            tipo: 'manual',
            Nome: inNome,
            Sobrenome: inSobrenome,
            Celular: inCelular,
            Email: inEmail,
        }).then(res => {
            navigation.navigate('ContasEmPreenchimento', { id: res.data.conta.id, 
                    progresso:res.data.conta.conta.progresso, porcentagem: res.data.conta.conta.porcentagem, 
                    preenchidos: res.data.conta.conta.preenchidos });
        }).catch(error => {
            console.log(error);
            let errors = error?.response?.data?.errors ?? null;
            let errorText = '';
            if (errors) {
                Object.keys(errors).map(key => {
                    errors[key].map(error => {
                        errorText += `- ${error}\n`;
                    });
                });
            }
            Alert.alert('Não foi possível salvar', errorText);
        }).then(() => setLoading(false));
    }

    const focusNextField = (index) => {
        if (inputRefs.current[index + 1]) {
          inputRefs.current[index + 1].focus();
        }
    };

    return (
        <>
            <View style={mainStyles.wrapper}>
                <PrivateHeader title={'Contas'} />
                <TouchableOpacity
                    style={styles.buttonTitle}
                    onPress={() => navigation.goBack()}>
                    <View style={styles.icArrow}>
                        <ArrowLeft />
                    </View>
                    <Text style={styles.titlePag}>Dados pessoais</Text>
                </TouchableOpacity>
                <KeyboardAvoidingView style={{ flex: 1 }} behavior="padding">
                    <ScrollView keyboardShouldPersistTaps='handled'>
                        {loading &&
                            <View style={{ flex: 1, marginTop: 50, justifyContent: "center", alignItems: "center" }}>
                                <ActivityIndicator size="large" color="#00467F" />
                            </View>
                        }
                        {!loading &&
                            <>
                                <View style={mainStyles.wrapperRegister}>
                                    <View style={[mainStyles.container, styles.container]}>
                                        <View style={{ marginTop: 20 }}>
                                            <Text style={mainStyles.label}>NOME:</Text>
                                            <TextInput
                                                returnKeyType='next'
                                                ref={el => inputRefs.current[0] = el}
                                                onSubmitEditing={() => focusNextField(0)}
                                                blurOnSubmit={false}
                                                underlineColorAndroid="transparent"
                                                style={mainStyles.inputText}
                                                value={inNome}
                                                onChangeText={text => setInNome(text)}
                                                autoCapitalize="words"
                                            />
                                        </View>
                                        <View>
                                            <Text style={mainStyles.label}>SOBRENOME:</Text>
                                            <TextInput
                                                returnKeyType='next'
                                                ref={el => inputRefs.current[1] = el}
                                                onSubmitEditing={() => focusNextField(1)}
                                                blurOnSubmit={false}
                                                underlineColorAndroid="transparent"
                                                style={mainStyles.inputText}
                                                value={inSobrenome}
                                                onChangeText={text => setInSobrenome(text)}
                                                autoCapitalize="words"
                                            />
                                        </View>
                                        <View>
                                            <Text style={[mainStyles.label]}>TELEFONE:</Text>
                                            <TextInput
                                                returnKeyType='next'
                                                ref={el => inputRefs.current[2] = el}
                                                onSubmitEditing={() => focusNextField(2)}
                                                blurOnSubmit={false}
                                                underlineColorAndroid="transparent"
                                                style={mainStyles.inputText}
                                                keyboardType="phone-pad"
                                                value={inCelular}
                                                onChangeText={text => setInCelular(phoneMask(text))}
                                            />
                                        </View>
                                        <View>
                                            <Text style={[mainStyles.label]}>E-MAIL:</Text>
                                            <TextInput
                                                returnKeyType='done'
                                                autoCapitalize="none"
                                                underlineColorAndroid="transparent"
                                                style={mainStyles.inputText}
                                                keyboardType="email-address"
                                                value={inEmail}
                                                onChangeText={text => setInEmail(text.trim())}
                                            />
                                        </View>
                                    </View>
                                </View>
                            </>
                        }
                    </ScrollView>
                    <View style={styles.contentBottom}>
                        <View style={[mainStyles.container, styles.contentButtons]}>
                            <TouchableOpacity
                                disabled={loading}
                                style={[mainStyles.btnBlueNew, styles.buttonLarge]}
                                onPress={store}>
                                <Text style={mainStyles.btnTextBlueNew}>{loading ? 'CARREGANDO' : 'SALVAR FICHA'}</Text>
                            </TouchableOpacity>
                        </View>
                    </View>
                </KeyboardAvoidingView>
            </View>

        </>
    );
}

export default CriarManualmente;