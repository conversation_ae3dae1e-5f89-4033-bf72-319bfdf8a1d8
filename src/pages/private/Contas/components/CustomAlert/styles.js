import { Dimensions, Platform, StyleSheet } from 'react-native';


const { width } = Dimensions.get('window');

const styles = StyleSheet.create({
    container: {
        flex: 1,
        justifyContent: 'center',
        alignItems: 'center',
    },
    overlay: {
        flex: 1,
        justifyContent: 'center',
        alignItems: 'center',
        backgroundColor: 'rgba(0, 0, 0, 0.5)',
    },
    alertContainer: {
        width: width * 0.9, 
        backgroundColor: 'white',
        borderRadius: 10,
        padding: 20,
        elevation: 5,
    },
    title: {
        fontSize: 18,
        fontWeight: 'bold',
        marginBottom: 10,
        textAlign: 'center',
    },
    message: {
        fontSize: 16,
        marginBottom: 20,
        textAlign: 'center',
    },
    buttonContainer: {
        flexDirection: 'column',  
    },
    button: {
        paddingVertical: 15,
        borderBottomWidth: 1,
        borderColor: '#E0E0E0',
    },
    buttonText: {
        textAlign: 'center',
        color: '#009688', 
        fontSize: 14,
    },
});

export default styles;