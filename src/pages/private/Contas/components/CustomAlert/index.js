import React, { useState } from 'react';
import { Modal, View, Text, TouchableOpacity } from 'react-native';
import styles from './styles';

const CustomAlert = ({ visible, onClose, title, message, options }) => {
    return (
        <Modal
            transparent={true}
            animationType="fade"
            visible={visible}
            onRequestClose={onClose}
        >
            <View style={styles.overlay}>
                <View style={styles.alertContainer}>
                    {title ? <Text style={styles.title}>{title}</Text> : null}
                    {message ? <Text style={styles.message}>{message}</Text> : null}
                    <View style={styles.buttonContainer}>
                        {options.map((option, index) => (
                            <TouchableOpacity
                                key={index}
                                onPress={option.onPress}
                                style={styles.button}
                            >
                                <Text style={styles.buttonText}>{option.text}</Text>
                            </TouchableOpacity>
                        ))}
                    </View>
                </View>
            </View>
        </Modal>
    );
};

export default CustomAlert;