import { StyleSheet, Dimensions } from 'react-native';

const windowWidth = Dimensions.get('window').width;
const windowHeight = Dimensions.get('window').height;

const styles = StyleSheet.create({
    container: {
        paddingTop: 0
    },
    wrapper: {
        justifyContent: "space-between"
    },
    divider: {
        borderBottomWidth: 1,
        borderBottomColor: '#DADADA',
        marginTop: 20,
        marginBottom: 30
    },
    buttonTitle: {
      backgroundColor: '#90B0C0',
      height: 70,
      width: '100%',
      alignItems: 'center',
      justifyContent: 'center',
      flexDirection: 'row',
      marginBottom: 20
    },
    icArrow: {
      position: 'absolute',
      left: 20,
    },
    titlePag: {
      fontFamily: 'Ubuntu-Regular',
      color: '#FFF',
      fontSize: 15,
      letterSpacing: 0.46,
      textTransform: 'uppercase'
    },
    errorMessage: {
      fontFamily: 'Ubuntu-Regular',
      textAlign: 'center',
      color: '#979797',
      fontSize: 14,
      marginTop: 15,
      marginBottom: 15
    },
    gif: {
        width: 50,
        height: 50,
        resizeMode: "cover"
    },
    icCheck: {
        color: "#62829A"
    },
    boxOption: {
      flexDirection: 'row',
      minHeight: 70,
      marginTop: -10
    },
    totalIcon: {
      backgroundColor: '#90B0C0',
      width: 60,
      alignItems: 'center',
      justifyContent: 'center',
    },
    totalInfos: {
      backgroundColor: '#F2F2F2',
      flex: 1,
      paddingLeft: 10,
      paddingRight: 20,
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'space-between',
    },
    titleBox: {
      fontFamily: 'Ubuntu-Regular',
      fontSize: 16,
      color: '#00467F',
      letterSpacing: 0.46,
      marginTop: 2,
      marginBottom: 4,
      lineHeight: 17.32,
    },
    subtitleBox: {
      fontFamily: 'Ubuntu-Light',
      fontSize: 14,
      color: '#000',
      letterSpacing: 0.46,
      lineHeight: 17.32,
      width: 230,
    },
    status: {
      fontFamily: 'Ubuntu-Light',
      fontSize: 14,
      letterSpacing: 0.46,
      lineHeight: 17.32,
      width: 230,
      marginTop: 4
    },
});

export default styles;