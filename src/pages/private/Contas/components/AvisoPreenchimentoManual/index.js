import React from 'react';
import { View, Text, Button, TouchableOpacity } from 'react-native';
import mainStyles from "../../../../../mainStyles";
import styles from './styles';
import Bad from '../../../../../assets/svgs/Alert2';
import { useNavigation } from '@react-navigation/native';

const AvisoPreenchimentoManual = ({ setExibirAviso }) => {
  const navigation = useNavigation();
  return (
    <>
      <View>
        <View style={[mainStyles.container, styles.container]}>
          <View style={styles.divider}></View>
          <View style={{ flexDirection: "row", justifyContent: "center", marginBottom: 15 }}>
            <Bad />
          </View>
          <Text style={[mainStyles.label, { textAlign: "center" }]}>Atenção: Caso siga com a opção de preenchimento manual, a ficha cadastral <Text style={[{ fontWeight: 'bold' }]}>NÃO</Text> {'\n'} será anexada na Conta do seu cliente no Salesforce.{'\n'}{'\n'}
            Para ter sua ficha cadastral completa, volte e tire a foto do documento do seu cliente.</Text>
          <View style={styles.divider}></View>
        </View>
        <View style={{ flexDirection: "column", alignItems: "center" }}>

          <TouchableOpacity
            style={[
              mainStyles.btnOutlineBlueDark,
              { height: 50 },
            ]}
            onPress={() => {
              setExibirAviso(false);
              navigation.navigate('ContasCriarManualmente');
            }}>
            <Text style={mainStyles.btnTextOutlineBlueDark}>PREENCHER DADOS MANUALMENTE</Text>
          </TouchableOpacity>
        </View>
      </View>
      <View style={styles.contentBottom}>
        <View style={[mainStyles.container, styles.contentButtons]}>
          <TouchableOpacity
            style={[mainStyles.btnBlueNew, styles.buttonLarge]}
            onPress={() => navigation.goBack()}>
            <Text style={mainStyles.btnTextBlueNew}>VOLTAR</Text>
          </TouchableOpacity>
        </View>
      </View>
    </>
  );
};

export default AvisoPreenchimentoManual;