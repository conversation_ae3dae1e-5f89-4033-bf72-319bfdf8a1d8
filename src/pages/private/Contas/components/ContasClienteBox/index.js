import React from "react";
import { Text, View } from "react-native";

import moment from "moment";
import styles from './styles';
import statuses from "../../helpers/statuses";
import User from '../../../../../assets/svgs/Users3';


export const ContasClienteBox = ({cliente}) => {
    return (
        <View style={styles.boxOption}>
            <View style={[styles.totalIcon, { backgroundColor: '#F2F2F2' }]}>
                <User />
            </View>
            <View style={[styles.totalInfos, {backgroundColor: '#FFF'}]}>
                {cliente &&
                    <View>
                        <Text style={[styles.titleBox, {textTransform: 'none'}]}>{cliente.nome}</Text>
                        {cliente.status === 'enviada' &&
                            <Text style={styles.subtitleBox}>Enviada em {moment(cliente.salesforce_sent_at).format('DD/MM/YYYY')}</Text>
                        }
                        {cliente.status === 'em-preenchimento' &&
                            <Text style={styles.subtitleBox}>Criada em {moment(cliente.created_at).format('DD/MM/YYYY')}</Text>
                        }
                        {cliente.status === 'em-preenchimento' &&
                            <Text style={[styles.status, {color: "#00467F"}]}>{statuses[cliente.status]}</Text>
                        }
                    </View>
                }
            </View>
        </View>
        
    );
}