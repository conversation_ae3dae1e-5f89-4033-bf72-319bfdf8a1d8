import {Dimensions, Platform, StyleSheet} from 'react-native';

const windowWidth = Dimensions.get('window').width;

const styles = StyleSheet.create({
  totalInfos: {
    backgroundColor: '#F2F2F2',
    flex: 1,
    paddingLeft: 10,
    paddingRight: 20,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  titleBox: {
    fontFamily: 'Ubuntu-Regular',
    fontSize: 16,
    color: '#00467F',
    letterSpacing: 0.46,
    marginTop: 2,
    marginBottom: 4,
    lineHeight: 17.32,
  },
  subtitleBox: {
    fontFamily: 'Ubuntu-Light',
    fontSize: 14,
    color: '#000',
    letterSpacing: 0.46,
    lineHeight: 17.32,
    width: 230,
  },
  status: {
    fontFamily: 'Ubuntu-Light',
    fontSize: 14,
    letterSpacing: 0.46,
    lineHeight: 17.32,
    width: 230,
    marginTop: 4
  },
  boxOption: {
    flexDirection: 'row',
    minHeight: 70,
    marginTop: 15
  },
  totalIcon: {
    backgroundColor: '#90B0C0',
    width: 60,
    alignItems: 'center',
    justifyContent: 'center',
  },
});

export default styles;
