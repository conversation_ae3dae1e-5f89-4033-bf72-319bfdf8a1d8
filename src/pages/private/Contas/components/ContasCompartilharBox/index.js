import React from "react";
import mainStyles from "../../../../../mainStyles";
import Clipboard from "@react-native-clipboard/clipboard";
import Share from 'react-native-share';
import { Alert, Text, TouchableOpacity, View } from "react-native";
import styles from "./styles";

const ContasCompartilharBox = ({ conta }) => {
    return (
        <View>
            {conta.itens.autorizacoes.pendencias > 0 &&
                <Text style={styles.textAlert}>Envie o link abaixo para o cliente confirmar os dados e autorizar o uso dos mesmos.</Text>
            }
            {conta.link === null && conta.itens.autorizacoes.pendencias > 0 &&
                <Text style={[styles.textAlert]}>Preencha o Nome e o CPF do cliente dentro de "Dados pessoais” para habilitar os links.</Text>
            }
            {conta.itens.autorizacoes.pendencias === 0 &&
                <Text style={styles.textAlert}>Dados já confirmados pelo cliente. Se necessária alguma alteração, envie novamente o link para o cliente fazer os ajustes pertinentes.</Text>
            }
            <View style={{flexDirection: "row", justifyContent: "space-between"}}>
                <TouchableOpacity
                    disabled={conta.link === null}
                    style={[
                        mainStyles.btnOutlineBlueDark,
                        mainStyles.buttonW48,
                        {height: 50},
                        conta.link === null ? mainStyles.btnOutlineBlueDarkDisabled : null
                    ]}
                    onPress={() => {
                        Clipboard.setString(conta.link);
                        Alert.alert('Link copiado');
                    }}>
                    <Text style={[mainStyles.btnTextOutlineBlueDark, conta.link === null ? mainStyles.btnTextOutlineBlueDarkDisabled : null ]}>COPIAR LINK</Text>
                </TouchableOpacity>
                <TouchableOpacity
                    disabled={conta.link === null}
                    style={[
                        mainStyles.btnOutlineBlueDark,
                        mainStyles.buttonW48,
                        {height: 50},
                        conta.link === null ? mainStyles.btnOutlineBlueDarkDisabled : null
                    ]}
                    onPress={() => {
                        Share.open({
                            title: 'Confirme seus dados',
                            message: conta.link
                        });
                    }}>
                    <Text style={[mainStyles.btnTextOutlineBlueDark, conta.link === null ? mainStyles.btnTextOutlineBlueDarkDisabled : null]}>COMPARTILHAR</Text>
                </TouchableOpacity>
            </View>
        </View>
    );
} 

export default ContasCompartilharBox;