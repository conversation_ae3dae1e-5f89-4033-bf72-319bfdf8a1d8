import React, { useState, useRef, useEffect } from 'react';
import { Platform, Text, TouchableOpacity, View, Image, Alert, ActivityIndicator, TextInput, PermissionsAndroid, KeyboardAvoidingView, ScrollView, Keyboard } from 'react-native';

import PrivateHeader from '../../../../components/headers/PrivateHeader';

import mainStyles from '../../../../mainStyles';
import styles from './styles';

import Bad from '../../../../assets/svgs/Alert2';
import ArrowLeft from '../../../../assets/svgs/ArrowLeftSmallWhite';
import CheckBlue from '../../../../assets/svgs/CheckBlue';
import { useIsFocused } from "@react-navigation/native";
import { useConta } from "../../../../context/conta";
import api from "../../../../services/api";
import { ContasClienteBox } from "../components/ContasClienteBox";

const AnalisandoDocumento = ({ route, navigation }) => {
    const { image, tipo, id } = route.params;
    const { documentoFrenteId, setDocumentoFrenteId } = useConta();

    const isFocused = useIsFocused();

    const [status, setStatus] = useState('loading');
    const [errorMessage, setErrorMessage] = useState('');
    const [cliente, setCliente] = useState(null);

    useEffect(() => {
        if (isFocused) {
            upload();
        }
    }, [isFocused])

    const upload = () => {
        const data = new FormData();

        data.append('tipo', 'upload');
        data.append('id', id);

        if (tipo === 'frente') {
            data.append('documento_frente', {
                uri: image.uri,
                type: 'image/jpeg',
                name: `documento_frente.jpg`
            });
        }

        if (tipo === 'verso') {
            data.append('documento_frente_id', documentoFrenteId);
            data.append('documento_verso', {
                uri: image.uri,
                type: 'image/jpeg',
                name: `documento_verso.jpg`
            });
        }

        api.post('/crm/contas/criar', data)
            .then(res => {
                console.log('succesupload', res.data);
                setStatus('success');
                setCliente(res.data.conta);
            })
            .catch(error => {
                console.log('errorupload', error);
                if (error?.response?.data?.type === 'upload-verso') {
                    setDocumentoFrenteId(error.response.data.documento_frente_id);
                    navigation.navigate('ContasDigitalizarDocumento', { image, id });
                } else {
                    setStatus('error');
                    setErrorMessage(error?.response?.data?.message ?? '');
                }
            }).then(() => console.log('contas: chamou'));
    }

    return (
        <>
            <PrivateHeader title={'Contas'} />
            <ScrollView contentContainerStyle={{ flexGrow: 1, backgroundColor: "#FFF" }}>
                <TouchableOpacity
                    style={styles.buttonTitle}
                    onPress={() => navigation.goBack()}>
                    <View style={styles.icArrow}>
                        <ArrowLeft />
                    </View>
                    <Text style={styles.titlePag}>Analisando Documento</Text>
                </TouchableOpacity>
                <View style={mainStyles.wrapperRegister}>
                    {status === 'loading' &&
                        <View>
                            <View style={[mainStyles.container, styles.container]}>
                                <Text style={[mainStyles.label, { textAlign: "center", marginTop: 20 }]}>Aguarde...</Text>
                                <Text style={[mainStyles.label, { textAlign: "center", color: "#979797" }]}>Estamos avaliando a foto</Text>
                                <View style={styles.divider}></View>
                                <View style={{ flexDirection: "row", justifyContent: "center" }}>
                                    <Image
                                        style={styles.gif}
                                        source={require('../../../../assets/loadingChatTransparent.gif')}
                                    />
                                </View>
                            </View>
                        </View>
                    }
                    {status === 'error' &&
                        <View>
                            <View style={[mainStyles.container, styles.container]}>
                                <View style={styles.divider}></View>
                                <View style={{ flexDirection: "row", justifyContent: "center", marginBottom: 15 }}>
                                    <Bad />
                                </View>
                                <Text style={[mainStyles.label, { textAlign: "center" }]}>{errorMessage}</Text>
                                {/* <Text style={styles.errorMessage}>{errorMessage}</Text> */}
                                <View style={styles.divider}></View>
                                <View style={{ flexDirection: "column", alignItems: "center" }}>
                                    <TouchableOpacity
                                        style={[
                                            mainStyles.btnOutlineBlueDark,
                                            { height: 50 },
                                        ]}
                                        onPress={() => navigation.goBack()}>
                                        <Text style={mainStyles.btnTextOutlineBlueDark}>TENTAR NOVAMENTE</Text>
                                    </TouchableOpacity>
                                    <TouchableOpacity
                                        style={[
                                            mainStyles.btnOutlineBlueDark,
                                            { height: 50 },
                                        ]}
                                        onPress={() => navigation.goBack()}>
                                        <Text style={mainStyles.btnTextOutlineBlueDark}>PREENCHER MANUALMENTE</Text>
                                    </TouchableOpacity>
                                </View>
                            </View>
                        </View>
                    }
                    {status === 'success' &&
                        <View>
                            <View style={[mainStyles.container, styles.container]}>
                                <View style={styles.divider}></View>
                                <View style={{ flexDirection: "row", justifyContent: "center", marginBottom: 15 }}>
                                    <CheckBlue />
                                </View>
                                <Text style={[mainStyles.label, { textAlign: "center" }]}>{`Documento digitalizado\ncom sucesso`}</Text>
                                <View style={styles.divider}></View>
                                <ContasClienteBox cliente={cliente} />
                                <View style={styles.divider}></View>
                                <View style={{ flexDirection: "column", alignItems: "center" }}>
                                    <TouchableOpacity
                                        style={[
                                            mainStyles.btnOutlineBlueDark,
                                            { height: 50 },
                                        ]}
                                        onPress={() => navigation.navigate('ContasEmPreenchimento', {
                                            id: cliente.id, progresso: cliente.conta.progresso,
                                            porcentagem: cliente.conta.porcentagem, preenchidos: cliente.conta.preenchidos
                                        })}>
                                        <Text style={mainStyles.btnTextOutlineBlueDark}>IR PARA A CONTA</Text>
                                    </TouchableOpacity>
                                </View>
                            </View>
                        </View>
                    }
                </View>
            </ScrollView>
        </>
    );
}

export default AnalisandoDocumento;