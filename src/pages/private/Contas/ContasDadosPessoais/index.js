import React, { useState, useRef, useEffect } from 'react';
import { Platform, Text, TouchableOpacity, View, Image, Alert, ActivityIndicator, TextInput, PermissionsAndroid, KeyboardAvoidingView, ScrollView, Keyboard, FlatList } from 'react-native';

import PrivateHeader from '../../../../components/headers/PrivateHeader';
import Select from '../../../../components/Select';

import { cpfMask, phoneMask, dateMask } from '../../../../useful/masks';

import mainStyles from '../../../../mainStyles';
import styles from './styles';

import ArrowLeft from '../../../../assets/svgs/ArrowLeftSmallWhite';
import User from '../../../../assets/svgs/Users3';
import LightboxSearch from '../../../../components/LightboxSearch';
import { useIsFocused } from "@react-navigation/native";
import api from "../../../../services/api";
import moment from "moment";
import { createSelectOptions } from "../helpers/functions";
import SelectButton from "../../../../components/SelectButton";
import { convertDateToAmerican } from "../../../../useful/conversions";
import { ContasClienteBox } from "../components/ContasClienteBox";
import ContasCompartilharBox from "../components/ContasCompartilharBox";

const DadosPessoais = ({route, navigation}) => {
    const inputRefs = useRef([]);

    const { id, conta } = route.params;

    const editable = conta.itens.autorizacoes.pendencias > 0;

    const isFocused = useIsFocused();

    const [loading, setLoading] = useState(false);

    const [cliente, setCliente] = useState(null);

    const [inNome, setInNome] = useState('');
    const [inSobrenome, setInSobrenome] = useState('');
    const [inCelular, setInCelular] = useState('');
    const [inEmail, setInEmail] = useState('');
    const [inCpf, setInCpf] = useState('');
    const [inDataNascimento, setInDataNascimento] = useState('');
    const [inSexo, setInSexo] = useState('');
    const [inEstadoCivil, setInEstadoCivil] = useState('');
    const [inRegime, setInRegime] = useState('');
    const [inProfissao, setInProfissao] = useState('');
    const [inOutraProfissao, setInOutraProfissao] = useState('');

    const [showModalProfissao, setShowModalProfissao] = useState(false);

    const [estadoCivilOptions, setEstadoCivilOptions] = useState([]);
    const [profissaoOptions, setProfissaoOptions] = useState([]);
    const [regimeBensOptions, setRegimeBensOptions] = useState([]);
    const [sexoOptions, setSexoOptions] = useState([]);

    useEffect(() => {
        if(isFocused){
            getDadosPessoais();
        }
    }, [isFocused]);

    const getDadosPessoais = () => {
        setLoading(true);
        
        api.get(`/crm/contas/${id}/dados-pessoais`).then(res => {
            const { itens, selects } = res.data;
            console.log(res);
            setCliente(res.data.cliente);

            setInNome(itens.Nome ?? '');
            setInSobrenome(itens.Sobrenome ?? '');
            setInCelular(itens.Celular ?? '');
            setInEmail(itens.Email ?? '');
            setInCpf(cpfMask(itens.cpf_cnpj ?? ''));
            setInDataNascimento(itens.DataNascimento ? moment(itens.DataNascimento).format('DD/MM/YYYY') : '');
            setInSexo(itens.Sexo ?? '');
            setInEstadoCivil(itens.EstadoCivil ?? '');
            setInProfissao(itens.Profissao ?? '');
            setInOutraProfissao(itens.ProfissaoOutra ?? '');
            setInRegime(itens.RegimeBens ?? '');

            setEstadoCivilOptions(createSelectOptions(selects.EstadoCivil));
            setProfissaoOptions(createSelectOptions(selects.Profissao));
            setRegimeBensOptions(createSelectOptions(selects.RegimeBens));
            setSexoOptions(createSelectOptions(selects.Sexo));
        }).catch(error => {
            Alert.alert('Erro ao obter dados pessoais');
            console.log(error);
        }).then(() => setLoading(false));
    }

    const update = () => {
        setLoading(true);

        api.put(`/crm/contas/${id}/dados-pessoais`, {
            Nome: inNome,
            Sobrenome: inSobrenome,
            Celular: inCelular,
            Email: inEmail,
            cpf_cnpj: inCpf,
            DataNascimento: inDataNascimento ? convertDateToAmerican(inDataNascimento) : null,
            Sexo: inSexo,
            EstadoCivil: inEstadoCivil,
            Profissao: inProfissao,
            RegimeBens: inRegime,
            ProfissaoOutra: inOutraProfissao
        }).then(res => {
            navigation.goBack();
        }).catch(error => {
            let errors = error?.response?.data?.errors ?? null;
            let errorText = '';
            if(errors){
                Object.keys(errors).map(key => {
                    errors[key].map(error => {
                        errorText += `- ${error}\n`;
                    });
                });
            }
            Alert.alert('Não foi possível salvar', errorText);
            console.log('error dados pessoais', error);
        }).then(() => setLoading(false));
    }

    const focusNextField = (index) => {
        if (inputRefs.current[index + 1]) {
          inputRefs.current[index + 1].focus();
        }
    };

    return (
        <View style={{ flex: 1 }}>
            <View style={mainStyles.wrapper}>
                <PrivateHeader title={'Contas'} />
                <TouchableOpacity
                    style={styles.buttonTitle}
                    onPress={() => navigation.goBack()}>
                    <View style={styles.icArrow}>
                        <ArrowLeft />
                    </View>
                    <Text style={styles.titlePag}>Dados pessoais</Text>
                </TouchableOpacity>
                <KeyboardAvoidingView style={{ flex: 1 }} behavior="padding">
                    <ScrollView keyboardShouldPersistTaps='handled'>
                        {loading && 
                            <View style={{flex: 1, marginTop: 50, justifyContent: "center", alignItems: "center"}}>
                                <ActivityIndicator size="large" color="#00467F" />
                            </View>
                        }
                        {!loading && 
                            <>
                                <View style={mainStyles.wrapperRegister}>
                                    <View>
                                        <View style={[mainStyles.container, styles.container]}>
                                            <ContasClienteBox cliente={cliente} />
                                            <View style={styles.divider}></View>
                                            <View>
                                                <Text style={mainStyles.label}>NOME:</Text>                                
                                                <TextInput
                                                    returnKeyType='next'
                                                    ref={el => inputRefs.current[0] = el}
                                                    onSubmitEditing={() => focusNextField(0)}
                                                    blurOnSubmit={false}
                                                    editable={editable}
                                                    underlineColorAndroid="transparent"  
                                                    style={[mainStyles.inputText, editable ? null : mainStyles.inputTextDisabled]}
                                                    value={inNome}
                                                    onChangeText={text => setInNome(text)}
                                                    autoCapitalize="words"
                                                />
                                            </View>
                                            <View>
                                                <Text style={mainStyles.label}>SOBRENOME:</Text>                                
                                                <TextInput
                                                    returnKeyType='next'
                                                    ref={el => inputRefs.current[1] = el}
                                                    onSubmitEditing={() => focusNextField(1)}
                                                    blurOnSubmit={false}
                                                    editable={editable}
                                                    underlineColorAndroid="transparent"  
                                                    style={[mainStyles.inputText, editable ? null : mainStyles.inputTextDisabled]}
                                                    value={inSobrenome}
                                                    onChangeText={text => setInSobrenome(text)}
                                                    autoCapitalize="words"
                                                />
                                            </View>
                                            <View>
                                                <Text style={[mainStyles.label]}>TELEFONE:</Text>
                                                <TextInput
                                                    returnKeyType='next'
                                                    ref={el => inputRefs.current[2] = el}
                                                    onSubmitEditing={() => focusNextField(2)}
                                                    blurOnSubmit={false}
                                                    editable={editable}
                                                    underlineColorAndroid="transparent"  
                                                    style={[mainStyles.inputText, editable ? null : mainStyles.inputTextDisabled]}
                                                    keyboardType="phone-pad"
                                                    value={inCelular}
                                                    onChangeText={text => setInCelular( phoneMask(text) )}
                                                />
                                            </View>
                                            <View>
                                                <Text style={[mainStyles.label]}>E-MAIL:</Text>
                                                <TextInput
                                                    returnKeyType='next'
                                                    ref={el => inputRefs.current[3] = el}
                                                    onSubmitEditing={() => focusNextField(3)}
                                                    blurOnSubmit={false}
                                                    editable={editable}
                                                    autoCapitalize="none"  
                                                    underlineColorAndroid="transparent"  
                                                    style={[mainStyles.inputText, editable ? null : mainStyles.inputTextDisabled]}
                                                    keyboardType="email-address"
                                                    value={inEmail}
                                                    onChangeText={text => setInEmail(text.trim())}
                                                />
                                            </View>
                                            <View>
                                                <Text style={mainStyles.label}>CPF:</Text>
                                                <TextInput
                                                    returnKeyType='next'
                                                    ref={el => inputRefs.current[4] = el}
                                                    onSubmitEditing={() => focusNextField(4)}
                                                    blurOnSubmit={false}
                                                    editable={editable}
                                                    underlineColorAndroid="transparent"  
                                                    style={[mainStyles.inputText, editable ? null : mainStyles.inputTextDisabled]}
                                                    value={inCpf}
                                                    onChangeText={text => setInCpf( cpfMask(text) )}
                                                    keyboardType="phone-pad"
                                                />
                                            </View>
                                            <View>
                                                <Text style={mainStyles.label}>DATA DE NASCIMENTO</Text>                                
                                                <TextInput  
                                                    returnKeyType='done'
                                                    ref={el => inputRefs.current[5] = el}
                                                    editable={editable}
                                                    underlineColorAndroid="transparent"  
                                                    style={[mainStyles.inputText, editable ? null : mainStyles.inputTextDisabled]}
                                                    value={inDataNascimento}
                                                    onChangeText={value => setInDataNascimento( dateMask(value))}
                                                    placeholder="dd/mm/aaaa"
                                                    keyboardType="phone-pad"
                                                />
                                            </View>
                                            <View>
                                                <Text style={mainStyles.label}>SEXO:</Text>                                
                                                <Select 
                                                    disabled={!editable}
                                                    removeBorder={true}
                                                    placeholder="SELECIONE"
                                                    options={sexoOptions} 
                                                    value={inSexo} 
                                                    onChange={setInSexo} 
                                                />
                                            </View>
                                            <View>
                                                <Text style={mainStyles.label}>ESTADO CIVIL:</Text>                                
                                                <Select 
                                                    disabled={!editable}
                                                    removeBorder={true}
                                                    placeholder="SELECIONE"
                                                    options={estadoCivilOptions} 
                                                    value={inEstadoCivil} 
                                                    onChange={setInEstadoCivil} 
                                                />
                                            </View>
                                            {inEstadoCivil == 'Casado (a)' &&
                                                <View>
                                                    <Text style={mainStyles.label}>REGIME DE BENS:</Text>                                
                                                    <Select 
                                                        disabled={!editable}
                                                        removeBorder={true}
                                                        placeholder="SELECIONE"
                                                        options={regimeBensOptions} 
                                                        value={inRegime} 
                                                        onChange={setInRegime} 
                                                    />
                                                </View>
                                            }
                                            <View>
                                                <Text style={mainStyles.label}>PROFISSÃO:</Text> 
                                                <SelectButton 
                                                    onPress={() => setShowModalProfissao(true)}
                                                    placeholder="SELECIONE"
                                                    value={inProfissao}
                                                    disabled={!editable}
                                                />
                                            </View>
                                            {inProfissao === 'Outra profissão' &&
                                                <View>
                                                    <Text style={mainStyles.label}>OUTRA PROFISSÃO:</Text>                                
                                                    <TextInput
                                                        underlineColorAndroid="transparent"  
                                                        style={[mainStyles.inputText, editable ? null : mainStyles.inputTextDisabled]}
                                                        value={inOutraProfissao}
                                                        onChangeText={text => setInOutraProfissao(text)}
                                                        autoCapitalize="words"
                                                    />
                                                </View>
                                            }
                                            {!editable &&
                                                <View style={{ marginBottom: 40 }}>
                                                    <View style={styles.divider}></View>
                                                    <ContasCompartilharBox conta={conta} />
                                                </View>
                                            }
                                        </View>
                                    </View>
                                </View>
                                <View style={[mainStyles.container, styles.contentButtons]}>
                                    <TouchableOpacity
                                        disabled={loading}
                                        style={[mainStyles.btnBlueNew, styles.buttonLarge]}
                                        onPress={editable ? update : () => navigation.goBack()}>
                                        <Text style={mainStyles.btnTextBlueNew}>{loading ? 'CARREGANDO' : (editable ? 'SALVAR' : 'VOLTAR')}</Text>
                                    </TouchableOpacity>
                                </View>
                            </>
                        }
                        
                    </ScrollView>
                </KeyboardAvoidingView>
            </View>
            {showModalProfissao && (
                <LightboxSearch
                    close={() => setShowModalProfissao(false)}
                    title="Profissão"
                    options={profissaoOptions}
                    value={inProfissao}
                    setValue={setInProfissao}
                />
            )}
        </View>
    );
}

export default DadosPessoais;