import { useIsFocused } from '@react-navigation/native';
import React, { useEffect, useState } from 'react';
import {
  ActivityIndicator,
  Alert,
  Image,
  Text,
  TouchableOpacity,
  View,
  TextInput,
  Switch
} from 'react-native';

import PrivateHeader from '../../../../components/headers/PrivateHeader';

import ArrowLeft from '../../../../assets/svgs/ArrowLeftSmallWhite';
import Edit from '../../../../assets/svgs/Edit';

import { ScrollView } from 'react-native-gesture-handler';
import ArrowRightGray from '../../../../assets/svgs/ArrowRight';
import Less2 from '../../../../assets/svgs/Less2';
import More from '../../../../assets/svgs/More';
import mainStyles from '../../../../mainStyles';
import styles from './styles';
import api from "../../../../services/api";
import moment from "moment";
import colors from "../helpers/colors";
import ProgressBar from '../../../../components/ProgressBar';


const ContasMain = ({ navigation }) => {

  const [search, setSearch] = useState('');
  const isFocused = useIsFocused();
  const [contas, setContas] = useState([]);
  const [filteredContas, setFilteredContas] = useState([]);
  const [isComplete, setIsComplete] = useState(true);

  const [loadingContas, setLoadingContas] = useState(true);

  const toggleSwitch = () => {
    setIsComplete(previousState => !previousState);
    filterContas(!isComplete, search);
  };

  useEffect(() => {
    if (isFocused) {
      getContas();
    }
  }, [isFocused]);

  useEffect(() => {
    filterContas(isComplete, search);
  }, [contas]);

  const filterContas = (includeComplete, searchTerm) => {
    console.log(searchTerm)
    let toFilteredContas = [];
    contas.map(conta => {
      let nome = conta.nome.toLowerCase();
      let term = search.toLowerCase().trim();
      if (nome.indexOf(term) > -1) {
        if (includeComplete || conta.porcentagem !== "100%") {
          toFilteredContas.push(conta);
        }
      }
    });

    toFilteredContas.sort((contaA, contaB) => {
      return contaB.progresso - contaA.progresso;
    });

    setFilteredContas(toFilteredContas);
  };

  const handleSearchChange = value => {
    setSearch(value);
    filterContas(isComplete, value);
  };

  const getContas = () => {
    setLoadingContas(true);

    api.get('/crm/contas/').then(res => {
      setContas(res.data.data);
      console.log(res.data);
    }).catch(error => {
      Alert.alert('Não foi possível obter as contas');
      console.log(error);
    }).then(() => setLoadingContas(false));
  }


  return (
    <View style={mainStyles.wrapper}>
      <PrivateHeader title={'Contas'} />
      <TouchableOpacity
        style={styles.buttonTitle}
        onPress={() => navigation.goBack()}>
        <View style={styles.icArrow}>
          <ArrowLeft />
        </View>
        <Text style={styles.titlePag}>FICHA DE CADASTRO DIGITAL</Text>
      </TouchableOpacity>
      <View style={[mainStyles.privateContainer, styles.searchContent]}>
        <View style={styles.contentButtons}>
          <TextInput
            placeholderTextColor="#BDBDBD"
            placeholder="Busque pelo nome"
            underlineColorAndroid="transparent"
            style={[mainStyles.inputText, styles.input]}
            value={search}
            onChangeText={value => setSearch(value)}
            autoCapitalize="words"
          />
          <TouchableOpacity style={[mainStyles.btnBlueNew, styles.button]} onPress={() => filterContas(isComplete, search)}>
            <Text style={mainStyles.btnTextBlueNew}>OK</Text>
          </TouchableOpacity>
        </View>
      </View>
      <View style={[mainStyles.privateContainer, styles.switchContainer]}>
        <Text style={styles.switchLabel}>Exibir fichas completas</Text>
        <View style={styles.switchSpacer} />
        <Switch
          trackColor={{ false: "#767577", true: "#1B9C20" }}
          thumbColor={isComplete ? "#fff" : "#f4f3f4"}
          ios_backgroundColor="#3e3e3e"
          onValueChange={toggleSwitch}
          value={isComplete}
        />
      </View>
      <ScrollView>
        <View style={mainStyles.privateContainer}>
          {loadingContas &&
            <Text style={{ marginTop: 25 }}>Buscando contas...</Text>
          }
          {!loadingContas && (
            <>
              {loadingContas && (
                <Text style={{ marginVertical: 20 }}>Carregando contas...</Text>
              )}
              {!loadingContas && filteredContas.map((conta, index) => (
                <View key={conta.id} style={{ marginTop: index === 0 ? 15 : 0 }}>
                  <TouchableOpacity
                    style={styles.boxOption}
                    onPress={() => navigation.navigate('ContasEmPreenchimento', { id: conta.id, progresso: conta.progresso, porcentagem: conta.porcentagem, preenchidos: conta.preenchidos })}
                  >
                    <View
                      style={[styles.totalIcon, { backgroundColor: '#F2F2F2' }]}>
                      <Edit style={styles.icEdit} />
                    </View>
                    <View
                      style={[styles.totalInfos, { backgroundColor: '#FFF' }]}>
                      <View style={{ flex: 1 }}>
                        <Text style={[styles.titleBox, { textTransform: 'none' }]}>
                          {conta.nome}
                        </Text>
                        <Text style={styles.subtitleBox}>
                          Criada em {moment(conta.criada_em).format('DD/MM/YYYY')}
                        </Text>
                        <ProgressBar
                          progress={conta.progresso * 100}
                        />
                        <Text style={[styles.status, { color: colors[conta.info_status] }]}>
                          {conta.info_texto}
                        </Text>
                      </View>
                      <ArrowRightGray style={styles.icArrowRight} />
                    </View>
                  </TouchableOpacity>
                  <View style={styles.divider} />
                </View>
              ))}
            </>
          )}
        </View>
      </ScrollView>
      <View style={styles.contentBottom}>
        <View style={[mainStyles.container, styles.contentButtons]}>
          <TouchableOpacity
            style={[mainStyles.btnBlueNew, styles.buttonLarge]}
            onPress={() => navigation.navigate('NovaFicha')}>
            <Text style={mainStyles.btnTextBlueNew}>NOVA FICHA</Text>
          </TouchableOpacity>
        </View>
      </View>
    </View>
  );
};

export default ContasMain;
