import React, { useEffect, useState } from 'react';
import { TextInput, Text, View, TouchableOpacity, ActivityIndicator, SafeAreaView } from 'react-native';
import { useIsFocused, useNavigation } from '@react-navigation/native';

import Users3 from '../../../../assets/svgs/Users3';
import Whats2 from '../../../../assets/svgs/Whats2';
import Image2 from '../../../../assets/svgs/Image';
import YT from '../../../../assets/svgs/YT';
import PDF from '../../../../assets/svgs/PDF';
import XLS from '../../../../assets/svgs/XLS';

import PrivateHeader from '../../../../components/headers/PrivateHeader';

import FileDetails from '../../../../components/FileDetails';

import styles from './styles';
import mainStyles from '../../../../mainStyles';
import { ScrollView } from 'react-native-gesture-handler';
import api from '../../../../services/api';
import { useAuth } from '../../../../context/auth';

const FilesAboutProduct = ({ navigation }) => {
    const { user } = useAuth();
    const { updateHasNotification } = useAuth();
    const isFocused = useIsFocused();

    const [loading, setLoading] = useState(true);
    const [notifications, setNotifications] = useState([]);

    const [showModalFile, setShowModalFile] = useState(false);
    
    useEffect(() => {
        if(isFocused){
            getNotifications();
        }

        return () => {
            setNotifications([]);
        }
    }, [isFocused]);

    const getNotifications = () => {
        api.get(`notificacoes`).then(res => {
            setNotifications(res.data.notificacoes);
            updateHasNotification(false);
        }).catch(error => {
            console.log(error)
        }).then(() => setLoading(false));
    }

    return (
        <>
        {showModalFile &&
            <FileDetails
                close={() => setShowModalFile(false)}
            />
        }
        <View style={mainStyles.wrapper}>
            <PrivateHeader title={`Arquivos`} />
            <View style={styles.container}>
                <View style={styles.rowOptions}>
                    <View style={styles.contentOption}>
                        <Text style={styles.textStatusBold}>Produtos > NOME DO EMPREENDIMENTO</Text>
                    </View>
                </View>
            </View>
            <ScrollView showsVerticalScrollIndicator={false}>
                <View style={styles.container}>
                    <View style={styles.boxPadding}>
                        <View style={[styles.boxFlex, styles.boxOffice]}>
                            <View style={styles.boxFlexWithText}>
                                <View style={[styles.boxIconWidth, styles.boxIconWidthMargin]}>
                                    <Users3 />
                                </View>
                                <View>
                                    <Text style={styles.textOffice}>Gerente de Produto</Text>
                                    <Text style={styles.textNameOffice}>NOME DO GERENTE DE PRODUTO</Text>
                                </View>
                            </View>
                            <TouchableOpacity>
                                <Whats2 />
                            </TouchableOpacity>
                        </View>
                        <View style={[styles.boxFlex, styles.boxOffice]}>
                            <View style={styles.boxFlexWithText}>
                                <View style={[styles.boxIconWidth, styles.boxIconWidthMargin]}>
                                    <Users3 />
                                </View>
                                <View>
                                    <Text style={styles.textOffice}>Coordenador</Text>
                                    <Text style={styles.textNameOffice}>NOME DO COORDENADOR</Text>
                                </View>
                            </View>
                            <TouchableOpacity>
                                <Whats2 />
                            </TouchableOpacity>
                        </View>
                    </View>
                    <TouchableOpacity onPress={() => setShowModalFile(true)}>
                        <View style={styles.boxFlex}>
                            <View style={styles.boxFlexWithText}>
                                <View style={styles.boxIconWidth}>
                                    <Image2 />
                                </View>  
                                <Text style={styles.textBoxFlex}>8192_Cury_SP_Campanha100_Digital_AnaliseDeCredito_EmailMarketing.jpg</Text>
                            </View>
                        </View>
                    </TouchableOpacity>
                    <TouchableOpacity onPress={() => setShowModalFile(true)}>
                        <View style={styles.boxFlex}>
                            <View style={styles.boxFlexWithText}>
                                <View style={styles.boxIconWidth}>
                                    <YT />
                                </View>  
                                <Text style={styles.textBoxFlex}>8192_Cury_SP_Campanha100_Digital_AnaliseDeCredito.mp4</Text>
                            </View>
                        </View>
                    </TouchableOpacity>
                    <TouchableOpacity onPress={() => setShowModalFile(true)}>
                        <View style={styles.boxFlex}>
                            <View style={styles.boxFlexWithText}>
                                <View style={styles.boxIconWidth}>
                                    <PDF />
                                </View>  
                                <Text style={styles.textBoxFlex}>8192_Cury_SP_Campanha100_Digital_Analise.pdf</Text>
                            </View>
                        </View>
                    </TouchableOpacity>
                    <TouchableOpacity onPress={() => setShowModalFile(true)}>
                        <View style={styles.boxFlex}>
                            <View style={styles.boxFlexWithText}>
                                <View style={styles.boxIconWidth}>
                                    <XLS />
                                </View>  
                                <Text style={styles.textBoxFlex}>8192_Cury_SP_Campanha100_Digital_Analise.xls</Text>
                            </View>
                        </View>
                    </TouchableOpacity>
                </View>
            </ScrollView>
            {/* <View style={styles.contentBottom}>
                <View style={[mainStyles.container, styles.contentButtons]}>
                    <TouchableOpacity style={[mainStyles.btnOutlineBlue, styles.button]} onPress={() => navigation.navigate('PrivateMain')}>
                        <Text style={styles.btnTextOutlineBlue}>VOLTAR</Text>
                    </TouchableOpacity>
                </View>
            </View> */}
        </View>
        </>
    );
}

export default FilesAboutProduct;