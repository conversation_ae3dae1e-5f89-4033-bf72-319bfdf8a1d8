import { StyleSheet, Dimensions, Platform } from 'react-native';

const windowWidth = Dimensions.get('window').width;

const styles = StyleSheet.create({
    wrapper: {
        justifyContent: "space-between"
    },
    contentBottom: {
        marginTop: 40,      
        marginBottom: 50
    },
    contentButtons: {
        flexDirection: "row",
        justifyContent: "center"
    },
    button: {
        width: '80%'
    },
    container: {
        maxWidth: 340,
        marginLeft: ((windowWidth - 340) /2),
        marginTop: 10
    },
    contentOption: {
        marginTop: 20,
        paddingLeft: 20
    },
    textStatusBold: {
        fontFamily: "Roboto-Bold",
        color: "#00467F",
        fontSize: 13,
        letterSpacing: 1,
        marginTop: 5,
        marginBottom: 15
    },
    inputText: {
        backgroundColor: "#FFF",
        width: 125,
        paddingLeft: 13,
    },
    buttonCenterBlue: {
        width: 70,
        height: 50,
        marginTop: 10,
        borderRadius: 5,
        marginLeft: 15
    },
    textBtnCenterBlue: {
        fontSize: 20
    }, 
    rowCenter: {
        flexDirection: "row",
        justifyContent: "space-between",
        alignItems: 'center',
        marginBottom: 15,
        marginTop: 10
    },
    rowCenterMargin: {
        flexDirection: "row",
        alignItems: 'center',
        marginVertical: 20
    },
    optionTextBold: {
        fontFamily: "Roboto-Bold",
        fontSize: 12,
        letterSpacing: 0.7,
        color: "#4ea1cc",
        lineHeight: 18
    },
    boxFlex: {
        flexDirection: "row",
        justifyContent: "space-between",
        alignItems: "center",
        borderColor: "#4EA1CC",
        borderTopWidth: 1,
        paddingTop: 15,
        paddingBottom: 15,
        paddingLeft: 20
    },
    boxIconWidth: {
        width: 40,
        flexDirection: "row",
        justifyContent: "center"
    },
    boxFlexWithText: {
        flexDirection: "row",
        alignItems: "center"
    },
    textBoxFlex: {
        fontSize: 15,
        letterSpacing: 1,
        width: 240,
        marginLeft: 15,
        lineHeight: 21,
        color: "#828282"
    },
    btnTextOutlineBlue: {
        fontFamily: "Roboto-Bold",
        color: "#00467F",
        fontSize: 25,
        fontWeight: "bold",
        letterSpacing: 1,
        textAlign: "center"
    },
    boxPadding: {
        paddingLeft: 5,
        paddingRight: 5,
        borderTopColor: "#4EA1CC",
        borderTopWidth: 1,
        paddingTop: 20
    },
    boxOffice: {
        flexDirection: "row",
        alignItems: "center",
        borderWidth: 1,
        borderColor: "#00467F",
        borderRadius: 10,
        paddingRight: 20,
        paddingTop: 10,
        paddingBottom: 10,
        marginBottom: 20,
        minHeight: 80
    },
    textOffice: {
        fontFamily: "Roboto-Regular",
        width: 180,
        marginLeft: 10,
        color: "#00467F",
        letterSpacing: 1,
        fontSize: 14
    },
    textNameOffice: {
        fontFamily: "Roboto-Regular",
        width: 180,
        marginLeft: 10,
        color: "#828282",
        letterSpacing: 1,
        fontSize: 13,
        marginTop: 5
    },
    boxIconWidthMargin: {
        marginLeft: -7
    }
});

export default styles;