import { StyleSheet, Dimensions, Platform } from 'react-native';

const windowWidth = Dimensions.get('window').width;

const styles = StyleSheet.create({
    wrapper: {
        justifyContent: "space-between"
    },
    contentBottom: {
        marginTop: 40,      
        marginBottom: 50
    },
    contentButtons: {
        flexDirection: "row",
        justifyContent: "center"
    },
    button: {
        width: '80%'
    },
    container: {
        maxWidth: 340,
        marginLeft: ((windowWidth - 340) /2),
        marginTop: 10
    },
    contentOption: {
        marginTop: 20,
        paddingLeft: 20
    },
    textStatusBold: {
        fontFamily: "Roboto-Bold",
        color: "#00467F",
        fontSize: 13,
        letterSpacing: 1,
        marginTop: 5,
        marginBottom: 15
    },
    inputText: {
        backgroundColor: "#FFF",
        width: 125,
        paddingLeft: 13,
    },
    buttonCenterBlue: {
        width: 70,
        height: 50,
        marginTop: 10,
        borderRadius: 5,
        marginLeft: 15
    },
    textBtnCenterBlue: {
        fontSize: 20
    }, 
    rowCenter: {
        flexDirection: "row",
        justifyContent: "space-between",
        alignItems: 'center',
        marginBottom: 15,
        marginTop: 10
    },
    rowCenterMargin: {
        flexDirection: "row",
        alignItems: 'center',
        marginVertical: 20
    },
    optionTextBold: {
        fontFamily: "Roboto-Bold",
        fontSize: 12,
        letterSpacing: 0.7,
        color: "#4ea1cc",
        lineHeight: 18
    },
    boxFlex: {
        flexDirection: "row",
        justifyContent: "space-between",
        alignItems: "center",
        borderColor: "#4EA1CC",
        borderTopWidth: 1,
        paddingTop: 15,
        paddingBottom: 15,
        paddingLeft: 20
    },
    boxIconWidth: {
        width: 40,
        flexDirection: "row",
        justifyContent: "center"
    },
    boxFlexWithText: {
        flexDirection: "row",
        alignItems: "center"
    },
    textBoxFlex: {
        fontSize: 14,
        letterSpacing: 1,
        width: 240,
        marginLeft: 15,
        lineHeight: 17,
        color: "#828282"
    },
    btnTextOutlineBlue: {
        fontFamily: "Roboto-Bold",
        color: "#00467F",
        fontSize: 25,
        fontWeight: "bold",
        letterSpacing: 1,
        textAlign: "center"
    }
});

export default styles;