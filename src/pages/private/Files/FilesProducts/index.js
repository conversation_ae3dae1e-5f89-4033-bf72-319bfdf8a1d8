import React, { useEffect, useState } from 'react';
import { TextInput, Text, View, TouchableOpacity, ActivityIndicator, SafeAreaView } from 'react-native';
import { useIsFocused, useNavigation } from '@react-navigation/native';

import FolderProduct from '../../../../assets/svgs/FolderProduct';

import PrivateHeader from '../../../../components/headers/PrivateHeader';

import styles from './styles';
import mainStyles from '../../../../mainStyles';
import { ScrollView } from 'react-native-gesture-handler';
import api from '../../../../services/api';
import { useAuth } from '../../../../context/auth';

const FilesProducts = ({ navigation }) => {
    const { user } = useAuth();
    const { updateHasNotification } = useAuth();
    const isFocused = useIsFocused();

    const [loading, setLoading] = useState(true);
    const [notifications, setNotifications] = useState([]);
    
    useEffect(() => {
        if(isFocused){
            getNotifications();
        }

        return () => {
            setNotifications([]);
        }
    }, [isFocused]);

    const getNotifications = () => {
        api.get(`notificacoes`).then(res => {
            setNotifications(res.data.notificacoes);
            updateHasNotification(false);
        }).catch(error => {
            console.log(error)
        }).then(() => setLoading(false));
    }

    return (
        <View style={mainStyles.wrapper}>
            <PrivateHeader title={`Arquivos`} />
            <View style={styles.container}>
                <View style={styles.rowOptions}>
                    <View style={styles.contentOption}>
                        <Text style={styles.textStatusBold}>Arquivos > Produtos </Text>
                    </View>
                </View>
            </View>
            <ScrollView showsVerticalScrollIndicator={false}>
                <View style={styles.container}>
                    <TouchableOpacity onPress={() => navigation.navigate('FilesAboutProduct')}>
                        <View style={styles.boxFlex}>
                            <View style={styles.boxFlexWithText}>
                                <View style={styles.boxIconWidth}>
                                    <FolderProduct />
                                </View>  
                                <Text style={styles.textBoxFlex}>CURY MY MIGUEL NUNES</Text>
                            </View>
                        </View>
                    </TouchableOpacity>
                    <TouchableOpacity onPress={() => navigation.navigate('FilesAboutProduct')}>
                        <View style={styles.boxFlex}>
                            <View style={styles.boxFlexWithText}>
                                <View style={styles.boxIconWidth}>
                                    <FolderProduct />
                                </View>  
                                <Text style={styles.textBoxFlex}>CURY CIDADE MOOCA VILA PALERMO</Text>
                            </View>
                        </View>
                    </TouchableOpacity>
                    <TouchableOpacity onPress={() => navigation.navigate('FilesAboutProduct')}>
                        <View style={styles.boxFlex}>
                            <View style={styles.boxFlexWithText}>
                                <View style={styles.boxIconWidth}>
                                    <FolderProduct />
                                </View>  
                                <Text style={styles.textBoxFlex}>CURY CIDADE JAGUARÉ - VILA PINHEIROS</Text>
                            </View>
                        </View>
                    </TouchableOpacity>
                    <TouchableOpacity onPress={() => navigation.navigate('FilesAboutProduct')}>
                        <View style={styles.boxFlex}>
                            <View style={styles.boxFlexWithText}>
                                <View style={styles.boxIconWidth}>
                                    <FolderProduct />
                                </View>  
                                <Text style={styles.textBoxFlex}>DEZ CELESTE</Text>
                            </View>
                        </View>
                    </TouchableOpacity>
                    <TouchableOpacity onPress={() => navigation.navigate('FilesAboutProduct')}>
                        <View style={styles.boxFlex}>
                            <View style={styles.boxFlexWithText}>
                                <View style={styles.boxIconWidth}>
                                    <FolderProduct />
                                </View>  
                                <Text style={styles.textBoxFlex}>CURY CIDADE MOOCA VILA PALERMO</Text>
                            </View>
                        </View>
                    </TouchableOpacity>
                    <TouchableOpacity onPress={() => navigation.navigate('FilesAboutProduct')}>
                        <View style={styles.boxFlex}>
                            <View style={styles.boxFlexWithText}>
                                <View style={styles.boxIconWidth}>
                                    <FolderProduct />
                                </View>  
                                <Text style={styles.textBoxFlex}>CURY CIDADE JAGUARÉ - VILA PINHEIROS</Text>
                            </View>
                        </View>
                    </TouchableOpacity>
                    <TouchableOpacity onPress={() => navigation.navigate('FilesAboutProduct')}>
                        <View style={styles.boxFlex}>
                            <View style={styles.boxFlexWithText}>
                                <View style={styles.boxIconWidth}>
                                    <FolderProduct />
                                </View>  
                                <Text style={styles.textBoxFlex}>DEZ CELESTE</Text>
                            </View>
                        </View>
                    </TouchableOpacity>
                    <TouchableOpacity onPress={() => navigation.navigate('FilesAboutProduct')}>
                        <View style={styles.boxFlex}>
                            <View style={styles.boxFlexWithText}>
                                <View style={styles.boxIconWidth}>
                                    <FolderProduct />
                                </View>  
                                <Text style={styles.textBoxFlex}>CURY CIDADE MOOCA VILA PALERMO</Text>
                            </View>
                        </View>
                    </TouchableOpacity>
                </View>
            </ScrollView>
            {/* <View style={styles.contentBottom}>
                <View style={[mainStyles.container, styles.contentButtons]}>
                    <TouchableOpacity style={[mainStyles.btnOutlineBlue, styles.button]} onPress={() => navigation.navigate('PrivateMain')}>
                        <Text style={styles.btnTextOutlineBlue}>VOLTAR</Text>
                    </TouchableOpacity>
                </View>
            </View> */}
        </View>
    );
}

export default FilesProducts;