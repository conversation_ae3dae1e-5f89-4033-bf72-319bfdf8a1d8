import { StyleSheet, Dimensions, Platform } from 'react-native';

const windowWidth = Dimensions.get('window').width;

const styles = StyleSheet.create({
    wrapper: {
        justifyContent: "space-between"
    },
    contentBottom: {
        paddingTop: 20,
        paddingBottom: 40,
        shadowRadius: Platform.OS === 'ios' ? 2 : 16,
        shadowOffset: {
            width: 0,
            height: Platform.OS === 'ios' ? -2 : 12,
        },
        shadowColor: Platform.OS === 'ios' ? "#CCC" : "#000",
        elevation: Platform.OS === 'ios' ? 2 : 24,
        shadowOpacity: 1,
        backgroundColor: "#FFF",
        position: "absolute",
        bottom: 0,
        zIndex: 10
    },
    buttonLarge: {
        width: "100%"
    },
    contentButtons: {
        flexDirection: "row",
        justifyContent: "center"
    },
    button: {
        width: '80%'
    },
    container: {
        maxWidth: 370,
        marginLeft: ((windowWidth - 370) /2),
        marginTop: 20,
        paddingBottom: 120
    },
    contentOption: {
        marginTop: 20,
        paddingLeft: 20,
        flexDirection: 'row',
        flexWrap: 'wrap'
    },
    textStatusBold: {
        fontFamily: "Ubuntu-Regular",
        color: "#FFF",
        fontSize: 15,
        letterSpacing: 0.46
    },
    inputText: {
        backgroundColor: "#FFF",
        width: 125,
        paddingLeft: 13,
    },
    buttonCenterBlue: {
        width: 70,
        height: 50,
        marginTop: 10,
        borderRadius: 5,
        marginLeft: 15
    },
    textBtnCenterBlue: {
        fontSize: 20
    }, 
    rowCenter: {
        flexDirection: "row",
        justifyContent: "space-between",
        alignItems: 'center',
        marginBottom: 15,
        marginTop: 10
    },
    rowCenterMargin: {
        flexDirection: "row",
        alignItems: 'center',
        marginVertical: 20
    },
    optionTextBold: {
        fontFamily: "Roboto-Bold",
        fontSize: 12,
        letterSpacing: 0.7,
        color: "#4ea1cc",
        lineHeight: 18
    },
    boxFlex: {
        flexDirection: "row",
        alignItems: "center",
        marginVertical: 7,
        backgroundColor: "#F2F2F2",
        minHeight: 65
    },
    icArrowBtn: {
        transform: [{rotate: "-90deg"}]
    },
    boxIcon: {
        backgroundColor: "#90B0C0",
        minHeight: 65,
        justifyContent: "center",
        alignItems: "center",
        width: 55
    },
    boxIconWidth: {
        backgroundColor: "#90B0C0",
        minHeight: 65,
        justifyContent: "center",
        alignItems: "center",
        width: 55
    },
    boxFlexWithText: {
        flexDirection: "row",
        alignItems: "center"
    },
    textBoxFlex: {
        fontFamily: "Ubuntu-Regular",
        fontSize: 15,
        letterSpacing: 1,
        width: 275,
        marginLeft: 10,
        lineHeight: 18,
        color: "#00467F"
    },
    btnTextOutlineBlue: {
        fontFamily: "Roboto-Bold",
        color: "#00467F",
        fontSize: 25,
        fontWeight: "bold",
        letterSpacing: 1,
        textAlign: "center"
    },
    buttonDate: {
        backgroundColor: "#90B0C0",
        height: 58,
        width: '100%',
        alignItems: "center",
        justifyContent: "center"
    },
    icArrow: {
        position: "absolute",
        top: 23,
        left: 25
    },
});

export default styles;