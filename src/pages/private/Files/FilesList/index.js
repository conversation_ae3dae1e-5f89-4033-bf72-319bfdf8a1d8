import React, { useEffect, useState } from 'react';
import { TextInput, Text, View, TouchableOpacity, ActivityIndicator, SafeAreaView } from 'react-native';
import { useIsFocused, useNavigation } from '@react-navigation/native';

import ArrowRight from '../../../../assets/svgs/ArrowDownGrey';
import Folder from '../../../../assets/svgs/Folder';
import Image from '../../../../assets/svgs/Image';
import YT from '../../../../assets/svgs/YT';
import PDF from '../../../../assets/svgs/PDF';
import XLS from '../../../../assets/svgs/XLS';
import ArrowLeft from '../../../../assets/svgs/ArrowLeftSmallWhite';

import PrivateHeader from '../../../../components/headers/PrivateHeader';

import FileDetails from '../../../../components/FileDetails';

import styles from './styles';
import mainStyles from '../../../../mainStyles';
import { ScrollView } from 'react-native-gesture-handler';
import api from '../../../../services/api';
import { useAuth } from '../../../../context/auth';

const FilesList = ({ navigation }) => {
    const { user } = useAuth();
    const { updateHasNotification } = useAuth();
    const isFocused = useIsFocused();

    const [loading, setLoading] = useState(true);
    const [files, setFiles] = useState([]);

    const [fileToShow, setFileToShow] = useState(null);

    const [breadcrumbs, setBreadcrumbs] = useState([
        {
            id: null,
            filename: 'Arquivos'
        }
    ]);
    
    useEffect(() => {
        if(isFocused){
            getFiles();
        }

        return () => {
            setFiles([]);
        }
    }, [isFocused]);

    const getFiles = (id = null) => {
        console.log(id)
        setLoading(true);

        api.get(`arquivos${id === null ? '' : `/${id}`}`).then(res => {
            console.log(res.data);
            setFiles(res.data.arquivos);
            updateHasNotification(false);
        }).catch(error => {
            console.log(error.response)
        }).then(() => setLoading(false));
    }

    const openFolderByPosition = (position) => {
        let file = {...breadcrumbs[position]};
        getFiles(file.id);
        let toBreadcrumbs = [];
        breadcrumbs.map((breadcrumb, index) => {
            if(index <= position){
                toBreadcrumbs.push(breadcrumb);
            }
        });
        setBreadcrumbs(toBreadcrumbs);
    }

    const openFolderByFile = (file) => {
        setBreadcrumbs(prev => [...prev, file]);
        getFiles(file.id);
    }

    const back = () => {
        if (breadcrumbs.length === 1) {
            navigation.goBack();
        } else {
            openFolderByPosition(breadcrumbs.length - 2);
        }
    }

    const Breadcrumb = () => {
        return (
            <>
                {breadcrumbs.length > 1 &&
                    <TouchableOpacity style={styles.buttonDate} onPress={() => openFolderByPosition(breadcrumbs.length - 2)}>
                        <View style={styles.icArrow}>
                            <ArrowLeft />
                        </View>
                        <Text style={styles.textStatusBold}>{breadcrumbs[breadcrumbs.length-1].filename}</Text>
                    </TouchableOpacity>
                }
            </>
        );
    }

    const FileItem = ({file}) => {
        return (
            <View>
                <TouchableOpacity onPress={file.type === 'dir' ? () => openFolderByFile(file) : () => setFileToShow(file)}>
                    <View style={styles.boxFlex}>
                        <View style={styles.boxIcon}>
                            <FileIcon file={file} />
                        </View>
                        <Text style={styles.textBoxFlex}>{file.filename}.{file.extension}</Text>
                        <ArrowRight style={styles.icArrowBtn} />
                    </View>
                </TouchableOpacity>
            </View>
        );
    }
    
    const FileIcon = ({file}) => {
        const icons = {
            dir: <Folder style={{color: "#FFF"}} />,
            jpg: <Image style={{color: "#FFF"}} />,
            pdf: <PDF style={{color: "#FFF"}} />,
            xlsx: <XLS style={{color: "#FFF"}} />,
            mp4: <YT style={{color: "#FFF"}} />,
            undefined: <Image style={{color: "#FFF"}} />
        };
    
        let icon = <Image style={{color: "#FFF"}} />;
    
        if(file.type === 'dir'){
            icon = <Folder style={{color: "#FFF"}} />
        } else {
            icon = icons[file.extension] ? icons[file.extension] : icons.undefined;
        }
    
        return (
            <View style={styles.boxIconWidth}>
                {icon}
            </View> 
        );
    }

    return (
        <>
        {fileToShow !== null &&
            <FileDetails
                file={fileToShow}
                close={() => setFileToShow(null)}
            />
        }
        <View style={mainStyles.wrapper}>
            <PrivateHeader title={`Arquivos`} />
            <Breadcrumb />
            <ScrollView showsVerticalScrollIndicator={false}>
                <View style={styles.container}>
                    {!loading && files.map((file, index) => (
                        <FileItem key={index} file={file} />
                    ))}
                    {loading &&
                        <View style={{ flex: 1, justifyContent: "center", alignItems: "center", marginTop: 100 }}>
                            <ActivityIndicator size="large" color="#00467F" />
                        </View>
                    }
                </View>
            </ScrollView>
            <View style={styles.contentBottom}>
                <View style={[mainStyles.container, styles.contentButtons]}>
                    <TouchableOpacity style={[mainStyles.btnBlueNew, styles.buttonLarge]} onPress={back}>
                        <Text style={mainStyles.btnTextBlueNew}>VOLTAR</Text>
                    </TouchableOpacity>
                </View>
            </View>
        </View>
    </>
    );
}

export default FilesList;