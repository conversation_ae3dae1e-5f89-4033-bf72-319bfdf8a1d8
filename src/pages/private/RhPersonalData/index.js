import React, { useEffect, useState, useRef } from 'react';
import { Text, View, TouchableOpacity, ScrollView, ActivityIndicator, Keyboard, Alert, TextInput, KeyboardAvoidingView } from 'react-native';

import ArrowLeft from '../../../assets/svgs/ArrowLeftSmallWhite';

import Dash from 'react-native-dash';

import PrivateHeader from '../../../components/headers/PrivateHeader';

import styles from './styles';
import mainStyles from '../../../mainStyles';

import api from '../../../services/api';
import { useAuth } from '../../../context/auth';
import { dateMask, phoneMask } from '../../../useful/masks';
import { convertDateToAmerican, convertDateToBrazil } from '../../../useful/conversions';
import Select from '../../../components/Select';
import { validateDate } from '../../../useful/validate';

const sizesOptions = [
    { label: 'P', value: 'P' },
    { label: 'M', value: 'M' },
    { label: 'G', value: 'G' },
    { label: '2G', value: '2G' },
    { label: '3G', value: '3G' }
]

const RhPersonalData = ({navigation}) => {
    const { user } = useAuth();

    const [loading, setLoading] = useState(true);
    const [alert, setAlert] = useState(false);
    const [keyboardVisible, setKeyboardVisible] = useState(false);
    const scrollViewRef = useRef();

    const [inNome, setInNome] = useState('');
    const [inGerente, setInGerente] = useState('');
    const [inRegional, setInRegional] = useState('');
    const [inCanal, setInCanal] = useState('');
    const [inApelido, setInApelido] = useState('');
    const [inEmail, setInEmail] = useState('');
    const [inEmailVendas, setInEmailVendas] = useState('');
    const [inTelefone, setInTelefone] = useState('');
    const [inCpf, setInCpf] = useState('');
    const [inTamanhoCamisa, setInTamanhoCamisa] = useState('');
    const [inDataNascimento, setInDataNascimento] = useState('');


    const { signOut } = useAuth();

    useEffect(() => {
        getData();

        const keyboardDidShowListener = Keyboard.addListener('keyboardDidShow', () => {
            setKeyboardVisible(true);
          }
        );
        
        const keyboardDidHideListener = Keyboard.addListener( 'keyboardDidHide', () => {
              setKeyboardVisible(false); // or some other action
            }
        );
      
        return () => {
            keyboardDidHideListener.remove();
            keyboardDidShowListener.remove();
        };

    }, []);

    useEffect(() => {
        scroll();
    }, [keyboardVisible]);

    const scroll = () => {
        // scrollViewRef.current.scrollToEnd({ animated: true });
    }

    const getData = () => {
        api.get(`/rh/cadastro/dados-pessoais`).then(res => {
            let { dados } = res.data;
            console.log(dados, 'dadospessoais')
            setInNome(dados?.nome ?? '');
            setInGerente(dados?.gerente?.apelido ?? '');
            setInRegional(dados?.regional?.nome ?? '');
            setInCanal(dados?.canal ?? '');
            setInApelido(dados?.apelido ?? '');
            setInEmail(dados?.email ?? '');
            setInEmailVendas(dados?.email_cury_vendas ?? '');
            setInTelefone(dados?.telefone ?? '');
            setInCpf(dados?.cpf ?? '');
            setInTamanhoCamisa(dados?.camisa_tamanho ?? '');
            setInDataNascimento(dados?.data_nascimento ? convertDateToBrazil(dados.data_nascimento) : '');
        }).catch(error => {
            console.log(error);
            Alert.alert('Erro ao obter dados');
        }).then(() => {
            setLoading(false);
        });
    }

    const update = () => {
        const validation = validate();
        if(!validation) return;

        setLoading(true);

        let data = {
            nome: inNome,
            apelido: inApelido,
            email: inEmail,
            telefone: inTelefone,
            camisa_tamanho: inTamanhoCamisa
        }

        if(inDataNascimento !== ''){
            data.data_nascimento = convertDateToAmerican(inDataNascimento);
        }

        api.put(`/rh/cadastro/dados-pessoais`, data, {
            headers: {
                // 'content-type': 'application/x-www-form-urlencoded'
            }
        }).then(res => {
            Alert.alert(user.status_cadastro === 'reprovado' ? 'Dados salvos, caso tenha encerrado enviar para análise.' : 'Dados salvos e enviados para análise.');
            navigation.navigate('RhList')
        }).catch(err => {
            console.log(err.response);
            if(err?.response?.data?.errors){
                let errors = err.response.data.errors;
                let text = '';
                Object.keys(errors).map(error => {
                    text += `\n${errors[error][0]}`;
                });
                Alert.alert('Verifique os campos', text);
            } else {
                Alert.alert('Erro ao atualizar os dados.', 'Por favor, verifique os dados e tente novamente.');
            }
        }).then(() => {
            setLoading(false);
        });
    }

    const validate = () => {
        if(inDataNascimento.length > 0){
            if(!validateDate(inDataNascimento) ){
                Alert.alert('Verifique a data de nascimento', 'Por favor, verifique a data de nascimento.\nO formato deve ser:\n dd/mm/aaaa');
                return false;
            }
        }

        return true;
    }

    return (
        <KeyboardAvoidingView style={{ flex: 1 }} behavior="padding" >
            <PrivateHeader title={`Meu Perfil`} back={() => navigation.navigate('PrivateMain')} />
            <TouchableOpacity style={styles.shift} onPress={() => navigation.navigate('RhMyData')}>
                <View style={styles.icAbs}>
                    <ArrowLeft style={styles.icArrow} />
                </View>
                <Text style={[mainStyles.textWhite]}>DADOS PESSOAIS</Text>
            </TouchableOpacity>
            <ScrollView contentContainerStyle={{ flexGrow: 1 }} keyboardShouldPersistTaps="handled" showsVerticalScrollIndicator={false}>
                {loading && 
                    <View style={{flex: 1, justifyContent: "center", alignItems: "center"}}>
                        <ActivityIndicator size="large" color="#00467F" />
                    </View>
                }
                {!loading &&
                    <View style={mainStyles.containerRh}>
                        <View>
                            <Text style={mainStyles.label}>Regional:</Text>                                
                            <TextInput
                                underlineColorAndroid="transparent"  
                                style={mainStyles.inputReadOnly}
                                value={inRegional}
                                editable={false}
                            />
                            <Text style={mainStyles.label}>Gerente:</Text>                                
                            <TextInput
                                underlineColorAndroid="transparent"  
                                style={mainStyles.inputReadOnly}
                                value={inGerente}
                                editable={false}
                            />
                            <Text style={mainStyles.label}>Canal:</Text>                                
                            <TextInput
                                underlineColorAndroid="transparent"  
                                style={mainStyles.inputReadOnly}
                                value={inCanal}
                                editable={false}
                            />
                            <Text style={mainStyles.label}>Nome completo:</Text>                                
                            <TextInput
                                underlineColorAndroid="transparent"  
                                style={mainStyles.inputText}
                                value={inNome}
                                onChangeText={setInNome}
                                onFocus={() => scroll()}
                                autoCapitalize="words"
                            />
                            <Text style={mainStyles.label}>Apelido:</Text>                                
                            <TextInput
                                underlineColorAndroid="transparent"  
                                style={user.status_cadastro !== 'aprovado' ? mainStyles.inputText : mainStyles.inputReadOnly}
                                value={inApelido}
                                onChangeText={setInApelido}
                                onFocus={() => scroll()}
                                autoCapitalize="words"
                                editable={user.status_cadastro !== 'aprovado'}
                            />
                            <Text style={mainStyles.label}>E-mail de cadastro:</Text>                                
                            <TextInput
                                underlineColorAndroid="transparent"  
                                style={[mainStyles.inputText, mainStyles.inputLowercase]}
                                value={inEmail}
                                onChangeText={setInEmail}
                                onFocus={() => scroll()}
                                keyboardType="email-address"
                            />
                            {inEmailVendas !== "" &&
                            <>
                                <Text style={mainStyles.labelBlue}>E-MAIL CURY VENDAS</Text>                                
                                <TextInput
                                    underlineColorAndroid="transparent"  
                                    style={mainStyles.inputReadOnly}
                                    value={inEmailVendas}
                                    editable={false}
                                    />
                                </>
                            }
                            <Text style={mainStyles.label}>Telefone:</Text>                                
                            <TextInput
                                underlineColorAndroid="transparent"  
                                style={[mainStyles.inputText, mainStyles.inputLowercase]}
                                value={inTelefone}
                                onFocus={() => scroll()}
                                keyboardType="phone-pad"
                                onChangeText={text => setInTelefone( phoneMask(text) )}
                            />
                            <Text style={mainStyles.label}>CPF:</Text>                                
                            <TextInput
                                underlineColorAndroid="transparent"  
                                style={mainStyles.inputReadOnly}
                                value={inCpf}
                                editable={false}
                            />
                            <Text style={mainStyles.label}>Tamanho da camiseta:</Text>                                
                            <Select 
                                removeBorder={true}
                                placeholder="TAMANHO DA CAMISA"
                                options={sizesOptions} 
                                value={inTamanhoCamisa} 
                                onChange={setInTamanhoCamisa} 
                            />
                            <Text style={mainStyles.label}>Data de nascimento:</Text>                                
                            <TextInput  
                                underlineColorAndroid="transparent"  
                                style={mainStyles.inputText}
                                value={inDataNascimento}
                                onChangeText={value => setInDataNascimento( dateMask(value))}
                                onFocus={() => scroll()}
                                placeholder="DD/MM/AAAA"
                                keyboardType="phone-pad"
                            />
                        </View>                        
                    </View>
                }
            </ScrollView>
            <View style={styles.contentBottom}>
                <View style={[mainStyles.container, styles.contentButtons]}>              
                    <TouchableOpacity style={[mainStyles.btnOutlineBlue, mainStyles.buttonW48]} onPress={() => navigation.navigate('RhMyData')}>
                        <Text style={mainStyles.btnTextOutlineBlue}>VOLTAR</Text>
                    </TouchableOpacity>
                    <TouchableOpacity style={[mainStyles.btnBlueLight, styles.btnLogin, mainStyles.buttonW48]} onPress={update}>
                        <Text style={mainStyles.btnTextCenterBlueLight}>SALVAR</Text>
                    </TouchableOpacity>
                </View>
            </View>
        </KeyboardAvoidingView>
    );
}

export default RhPersonalData;