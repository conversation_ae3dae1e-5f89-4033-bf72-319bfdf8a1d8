import React, { useEffect, useState, useRef } from 'react';
import { Text, View, TouchableOpacity, ScrollView, ActivityIndicator, Image, Keyboard, TextInput, Alert, KeyboardAvoidingView } from 'react-native';
import * as ImagePicker from 'react-native-image-picker';
import ArrowLeft from '../../../assets/svgs/ArrowLeftSmallWhite';
import Camera from '../../../assets/svgs/Camera2';

import Dash from 'react-native-dash';

import PrivateHeader from '../../../components/headers/PrivateHeader';

import styles from './styles';
import mainStyles from '../../../mainStyles';

import api from '../../../services/api';
import { convertDateToAmerican, convertDateToBrazil } from '../../../useful/conversions';
import { useAuth } from '../../../context/auth';
import { dateMask } from '../../../useful/masks';
import { validateDate } from '../../../useful/validate';
import { requestCameraPermission } from "../../../useful/permissions";

const RhDocuments = ({navigation}) => {    
    const { user } = useAuth();

    const [loading, setLoading] = useState(true);

    const [inRg, setInRg] = useState('');
    
    const [inTipo, setInTipo] = useState('');

    const [inCreciNumero, setInCreciNumero] = useState('');
    const [inCreciValidade, setInCreciValidade] = useState('');

    const [inProtocoloEstagioNumero, setInProtocoloEstagioNumero] = useState('');
    const [inProtocoloEstagioValidade, setInProtocoloEstagioValidade] = useState('');


    const [imageDocFrentePath, setImageDocFrentePath] = useState('');
    const [imageDocFrenteId, setImageDocFrenteId] = useState('');
    const [imageDocVersoPath, setImageDocVersoPath] = useState('');
    const [imageDocVersoId, setImageDocVersoId] = useState('');

    const [imageCreciFrentePath, setImageCreciFrentePath] = useState('');
    const [imageCreciFrenteId, setImageCreciFrenteId] = useState('');
    const [imageCreciVersoPath, setImageCreciVersoPath] = useState('');
    const [imageCreciVersoId, setImageCreciVersoId] = useState('');

    const [imageProtocoloEstagioPath, setImageProtocoloEstagioPath] = useState('');
    const [imageProtocoloEstagioId, setImageProtocoloEstagioId] = useState('');

    useEffect(() => {
        getData();
    }, []);

    const getData = () => {
        api.get(`/rh/cadastro/documentos`).then(res => {
            let { dados } = res.data;
            console.log('dados', dados);
            setInTipo(dados.tipo_contrato);

            setInRg(dados.documento_numero);
            setImageDocFrentePath(dados?.documento_arquivo?.url ?? '');
            setImageDocFrenteId(dados?.documento_arquivo?.id ?? '');
            setImageDocVersoPath(dados?.documento_verso_arquivo?.url ?? '');
            setImageDocVersoId(dados?.documento_verso_arquivo?.id ?? '');

            if(dados.tipo_contrato === 'credenciado'){
                setInCreciNumero(dados.creci_numero);
                setInCreciValidade(convertDateToBrazil(dados.creci_validade));

                setImageCreciFrentePath(dados?.creci_arquivo?.url ?? '');
                setImageCreciFrenteId(dados?.creci_arquivo?.id ?? '');
                setImageCreciVersoPath(dados?.creci_verso_arquivo?.url ?? '');
                setImageCreciVersoId(dados?.creci_verso_arquivo?.id ?? '');
            }
            if(dados.tipo_contrato === 'estagiario'){
                setInProtocoloEstagioNumero(dados.protocolo_estagio_numero);
                setInProtocoloEstagioValidade(convertDateToBrazil(dados.protocolo_estagio_validade));

                setImageProtocoloEstagioPath(dados?.protocolo_estagio_arquivo?.url ?? '');
                setImageProtocoloEstagioId(dados?.protocolo_estagio_arquivo?.id ?? '');
            }
        }).catch(error => {
            console.log(error);
            Alert.alert('Erro ao obter dados');
        }).then(() => {
            setLoading(false);
        });
    }

    const update = async () => {
        const validation = validate();
        if(!validation) return;

        setLoading(true);


        let documento_id = imageDocFrenteId === '' ? await uploadImage('documento', imageDocFrentePath, setImageDocFrenteId) : imageDocFrenteId;
        let documento_verso_id = imageDocVersoId === '' ? await uploadImage('documento', imageDocVersoPath, setImageDocVersoId) : imageDocVersoId;

        let contrato_numero;
        let contrato_validade;
        let contrato_id;
        let contrato_verso_id;

        if(inTipo === 'credenciado'){
            contrato_numero = inCreciNumero;
            contrato_validade = convertDateToAmerican(inCreciValidade);
            contrato_id = imageCreciFrenteId === '' ? await uploadImage('creci', imageCreciFrentePath, setImageCreciFrenteId) : imageCreciFrenteId;
            contrato_verso_id = imageCreciVersoId === '' ? await uploadImage('creci', imageCreciVersoPath, setImageCreciVersoId) : imageCreciVersoId;
        }
        if(inTipo === 'estagiario'){
            contrato_numero = inProtocoloEstagioNumero;
            contrato_validade = convertDateToAmerican(inProtocoloEstagioValidade);
            contrato_id = imageProtocoloEstagioId === '' ? await uploadImage('protocolo_estagio', imageProtocoloEstagioPath, setImageProtocoloEstagioId) : imageProtocoloEstagioId;
            contrato_verso_id = null;
        }

        let data = {
            documento_numero: inRg,
            documento_id,
            documento_verso_id,
            contrato_numero,
            contrato_validade,
            contrato_id,
            contrato_verso_id
        };

        api.put(`/rh/cadastro/documentos`, data, {
            headers: {
                // 'content-type': 'application/x-www-form-urlencoded'
            }
        }).then(res => {
            Alert.alert(user.status_cadastro === 'reprovado' ? 'Dados salvos, caso tenha encerrado enviar para análise.' : 'Dados salvos e enviados para análise.');
            navigation.navigate('RhList')
        }).catch(err => {
            if(err?.response?.data?.errors){
                let errors = err.response.data.errors;
                let text = '';
                Object.keys(errors).map(error => {
                    text += `\n${errors[error][0]}`;
                });
                Alert.alert('Verifique os campos', text);
            } else {
                Alert.alert('Erro ao atualizar os dados.', 'Por favor, verifique os dados e tente novamente.');
            }
        }).then(() => {
            setLoading(false);
        });
    }

    const validate = () => {
        if(inCreciValidade.length > 0){
            if(!validateDate(inCreciValidade) ){
                Alert.alert('Verifique a data de validade', 'Por favor, verifique a data de validade.\nO formato deve ser:\n dd/mm/aaaa');
                return false;
            }
        }

        if(inProtocoloEstagioValidade.length > 0){
            if(!validateDate(inProtocoloEstagioValidade) ){
                Alert.alert('Verifique a data de validade', 'Por favor, verifique a data de validade.\nO formato deve ser:\n dd/mm/aaaa');
                return false;
            }
        }

        return true;
    }

    const uploadImage = async (name, path, updateId) => {
        const data = new FormData();

        data.append(name, {
            uri: path,
            type: 'image/jpeg',
            name: `${name}.jpg`
        });

        return await api.post('/cadastro/upload', data)
            .then(res => {
                let id = res.data.id;
                updateId(id)
                return id;
            })
            .catch(err =>{ 
                console.log(err)
                alert('Por favor, escolha uma imagem e tente novamente.');
                return '';
            });
    }

    return (
        <KeyboardAvoidingView style={{ flex: 1 }} behavior="padding" >
            <PrivateHeader title={`Meu Perfil`} back={() => navigation.navigate('PrivateMain')} />
            <TouchableOpacity style={styles.shift} onPress={() => navigation.navigate('RhMyData')}>
                <View style={styles.icAbs}>
                    <ArrowLeft style={styles.icArrow} />
                </View>
                <Text style={[mainStyles.textWhite]}>DOCUMENTOS</Text>
            </TouchableOpacity>
            <ScrollView contentContainerStyle={{ flexGrow: 1 }} keyboardShouldPersistTaps="handled" showsVerticalScrollIndicator={false}>
                {loading && 
                    <View style={{flex: 1, justifyContent: "center", alignItems: "center"}}>
                        <ActivityIndicator size="large" color="#00467F" />
                    </View>
                }
                {!loading &&
                    <View style={mainStyles.containerRh}>
                        {/* <Dash dashThickness={1} dashLength={3} dashColor="#DADADA" /> */}
                        <View>
                            <Text style={mainStyles.label}>RG OU CNH:</Text>                                
                            <TextInput
                                underlineColorAndroid="transparent"  
                                style={mainStyles.inputReadOnly}
                                value={inRg}
                                editable={false}
                            />
                        </View>
                        <View style={[styles.rowCenterMargin, mainStyles.rowUploads]}>
                            <BoxImage
                                path={imageDocFrentePath}
                                updatePath={setImageDocFrentePath}
                                updateId={setImageDocFrenteId}
                                label="Frente do RG ou CNH"
                                text={<Text>{`FOTO DO SEU\nDOCUMENTO\n`}<Text style={mainStyles.bold}>FRENTE</Text></Text>}
                            />
                            <BoxImage
                                path={imageDocVersoPath}
                                updatePath={setImageDocVersoPath}
                                updateId={setImageDocVersoId}
                                label="Verso do RG ou CNH"
                                text={<Text>{`FOTO DO SEU\nDOCUMENTO\n`}<Text style={mainStyles.bold}>VERSO</Text></Text>}
                            />
                        </View>
                        <View style={styles.divider}></View>
                        {inTipo === 'credenciado' &&
                            <>
                                <View>
                                    <Text style={mainStyles.label}>Nº do CRECI:</Text>                                
                                    <TextInput
                                        underlineColorAndroid="transparent"  
                                        style={[mainStyles.inputText, mainStyles.inputLowercase]}
                                        value={inCreciNumero}
                                        onChangeText={setInCreciNumero}
                                    />
                                    <Text style={mainStyles.label}>Validade:</Text>                                
                                    <TextInput
                                        underlineColorAndroid="transparent"  
                                        style={mainStyles.inputText}
                                        value={inCreciValidade}
                                        placeholder="dd/mm/aaaa"
                                        placeholderTextColor="#CCC"
                                        onChangeText={value => setInCreciValidade(dateMask(value))}
                                    />
                                </View>
                                {/* <Text style={[mainStyles.inputInfo, mainStyles.inputInfoDashed]}>O formato deve ser dd/mm/aaaa</Text> */}
                                <View style={[styles.rowCenterMargin, mainStyles.rowUploads]}>
                                    <BoxImage
                                        path={imageCreciFrentePath}
                                        updatePath={setImageCreciFrentePath}
                                        updateId={setImageCreciFrenteId}
                                        label="Frente do CRECI"
                                        text={<Text>{`FOTO DO\nSEU CRECI\n`}<Text style={mainStyles.bold}>FRENTE</Text></Text>}
                                    />
                                    <BoxImage
                                        path={imageCreciVersoPath}
                                        updatePath={setImageCreciVersoPath}
                                        updateId={setImageCreciVersoId}
                                        label="Verso do CRECI"
                                        text={<Text>{`FOTO DO\nSEU CRECI\n`}<Text style={mainStyles.bold}>VERSO</Text></Text>}
                                    />
                                </View>
                            </>
                        }
                        {inTipo === 'estagiario' &&
                            <>
                                <View>
                                    <Text style={mainStyles.label}>Nº do protocolo de estágio:</Text>                                
                                    <TextInput
                                        underlineColorAndroid="transparent"  
                                        style={[mainStyles.inputText, mainStyles.inputLowercase]}
                                        value={inProtocoloEstagioNumero}
                                        onChangeText={setInProtocoloEstagioNumero}
                                    />
                                    <Text style={mainStyles.label}>Validade:</Text>                                
                                    <TextInput
                                        underlineColorAndroid="transparent"  
                                        style={mainStyles.inputText}
                                        value={inProtocoloEstagioValidade}
                                        placeholder="dd/mm/aaaa"
                                        placeholderTextColor="#CCC"
                                        onChangeText={value => setInProtocoloEstagioValidade(dateMask(value))}
                                    />
                                </View>
                                {/* <Text style={[mainStyles.inputInfo, mainStyles.inputInfoDashed]}>O formato deve ser dd/mm/aaaa</Text> */}
                                <View style={[styles.rowCenterMargin, mainStyles.rowUploads]}>
                                    <BoxImage
                                        path={imageProtocoloEstagioPath}
                                        updatePath={setImageProtocoloEstagioPath}
                                        updateId={setImageProtocoloEstagioId}
                                        label="Foto do protocolo"
                                        text={<Text>{`FOTO DO\nSEU PROTOCOLO\nDE ESTÁGIO`}</Text>}
                                    />
                                </View>
                            </>
                        }
                    </View>
                }
            </ScrollView>
            <View style={styles.contentBottom}>
                <View style={[mainStyles.container, styles.contentButtons]}>              
                    <TouchableOpacity style={[mainStyles.btnOutlineBlue, mainStyles.buttonW48]} onPress={() => navigation.navigate('RhMyData')}>
                        <Text style={mainStyles.btnTextOutlineBlue}>VOLTAR</Text>
                    </TouchableOpacity>
                    <TouchableOpacity style={[mainStyles.btnBlueLight, styles.btnLogin, mainStyles.buttonW48]} onPress={update}>
                        <Text style={mainStyles.btnTextCenterBlueLight}>SALVAR</Text>
                    </TouchableOpacity>
                </View>
            </View>
        </KeyboardAvoidingView>
    );
}

export const BoxImage = ({path, updatePath, updateId, text, label}) => {
    const { token } = useAuth();

    const chooseImage = () => {
        Keyboard.dismiss();
        let alertProperties;
        if(Platform.OS === 'ios'){
            alertProperties = [
                { text: "Tirar foto", onPress: () => openCamera() },
                { text: "Galeria", onPress: () => openImageLibrary() },
                { text: "Fechar" }
            ];
        } else {
            alertProperties = [
                { text: "Fechar" },
                { text: "Galeria", onPress: () => openImageLibrary() },
                { text: "Tirar foto", onPress: () => openCamera() }
            ];
        }
        Alert.alert(
            "Enviar documento",
            "Como deseja enviar o documento?",
            alertProperties,
            { cancelable: false }
        );
    }

    const imageOptions = {
        mediaType: 'photo',
        includeBase64: false,
        maxHeight: 1024,
        maxWidth: 1024
    };

    const openCamera = async () => {
        const hasPermission = await requestCameraPermission();
        if(hasPermission){
            ImagePicker.launchCamera (imageOptions, res => {
                if(res.errorCode){
                    console.log(res);
                    Alert.alert('Erro', 'Câmera não disponível');
                    return;
                }
                if(res?.assets){
                    updateImagePath(res.assets[0].uri);
                }
            });
        }
    }

    const openImageLibrary = () => {
        ImagePicker.launchImageLibrary(imageOptions, res => {
            if(res?.assets){
                updateImagePath(res.assets[0].uri);
            }
        });
    }
    
    const updateImagePath = path => {
        updatePath(path);
        updateId('');
    }
    
    return (
        <>
        {path !== '' &&
            <View style={mainStyles.boxPreview2}>                
                {/* <Text style={mainStyles.labelImage}>{label}</Text> */}
                <View style={mainStyles.boxPreviewImageContent2}>
                    <Image 
                        source={{ 
                            uri: path,
                            headers: {
                                Authorization: `Bearer ${token}`
                            }
                        }}
                        style={mainStyles.previewCol}
                    />
                </View>
                <TouchableOpacity onPress={chooseImage}>
                    <Text style={mainStyles.deletePreview}>ATUALIZAR</Text>
                </TouchableOpacity>
            </View>
        }
        {path === '' &&
            <TouchableOpacity style={mainStyles.btnUploadCol2} onPress={chooseImage}>
                <Camera style={[mainStyles.btnIconUpload, mainStyles.btnIconUploadM]} />
                <Text style={[mainStyles.btnTextUpload2, mainStyles.btnTextUploadCol, mainStyles.btnTextUploadCol2]}>
                    {text}
                </Text>
            </TouchableOpacity>
        }
        </>
    );
}

export default RhDocuments;