import { StyleSheet, Dimensions, Platform } from 'react-native';

const windowWidth = Dimensions.get('window').width;

const styles = StyleSheet.create({
    wrapper: {
        justifyContent: "space-between"
    },
    contentBottom: {
        paddingTop: 20,
        paddingBottom: 20,
        shadowRadius: Platform.OS === 'ios' ? 2 : 16,
        shadowOffset: {
            width: 0,
            height: Platform.OS === 'ios' ? -2 : 12,
        },
        shadowColor: Platform.OS === 'ios' ? "#CCC" : "#000",
        elevation: Platform.OS === 'ios' ? 2 : 24,
        shadowOpacity: 1,
        backgroundColor: "#FFF",
        zIndex: 10
    },
    buttonLarge: {
        width: "100%"
    },
    contentButtons: {
        flexDirection: "row",
        justifyContent: "space-between"
    },
    btnTextOutlineBlue: {
        fontFamily: "Roboto-Regular",
        color: "#FFF",
        fontSize: 25,
        letterSpacing: 1,
        textAlign: "center"
    },
    separator: {
        marginLeft: 25,
        marginRight: 25,
        width: 1,
        height: '70%',
        backgroundColor: '#BDBDBD'
    },
    shift: {
        minHeight: 65,
        backgroundColor: "#90B0C0",
        alignItems: "center",
        justifyContent: "center",
        position: "relative"
    },
    infos: {
        flexDirection: "row"
    },
    btnBack: {
        flexDirection: "row",
        alignItems: "center",
        marginBottom: 20
    },
    icAbs: {
        position: "absolute",
        left: 20
    },
    icArrow: {
        width: 10,
        marginRight: 15
    },
    marginIc: {
        marginLeft: 20
    },
    dFlex: {
        flexDirection: "row",
        justifyContent: "center"
    },
    boxBorder: {
        height: 80,
        borderRadius: 12,
        shadowColor: "#CCC",
        shadowOffset: {
            width: 0,
            height: 9,
        },
        shadowOpacity: 0.3,
        shadowRadius: 6,
        elevation: 9,
        backgroundColor: "#FFF",
        paddingHorizontal: 20,
        marginBottom: 30,
        width: "95%"
    },
    viewFlex: {
        flexDirection: "row",
        alignItems: 'center',
        height: 80
    },
    rowCenterMargin: {
        flexDirection: "row",
        justifyContent: "space-between",
        marginTop: 30
    },
    icWidth: {
        width: 45
    },
    textRed: {
        fontFamily: "Roboto-Regular",
        fontSize: 16,
        color: "#FF6542",
        letterSpacing: 1,
        marginTop: 15,
        marginBottom: 40
    },
    marginTop: {
        marginTop: 20
    },
    btnPicture: {
        width: "45%",
        alignItems: "center"
    },
    boxPicture: {
        borderColor: "#4EA1CC",
        borderWidth: 2,
        borderRadius: 8,
        paddingLeft: 10,  
        paddingRight: 10,  
        paddingTop: 7, 
        paddingBottom: 15
    },
    tinyLogo: {
        width: 130,
        height: 150,
        resizeMode: "cover"
    },
    btnUpload: {
        height: 140
    },
    divider: {
        height: 1,
        backgroundColor: "#DADADA",
        marginBottom: 25
    }
});

export default styles;