import React, { useEffect, useState, useRef } from 'react';
import { Text, View, TouchableOpacity, ScrollView, ActivityIndicator, Keyboard, Alert, TextInput, Image, KeyboardAvoidingView } from 'react-native';
import * as ImagePicker from 'react-native-image-picker';
import ArrowLeft from '../../../assets/svgs/ArrowLeftSmallWhite';
import Camera from '../../../assets/svgs/Camera2';

import Dash from 'react-native-dash';

import PrivateHeader from '../../../components/headers/PrivateHeader';

import styles from './styles';
import mainStyles from '../../../mainStyles';
import { cepMask } from '../../../useful/masks';
import api from '../../../services/api';
import { useAuth } from '../../../context/auth';
import { requestCameraPermission } from "../../../useful/permissions";

const RhPersonalData = ({navigation}) => {
    const { token, user } = useAuth();

    const [loading, setLoading] = useState(false);
    const [inCep, setInCep] = useState('');
    const [inLogradouro, setInLogradouro] = useState('');
    const [inNumero, setInNumero] = useState('');
    const [inComplemento, setInComplemento] = useState('');
    const [inBairro, setInBairro] = useState('');
    const [inCidade, setInCidade] = useState('');
    const [inUf, setInUf] = useState('');

    const [imagePath, setImagePath] = useState('');
    const [imageId, setImageId] = useState('');

    useEffect(() => {
        getData();
    }, []);

    useEffect(() => {
        if(inCep.length > 8){
            api.post('/cadastro/endereco', {
                cep: inCep
            }).then(res => {
                let bairro = res.data.bairro;
                bairro = typeof bairro === 'string' || bairro instanceof String ? bairro : '';
                
                let cidade = res.data.cidade;
                cidade = typeof cidade === 'string' || cidade instanceof String ? cidade : '';

                let uf = res.data.uf;
                uf = typeof uf === 'string' || uf instanceof String ? uf : '';

                let logradouro = res.data.logradouro;
                logradouro = typeof logradouro === 'string' || logradouro instanceof String ? logradouro : '';

                setInBairro(bairro);
                setInCidade(cidade);
                setInUf(uf);
                setInLogradouro(logradouro);
            }).catch(err => console.log(err));
        }
    }, [inCep]);

    const getData = () => {
        api.get(`/rh/cadastro/endereco`).then(res => {
            let { dados } = res.data;
            setInCep(dados.cep)
            setInLogradouro(dados.logradouro)
            setInNumero(dados.numero)
            setInComplemento(dados.complemento)
            setInBairro(dados.bairro)
            setInCidade(dados.cidade)
            setInUf(dados.uf)
            setImageId(dados?.comprovante_endereco_arquivo?.id ?? '');
            setImagePath(dados?.comprovante_endereco_arquivo?.url ?? '');
        }).catch(error => {
            console.log(error.response);
            Alert.alert('Erro ao obter dados');
        }).then(() => {
            setLoading(false);
        });
    }

    const update = async () => {
        setLoading(true);

        let comprovante_endereco_id = imageId === '' ? await uploadImage() : imageId;

        let data = {
            cep: inCep,
            logradouro: inLogradouro,
            numero: inNumero,
            complemento: inComplemento,
            bairro: inBairro,
            cidade: inCidade,
            uf: inUf,
            comprovante_endereco_id
        };

        api.put(`/rh/cadastro/endereco`, data, {
            headers: {
                // 'content-type': 'application/x-www-form-urlencoded'
            }
        }).then(res => {
            Alert.alert(user.status_cadastro === 'reprovado' ? 'Dados salvos, caso tenha encerrado enviar para análise.' : 'Dados salvos e enviados para análise.');
            navigation.navigate('RhList')
        }).catch(err => {
            if(err?.response?.data?.errors){
                let errors = err.response.data.errors;
                let text = '';
                Object.keys(errors).map(error => {
                    text += `\n${errors[error][0]}`;
                });
                Alert.alert('Verifique os campos', text);
            } else {
                Alert.alert('Erro ao atualizar os dados.', 'Por favor, verifique os dados e tente novamente.');
            }
        }).then(() => {
            setLoading(false);
        });
    }

    const uploadImage = async () => {
        const data = new FormData();

        data.append('comprovante_endereco', {
            uri: imagePath,
            type: 'image/jpeg',
            name: 'comprovante_endereco.jpg'
        });

        return await api.post('/cadastro/upload', data)
            .then(res => {
                let id = res.data.id;
                setImageId(id)
                return id;
            })
            .catch(err =>{ 
                alert('Por favor, escolha uma imagem e tente novamente.');
                return '';
            });
    }

    const chooseImage = () => {
        Keyboard.dismiss();
        let alertProperties;
        if(Platform.OS === 'ios'){
            alertProperties = [
                { text: "Tirar foto", onPress: () => openCamera() },
                { text: "Galeria", onPress: () => openImageLibrary() },
                { text: "Fechar" }
            ];
        } else {
            alertProperties = [
                { text: "Fechar" },
                { text: "Galeria", onPress: () => openImageLibrary() },
                { text: "Tirar foto", onPress: () => openCamera() }
            ];
        }
        Alert.alert(
            "Enviar documento",
            "Como deseja enviar o documento?",
            alertProperties,
            { cancelable: false }
        );
    }

    const imageOptions = {
        mediaType: 'photo',
        includeBase64: false,
        maxHeight: 1024,
        maxWidth: 1024
    };
    

    const openCamera = async () => {
        const hasPermission = await requestCameraPermission();
        if(hasPermission){
            ImagePicker.launchCamera (imageOptions, res => {
                if(res.errorCode){
                    console.log(res);
                    Alert.alert('Erro', 'Câmera não disponível');
                    return;
                }
                if(res?.assets){
                    updateImagePath(res.assets[0].uri);
                }
            });
        }
    }

    const openImageLibrary = () => {
        ImagePicker.launchImageLibrary(imageOptions, res => {
            if(res?.assets){
                updateImagePath(res.assets[0].uri);
            }
        });
    }
    
    const updateImagePath = path => {
        setImagePath(path);
        setImageId('');
    }

    return (
        <KeyboardAvoidingView style={{ flex: 1 }} behavior="padding" >
            <PrivateHeader title={`Meu Perfil`} back={() => navigation.navigate('PrivateMain')} />
            <TouchableOpacity style={styles.shift} onPress={() => navigation.navigate('RhMyData')}>
                <View style={styles.icAbs}>
                    <ArrowLeft style={styles.icArrow} />
                </View>
                <Text style={[mainStyles.textWhite]}>ENDEREÇO</Text>
            </TouchableOpacity>
            <ScrollView contentContainerStyle={{ flexGrow: 1 }} keyboardShouldPersistTaps="handled" showsVerticalScrollIndicator={false}>
                {loading && 
                    <View style={{flex: 1, justifyContent: "center", alignItems: "center"}}>
                        <ActivityIndicator size="large" color="#00467F" />
                    </View>
                }
                {!loading &&
                <>
                    <View style={[mainStyles.containerRh, {paddingBottom: 20}]}>
                        {/* <Dash dashThickness={1} dashLength={3} dashColor="#DADADA" /> */}
                        <View>
                            <Text style={mainStyles.label}>CEP:</Text>         
                            <TextInput
                                underlineColorAndroid="transparent"  
                                style={mainStyles.inputText}
                                value={inCep}
                                onChangeText={value => setInCep( cepMask(value))}
                                keyboardType="phone-pad"
                            />
                            <Text style={mainStyles.label}>Logradouro:</Text>                                
                            <TextInput  
                                underlineColorAndroid="transparent"  
                                style={mainStyles.inputText}
                                value={inLogradouro}
                                onChangeText={setInLogradouro}
                            />
                            <Text style={mainStyles.labelBlue}>Número:</Text>                                
                            <TextInput
                                underlineColorAndroid="transparent"  
                                style={mainStyles.inputText}
                                value={inNumero}
                                onChangeText={setInNumero}
                            />
                            <Text style={mainStyles.label}>Complemento:</Text>                                
                            <TextInput
                                underlineColorAndroid="transparent"  
                                style={mainStyles.inputText}
                                value={inComplemento}
                                onChangeText={setInComplemento}
                            />
                            <Text style={mainStyles.label}>Bairro:</Text>                                
                            <TextInput
                                underlineColorAndroid="transparent"  
                                style={[mainStyles.inputText, mainStyles.inputCapitalize]}
                                value={inBairro}
                                onChangeText={setInBairro}
                            />
                            <Text style={mainStyles.label}>Cidade:</Text>                                
                            <TextInput
                                underlineColorAndroid="transparent"  
                                style={mainStyles.inputText}
                                value={inCidade}
                                onChangeText={setInCidade}
                            />
                            <Text style={mainStyles.label}>Estado:</Text>                                
                            <TextInput
                                underlineColorAndroid="transparent"  
                                style={mainStyles.inputText}
                                value={inUf}
                                onChangeText={setInUf}
                            />
                        </View>
                        <View style={mainStyles.centerContainer}>
                            {imagePath === '' &&
                                <TouchableOpacity style={[mainStyles.btnUpload2, styles.btnUpload]} onPress={() => chooseImage()}>
                                    <Camera style={mainStyles.btnIconUpload} />
                                    <Text style={[mainStyles.btnTextUpload2, mainStyles.btnTextUploadCol]}>FOTO DO SEU<Text style={mainStyles.bold}>{`\nCOMPROVANTE DE \nRESIDÊNCIA`}</Text></Text>
                                </TouchableOpacity>
                            }
                            {imagePath !== '' &&
                                <View style={[mainStyles.boxPreview2, { alignSelf: 'center', marginTop: 30 }, styles.boxPictureProfile]}>
                                    <View style={[mainStyles.boxPreviewImageContent2, styles.boxPreviewImageProfile]}>
                                        {token &&
                                            <Image 
                                                source={{ 
                                                    uri: imagePath,
                                                    headers: {
                                                        Authorization: `Bearer ${token}`
                                                    }
                                                }}
                                                style={mainStyles.imgProfile}
                                            />
                                        }
                                    </View>
                                    <TouchableOpacity onPress={() => chooseImage()}>
                                        <Text style={mainStyles.deletePreview}>ATUALIZAR</Text>
                                    </TouchableOpacity>
                                </View>
                            }
                        </View>
                    </View>
                </>
                }
            </ScrollView>
            <View style={styles.contentBottom}>
                <View style={[mainStyles.container, styles.contentButtons]}>              
                    <TouchableOpacity style={[mainStyles.btnOutlineBlue, mainStyles.buttonW48]} onPress={() => navigation.navigate('RhMyData')}>
                        <Text style={mainStyles.btnTextOutlineBlue}>VOLTAR</Text>
                    </TouchableOpacity>
                    <TouchableOpacity style={[mainStyles.btnBlueLight, styles.btnLogin, mainStyles.buttonW48]} onPress={update}>
                        <Text style={mainStyles.btnTextCenterBlueLight}>SALVAR</Text>
                    </TouchableOpacity>
                </View>
            </View>
        </KeyboardAvoidingView>
    );
}

export default RhPersonalData;