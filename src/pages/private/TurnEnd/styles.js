import { StyleSheet, Dimensions, Platform } from 'react-native';

const windowWidth = Dimensions.get('window').width;

const styles = StyleSheet.create({
    container: {
        width: windowWidth * 0.9,
        marginLeft: windowWidth * 0.05,
        marginTop: 10
    }, 
    rowOptions: {
        flexDirection: "row",
        justifyContent: "space-between"
    },
    provisionalRow: {
        flexDirection: "row",
        flexWrap: "wrap",
        borderColor: "#e0e0e0",
        borderWidth: 1,
        borderRadius: 14,
        paddingTop: 14,
        paddingBottom: 14,
        paddingLeft: 26,
        paddingRight: 26,
        height: 120
    },
    provisionalText: {
        fontFamily: "Roboto-Regular",
        color: "#979797",
        marginTop: Platform.OS === 'ios' ? 16 : 12,
        fontSize: Platform.OS === 'ios' ? 18 : 16,
        width: 225,
        lineHeight: 15,
        letterSpacing: 1,
        fontSize: 12
    },
    spanColor: {
        color: "#FF312E",
        fontWeight: "bold",
    },
    textStatus: {
        fontFamily: "Ubuntu-Regular",
        color: "#2D719F",
        letterSpacing: 1,
        marginTop: 20,
        fontSize: Platform.OS === 'ios' ? 16 : 14
    },
    textWait: {
        color: '#2D719F',
        fontFamily: 'Ubuntu-Medium',
        fontSize: 22,
        letterSpacing: 1,
        marginTop: 5
    },
    option: {
        alignItems: 'center',
        height: 160,
        backgroundColor: "#90B0C0",
        alignItems: 'center',
        justifyContent: 'center',
        borderRadius: 12,
        // shadowColor: "#000",
        // shadowOffset: {
        //     width: 0,
        //     height: 9,
        // },
        // shadowOpacity: 0.1,
        // shadowRadius: 11.95,
        // elevation: 12,
        marginTop: 25,
        position: 'relative'
    },
    optionNumber: {
        fontSize: 50,
        fontFamily: 'Ubuntu-Bold',
        color: "#fff",
        marginTop: -10
    },
    alertRow: {
        fontSize: 14,
        color: "#fff",
        letterSpacing: 1,
        marginTop: -5
    },
    contentTop: {        
        marginTop: 30,
        marginBottom: 50
    },
    buttonCenterBlue: {
        width: '90%',
        height: 50
    },
    buttonCenterOutlineBlue: {
        width: '65%',
        marginTop: 20
    },
    textBtnCenterBlue: {
        fontSize: 14,
        fontFamily: "Ubuntu-Regular",
        
    },
    textBtnCenterOutlineBlue: {
        fontSize: 20,
        color: "#4ea1cc"
    },
    boxWaitingRow: {
        flexDirection: "row",
        justifyContent: "space-between",
        marginTop: 30,
    },
    textClient: {
        fontFamily: "Ubuntu-Regular",
        color: "#2D719F",
        letterSpacing: 1,
    },
    nameClient: {
        fontFamily: "Ubuntu-Regular",
        color: "#2D719F",
        fontSize: 24,
        letterSpacing: 1,
        marginTop: 8 
    },
    optionWaiting: {
        alignItems: "center"
    },
    divider: {
        borderTopColor: "#DADADA",
        borderTopWidth: 1,
        marginTop: 30
    },
    optionText: {
       fontSize: 12,
       letterSpacing: 0.7,
       color: "#4ea1cc",
       marginTop: 5 
    },
    aboutClient: {
        marginTop: 30
    },  
    textInfo: {
        fontFamily: "Roboto-Regular",
        fontSize: 16,
        letterSpacing: 1,
        color: "#828282",
        marginTop: 7
    },
    infoClient: {
        fontFamily: "Roboto-Bold",
        fontSize: 16,
        letterSpacing: 1,
        color: "#00467F"
    },
    actions: {
        flexDirection: "row",
        marginTop: 30,
        marginBottom: 5,
        justifyContent: "flex-start",
    },
    boxBtn: {
        marginRight: 20
    },
    btnBorder: {
        alignItems: "center",
        justifyContent: "center",
        borderWidth: 1,
        borderColor: "#00467F",
        height: 65,
        width: 75,
        borderRadius: 10
    },
    btnBorderDisabled: {
        borderColor: "#979797"
    },
    icUser: {
        width: 55,
        marginTop: 5
    },
    icCel: {
        width: 30,
    },
    icContact: {
        width: 45,
    },
    icContrato: {
        marginRight: -7
    },
    typeBtn: {
        fontFamily: "Ubuntu-Regular",
        color: "#828282",
        fontSize: 11,
        letterSpacing: 1,
        textAlign: "center",
        marginTop: 5
    },
    textAlert: {
        fontFamily: "Ubuntu-Regular",
        color: "#452323",
        fontSize: 13,
        letterSpacing: 1,
        lineHeight: 20,
        textAlign: "left",
        marginTop: 25,
        marginBottom: 15
    },
    contentBottom: {
        paddingTop: 25,      
        paddingBottom: 40,
        shadowRadius: Platform.OS === 'ios' ? 2 : 16,
        shadowOffset: {
            width: 0,
            height: Platform.OS === 'ios' ? -2 : 12,
        },
        shadowColor: Platform.OS === 'ios' ? '#CCC' : "#000",
        elevation: Platform.OS === 'ios' ? 2 : 24,
        shadowOpacity: 1,
        backgroundColor: "#FFF",
        position: "relative"
    },
    boxShadow: {
        height: 20,
        backgroundColor: "#FFF",
        elevation: 5,
        shadowColor: "#000",
        marginBottom: 0,
        position: "absolute",
        top: -20,
        right: 0,
        left: 0
    },
    contentButton: {
        flexDirection: "row",
        justifyContent: "center"
    }
});

export default styles;