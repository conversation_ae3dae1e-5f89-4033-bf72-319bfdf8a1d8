import React, { useState, useEffect } from 'react';
import { Text, View, ScrollView, TouchableOpacity, ActivityIndicator, BackHandler, Linking } from 'react-native';

import Check from '../../../assets/svgs/CheckLight';
import Celphone from '../../../assets/svgs/Celphone';
import CelphoneDisabled from '../../../assets/svgs/CelphoneDisabled';
import EmailDisabled from '../../../assets/svgs/EmailDisabled';
import Email from '../../../assets/svgs/Email';
import Whats from '../../../assets/svgs/Whats2';

import PrivateHeader from '../../../components/headers/PrivateHeader';
import AlertFooter from '../../../components/AlertFooter';

import styles from './styles';
import mainStyles from '../../../mainStyles';

import { useService } from '../../../context/service';
import { clearPhone } from '../../../useful/conversions';
import { useAuth } from '../../../context/auth';

const TurnEnd = ({ route, navigation }) => {
    const [loading, setLoading] = useState(false);
    const [confirmExit, setConfirmExit] = useState(false);

    const { exit, redirectTo } = useService();

    const { name, email, phone } = route.params;
    const { user } = useAuth();

    const back = () => {
        return true;
    }

    const celAtivo = () => {
        return true;
    }

    const whatsAtivo = () => {
        return true;
    }

    const emailAtivo = () => {
        if (email !== '' && email !== null && email !== undefined) {
            return true;
        }
        return false;
    }

    useEffect(() => {
        const backHandler = BackHandler.addEventListener(
            "hardwareBackPress",
            back
        );

        return () => backHandler.remove();
    }, []);

    const handleExit = async () => {
        setLoading(true);
        setConfirmExit(false);
        const result = await exit();
        if (result) {
            navigation.navigate('PrivateMain');
        } else {
            Alert.alert('Erro', 'Não foi possível sair da fila, por favor, tente novamente.');
            setLoading(false);
        }
    }

    return (
        <>
            <View style={mainStyles.wrapper}>
                {confirmExit &&
                    <AlertFooter
                        text={
                            <Text>
                                {`Você deseja realmente\nsair da fila?`}
                            </Text>
                        }
                        btnText={`SIM, SAIR DA FILA`}
                        btnBorder={true}
                        close={() => setConfirmExit(false)}
                        action={() => handleExit()}
                    />
                }
                <PrivateHeader title={`Fila`} back={() => navigation.navigate('PrivateMain')} />
                {loading &&
                    <View style={{ flex: 1, justifyContent: "center", alignItems: "center" }}>
                        <ActivityIndicator size="large" color="#00467F" />
                    </View>
                }
                {!loading &&
                    <ScrollView style={styles.container} showsVerticalScrollIndicator={false}>
                        <View style={mainStyles.bgHourCheckin}>
                            <Text style={mainStyles.textHourCheckin}>Horário do seu check in: {user.fila.checkin_horario}</Text>
                        </View>
                        {user.fila.sorteio_horario &&
                            <View>
                                <Text style={mainStyles.sorteioText}>Horário do sorteio: {user.fila.sorteio_horario}</Text>
                                <View style={styles.divider}></View>
                            </View>
                        }
                        <View style={styles.rowOptions}>
                            <View style={styles.contentOption}>
                                <Text style={styles.textStatus}>Status e posição na fila:</Text>
                                <Text style={styles.textWait}>Atendimento finalizado</Text>
                            </View>
                        </View>
                        <View style={styles.contentOption}>
                            <View style={styles.option}>
                                <Check />
                            </View>
                        </View>
                        <View style={styles.boxWaiting}>
                            <View style={styles.boxWaitingRow}>
                                <View style={styles.contentWaiting}>
                                    <Text style={styles.textClient}>Quem é o seu cliente:</Text>
                                    <Text style={styles.nameClient}>{name}</Text>
                                </View>
                            </View>
                        </View>
                        <View style={styles.divider}></View>
                        {/* <View style={styles.aboutClient}>
                            <Text style={styles.textInfo}>Nome: <Text style={styles.infoClient}>{name}</Text></Text>
                            <Text style={styles.textInfo}>E-Mail: <Text style={styles.infoClient}>{email}</Text></Text>
                            <Text style={styles.textInfo}>Telefone: <Text style={styles.infoClient}>{phone}</Text></Text>
                        </View> */}
                        <View style={styles.actions}>
                            <View style={styles.boxBtn}>
                                <TouchableOpacity
                                    style={[styles.btnBorder, !celAtivo() ? styles.btnBorderDisabled : null]}
                                    onPress={() => Linking.openURL(`tel:+55${clearPhone(phone)}`)}
                                    disabled={!celAtivo()}
                                >
                                    {!celAtivo() &&
                                        <CelphoneDisabled style={styles.icCel} />
                                    }
                                    {celAtivo() &&
                                        <Celphone style={styles.icCel} />
                                    }
                                </TouchableOpacity>
                                <Text style={styles.typeBtn}>Telefone</Text>
                            </View>
                            <View style={styles.boxBtn}>
                                <TouchableOpacity
                                    style={[styles.btnBorder, !whatsAtivo() ? styles.btnBorderDisabled : null]}
                                    onPress={() => Linking.openURL(`https://wa.me/55${clearPhone(phone)}`)}
                                    disabled={!whatsAtivo()}
                                >
                                    {!whatsAtivo() &&
                                        <WhatsDisabled style={styles.icContact} />
                                    }
                                    {whatsAtivo() &&
                                        <Whats style={styles.icContact} />
                                    }
                                </TouchableOpacity>
                                <Text style={styles.typeBtn}>WhatsApp</Text>
                            </View>
                            <View style={styles.boxBtn}>
                                <TouchableOpacity
                                    onPress={() => Linking.openURL(`mailto:${email}`)}
                                    style={[styles.btnBorder, !emailAtivo() ? styles.btnBorderDisabled : null]}
                                    disabled={!emailAtivo()}
                                >
                                    {!emailAtivo() &&
                                        <EmailDisabled style={styles.icEmail} />
                                    }
                                    {emailAtivo() &&
                                        <Email style={styles.icEmail} />
                                    }
                                </TouchableOpacity>
                                <Text style={styles.typeBtn}>E-mail</Text>
                            </View>
                        </View>
                    </ScrollView>
                }
                <View style={styles.contentBottom}>
                    <View style={styles.contentButton}>
                        <TouchableOpacity style={[mainStyles.btnBlueNew, styles.buttonCenterBlue]} onPress={() => redirectTo(true)}>
                            <Text style={[mainStyles.btnTextCenterBlue, styles.textBtnCenterBlue]}>VOLTAR</Text>
                        </TouchableOpacity>
                        {/* <TouchableOpacity style={[mainStyles.btnCenterOutlineBlue, styles.buttonCenterOutlineBlue]} onPress={() => setConfirmExit(true)}>
                            <Text style={[mainStyles.btnTextCenterOutlineBlue, styles.textBtnCenterOutlineBlue]}>SAIR DA FILA</Text>
                        </TouchableOpacity> */}
                    </View>
                </View>
            </View>
        </>
    );
}

export default TurnEnd;