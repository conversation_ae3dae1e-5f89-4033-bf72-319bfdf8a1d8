import { StyleSheet, Dimensions, Platform } from 'react-native';

const windowWidth = Dimensions.get('window').width;

const styles = StyleSheet.create({
    wrapper: {
        justifyContent: "space-between"
    },
    contentBottom: {
        paddingTop: 20,
        paddingBottom: 40,
        shadowRadius: Platform.OS === 'ios' ? 2 : 16,
        shadowOffset: {
            width: 0,
            height: Platform.OS === 'ios' ? -2 : 12,
        },
        shadowColor: Platform.OS === 'ios' ? "#CCC" : "#000",
        elevation: Platform.OS === 'ios' ? 2 : 24,
        shadowOpacity: 1,
        backgroundColor: "#FFF",
        position: "absolute",
        bottom: 0,
        zIndex: 10
    },
    buttonLarge: {
        width: "100%"
    },
    btnTextOutlineBlue: {
        fontFamily: "Roboto-Regular",
        color: "#FFF",
        fontSize: 25,
        letterSpacing: 1,
        textAlign: "center"
    },
    menu: {
        flexDirection: "row",
        backgroundColor: "#FFF",
        alignItems: 'center',
        justifyContent: 'center',
        borderRadius: 12,
        shadowColor: "#000",
        shadowOffset: {
            width: 0,
            height: 9,
        },
        shadowOpacity: 0.1,
        shadowRadius: 11.95,
        elevation: 12,
        marginTop: 15,
        paddingLeft: 30,
        paddingRight: 30,
        paddingBottom: 15,
        paddingTop: 15
    },
    menuItem: {
        flex: 1,
        flexDirection: "row",
        justifyContent: "center",
        alignItems: "center"
    },
    separator: {
        marginLeft: 25,
        marginRight: 25,
        width: 1,
        height: '70%',
        backgroundColor: '#BDBDBD'
    },
    shift: {
        paddingTop: 30,
        paddingBottom: 0,
        paddingLeft: 0,
        paddingRight: 0,
        marginBottom: 20,
    },
    infos: {
        flexDirection: "row"
    },
    devTitle: {
        fontFamily: "Roboto-Regular",
        fontSize: Platform.OS === 'ios' ? 17 : 15,
        letterSpacing: 1,
        color: "#00467F"
    },
    devSubtitle: {
        fontFamily: "Ubuntu-Medium",
        fontSize: Platform.OS === 'ios' ? 15 : 13,
        letterSpacing: 1,
        color: "#828282",
        marginTop: 10
    },
    devName: {
        fontFamily: "Ubuntu-Light",
        fontSize: Platform.OS === 'ios' ? 15 : 13,
        letterSpacing: 1,
        color: "#828282",
        marginTop: 3,
        marginBottom: 15,
        lineHeight: 16
    },
    boxNameCenter: {
        flexDirection: "row",
        alignItems: "center",
        marginTop: 20
    },
    divider: {
        height: 1,
        backgroundColor: "#DADADA"
    },
    textRight: {
        fontFamily: "Roboto-Regular",
        fontSize: Platform.OS === 'ios' ? 20 : 16,
        textTransform: "uppercase",
        letterSpacing: 1,
        color: "#00467F",
        textAlign: "right"
    },
    nameActive: {
        fontFamily: "Roboto-Regular",
        fontSize: Platform.OS === 'ios' ? 20 : 16,
        textTransform: "uppercase",
        letterSpacing: 1,
        color: "#1B9C20",
        marginLeft: 5
    },
    actions: {
        flexDirection: "row",
        marginTop: 15,
        justifyContent: "center",
    },
    colorGreen: {
        color: "#00467F",
        fontFamily: "Ubuntu-Regular",
        textTransform: "capitalize"
    },
    btnBorder: {
        alignItems: "center",
        justifyContent: "center",
        borderWidth: 1,
        borderColor: "#00467F",
        height: 65,
        width: 75,
        borderRadius: 10,
        marginLeft: 7,
        marginRight: 7
    },
    btnBorderDisabled: {
        borderColor: "#979797"
    },
    icUser: {
        width: 55,
        marginTop: 5
    },
    icCel: {
        width: 45,
    },
    icContact: {
        width: 40,
        color: "#FFF"
    },
    icArrow: {
        transform: [{rotate: "-90deg"}],
        position: "absolute",
        right: 15
    },
    icEdit: {
        color: "#FFF"
    },
    icClose: {
        color: "#FFF"
    },
    textLower: {
        textTransform: "capitalize",
        fontFamily: "Ubuntu-Regular"
    },
    notShifts: {
        fontFamily: "Roboto-Regular",
        fontSize: 16,
        textAlign: "center",
        marginTop: 45
    },
    marginIc: {
        marginLeft: 15
    },
    dFlex: {
        flexDirection: "row",
        justifyContent: "center"
    },
    boxBorder: {
        minHeight: 65,
        backgroundColor: "#F2F2F2",
        marginBottom: 25,
        width: "100%"
    },
    viewFlex: {
        flexDirection: "row",
        alignItems: 'center'
    },
    boxIcon: {
        backgroundColor: "#90B0C0",
        minHeight: 65,
        width: 55,
        justifyContent: "center",
        alignItems: "center"
    },
    rowCenterMargin: {
        flexDirection: "row",
        alignItems: "center",
        justifyContent: "space-between",
        position: "relative"
    },
    boxStatus: {
        marginHorizontal: 10,
        marginTop: 30,
        padding: 20,
        borderRadius: 12,
        shadowColor: "#CCC",
        shadowOffset: {
            width: 0,
            height: 9,
        },
        shadowOpacity: 0.48,
        shadowRadius: 5.95,
        elevation: 12,
        backgroundColor: "#FFF"
    },
    boxStatusTitle: {
        flexDirection: 'row',
        alignItems: 'center'
    },
    boxStatusTitleText: {
        marginLeft: 12,
        color: '#FF6542',
        textTransform: 'uppercase',
        fontFamily: "Roboto-Regular",
        fontSize: 15,
        letterSpacing: 1
    },
    boxStatusText: {
        marginVertical: 15,
        fontFamily: "Roboto-Regular",
        fontSize: 13,
        color: '#828282'
    },
    boxStatusBtnEdit: {
        backgroundColor: 'transparent',
        borderWidth: 2,
        borderColor: '#00467F',
        borderRadius: 5,
        height: 50,
        justifyContent: "center",
        alignItems: 'center',
        marginBottom: 15

    },
    boxStatusBtnEditText: {
        fontFamily: 'Ubuntu-Medium',
        color: '#00467F',
        textTransform: 'uppercase',
        letterSpacing: 1,
        fontSize: 15
    },
    boxStatusBtnSend: {
        backgroundColor: 'transparent',
        backgroundColor: '#00467F',
        borderRadius: 5,
        height: 50,
        justifyContent: "center",
        alignItems: 'center'

    },
    boxStatusBtnSendText: {
        fontFamily: 'Ubuntu-Medium',
        color: '#FFF',
        textTransform: 'uppercase',
        letterSpacing: 1,
        fontSize: 15,
        textAlign: 'center'
    },
    boxStatusBtnSendCanceled: {
        backgroundColor: '#979797'
    }
});

export default styles;