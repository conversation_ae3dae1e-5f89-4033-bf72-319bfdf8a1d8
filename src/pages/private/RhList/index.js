import React, { useEffect, useState } from 'react';
import { Text, View, TouchableOpacity, ScrollView, ActivityIndicator, Linking, Alert } from 'react-native';

import Phone from '../../../assets/svgs/Phone';
import Email from '../../../assets/svgs/Email';
import Whats from '../../../assets/svgs/Whats3';
import Close from '../../../assets/svgs/CloseRed';
import Edit from '../../../assets/svgs/Edit';
import Arrow from '../../../assets/svgs/ArrowDownGrey';
import BadgeIc from '../../../assets/svgs/Badge';

import Dash from 'react-native-dash';

import PrivateHeader from '../../../components/headers/PrivateHeader';
import AlertFooterRed from '../../../components/AlertFooterRedRh';
import Badge from '../../../components/Badge';

import styles from './styles';
import mainStyles from '../../../mainStyles';

import api from '../../../services/api';
import { useAuth } from '../../../context/auth';
import Alert2 from '../../../assets/svgs/Alert2';
import { useIsFocused } from '@react-navigation/native';
import Awaiting from '../../../assets/svgs/Awaiting';

const RhList = ({navigation}) => {
    const [loading, setLoading] = useState(false);
    const [loadingRh, setLoadingRh] = useState(true);
    const [alert, setAlert] = useState(false);
    const [badge, setBadge] = useState(false);
    const [showMessages, setShowMessages] = useState(false);
    const [showEditButtons, setShowEditButtons] = useState(false);
    const [enviarNovamente, setEnviarNovamente] = useState(false);
    const [status, setStatus] = useState('');
    const [statusInfos, setStatusInfos] = useState('');
    const [statusTitulo, setStatusTitulo] = useState('');
    const [icon, setIcon] = useState('');

    const { signOut, getUser, user } = useAuth();
    const isFocused = useIsFocused();

    useEffect(() => {
        if(isFocused){
            getRh();
            getUser();
        }
    }, [isFocused]);

    useEffect(() => {
        getRh();
    }, [user]);

    const getRh = () => {
        setLoadingRh(true);
        api.get(`/rh`).then(res => {
            console.log('rh', res.data);
            let toStatus = res.data.status;
            let toAlteracoesPendentes = res.data.alteracoes_pendentes;
            let toTitulo = '';
            let toIcon = '';
            let toShowMessages = false;
            let toShowEditButtons = false;
            
            // if(toStatus === 'aprovado' && toAlteracoesPendentes.length > 0){
            //     toIcon = 'awaiting';
            //     toTitulo = 'Alterações em análise';
            //     toShowMessages = true;
            // }
            if(toStatus === 'pendente'){
                toIcon = 'awaiting';
                toTitulo = 'Cadastro em análise';
                toShowMessages = true;
                toShowEditButtons = false;
            }
            if(toStatus === 'reprovado' && user.status_cadastro === 'aprovado'){
                toIcon = 'alert';
                toTitulo = 'Alterações reprovadas';
                toShowMessages = true;
                toShowEditButtons = true;
            }
            if(toStatus === 'reprovado' && user.status_cadastro !== 'aprovado'){
                toIcon = 'alert';
                toTitulo = 'Cadastro reprovado';
                toShowMessages = true;
                toShowEditButtons = true;
            }
            
            setIcon(toIcon);
            setShowEditButtons(toShowEditButtons);
            setShowMessages(toShowMessages);
            setEnviarNovamente(res.data.enviar_novamente_enabled);
            setStatusTitulo(toTitulo);
            setStatusInfos(res.data.status_infos);
            setLoadingRh(false);
            setStatus(toStatus);
        });
    }

    const deleteAccount = () => {
        setLoading(true);
        setAlert(false);

        api.delete(`/user`).then(res => {
            signOut();
        }).catch(error => {
            Alert.alert('Tente novamente', 'Não foi possível excluir sua conta. Por favor, tente novamente.');
        }).then(() => setLoading(false));
    }

    const send = () => {
        setLoadingRh(true);
        api.post('/rh/cadastro').then(res => {
            getRh();
        }).catch(error => {
            console.log(error.response);
            setLoadingRh(false);
        });
    }

    return (
        <>
        {alert && 
            <AlertFooterRed
                title={`Excluir minha conta`}
                close={() => setAlert(false)}
                route={() => deleteAccount()}
            />
        }

        {badge && 
            <Badge
                close={() => setBadge(false)}
            />
        }

        <View style={mainStyles.wrapper}>
            <PrivateHeader title={`Meu Perfil`} back={() => navigation.navigate('PrivateMain')} />
            <View style={[mainStyles.privateContainer, {flex: 1}]}>
                {loading && 
                    <View style={{flex: 1, justifyContent: "center", alignItems: "center"}}>
                        <ActivityIndicator size="large" color="#00467F" />
                    </View>
                }
                {!loading &&
                <>
                    <ScrollView showsVerticalScrollIndicator={false}>
                        {loadingRh &&
                            <View style={{flex: 1, marginTop: 25, justifyContent: "center", alignItems: "center"}}>
                                <ActivityIndicator size="large" color="#00467F" />
                            </View>
                        }
                        {!loadingRh && showMessages &&
                            <View style={styles.boxStatus}>
                                <View style={styles.boxStatusTitle}>
                                    {icon === 'alert' &&
                                        <Alert2 />
                                    }
                                    {icon === 'awaiting' &&
                                        <Awaiting style={{ width: 30 }} />
                                    }
                                    <Text style={[styles.boxStatusTitleText, icon === 'awaiting' ? { color: '#00467F' } : null]}>{statusTitulo}</Text>
                                </View>
                                <Text style={styles.boxStatusText}>
                                    {statusInfos}
                                </Text>
                                {showEditButtons &&
                                    <>
                                        <TouchableOpacity style={styles.boxStatusBtnEdit} onPress={() => navigation.navigate('RhMyData')}>
                                            <Text style={styles.boxStatusBtnEditText}>Editar meus dados</Text>
                                        </TouchableOpacity>
                                        <TouchableOpacity 
                                            style={[
                                                styles.boxStatusBtnSend, 
                                                enviarNovamente ? null : styles.boxStatusBtnSendCanceled
                                            ]}
                                            onPress={send}
                                            disabled={!enviarNovamente}
                                        >
                                            <Text style={styles.boxStatusBtnSendText}>
                                                {`Enviar novamente\npara aprovação`}
                                            </Text>
                                        </TouchableOpacity>
                                    </>
                                }
                                
                            </View>
                        }
                        <View style={styles.shift}>
                            {status !== 'pendente' && user?.regional === 'São Paulo' &&
                            <>
                            {/* <Dash dashThickness={1} dashLength={3} dashColor="#DADADA" /> */}
                                <View style={styles.infos}>
                                    <View style={styles.devBox}>
                                        <Text style={styles.devTitle}>Fale com a Gestão de Autônomos</Text>
                                        <Text style={styles.devSubtitle}>Horário de atendimento:</Text>
                                        <Text style={styles.devName}>{`2ª a 6ª - 09:00 às 18:00\nSábado - 09:00 às 13:00`}</Text>
                                    </View>
                                </View>
                                <View style={styles.divider}></View>
                                <View>
                                    <View style={styles.boxNameCenter}>
                                        <Text style={styles.textRight}>{`Regional:`}</Text>
                                        <Text style={styles.nameActive}>São Paulo</Text>
                                    </View>
                                    <View style={styles.actions}>
                                        {/* <TouchableOpacity style={styles.btnBorder} onPress={() => Linking.openURL(`tel:1145507321`) }>
                                            <Phone style={styles.icCel}/>
                                        </TouchableOpacity> */}
                                        {/* <TouchableOpacity style={styles.btnBorder} onPress={() => Linking.openURL(`whatsapp://send?&phone=5511992509026`) }>
                                            <Whats style={styles.icContact}/>
                                        </TouchableOpacity> */}
                                        <View style={styles.dFlex}>
                                            <View style={[styles.boxBorder, styles.btnWidth]}>
                                                <TouchableOpacity style={styles.rowCenterMargin} onPress={() => Linking.openURL(`whatsapp://send?&phone=5511992509026`) }>
                                                    <View style={styles.viewFlex}>  
                                                        <View style={styles.boxIcon}>
                                                            <Whats style={styles.icContact}/>                                  
                                                        </View>
                                                        <View style={styles.marginIc}>
                                                            <Text style={[mainStyles.textBlue, styles.colorGreen]}>WHATSAPP</Text>                                        
                                                        </View>
                                                    </View>
                                                    <View style={styles.icArrow}>
                                                        <Arrow />
                                                    </View>
                                                </TouchableOpacity>
                                            </View>
                                        </View>
                                        {/* <TouchableOpacity style={styles.btnBorder} onPress={() => Linking.openURL(`mailto:<EMAIL>`) }>
                                            <Email style={styles.icEmail}/>
                                        </TouchableOpacity> */}
                                    </View>
                                    <View style={styles.divider}></View>
                                    {/* <Dash dashThickness={1} dashLength={3}  dashColor="#DADADA" /> */}
                                </View>
                            </>
                            }
                            {/* <Dash dashThickness={1} dashLength={3}  dashColor="#DADADA" /> */}
                            {status !== 'pendente' && user?.regional === 'Rio de Janeiro' &&
                            <>
                                <View style={styles.infos}>
                                    <View style={styles.devBox}>
                                        <Text style={styles.devTitle}>Horário de atendimento:</Text>
                                        <Text style={styles.devName}>{`2ª a 6ª - 09:00 às 18:00`}</Text>
                                    </View>
                                </View>
                                <Dash dashThickness={1} dashLength={3} dashColor="#DADADA" />
                                <View>
                                    <View style={styles.boxNameCenter}>
                                        <Text style={styles.textRight}>{`Regional:`}</Text>
                                        <Text style={styles.nameActive}>Rio de Janeiro</Text>
                                    </View>
                                    <View style={styles.actions}>
                                        {/* <TouchableOpacity style={styles.btnBorder} onPress={() => Linking.openURL(`tel:2135543393`) }>
                                            <Phone style={styles.icCel}/>
                                        </TouchableOpacity> */}
                                        {/* <TouchableOpacity style={styles.btnBorder} onPress={() => Linking.openURL(`whatsapp://send?&phone=5521991143851`) }>
                                            <Whats style={styles.icContact}/>
                                        </TouchableOpacity> */}
                                        <View style={styles.dFlex}>
                                            <View style={[styles.boxBorder, styles.btnWidth]}>
                                                <TouchableOpacity style={styles.rowCenterMargin} onPress={() => Linking.openURL(`whatsapp://send?&phone=5521991143851`) }>
                                                    <View style={styles.viewFlex}>
                                                        <View style={styles.boxIcon}>
                                                            <Whats style={styles.icContact}/>                                  
                                                        </View>
                                                        <View style={styles.marginIc}>
                                                            <Text style={[mainStyles.textBlue, styles.colorGreen]}>WHATSAPP</Text>                                        
                                                        </View>
                                                    </View>
                                                    <View style={styles.icArrow}>
                                                        <Arrow />
                                                    </View>
                                                </TouchableOpacity>
                                            </View>
                                        </View>
                                        {/* <TouchableOpacity style={styles.btnBorder} onPress={() => Linking.openURL(`mailto:<EMAIL>`) }>
                                            <Email style={styles.icEmail}/>
                                        </TouchableOpacity> */}
                                    </View>
                                    {/* <Dash dashThickness={1} dashLength={3}  dashColor="#DADADA" /> */}
                                </View>
                            </>
                            }
                        </View>
                        {!showMessages &&
                            <>
                                <View style={[styles.dFlex, {marginTop: 5}]}>
                                    <View style={styles.boxBorder}>
                                        <TouchableOpacity style={styles.rowCenterMargin} onPress={() => navigation.navigate('RhMyData')}>
                                            <View style={styles.viewFlex}>
                                                <View style={styles.boxIcon}>
                                                    <Edit style={styles.icEdit} />                                    
                                                </View>
                                                <View style={styles.marginIc}>
                                                    <Text style={[mainStyles.textBlue, styles.textLower]}>MEUS DADOS</Text>                                        
                                                </View>
                                            </View>
                                            <View style={styles.icArrow}>
                                                <Arrow />
                                            </View>
                                        </TouchableOpacity>
                                    </View>
                                </View>
                                {/* <View style={styles.dFlex}>
                                    <View style={styles.boxBorder}>
                                        <TouchableOpacity style={styles.rowCenterMargin} onPress={() => setBadge(true)}>
                                            <View style={styles.viewFlex}>                                 
                                                <BadgeIc />                                    
                                                <View style={styles.marginIc}>
                                                    <Text style={[mainStyles.textBlue]}>MEU CRACHÁ</Text>                                        
                                                </View>
                                            </View>
                                            <Arrow />
                                        </TouchableOpacity>
                                    </View>
                                </View> */}
                            </>
                        }
                        <View style={styles.dFlex}>
                            <View style={styles.boxBorder}>
                                <TouchableOpacity style={styles.rowCenterMargin} onPress={() => setAlert(true)}>
                                    <View style={styles.viewFlex}>
                                        <View style={styles.boxIcon}>
                                            <Close style={styles.icClose} />
                                        </View>
                                        <View style={styles.marginIc}>
                                            <Text style={[mainStyles.textBlue, styles.textLower, {color: "#FF6542"}]}>EXCLUIR MINHA CONTA</Text>                                        
                                        </View>
                                    </View>
                                    <View style={styles.icArrow}>
                                        <Arrow />
                                    </View>
                                </TouchableOpacity>
                            </View>
                        </View>
                    </ScrollView>
                    {user.status_cadastro === 'aprovado' &&
                        <View style={styles.contentBottom}>
                            <View style={[mainStyles.container, styles.contentButtons]}>
                                <TouchableOpacity style={[mainStyles.btnBlueNew, styles.buttonLarge]} onPress={() => navigation.navigate('PrivateMain')}>
                                    <Text style={mainStyles.btnTextBlueNew}>VOLTAR</Text>
                                </TouchableOpacity>
                            </View>
                        </View>
                    }
                </>
                }
            </View>
        </View>
    </>
    );
}

export default RhList;