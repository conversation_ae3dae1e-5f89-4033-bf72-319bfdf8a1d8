import React, { useEffect, useRef, useState } from 'react';
import { TextInput, Text, View, TouchableOpacity, ActivityIndicator, SafeAreaView, FlatList } from 'react-native';
import { useIsFocused, useNavigation } from '@react-navigation/native';
import moment from 'moment';

import PrivateHeader from '../../../../components/headers/PrivateHeader';
import Select from '../../../../components/Select';

import CalendarLight from '../../../../assets/svgs/CalendarLight';
import ArrowDownGrey from '../../../../assets/svgs/ArrowDownGrey';

import styles from './styles';
import mainStyles from '../../../../mainStyles';
import { ScrollView } from 'react-native-gesture-handler';
import api from '../../../../services/api';
import { useAuth } from '../../../../context/auth';
import SelectBorder from '../../../../components/SelectBorder';
import LightboxCalendar from '../../../../components/LightboxCalendar';
import ArrowRightGray from '../../../../assets/svgs/ArrowRightGray';
import Less2 from '../../../../assets/svgs/Less2';
import More from '../../../../assets/svgs/More';
import { useSchedule } from '../../../../context/cliente';
import CalendarMonth from '../../../../components/CalendarMonth';
import CalendarHorizontal from '../../../../components/CalendarHorizontal';

const minDate = "2023-01-25";

const StatusText = ({lead, status}) => {
    const duracao = lead.validade.duracao;

    return (
        <>
            {status === 'aguardando' &&
                <Text style={[styles.schedulingStatus, { color: "#FF6542" }]}>Aguardando ativação</Text>
            }
            {status === 'ativado' &&
                <Text style={[styles.schedulingStatus, { color: "#1B9C20" }]}>Ativado com sucesso</Text>
            }
            {status === 'expirado' &&
                <Text style={[styles.schedulingStatus, { color: "#979797" }]}>Lead não ativado em {duracao}</Text>
            }
        </>
    );
}

const Lead = ({lead, navigation}) => {
    const [time, setTime] = useState(0);
    const [status, setStatus] = useState(lead.status);

    useEffect(() => {
        const interval = setInterval(() => {
            setTime(current => current + 1);
        }, 1000);

        return () => clearInterval(interval);
    }, []);

    useEffect(() => {
        expirarLead();
    }, [time]);

    const expirarLead = () => {
        if(time >= lead.validade.validade_segundos && status !== 'expirado'){
            setStatus('expirado');
        }
    }
    
    return (
        <TouchableOpacity
            style={styles.boxScheduling}
            onPress={() => navigation.navigate('LeadsContent', { id: lead.id })}
        >
            <View style={[styles.schedulingHour, { backgroundColor: status === 'aguardando' ? "#FF6542" : "#F2F2F2" }]}>
                <Text style={[styles.hour, status === 'aguardando' ? { color: "#FFF" } : null]}>{moment(lead.disparo.data).format('HH:mm')}</Text>
            </View>
            <View style={styles.schedulinglInfos}>
                <View style={styles.schedulingInfosText}>
                    <View style={styles.typeOrigem}>
                        <Text style={styles.schedulingProduct}>{lead?.origem}</Text>
                        {lead?.has_ai_chat_resume &&
                            <View style={styles.typeIA}>
                                <Text style={styles.textIA}>Lead IA</Text>
                            </View>
                        }
                    </View>
                    <Text style={styles.schedulingName}>{status === 'expirado' ? 'Lead expirado' : lead?.cliente?.nome}</Text>
                    <StatusText lead={lead} status={status} />
                </View>
                <ArrowRightGray style={{ width: 24 }} />
            </View>
        </TouchableOpacity>
    );
}

const LeadsList = ({ navigation, route }) => {
    const isFocused = useIsFocused();
    const flatListRef = useRef();

    const { clearFields } = useSchedule();
    const [loading, setLoading] = useState(true);
    const [calendar, setCalendar] = useState(false);
    const [showLeads, setShowLeads] = useState(true);

    const [days, setDays] = useState([]);
    const [selectedDate, setSelectedDate] = useState(moment().format('YYYY-MM-DD'));
    const [selectedPlantao, setSelectedPlantao] = useState(null);

    const [leads, setLeads] = useState([]);

    const [totalAtivados, setTotalAtivados] = useState(0);
    const [totalAguardando, setTotalAguardando] = useState(0);
    const [totalExpirados, setTotalExpirados] = useState(0);

    useEffect(() => {
        getDays();
    }, [selectedDate])

    useEffect(() => {
        goToCurrentDay();
    }, [days, selectedDate, isFocused]);

    useEffect(() => {
        if(isFocused) getLeads();
    }, [isFocused, selectedDate]);

    const getLeads = () => {
        setLoading(true);

        api.get(`/leads`, {
            params: {
                data: selectedDate
            }
        }).then(res => {
            let { totais, leads } = res.data;
            setLeads(leads);
            setTotalAguardando(totais.aguardando);
            setTotalAtivados(totais.ativados);
            setTotalExpirados(totais.expirados);
        }).catch(err => {
            console.log(err.response);
        }).then(() => setLoading(false));
    }

    const getDays = () => {
        let startOfMonth = moment(selectedDate).startOf('month');
        let endOfMonth = moment(selectedDate).endOf('month');

        let toDays = [];
        let currentDay = startOfMonth;
        let i = 1;

        while (currentDay <= endOfMonth) {
            toDays.push({
                id: i,
                date: currentDay.format('YYYY-MM-DD'),
                number: currentDay.format('DD'),
                text: currentDay.format('ddd')
            })
            currentDay = currentDay.clone().add(1, 'd');
            i++;
        }

        setDays(toDays);
    }

    const goToCurrentDay = () => {
        let currentDay = parseInt(selectedDate.split('-')[2]);
        let position = currentDay - 3;
        position = position < 0 ? 0 : position;
        setTimeout(() => {
            if (flatListRef.current) {
                flatListRef.current.scrollToIndex({ animated: true, index: position })
            }
        }, 200)
    }

    return (
        <View style={[mainStyles.wrapper, {paddingBottom: 50}]}>
            <PrivateHeader title={`Visita Virtual`} />
            <ScrollView showsVerticalScrollIndicator={false}>
                <CalendarMonth
                    selectedDate={selectedDate}
                    setSelectedDate={setSelectedDate}
                    setCalendar={setCalendar}
                    minDate={minDate}
                />
                <CalendarHorizontal
                    selectedDate={selectedDate}
                    setSelectedDate={setSelectedDate}
                    minDate={minDate}
                />
                <View style={mainStyles.privateContainer}>
                    <View style={styles.divider}></View>
                    {loading &&
                        <View style={{ flex: 1, justifyContent: "center", alignItems: "center", marginTop: 30 }}>
                            <ActivityIndicator size="large" color="#00467F" />
                        </View>
                    }
                    {/* {!loading && leads.length === 0 &&
                        <Text style={styles.textWarningScheduling}>Você não recebeu leads neste dia.</Text>
                    } */}
                    {!loading &&
                        <>
                            <TouchableOpacity disabled={true} style={styles.boxTotal} onPress={() => setShowLeads(!showLeads)}>
                                <View style={styles.totalIcon}>
                                    <CalendarLight />
                                </View>
                                <View style={styles.totalInfos}>
                                    <View>
                                        <Text style={[styles.textQtdTotal, {color: "#1B9C20"}]}>{totalAtivados} ativado com sucesso</Text>
                                        <Text style={[styles.textQtdTotal, {color: "#FF6542"}]}>{totalAguardando} aguardando ativação</Text>
                                        <Text style={[styles.textQtdTotal, {color: "#979797"}]}>{totalExpirados} lead expirado</Text>
                                    </View>
                                   {/* {showLeads ? <Less2 /> : <More />} */}
                                </View>
                            </TouchableOpacity>
                            {showLeads && leads.map((lead, index) => (
                                <View key={index}>
                                    <Lead lead={lead} navigation={navigation} />
                                    {index < leads.length - 1 &&
                                        <View style={styles.divider}></View>
                                    }
                                </View>
                            ))}
                        </>
                    }
                </View>
            </ScrollView>
            {calendar &&
                <LightboxCalendar
                    initialDate={selectedDate}
                    setSelectedDate={setSelectedDate}
                    close={() => setCalendar(false)}
                    minDate={minDate}
                />
            }
        </View>
    );
}

export default LeadsList;