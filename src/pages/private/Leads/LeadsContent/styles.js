import { StyleSheet, Dimensions, Platform } from 'react-native';

const windowWidth = Dimensions.get('window').width;

const styles = StyleSheet.create({
    wrapper: {
        justifyContent: "space-between"
    },
    contentBottom: {
        paddingTop: 20,
        paddingBottom: 40,
        shadowRadius: Platform.OS === 'ios' ? 2 : 16,
        shadowOffset: {
            width: 0,
            height: Platform.OS === 'ios' ? -2 : 12,
        },
        shadowColor: Platform.OS === 'ios' ? "#CCC" : "#000",
        elevation: Platform.OS === 'ios' ? 2 : 24,
        shadowOpacity: 1,
        backgroundColor: "#FFF",
        position: "absolute",
        bottom: 0,
        zIndex: 10
    },
    buttonLarge: {
        width: "100%"
    },
    w48: {
        width: "48%",
        flexDirection: "row"
    },
    btnTextOutlineBlue: {
        fontFamily: "Roboto-Regular",
        color: "#FFF",
        fontSize: 25,
        letterSpacing: 1,
        textAlign: "center"
    },
    ptop: {
        paddingTop: 0,
        paddingLeft: 20,
        paddingRight: 20,
        paddingBottom: 150
    },
    contentButtons: {
        flexDirection: "row",
        justifyContent: "space-between",
    },
    contentButtonsWithText: {
        flexDirection: "row",
        justifyContent: "space-between",
        position: "relative",
        paddingTop: 10
    },
    textAbs: {
        fontFamily: 'Ubuntu-Regular',
        color: "#00467F",
        fontSize: 12,
        letterSpacing: 1,
        textTransform: "uppercase",
        position: "absolute",
        top: -12,
        left: 0,
        right: 0,
        textAlign: "center",
        zIndex: 999999
    },
    input: {
        width: '75%',
        height: 50
    },
    button: {
        width: '100%',
    },
    container: {
        maxWidth: 340,
        marginLeft: ((windowWidth - 340) /2),
        marginTop: 10
    },
    buttonDate: {
        backgroundColor: "#90B0C0",
        height: 58,
        width: '100%',
        alignItems: "center",
        justifyContent: "center"
    },
    icArrow: {
        position: "absolute",
        top: 23,
        left: 25
    },
    btnTextButtonDate: {
        fontFamily: "Ubuntu-Regular",
        color: "#FFF",
        fontSize: 15,
        letterSpacing: 0.46,
        textTransform: "uppercase"
    },
    divider: {
        borderBottomWidth: 1,
        borderBottomColor: '#DADADA',
        marginTop: 10,
    },
    marginTop: {
        marginTop: 25
    },
    w90: {
        paddingLeft: 20,
        paddingRight: 20
    },
    customer: {
        flexDirection: "row",
        justifyContent: "space-between",
        alignItems: "center",
        paddingTop: 20,
        paddingBottom: 10
    },
    dFlex: {
        flexDirection: "row",
        alignItems: "center"
    },
    iconCustomer: {
        backgroundColor: "#F2F2F2",
        height: 70,
        width: 56,
        alignItems: "center",
        justifyContent: "center",
        marginRight: 6
    },
    iconUser: {
        width: 40
    },
    icArrowBtn: {
        transform: [{ rotate: "-90deg" }]
    },
    dataCustomer: {
        width: '76%'
    },
    nameCustomer: {
        fontFamily: 'Ubuntu-Regular',
        color: "#00467F",
        fontSize: 16,
        letterSpacing: 0.46
    },
    typeOrigem: {
        flexDirection: "row",
        alignItems: "center",
        marginTop: 5
    },
    productCustomer: {
        fontFamily: 'Ubuntu-Light',
        color: "#000",
        fontSize: 14,
        letterSpacing: 0.46
    },
    typeIA: {
        backgroundColor: "#1B9C20",
        paddingHorizontal: 5,
        paddingVertical: 2,
        marginLeft: 5
    },
    textIA: {
        fontFamily: 'Ubuntu-Regular',
        color: "#FFF",
        fontSize: 12,
        letterSpacing: 0.46,
        textTransform: "uppercase"
    },
    productStatusExpirado: {
        fontFamily: 'Ubuntu-Light',
        color: "#555555",
        fontSize: 13,
        letterSpacing: 0.46,
        marginTop: 2,
        marginBottom: 3
    },
    refCustomer: {
        fontFamily: 'Ubuntu-Light',
        color: "#2D719F",
        fontSize: 14,
        letterSpacing: 0.46
    },
    boxAbout: {
        marginTop: 20
    },
    boxText: {
        justifyContent: "center",
        alignItems: "center"
    },
    textDisabled: {
        fontFamily: "Ubuntu-Regular",
        color: "#828282",
        fontSize: 13,
        letterSpacing: 1
    },
    dFlexSB: {
        flexDirection: "row",
        justifyContent: "space-between",
        marginBottom: 20
    },
    w33: {
        width: "48%"
    },
    txtLeft: {
        alignItems: "flex-start",
        marginTop: 5
    },
    m20: {
        marginTop: 20
    },
    actions: {
        flexDirection: "row",
        marginTop: 10,
        marginBottom: 20,
        justifyContent: "flex-start",
    },
    boxBtn: {
        marginRight: 20
    },
    btnBorder: {
        alignItems: "center",
        justifyContent: "center",
        borderWidth: 2,
        borderColor: "#00467F",
        height: 65,
        width: 75,
        borderRadius: 10
    },
    btnBorderDisabled: {
        borderColor: "#979797"
    },
    icUser: {
        width: 55,
        marginTop: 5
    },
    icCel: {
        width: 22,
        color: "#FFF",
        marginRight: 10
    },
    icContact: {
        width: 35,
        color: "#FFF",
        marginRight: 10
    },
    typeBtn: {
        fontFamily: "Ubuntu-Regular",
        color: "#828282",
        fontSize: 11,
        letterSpacing: 1,
        textAlign: "center",
        marginTop: 5
    },
    pdBot: {
        paddingBottom: 5
    },
    vFlex: {
        flexDirection: "row",
        alignItems: "center",
        marginTop: 30,
    },
    textLead: {
        fontFamily: "Ubuntu-Regular",
        letterSpacing: 1.58,
        fontSize: 14,
        marginLeft: 15
    },
    textAlertLead: {
        fontFamily: "Ubuntu-Light",
        letterSpacing: 1,
        fontSize: 13,
        color: "#828282",
        lineHeight: 19,
        marginTop: 5,
        marginLeft: 15
    }
});

export default styles;