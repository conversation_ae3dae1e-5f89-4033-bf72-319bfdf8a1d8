import React, { useEffect, useState } from 'react';
import { TextInput, Text, View, TouchableOpacity, ActivityIndicator, SafeAreaView, Linking, Alert, useWindowDimensions } from 'react-native';
import { useIsFocused, useNavigation } from '@react-navigation/native';
import moment from 'moment';
import RenderHtml from 'react-native-render-html';

import PrivateHeader from '../../../../components/headers/PrivateHeader';

import User from '../../../../assets/svgs/Users3';
import ArrowDownGrey from '../../../../assets/svgs/ArrowDownGrey';
import ArrowLeft from '../../../../assets/svgs/ArrowLeftSmallWhite';
import Celphone from '../../../../assets/svgs/Celphone';
import CelphoneDisabled from '../../../../assets/svgs/CelphoneDisabled';
import EmailDisabled from '../../../../assets/svgs/EmailDisabled';
import Email from '../../../../assets/svgs/Email';
import Whats from '../../../../assets/svgs/Whats2';
import WhatsDisabled from '../../../../assets/svgs/WhatsDisabled';
import Alert2 from '../../../../assets/svgs/Alert2';
import Check2 from '../../../../assets/svgs/Check2';
import Bad from '../../../../assets/svgs/IconBad';

import styles from './styles';
import mainStyles from '../../../../mainStyles';
import { ScrollView } from 'react-native-gesture-handler';
import api from '../../../../services/api';
import { clearPhone } from '../../../../useful/conversions';

const LeadsContent = ({ navigation, route }) => {
    const { id } = route.params;
    const isFocused = useIsFocused();

    const [time, setTime] = useState(0);
    const [loading, setLoading] = useState(true);
    const [lead, setLead] = useState(null);
    const [status, setStatus] = useState(null);
    const [validadeDefault, setValidadeDefault] = useState(null);

    const contentWidth = useWindowDimensions().width;

    useEffect(() => {
        if(isFocused) getLead();
    }, [isFocused]);

    useEffect(() => {
        if(id) getLead();
    }, [id]);

    useEffect(() => {
        if(!lead) return;

        const interval = setInterval(() => {
            setTime(current => current + 1);
        }, 1000);

        return () => clearInterval(interval);
    }, [lead]);

    useEffect(() => {
        if(time > 1){
            expirarLead();
        }
    }, [time]);

    const expirarLead = () => {
        if(time >= lead.validade.validade_segundos && status !== 'expirado'){
            setStatus('expirado');
        }
    }

    const getLead = () => {
        setLead(null);
        setLoading(true);

        api.get(`/leads/${id}`).then(res => {
            console.log(res.data);
            setLead(res.data.lead);
            setStatus(res.data.lead.status);
            setValidadeDefault(res.data?.validade_default_min ?? null);
        }).catch(err => {
            Alert.alert('Lead não encontrado');
            navigation.navigate('LeadsList');
            console.log(err.response);
        }).then(() => setLoading(false));
    }

    const getLeadTitle = () => {
        switch (status) {
            case 'ativado':
                return 'LEAD ATIVADO';
                break;
            case 'aguardando':
                return 'LEAD AGUARDANDO ATIVAÇÃO';
                break;
            case 'expirado':
                return 'LEAD EXPIRADO';
                break;
            default:
                return 'LEAD';
                break;
        }
    }

    const celAtivo = () => {
        return lead?.cliente?.celular !== null && status !== 'expirado';
    }

    const whatsAtivo = () => {
        return lead?.cliente?.celular !== null && status !== 'expirado';
    }

    const emailAtivo = () => {
        return lead?.cliente?.email !== null && status !== 'expirado';
    }

    const ativarCliente = type => {
        setLoading(true);

        api.post(`/crm/ativar-cliente/${lead.id}/${type}`).then(res => {
            console.log(res);

            switch (type) {
                case 'whatsapp':
                    Linking.openURL(`https://wa.me/${res.data.whatsapp_phone}?text=${res.data.whatsapp_message}`);
                    break;
                case 'phone':
                    Linking.openURL(`tel:${res.data.phone_number}`);
                    break;
                case 'email':
                    Linking.openURL(`mailto:${res.data.email_address}`);
                    break;
            }
            
        }).catch(error => {
            console.log(error);
            console.log(error?.response);
            Alert.alert('Não foi possível chamar', 'Por favor, tente novamente.');
        }).then(() => getLead());
    }

    return (
        <View style={mainStyles.wrapper}>
            <PrivateHeader title={`Visita Virtual`} />
            <TouchableOpacity style={styles.buttonDate} onPress={() => navigation.goBack()}>
                <View style={styles.icArrow}>
                    <ArrowLeft />
                </View>
                <Text style={styles.btnTextButtonDate}>{getLeadTitle()}</Text>
            </TouchableOpacity>
            {loading &&
                <View style={{ flex: 1, justifyContent: "center", alignItems: "center", marginTop: 30 }}>
                    <ActivityIndicator size="large" color="#00467F" />
                </View>
            }
            {!loading && lead &&
            <>
                <ScrollView showsVerticalScrollIndicator={false}>                
                    <View style={[mainStyles.privateContainer, styles.ptop]}>
                        {status === 'ativado' &&
                            <View style={styles.pdBot}>
                                <View style={styles.vFlex}>
                                    <Check2 />
                                    <View>
                                        <Text style={[styles.textLead, {color: "#1B9C20"}]}>LEAD ATIVADO CORRETAMENTE</Text>
                                        <Text style={styles.textAlertLead}>BOAS VENDAS!</Text>
                                    </View>
                                </View>
                            </View>
                        }
                        {status === 'aguardando' &&
                            <View style={styles.pdBot}>
                                <View style={styles.vFlex}>
                                    <Alert2 />
                                    <View>
                                        <Text style={[styles.textLead, {color: "#FF6542"}]}>LEAD AGUARDANDO ATIVAÇÃO</Text>
                                        <Text style={styles.textAlertLead}>Ative o lead em no máximo {validadeDefault ?? 'alguns'} min</Text>
                                    </View>
                                </View>
                            </View>
                        }
                        {status === 'expirado' && 
                            <View style={styles.pdBot}>
                                <View style={styles.vFlex}>
                                    <Bad />
                                    <View>
                                        <Text style={[styles.textLead, {color: "#828282"}]}>LEAD EXPIRADO</Text>
                                        <Text style={styles.textAlertLead}>O lead foi transferido para outro corretor</Text>
                                    </View>
                                </View>
                            </View>
                        }
                        <View style={styles.customer}>
                            <View style={styles.dFlex}>
                                <View style={styles.iconCustomer}>
                                    <User style={styles.iconUser} />
                                </View>
                                <View style={styles.dataCustomer}>
                                    <Text style={styles.nameCustomer}>{status === 'expirado' ? 'Lead expirado'  : lead?.cliente?.nome}</Text>
                                    <View style={styles.typeOrigem}>
                                        <Text style={styles.productCustomer}>{lead?.origem}</Text>
                                        {lead?.has_ai_chat_resume &&
                                            <View style={styles.typeIA}>
                                                <Text style={styles.textIA}>Lead IA</Text>
                                            </View>
                                        }
                                    </View>
                                    {status === 'expirado' &&
                                        <Text style={styles.productStatusExpirado}>Lead não ativado em {lead.validade.duracao}</Text>
                                    }
                                </View>
                            </View>
                        </View>
                        <View style={styles.dFlexSB}>
                            <View style={styles.w33}>
                                <View style={styles.boxAbout}>
                                    <Text style={mainStyles.labelMargin}>CHEGADA DO LEAD</Text>
                                    <View style={[styles.boxText, styles.txtLeft]}>
                                        {lead.disparo.data !== null &&
                                            <Text style={styles.textDisabled}>{moment(lead.disparo.data).format('DD/MM/YYYY HH:mm')}</Text>
                                        }
                                    </View>
                                </View>
                            </View>
                            {lead.ativacao.data !== null &&
                            <View style={styles.w33}>
                                <View style={styles.boxAbout}>
                                    <Text style={mainStyles.labelMargin}>ATIVAÇÃO</Text>
                                    <View style={[styles.boxText, styles.txtLeft]}>
                                        <Text style={styles.textDisabled}>{moment(lead.ativacao.data).format('DD/MM/YYYY HH:mm')}</Text>                                  
                                    </View>
                                </View>
                            </View>
                            }
                        </View>
                        {lead?.has_ai_chat_resume &&
                            <>
                                <View style={styles.divider}></View>
                                <View style={styles.m20}>
                                    <View style={{marginTop: 5, marginBottom: 10}}>
                                        <Text style={mainStyles.labelMargin}>RESUMO DA CONVERSA COM A IA:</Text>
                                    </View>
                                    <RenderHtml 
                                        source={{ html: `<p style="color: #828282;">${lead?.ai_chat_resume ?? ''}</p>`  }} 
                                        contentWidth={contentWidth} 
                                    />
                                </View>
                            </>
                        }
                    </View>
                </ScrollView>

                {status !== 'expirado' &&
                    <View style={styles.contentBottom}>
                        <View style={[mainStyles.container, styles.contentButtonsWithText]}>
                            <Text style={styles.textAbs}>{status === 'aguardando' ? 'Ativar lead' : 'Falar com o cliente'}</Text>
                            <TouchableOpacity 
                                disabled={!whatsAtivo()}
                                style={[mainStyles.btnBlueNew, styles.w48, {backgroundColor: whatsAtivo() ? "#1B9C20" : "979797"}]} 
                                onPress={() => ativarCliente('whatsapp')}
                                
                            >
                                <Whats style={styles.icContact} />
                                <Text style={mainStyles.btnTextBlueNew}>WHATSAPP</Text>
                            </TouchableOpacity>
                            <TouchableOpacity 
                                disabled={!celAtivo()}
                                style={[mainStyles.btnBlueNew, styles.w48, {backgroundColor: celAtivo() ? "#2D719F" : "979797"}]} 
                                onPress={() => ativarCliente('phone')}
                            >
                                <Celphone style={styles.icCel} />
                                <Text style={mainStyles.btnTextBlueNew}>TELEFONE</Text>
                            </TouchableOpacity>
                        </View>
                    </View>
                }
                {status === 'expirado' &&
                    <View style={styles.contentBottom}>
                        <View style={[mainStyles.container, styles.contentButtons]}>
                            <TouchableOpacity style={[mainStyles.btnBlueNew, styles.buttonLarge]} onPress={() => navigation.goBack()}>
                                <Text style={mainStyles.btnTextBlueNew}>VOLTAR</Text>
                            </TouchableOpacity>
                        </View>
                    </View>
                }
            </>
            }
        </View>
    );
}

export default LeadsContent;