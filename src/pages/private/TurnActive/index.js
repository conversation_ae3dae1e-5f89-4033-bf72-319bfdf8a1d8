import React, { useState, useEffect } from 'react';
import { Text, View, TouchableOpacity, Alert, ActivityIndicator, BackHandler, Image, Linking } from 'react-native';

import Dialog from '../../../assets/svgs/Dialog';
import Celphone from '../../../assets/svgs/Celphone';
import CelphoneDisabled from '../../../assets/svgs/CelphoneDisabled';
import EmailDisabled from '../../../assets/svgs/EmailDisabled';
import Email from '../../../assets/svgs/Email';
import Whats from '../../../assets/svgs/Whats2';

import PrivateHeader from '../../../components/headers/PrivateHeader';

import styles from './styles';
import mainStyles from '../../../mainStyles';

import api from '../../../services/api';
import { ScrollView } from 'react-native-gesture-handler';
import { useService } from '../../../context/service';
import { clearPhone } from '../../../useful/conversions';
import { useAuth } from '../../../context/auth';

const TurnActive = ({navigation}) => {
    const [loading, setLoading] = useState(true);
    const [name, setName] = useState('');
    const [email, setEmail] = useState('');
    const [phone, setPhone] = useState('');

    const { refreshQueue, queue } = useService();
    const {user} = useAuth();

    const back = () => {
        return true;
    }

    const celAtivo = () => {
        return true;
    }

    const whatsAtivo = () => {
        return true;
    }

    const emailAtivo = () => {
        if(email !== '' && email !== null && email !== undefined){
            return true;
        }
        return false;
    }

    useEffect(() => {
        const backHandler = BackHandler.addEventListener(
            "hardwareBackPress",
            back
        );

        api.get('/fila').then(res => {
            let name = res.data.fila.atendimento.cliente.nome;
            let email = res.data.fila.atendimento.cliente.email;
            let phone = res.data.fila.atendimento.cliente.celular;
            if(name){
                setName(name);
                setEmail(email);
                setPhone(phone);
            }
            setLoading(false);
        })
        .catch(err => {
            console.log(err.response);
            setLoading(false);
        });

        return () => backHandler.remove();
    }, []);

    const handleFinish = () => {
        if(!queue?.atendimento?.inicio_confirmado_at){
            Alert.alert('Não foi possível encerrar', 'Aguardando recepção confirmar início de atendimento.');
        } else {
            setLoading(true);
            api.post('atendimento/encerrar').then(res => {
                console.log(res);
                refreshQueue();
                navigation.navigate('TurnEnd', { name, email, phone });
                setLoading(false);
            })
            .catch(err => {
                console.log(err.response);
                Alert.alert('Erro', 'Não foi possível encerrar o atendimento. Por favor, tente novamente.')
                setLoading(false);
            });
        }
    }

    return (
        <View style={mainStyles.wrapper}>
            <PrivateHeader title={`Fila`} />
            {!loading &&
                <ScrollView style={styles.container} showsVerticalScrollIndicator={false}>
                    <View style={mainStyles.bgHourCheckin}>
                        <Text style={mainStyles.textHourCheckin}>Horário do seu check in: {user.fila.checkin_horario}</Text>
                    </View>
                    {user.fila.sorteio_horario &&
                        <View>
                            <Text style={mainStyles.sorteioText}>Horário do sorteio: {user.fila.sorteio_horario}</Text>
                            <View style={styles.divider}></View>
                        </View>
                    }
                    <View style={styles.rowOptions}>
                        <View style={styles.contentOption}>
                            <Text style={styles.textStatus}>Status e posição na fila:</Text>
                            <Text style={styles.textWait}>Você está atendendo</Text>
                        </View>
                    </View>
                    <View style={styles.contentOption}>
                        <View style={styles.option}>
                            <Image source={require('../../../assets/svgs/DialogLight.png')} style={{width: 110}} />
                        </View>
                    </View>
                    <View style={styles.boxWaiting}>
                        <View style={styles.boxWaitingRow}>
                            <View style={styles.contentWaiting}>
                                <Text style={styles.textClient}>Quem é o seu cliente:</Text>                           
                                <Text style={styles.nameClient}>{name}</Text>                           
                            </View>
                        </View>
                    </View>
                    <View style={styles.divider}></View>
                    {/* <View style={styles.aboutClient}>
                        <Text style={styles.textInfo}>Nome: <Text style={styles.infoClient}>{name}</Text></Text>
                        <Text style={styles.textInfo}>E-mail: <Text style={styles.infoClient}>{email}</Text></Text>
                        <Text style={styles.textInfo}>Telefone: <Text style={styles.infoClient}>{phone}</Text></Text>
                    </View> */}
                    <View style={styles.actions}>
                        <View style={styles.boxBtn}>
                            <TouchableOpacity 
                                style={[styles.btnBorder, !celAtivo() ? styles.btnBorderDisabled : null]} 
                                onPress={() => Linking.openURL(`tel:+55${clearPhone(phone)}`)}
                                disabled={!celAtivo()}
                            >
                                {!celAtivo() &&
                                    <CelphoneDisabled style={styles.icCel} />
                                }
                                {celAtivo() &&
                                    <Celphone style={styles.icCel} />
                                }
                            </TouchableOpacity>
                            <Text style={styles.typeBtn}>Telefone</Text>
                        </View>
                        <View style={styles.boxBtn}>
                            <TouchableOpacity 
                                style={[styles.btnBorder, !whatsAtivo() ? styles.btnBorderDisabled : null]}
                                onPress={() => Linking.openURL(`https://wa.me/55${clearPhone(phone)}`)}
                                disabled={!whatsAtivo()}
                            >
                                {!whatsAtivo() &&
                                    <WhatsDisabled style={styles.icContact} />
                                }
                                {whatsAtivo() &&
                                    <Whats style={styles.icContact} />
                                }
                            </TouchableOpacity>
                            <Text style={styles.typeBtn}>WhatsApp</Text>
                        </View>
                        <View style={styles.boxBtn}>
                            <TouchableOpacity 
                                onPress={() => Linking.openURL(`mailto:${email}`)}
                                style={[styles.btnBorder, !emailAtivo() ? styles.btnBorderDisabled : null]}
                                disabled={!emailAtivo()}
                            >
                                {!emailAtivo() &&
                                    <EmailDisabled style={styles.icEmail} />
                                }
                                {emailAtivo() &&
                                    <Email style={styles.icEmail} />
                                }
                            </TouchableOpacity>
                            <Text style={styles.typeBtn}>E-mail</Text>
                        </View>
                    </View>
                    {/* <View style={styles.contentTop}>
                        <View style={ styles.contentButtons}>         
                            <TouchableOpacity style={!queue?.atendimento?.inicio_confirmado_at ? styles.buttonCenterOutlineGray : styles.buttonCenterOutlineGray} onPress={() => handleFinish()}>
                                <Text style={!queue?.atendimento?.inicio_confirmado_at ? styles.textBtnCenterOutlineGray : styles.textBtnCenterOutlineGray}>ENCERRAR ATENDIMENTO</Text>
                            </TouchableOpacity>
                        </View>
                    </View> */}
                </ScrollView>
            }
            <View style={styles.contentBottom}>
                <View style={styles.contentButtons}>         
                    <TouchableOpacity style={[mainStyles.btnBlueNew, styles.buttonCenterBlue]} onPress={() => handleFinish()}>
                        <Text style={[mainStyles.btnTextCenterBlue, styles.textBtnCenterBlue]}>FINALIZAR ATENDIMENTO</Text>
                    </TouchableOpacity>
                </View>
            </View>
            {loading &&
                <View style={{flex: 1, justifyContent: "center", alignItems: "center"}}>
                    <ActivityIndicator size="large" color="#00467F" />
                </View>
            }
        </View>
    );
}

export default TurnActive;