import { StyleSheet } from 'react-native';

const styles = StyleSheet.create({
    contentBottom: {
        // paddingTop: 20,
        // paddingBottom: 20,
        height: 70,
        shadowRadius: Platform.OS === 'ios' ? 2 : 16,
        shadowOffset: {
            width: 0,
            height: Platform.OS === 'ios' ? -2 : 12,
        },
        shadowColor: Platform.OS === 'ios' ? "#CCC" : "#000",
        elevation: Platform.OS === 'ios' ? 2 : 24,
        shadowOpacity: 1,
        backgroundColor: "#FFF",
        position: "absolute",
        bottom: 0,
        zIndex: 10,
        alignItems: "center",
        justifyContent: "center"
    },
    buttonLarge: {
        width: "100%"
    },
    pdBottom: {
        paddingBottom: 65
    },
    pageTitle: {
        color: "#00467F",
        fontFamily: "Ubuntu-Regular",
        letterSpacing: 1,
        fontSize: 16,
        marginBottom: 20,
        marginTop: 25
    },
    boxNotification: {
        flexDirection: "row",
        alignItems: "center",
        // paddingVertical: 15,
        backgroundColor: "#F2F2F2",
        minHeight: 60,
        marginBottom: 15
    },
    contentNotification: {
        flex: 1,
        paddingLeft: 10
    },
    boxIcNotification: {
        width: 55,
        flexDirection: "row",
        justifyContent: "center",
        alignItems: "center",
        backgroundColor: "#90B0C0",
        height: "100%"
    },
    boxIcNotificationNoRead: {
        width: 55,
        flexDirection: "row",
        justifyContent: "center",
        alignItems: "center",
        borderRightWidth: 1,
        borderRightColor: "#90B0C0",
        height: "100%"
    },
    icNotificationNoRead: {
        width: 25,
    },
    icNotificationRead: {
        width: 25,
        color: "#FFF"
    },
    dateNotification: {
        // textTransform: "uppercase",
        fontFamily: "Ubuntu-Light",
        fontSize: 12,
        color: "#000",
        letterSpacing: 0.46,
        marginTop: 3
    },
    titleNotification: {
        letterSpacing: 1,
        fontFamily: "Ubuntu-Regular",
        fontSize: 15
    },
    colorGestor: {
        // color: "#1B9C20"
        color: "#00467F"
    },
    colorSistema: {
        color: "#00467F"
    },
    boxIcClose: {
        width: 40,
        height: 40,
        alignItems: "center",
        justifyContent: "center"
    },
    icClose: {
        width: 20
    },
    notificationsList: {
        marginBottom: 210
    },
    btnLoadMore: {
        backgroundColor: 'transparent',
        borderWidth: 2,
        borderColor: '#4EA1CC',
        borderRadius: 5,
        height: 60,
        justifyContent: "center",
        alignItems: "center",

        marginTop: 30
    },
    textBtnLoadMore: {
        fontFamily: 'Ubuntu-Medium',
        color: '#4EA1CC',
        fontSize: Platform.OS === 'ios' ? 16 : 14,
        letterSpacing: 1,
        textAlign: "center"
    },
    expirationNotification: {
        fontFamily: 'Ubuntu-Regular',
        fontSize: 14,
        color: '#FF6542'
    },
    icStarRead: {
        color: "#FFF",
        width: 30
    },
    icStarNoRead: {
        color: "#FF6542",
        width: 30
    }
});

export default styles;