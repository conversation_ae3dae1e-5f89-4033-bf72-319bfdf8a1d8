import React, { useEffect, useState } from 'react';
import { Text, View, ScrollView, TouchableOpacity, ActivityIndicator, Linking } from 'react-native';

import moment from "moment";
import 'moment/locale/pt-br';

import Notification from '../../../assets/svgs/Notification';
import FilledNotification from '../../../assets/svgs/FilledNotification';
import Star from '../../../assets/svgs/Star';
import FilledStar from '../../../assets/svgs/FilledStar';
import Close from '../../../assets/svgs/Close';
import Trash from '../../../assets/svgs/Trash';

import Dash from 'react-native-dash';

import PrivateHeader from '../../../components/headers/PrivateHeader';

import styles from './styles';
import mainStyles from '../../../mainStyles';

import { useAuth } from '../../../context/auth';

import api from '../../../services/api';

import AlertFooter from '../../../components/AlertFooter';
import { useIsFocused } from '@react-navigation/native';

const PrivateNotifications = ({ navigation }) => {
    const isFocused = useIsFocused();
    const [loading, setLoading] = useState(true);
    const [notifications, setNotifications] = useState([]);
    const [nextPage, setNextPage] = useState(1);
    const [hasMore, setHasMore] = useState(false);
    const [toDelete, setToDelete] = useState({});
    const [confirmDelete, setConfirmDelete] = useState(false);

    const { updateHasNotification, hasNotification } = useAuth();

    useEffect(() => {
        // setNotifications([]);
        // setNextPage(1);
        // setHasMore(false);
        // getNotifications();
    }, [hasNotification]);

    useEffect(() => {
        if (isFocused) getNotifications();
    }, [isFocused]);

    const getNotifications = () => {
        setLoading(true);
        api.get(`/notificacoes?page=${nextPage}`).then(res => {
            let n = [...notifications];
            console.log(n);
            setHasMore(res.data.has_next_page);
            setNextPage(nextPage + 1);
            n.push(...res.data.notificacoes);
            console.log(n);
            setNotifications(n);
            setLoading(false);
            updateHasNotification(false);
        });
    }

    const goToNotification = (id) => {
        setLoading(true);
        api.get(`/notificacoes/${id}`).then(res => {
            let notification = res.data.notificacao;
            if (notification.url === null) {
                navigation.navigate('PrivateNotification', { id: notification.id });
            } else {
                Linking.openURL(notification.url);
                console.log(notification.url);
            }
            setLoading(false);
        });
    }

    const handleDeleteNotification = (id, index, type) => {

        setToDelete({ id, index });

        setConfirmDelete(true);
        return;
    }

    const deleteNotification = () => {
        let { id, index } = toDelete;

        let n = [...notifications];
        n.splice(index, 1);
        setNotifications(n);

        setConfirmDelete(false);
        api.delete(`/notificacoes/${id}`);

    }

    const NotificationComponent = (props) => {
        return (
            <TouchableOpacity style={styles.boxNotification} onPress={() => goToNotification(props.n.id)}>
                <View style={props.n.read_at !== null ? styles.boxIcNotification : styles.boxIcNotificationNoRead}>
                    {props.n.read_at === null && props.n.type_emitter === "gestor" &&
                        // <FilledStar />
                        <Star style={styles.icStarNoRead} />
                    }
                    {props.n.read_at !== null && props.n.type_emitter === "gestor" &&
                        <Star style={styles.icStarRead} />
                    }
                    {props.n.read_at === null && (props.n.type_emitter === "app" || props.n.type_emitter === "sistema") &&
                        <FilledNotification style={styles.icNotificationNoRead} />
                    }
                    {props.n.read_at !== null && (props.n.type_emitter === "app" || props.n.type_emitter === "sistema") &&
                        <Notification style={styles.icNotificationRead} />
                    }
                </View>

                <View style={styles.contentNotification}>
                    <Text style={[styles.titleNotification, props.n.type_emitter === "gestor" ? styles.colorGestor : styles.colorSistema]}>
                        {props.n.subject}
                    </Text>
                    <Text style={styles.dateNotification}>{`${moment(props.n.created_at).format('DD/MM/YYYY')}, às ${moment(props.n.created_at).format('HH:mm')}`}</Text>
                    {props.n.lead_expiracao !== null &&
                        <>
                            <Text style={styles.expirationNotification}>Ele expira às {`${moment(props.n.lead_expiracao).format('HH:mm')}`}</Text>
                        </>
                    }
                </View>

                <TouchableOpacity style={styles.boxIcClose} onPress={() => handleDeleteNotification(props.n.id, props.index, props.n.type_emitter)}>
                    <Trash style={styles.icClose} />
                </TouchableOpacity>
            </TouchableOpacity>
        );
    }

    return (
        <>
        <View style={[confirmDelete ? mainStyles.wrapper : mainStyles.wrapperMinHeight, styles.pdBottom]}>
            {confirmDelete &&
                <AlertFooter
                    text={
                        <Text>
                            {`Você deseja realmente `}<Text style={{ fontFamily: 'Ubuntu-Bold' }}>{`excluir esta notificação?`}</Text>
                        </Text>
                    }
                    btnText={`EXCLUIR`}
                    btnBorder={true}
                    close={() => setConfirmDelete(false)}
                    action={() => deleteNotification()}
                />
            }

            <PrivateHeader title={`Notificações`} />
            <View style={mainStyles.privateContainer}>
                <Text style={styles.pageTitle}>Últimas atualizações:</Text>
            </View>

            <ScrollView style={[mainStyles.privateContainer, styles.notificationsList]} showsVerticalScrollIndicator={false}>
                {!loading && notifications.length === 0 &&
                    <Text>Nenhuma notificação</Text>
                }
                {notifications.length > 0 && notifications.map((n, index) => (
                    <View key={index}>
                        {/* {index > 0 &&
                            <Dash dashThickness={1} dashLength={3} dashColor="#DADADA" />
                        } */}
                        <NotificationComponent n={n} index={index} />
                    </View>
                ))}
                {loading &&
                    <View style={{ flex: 1, marginTop: 30, justifyContent: "center", alignItems: "center" }}>
                        <ActivityIndicator size="large" color="#00467F" />
                    </View>
                }
                {!loading && hasMore &&
                    <TouchableOpacity style={mainStyles.btnBlueNew} onPress={() => getNotifications()}>
                        <Text style={mainStyles.btnTextBlueNew}>VER MAIS</Text>
                    </TouchableOpacity>
                }
            </ScrollView>
        </View>
        <View style={styles.contentBottom}>
            <View style={[mainStyles.container, styles.contentButtons]}>
                <TouchableOpacity
                    style={[mainStyles.btnBlueNew, styles.buttonLarge]}
                    onPress={() => navigation.goBack()}>
                    <Text style={mainStyles.btnTextBlueNew}>VOLTAR</Text>
                </TouchableOpacity>
            </View>
        </View>
        </>
    );
}

export default PrivateNotifications;