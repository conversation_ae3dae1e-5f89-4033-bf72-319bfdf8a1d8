import React, { useEffect, useState, useRef } from 'react';
import { Text, View, TouchableOpacity, ScrollView, ActivityIndicator, Alert, Image, KeyboardAvoidingView } from 'react-native';
import ImageCropPicker from 'react-native-image-crop-picker';
import ArrowLeft from '../../../assets/svgs/ArrowLeftSmallWhite';
import Camera from '../../../assets/svgs/Camera2';

import Dash from 'react-native-dash';

import PrivateHeader from '../../../components/headers/PrivateHeader';
import AlertFooterRed from '../../../components/AlertFooterRedRh';

import styles from './styles';
import mainStyles from '../../../mainStyles';

import api from '../../../services/api';
import { useAuth } from '../../../context/auth';
import { phoneMask } from '../../../useful/masks';
import { requestCameraPermission } from "../../../useful/permissions";

const RhPicture = ({navigation}) => {
    const { token, user } = useAuth();

    const [loading, setLoading] = useState(true);
    const [imagePath, setImagePath] = useState('');
    const [imageId, setImageId] = useState('');
    const [imageUpdated, setImageUpdated] = useState(false);

    useEffect(() => {
        getData();
    }, []);

    const getData = () => {
        api.get(`/rh/cadastro/foto`).then(res => {
            let { dados } = res.data;
            
            console.log('foto', dados?.selfie_arquivo?.url ?? '')
            setImageId(dados?.selfie_arquivo?.id ?? '');
            setImagePath(dados?.selfie_arquivo?.url ?? '');
            
        }).catch(error => {
            console.log(error);
            Alert.alert('Erro ao obter dados');
        }).then(() => {
            setLoading(false);
        });
    }

    const openUploadPicture = () => {
        let alertProperties;
        if(Platform.OS === 'ios'){
            alertProperties = [
                { text: "Tirar foto", onPress: () => openCamera() },
                { text: "Galeria", onPress: () => openImageLibrary() },
                { text: "Fechar" }
            ];
        } else {
            alertProperties = [
                { text: "Fechar" },
                { text: "Galeria", onPress: () => openImageLibrary() },
                { text: "Tirar foto", onPress: () => openCamera() }
            ];
        }
        Alert.alert(
            "Enviar foto",
            "Como deseja enviar sua foto?",
            alertProperties,
            { cancelable: false }
        );
        
    }

    const openImageLibrary = () => {
        ImageCropPicker.openPicker({
            width: 500,
            height: 500,
            cropping: true
        }).then(image => {
            storeImage(image);
        });
    }

    const openCamera = async() => {
        const hasPermission = await requestCameraPermission();
        if(hasPermission){
            ImageCropPicker.openCamera({
                width: 500,
                height: 500,
                cropping: true
            }).then(image => {
                storeImage(image);
            });
        }
    }

    const storeImage = image => {
        setImagePath(image.path);
        setImageUpdated(true);
    }

    const deleteImage = () => {
        setImagePath('');
    }

    const update = async () => {
        setLoading(true);

        let data = {
            selfie_id: imageUpdated ? await uploadImage() : imageId
        };

        api.put(`/rh/cadastro/foto`, data, {
            headers: {
                // 'content-type': 'application/x-www-form-urlencoded'
            }
        }).then(res => {
            Alert.alert(user.status_cadastro === 'reprovado' ? 'Dados salvos, caso tenha encerrado enviar para análise.' : 'Dados salvos e enviados para análise.');
            navigation.navigate('RhList')
        }).catch(err => {
            if(err?.response?.data?.errors){
                let errors = err.response.data.errors;
                let text = '';
                Object.keys(errors).map(error => {
                    text += `\n${errors[error][0]}`;
                });
                Alert.alert('Verifique os campos', text);
            } else {
                Alert.alert('Erro ao atualizar os dados.', 'Por favor, verifique os dados e tente novamente.');
            }
        }).then(() => {
            setLoading(false);
        });
    }

    const uploadImage = async () => {
        const data = new FormData();

        data.append('selfie', {
            uri: imagePath,
            type: 'image/jpeg',
            name: 'selfie.jpg'
        });

        return await api.post('/cadastro/upload', data)
            .then(res => {
                return res.data.id;
            })
            .catch(err =>{ 
                console.log(err)
                alert('Por favor, escolha uma imagem e tente novamente.');
                return '';
            });
    }

    return (
        <>
        <KeyboardAvoidingView style={{ flex: 1 }} behavior="padding">        
            <PrivateHeader title={`Meu Perfil`} back={() => navigation.navigate('PrivateMain')} />
            <TouchableOpacity style={styles.shift} onPress={() => navigation.navigate('RhMyData')}>
                <View style={styles.icAbs}>
                    <ArrowLeft style={styles.icArrow} />
                </View>
                <Text style={[mainStyles.textWhite]}>MINHA FOTO</Text>
            </TouchableOpacity>
            <View style={[mainStyles.privateContainer, {flex: 1}]}>
                {loading && 
                    <View style={{flex: 1, justifyContent: "center", alignItems: "center"}}>
                        <ActivityIndicator size="large" color="#00467F" />
                    </View>
                }
                {!loading &&
                <>
                    <ScrollView showsVerticalScrollIndicator={false}>
                        <View>
                            {imagePath === '' &&
                            <>
                                <View style={styles.marginTop}>
                                    <Text style={mainStyles.textAlert2}>Favor se atentar às seguintes recomendações:</Text>
                                    <View style={mainStyles.pdL}>
                                        <Text style={mainStyles.textAlert2}>• Foto somente do rosto</Text>
                                        <Text style={mainStyles.textAlert2}>• Usar um fundo branco</Text>
                                        <Text style={mainStyles.textAlert2}>• Escolha um local bem iluminado</Text>
                                    </View>
                                </View>
                                <View style={styles.divider}></View>
                                <View style={mainStyles.centerContainer}>
                                    <TouchableOpacity style={[mainStyles.btnUpload2, styles.btnMargin]} onPress={openUploadPicture}>
                                        <Camera style={mainStyles.btnIconUpload} />
                                        <Text style={[mainStyles.btnTextUpload2, mainStyles.btnTextUploadCol]}>{`CLIQUE AQUI\nPARA ENVIAR A\n`}<Text style={mainStyles.bold}>SUA FOTO</Text></Text>
                                    </TouchableOpacity>
                                </View>
                            </>
                            }
                            {imagePath !== '' &&
                            <>
                                <View style={styles.marginTop}>
                                    <Text style={mainStyles.textAlert2}>Tirar a foto somente do rosto, em um local bem iluminado e com fundo branco.</Text>
                                </View>
                                <View style={styles.divider}></View>
                                <View style={{flexDirection: "row", justifyContent: "center"}}>
                                    <View style={[mainStyles.boxProfile, mainStyles.boxPreview2, styles.boxPictureProfile]}>
                                        <View style={[mainStyles.boxPreviewImageContent2, styles.boxPreviewImageProfile]}>
                                            <Image
                                                style={mainStyles.imgProfile}
                                                source={{
                                                    uri: imagePath,
                                                    headers: {
                                                        Authorization: `Bearer ${token}`
                                                    }
                                                }}
                                            />
                                        </View>
                                        <Text style={mainStyles.deletePreview} onPress={deleteImage}>EXCLUIR</Text>
                                    </View>
                                </View>
                            </>
                            }
                        </View>
                    </ScrollView>
                    <View style={styles.contentBottom}>
                        <View style={[mainStyles.container, styles.contentButtons]}>              
                            <TouchableOpacity style={[mainStyles.btnOutlineBlue, mainStyles.buttonW48]} onPress={() => navigation.navigate('RhMyData')}>
                                <Text style={mainStyles.btnTextOutlineBlue}>VOLTAR</Text>
                            </TouchableOpacity>
                            <TouchableOpacity style={[mainStyles.btnBlueLight, styles.btnLogin, mainStyles.buttonW48]} onPress={update}>
                                <Text style={mainStyles.btnTextCenterBlueLight}>SALVAR</Text>
                            </TouchableOpacity>
                        </View>
                    </View>
                </>
                }
            </View>
        </KeyboardAvoidingView>
    </>
    );
}

export default RhPicture;