import { StyleSheet, Dimensions, Platform } from 'react-native';

const windowWidth = Dimensions.get('window').width;

const styles = StyleSheet.create({
    wrapper: {
        justifyContent: "space-between"
    },
    contentBottom: {
        paddingTop: 25,      
        paddingBottom: 40,
        shadowRadius: 2,
        shadowOffset: {
            width: 0,
            height: -2,
        },
        shadowColor: '#000000',
        elevation: 2,
        shadowOpacity: 1.0,
        backgroundColor: "#FFF"
    },
    customerContainer: {
        paddingTop: 25,
        paddingLeft: 40,
        paddingRight: 40,
        paddingBottom: 50,
    },
    contentButtons: {
        flexDirection: "row",
        justifyContent: "space-between",
        marginTop: 5,
        marginBottom: 10
    },
    button: {
        width: '100%',
    },
    container: {
        maxWidth: 340,
        marginLeft: ((windowWidth - 340) /2),
        marginTop: 10
    },
    contentOption: {
        flexDirection: "row",
        alignItems: "center",
        marginTop: 25,
        marginBottom: 25
    },  
    textStatusBold: {
        fontFamily: "Roboto-Bold",
        color: "#00467F",
        fontSize: 15,
        letterSpacing: 1,
        marginLeft: 15
    },
    inputText: {
        backgroundColor: "#FFF",
        width: 125,
        paddingLeft: 13,
    },
    buttonCenterBlue: {
        width: 70,
        height: 50,
        marginTop: 10,
        borderRadius: 5,
        marginLeft: 15
    },
    textBtnCenterBlue: {
        fontSize: 20
    }, 
    rowCenter: {
        flexDirection: "row",
        justifyContent: "space-between",
        alignItems: 'center',
        marginBottom: 15,
        marginTop: 10
    },
    rowCenterMargin: {
        flexDirection: "row",
        alignItems: 'center',
        marginVertical: 20
    },
    optionTextBold: {
        fontFamily: "Roboto-Bold",
        fontSize: 12,
        letterSpacing: 0.7,
        color: "#4ea1cc",
        lineHeight: 18
    },
    boxFlex: {
        flexDirection: "row",
        justifyContent: "space-between",
        alignItems: "center",
        borderColor: "#DADADA",
        borderTopWidth: 1,
        paddingTop: 20,
        paddingBottom: 20
    },
    boxFlexColumn: {
        flexDirection: "column"
    },
    boxIconWidth: {
        width: 40,
        flexDirection: "row",
        justifyContent: "center"
    },
    boxFlexWithText: {
        flexDirection: "row"
    },
    textBoxFlex: {
        fontSize: 14,
        letterSpacing: 1,
        width: 230,
        marginLeft: 10
    },
    textBoxFlexBold: {
        fontWeight: "bold",
        color: "#00467F",
        fontSize: 20,
        letterSpacing: 1,
        width: 230,
        marginLeft: 10,
        marginTop: -3,
        marginBottom: 10
    },
    textAboutProcessBold: {
        fontWeight: "bold",
        color: "#828282",
        fontSize: 12,
        letterSpacing: 0.5,
        width: 280,
        marginTop: 5,
        marginBottom: 5
    },
    textAboutProcess: {
        fontWeight: "normal",
        color: "#828282",
        fontSize: 12,
        letterSpacing: 0.5,
        marginLeft: 5
    },
    btnTextOutlineBlue: {
        fontFamily: "Roboto-Bold",
        color: "#00467F",
        fontSize: 25,
        fontWeight: "bold",
        letterSpacing: 1,
        textAlign: "center"
    },
    boxFlexColumn: {
        flexDirection: "column"
    },
    textSearch: {
        fontFamily: "Roboto-Regular",
        color: "#4EA1CC",
        fontSize: 17,
        letterSpacing: 1,
    },
    searchBox: {
        flexDirection: "row",
        justifyContent: "center",
        borderColor: "#DADADA",
        borderBottomWidth: 1,
        paddingBottom: 30
    },
    input: {
        borderWidth: 2,
        borderColor: "#4EA1CC",
        borderRadius: 5,
        width: 150,
        height: 55,
        paddingLeft: 15,
        paddingRight: 15,
        fontSize: 25,
        textAlign: "center"
    },
    buttonDate: {
        backgroundColor: "#90B0C0",
        height: 58,
        width: '100%',
        alignItems: "center",
        justifyContent: "center"
    },
    icArrow: {
        position: "absolute",
        top: 23,
        left: 25
    },
    btnTextButtonDate: {
        fontFamily: "Ubuntu-Regular",
        color: "#FFF",
        fontSize: 15,
        letterSpacing: 0.46,
        textTransform: "uppercase"
    },
    divider: {
        borderBottomWidth: 1,
        borderBottomColor: '#DADADA',
        marginTop: 20,
        marginBottom: 20
    },
    marginTop: {
        marginTop: 25
    },
    w90: {
        paddingLeft: 20,
        paddingRight: 20
    },
    selectMargin: {
        marginBottom: 20
    },
    infosAbout: {
        borderWidth: 1,
        borderColor: "#E0E0E0",
        paddingTop: 10,
        paddingLeft: 20,
        paddingRight: 20,
        paddingBottom: 10,
        minHeight: 150
    },
    textInfosAbout: {
        fontFamily: "Ubuntu-Regular",
        color: "#828282",
        fontSize: 14,
        letterSpacing: 1,
        lineHeight: 16
    },
    textAlert: {
        fontFamily: "Ubuntu-Regular",
        color: "#FF6542",
        marginBottom: 30,
        marginTop: 5,
        letterSpacing: 1,
        lineHeight: 20
    }
});

export default styles;