import React, { useEffect, useRef, useState } from 'react';
import { TextInput, Text, View, TouchableOpacity, ActivityIndicator, SafeAreaView, Alert, KeyboardAvoidingView } from 'react-native';
import { useIsFocused, useNavigation } from '@react-navigation/native';
import moment from 'moment';
import { Calendar, CalendarList, Agenda } from 'react-native-calendars';

import PrivateHeader from '../../../../components/headers/PrivateHeader';
import Select from '../../../../components/Select';

import CalendarLight from '../../../../assets/svgs/CalendarLight';
import ArrowDownGrey from '../../../../assets/svgs/ArrowDownGrey';
import ArrowLeft from '../../../../assets/svgs/ArrowLeftSmallWhite';

import styles from './styles';
import mainStyles from '../../../../mainStyles';
import { ScrollView } from 'react-native-gesture-handler';
import api from '../../../../services/api';
import { useAuth } from '../../../../context/auth';
import SelectBorder from '../../../../components/SelectBorder';
import LightboxCalendar from '../../../../components/LightboxCalendar';
import { cpfMask, phoneMask } from '../../../../useful/masks';

const MyCustomerEdit = ({ navigation, route }) => {
    const scrollViewRef = useRef();
    const { id } = route.params;

    useEffect(() => {
        loadProducts();
        getCustomer();
    }, []);

    const [loadingProducts, setLoadingProducts] = useState(true);
    const [loadingCustomer, setLoadingCustomer] = useState(true);

    const [cpfCorretorOutro, setCpfCorretorOutro] = useState(true);
    const [hasCpf, setHasCpf] = useState(false);

    const [plantao, setPlantao] = useState('');
    const [nome, setNome] = useState('');
    const [cpf, setCpf] = useState('');
    const [email, setEmail] = useState('');
    const [telefone, setTelefone] = useState('');
    const [anotacoes, setAnotacoes] = useState('');

    const [products, setProducts] = useState([]);

    const getCustomer = () => {
        api.get(`/crm/clientes/${id}/edit`)
            .then(res => {
                console.log('cliente', res.data);
                setNome(res.data.cliente.nome);
                setTelefone(res.data.cliente.celular);
                setEmail(res.data.cliente.email);
                setCpf(res.data.cliente.cpf ?? '');
                setAnotacoes(res.data.cliente.informacoes_adicionais ?? '');
                setPlantao(res.data.cliente?.plantao?.id ?? '');

                setHasCpf(res.data.cliente.has_cpf);
            })
            .catch(err => {
                Alert.alert('Ops, ocorreu algum erro!');
            })
            .then(() => setLoadingCustomer(false));
    }

    const loadProducts = () => {
        setLoadingProducts(true);

        api.get('/crm/plantoes')
            .then(res => {
                let productOptions = [];
                res.data.plantoes.map(plantao => productOptions.push({ label: plantao.imovel_nome, value: plantao.id }));
                setProducts(productOptions);
            })
            .catch(err => {
                Alert.alert('Ops, ocorreu algum erro!');
            })
            .then(() => setLoadingProducts(false));
    }

    const update = () => {
        validate();
        setLoadingCustomer(true);

        const data = { 
            nome, 
            celular: telefone, 
            email, 
            informacoes_adicionais: anotacoes,
            plantao_id: plantao
        };

        if(!hasCpf){
            data.cpf = cpf;
        }

        api.put(`/crm/clientes/${id}/edit`, data)
            .then(res => {
                Alert.alert('Cliente atualizado', 'Seu cliente foi atualizado com sucesso.');
                navigation.navigate('MyCustomerList');
            }).catch(err => {
                console.log(err.response);
                if (err?.response?.data?.errors) {
                    let errors = err.response.data.errors;
                    let text = '';
                    Object.keys(errors).map(error => {
                        text += `\n- ${errors[error][0]}`;
                    });
                    Alert.alert('Verifique os campos', text);
                } else {
                    Alert.alert('Ocorreu um erro', 'Por favor, tente novamente');
                }
            })
            .then(() => setLoadingCustomer(false));
    }

    const validate = () => {
        if (!plantao) Alert.alert('Por favor, selecione um plantão');

        return true;
    }

    return (
        <KeyboardAvoidingView style={{ flex: 1 }} behavior="padding" >
            <PrivateHeader title={`Clientes`} />
            <ScrollView ref={scrollViewRef} contentContainerStyle={{ flexGrow: 1 }} keyboardShouldPersistTaps="handled" showsVerticalScrollIndicator={false}>
                <TouchableOpacity style={styles.buttonDate} onPress={() => navigation.goBack()}>
                    <View style={styles.icArrow}>
                        <ArrowLeft />
                    </View>
                    <Text style={styles.btnTextButtonDate}>DADOS PESSOAIS</Text>
                </TouchableOpacity>
                {(loadingProducts || loadingCustomer) &&
                    <View style={{ flex: 1, justifyContent: "center", alignItems: "center", marginTop: 100 }}>
                        <ActivityIndicator size="large" color="#00467F" />
                    </View>
                }
                {!loadingProducts && !loadingCustomer &&
                    <View style={[mainStyles.privateContainer, styles.customerContainer]}>
                        <Text style={mainStyles.labelMargin}>NOME:</Text>
                        <TextInput
                            placeholderTextColor="#BDBDBD"
                            underlineColorAndroid="transparent"
                            style={mainStyles.inputBorder}
                            value={nome}
                            // onFocus={scroll}
                            autoCapitalize="words"
                            onChangeText={value => setNome(value)}
                        />
                        <Text style={mainStyles.labelMargin}>CELULAR:</Text>
                        <TextInput
                            placeholderTextColor="#BDBDBD"
                            underlineColorAndroid="transparent"
                            style={mainStyles.inputBorder}
                            value={telefone}
                            // onFocus={scroll}
                            keyboardType="phone-pad"
                            onChangeText={value => setTelefone(phoneMask(value))}
                        />
                        <Text style={mainStyles.labelMargin}>E-MAIL:</Text>
                        <TextInput
                            placeholderTextColor="#BDBDBD"
                            underlineColorAndroid="transparent"
                            style={mainStyles.inputBorder}
                            autoCapitalize="none"
                            keyboardType="email-address"
                            value={email}
                            onChangeText={value => setEmail(value)}
                        // onFocus={scroll}
                        />
                        <Text style={mainStyles.labelMargin}>CPF:</Text>
                        <TextInput
                            editable={!hasCpf}
                            selectTextOnFocus={!hasCpf}
                            placeholderTextColor="#BDBDBD"
                            underlineColorAndroid="transparent"
                            style={[mainStyles.inputBorder, hasCpf ? mainStyles.inputDisabled : null ]}
                            value={cpf}
                            // onFocus={scroll}
                            keyboardType="phone-pad"
                            onChangeText={value => setCpf(cpfMask(value))}
                        />
                        <View style={styles.selectMargin}>
                            <Text style={mainStyles.labelMargin}>PRODUTO</Text>
                            <SelectBorder
                                options={products} 
                                value={plantao} 
                                onChange={value => setPlantao(value)} 
                                placeholder="TODOS" 
                            />
                        </View>
                        <Text style={mainStyles.labelMargin}>INFORMAÇÕES ADICIONAIS:</Text>
                        <TextInput
                            placeholderTextColor="#BDBDBD"
                            underlineColorAndroid="transparent"
                            style={[mainStyles.inputBorder, { height: 170, textAlignVertical: 'top', paddingTop: 15 }]}
                            multiline={true}
                            numberOfLines={10}
                            onFocus={() => scrollViewRef.current.scrollTo({ x: 0, y: 300, animated: true })}
                            value={anotacoes}
                            onChangeText={value => setAnotacoes(value)}
                        />
                        <TouchableOpacity style={[mainStyles.btnBlueNew, styles.button]} onPress={update}>
                            <Text style={mainStyles.btnTextBlueNew}>SALVAR</Text>
                        </TouchableOpacity>
                    </View>
                }
            </ScrollView>
        </KeyboardAvoidingView>
    );
}

export default MyCustomerEdit;