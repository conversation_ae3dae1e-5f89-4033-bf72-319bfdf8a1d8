import { StyleSheet, Dimensions, Platform } from 'react-native';

const windowWidth = Dimensions.get('window').width;

const styles = StyleSheet.create({
    wrapper: {
        justifyContent: "space-between"
    },
    contentBottom: {
        paddingTop: 25,      
        paddingBottom: 40,
        shadowRadius: 2,
        shadowOffset: {
            width: 0,
            height: -2,
        },
        shadowColor: '#000000',
        elevation: 2,
        shadowOpacity: 1
    },
    ptop: {
        paddingTop: 25
    },
    contentButtons: {
        flexDirection: "row",
        justifyContent: "space-between"
    },
    input: {
        width: '75%',
        height: 50
    },
    button: {
        width: '100%',
    },
    container: {
        maxWidth: 340,
        marginLeft: ((windowWidth - 340) /2),
        marginTop: 10
    },
    buttonDate: {
        backgroundColor: "#90B0C0",
        height: 58,
        width: '100%',
        alignItems: "center",
        justifyContent: "center"
    },
    icArrow: {
        position: "absolute",
        top: 23,
        left: 25
    },
    btnTextButtonDate: {
        fontFamily: "Ubuntu-Regular",
        color: "#FFF",
        fontSize: 15,
        letterSpacing: 0.46,
        textTransform: "uppercase"
    },
    divider: {
        borderBottomWidth: 1,
        borderBottomColor: '#DADADA',
        marginTop: 10,
    },
    marginTop: {
        marginTop: 25
    },
    w90: {
        paddingLeft: 20,
        paddingRight: 20
    },
    customer: {
        flexDirection: "row",
        justifyContent: "space-between",
        alignItems: "center",
        paddingTop: 20,
        paddingBottom: 20,
        borderBottomColor: "#DADADA",
        borderBottomWidth: 1
    },
    dFlex: {
        flexDirection: "row",
        alignItems: "center"
    },
    iconCustomer: {
        backgroundColor: "#F2F2F2",
        height: 70,
        width: 56,
        alignItems: "center",
        justifyContent: "center",
        marginRight: 6
    },
    iconUser: {
        width: 40
    },
    icArrowBtn: {
        transform: [{ rotate: "-90deg" }]
    },
    dataCustomer: {
        width: '76%'
    },
    nameCustomer: {
        fontFamily: 'Ubuntu-Regular',
        color: "#00467F",
        fontSize: 16,
        letterSpacing: 0.46
    },
    productCustomer: {
        fontFamily: 'Ubuntu-Light',
        color: "#000",
        fontSize: 14,
        letterSpacing: 0.46,
        marginTop: 3,
        marginBottom: 3
    },
    refCustomer: {
        fontFamily: 'Ubuntu-Light',
        color: "#2D719F",
        fontSize: 14,
        letterSpacing: 0.46
    },
    m20: {
        marginTop: 20
    },
    actions: {
        flexDirection: "row",
        marginTop: 10,
        marginBottom: 20,
        justifyContent: "flex-start",
    },
    boxBtn: {
        marginRight: 20
    },
    btnBorder: {
        alignItems: "center",
        justifyContent: "center",
        borderWidth: 1,
        borderColor: "#00467F",
        height: 65,
        width: 75,
        borderRadius: 10
    },
    btnBorderDisabled: {
        borderColor: "#979797"
    },
    icUser: {
        width: 55,
        marginTop: 5
    },
    icCel: {
        width: 30,
    },
    icContact: {
        width: 45,
    },
    icContrato: {
        marginRight: -7
    },
    typeBtn: {
        fontFamily: "Ubuntu-Regular",
        color: "#828282",
        fontSize: 11,
        letterSpacing: 1,
        textAlign: "center",
        marginTop: 5
    },
    textAlert: {
        fontFamily: "Ubuntu-Regular",
        color: "#452323",
        fontSize: 13,
        letterSpacing: 1,
        lineHeight: 20,
        textAlign: "left",
        marginTop: 25,
        marginBottom: 15
    },
    textAlertStrong: {
        color: "#FF6542"
    },
    tag: {
        backgroundColor: "#828282",
        height: 20,
        width: 50,
        alignItems: "center",
        justifyContent: "center",
        borderRadius: 12,
        position: "absolute",
        top: -12,
        zIndex: 9
    },
    textTag: {
        color: "#FFF",
        letterSpacing: 0.5,
        fontFamily: "Ubuntu-Regular",
        textAlign: "center",
        fontSize: 10.2
    },
    textWarningRegular: {
        fontFamily: "Ubuntu-Regular",
        color: "#FF6542",
        fontSize: 13,
        letterSpacing: 1
    },
    textWarning: {
        fontFamily: "Ubuntu-Light",
        color: "#452323",
        fontSize: 13,
        letterSpacing: 1,
        lineHeight: 18,
        marginBottom: 15
    },
    rowLine: {
        flexDirection: 'row',
        marginTop: 15,
        marginBottom: 70
    },
    boxLine: {
        shadowColor: "#000",
        shadowOffset: {
            width: 0,
            height: 2,
        },
        shadowOpacity: 0.25,
        shadowRadius: 3.84,
        elevation: 5,
        backgroundColor: "#FFF",
        borderRadius: 10,
        maxWidth: 260,
        minHeight: 110,
        padding: 14,
        marginRight: 18,
        marginBottom: 10
    },
    dateLine: {
        fontFamily: "Ubuntu-Regular",
        fontSize: 15,
        color: "#4EA1CC",
        letterSpacing: 1,
        marginBottom: 20
    },
    titleLine: {
        fontFamily: "Ubuntu-Light",
        fontSize: 14,
        color: "#979797",
        letterSpacing: 1,
        marginBottom: 5
    },
    icDisabled: {
        color: "#828282"
    }
});

export default styles;