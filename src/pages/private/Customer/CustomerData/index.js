import React, { useEffect, useState } from 'react';
import { TextInput, Text, View, TouchableOpacity, Alert, ActivityIndicator, Linking, SafeAreaView } from 'react-native';
import { useIsFocused, useNavigation } from '@react-navigation/native';
import moment from 'moment';

import PrivateHeader from '../../../../components/headers/PrivateHeader';

import User from '../../../../assets/svgs/Users3';
import ArrowDownGrey from '../../../../assets/svgs/ArrowDownGrey';
import ArrowLeft from '../../../../assets/svgs/ArrowLeftSmallWhite';
import Celphone from '../../../../assets/svgs/Celphone';
import CelphoneDisabled from '../../../../assets/svgs/CelphoneDisabled';
import EmailDisabled from '../../../../assets/svgs/EmailDisabled';
import Email from '../../../../assets/svgs/Email';
import Whats from '../../../../assets/svgs/Whats2';
import SearchDarkBlue from '../../../../assets/svgs/SearchDarkBlue';
import Data from '../../../../assets/svgs/Data';
import Contrato from '../../../../assets/svgs/Contrato';

import styles from './styles';
import mainStyles from '../../../../mainStyles';
import { ScrollView } from 'react-native-gesture-handler';
import api from '../../../../services/api';
import { useAuth } from '../../../../context/auth';
import WhatsDisabled from '../../../../assets/svgs/WhatsDisabled';
import { clearPhone } from '../../../../useful/conversions';

const MyCustomerData = ({ navigation, route }) => {
    const isFocused = useIsFocused();

    const { getUser, user } = useAuth();

    const { clienteId } = route.params;
    const [cliente, setCliente] = useState([]);

    useEffect(() => {
        if(isFocused){
            getUser();
            loadClient();
        }
    }, [isFocused]);

    const [loading, setLoading] = useState(true);
    const loadClient = () => {

        api.get(`/crm/clientes/${clienteId}`)
            .then(res => {
                setCliente(res.data.cliente);
            })
            .catch(err => {
                Alert.alert('Ops, ocorreu algum erro.');
            })
            .then(() => {
                setLoading(false);
            });
    }

    const celAtivo = () => {
        return true;
        // return cliente.celular !== null && !cliente.has_cpf_corretor_outro;
    }

    const whatsAtivo = () => {
        return true;
        // return cliente.celular !== null && !cliente.has_cpf_corretor_outro;
    }

    const emailAtivo = () => {
        return true;
        // return cliente.email !== null && !cliente.has_cpf_corretor_outro;
    }

    return (

        <View style={mainStyles.wrapper}>
            {!loading &&
                <>
                    <PrivateHeader title={`Clientes`} />
                    <TouchableOpacity style={styles.buttonDate} onPress={() => navigation.goBack()}>
                        <View style={styles.icArrow}>
                            <ArrowLeft />
                        </View>
                        <Text style={styles.btnTextButtonDate}>MEUS CLIENTES</Text>
                    </TouchableOpacity>
                    <ScrollView showsVerticalScrollIndicator={false}>
                        <View style={[mainStyles.privateContainer, styles.ptop]}>
                            <View style={styles.divider}></View>
                            <View style={styles.customer}>
                                <View style={styles.dFlex}>
                                    <View style={styles.iconCustomer}>
                                        <User style={styles.iconUser} />
                                    </View>
                                    <View style={styles.dataCustomer}>
                                        <Text style={styles.nameCustomer}>{cliente.nome}</Text>
                                        <Text style={styles.productCustomer}>{cliente?.plantao?.imovel_nome}</Text>
                                        <Text style={styles.refCustomer}>{cliente.origem_txt}</Text>
                                    </View>
                                    {/* <ArrowDownGrey style={{ transform: [{ rotate: "-90deg" }] }} /> */}
                                </View>
                            </View>
                            <View style={{ marginTop: 30 }}>
                                <Text style={mainStyles.labelMargin}>Contato</Text>

                                <View style={styles.actions}>
                                    <View style={styles.boxBtn}>
                                        <TouchableOpacity 
                                            style={[styles.btnBorder, !celAtivo() ? styles.btnBorderDisabled : null]} 
                                            onPress={() => Linking.openURL(`tel:+55${clearPhone(cliente.celular)}`)}
                                            disabled={!celAtivo()}
                                        >
                                            {!celAtivo() &&
                                                <CelphoneDisabled style={styles.icCel} />
                                            }
                                            {celAtivo() &&
                                                <Celphone style={styles.icCel} />
                                            }
                                        </TouchableOpacity>
                                        <Text style={styles.typeBtn}>Telefone</Text>
                                    </View>
                                    <View style={styles.boxBtn}>
                                        <TouchableOpacity 
                                            style={[styles.btnBorder, !whatsAtivo() ? styles.btnBorderDisabled : null]}
                                            onPress={() => Linking.openURL(`https://wa.me/55${clearPhone(cliente.celular)}`)}
                                            disabled={!whatsAtivo()}
                                        >
                                            {!whatsAtivo() &&
                                                <WhatsDisabled style={styles.icContact} />
                                            }
                                            {whatsAtivo() &&
                                                <Whats style={styles.icContact} />
                                            }
                                        </TouchableOpacity>
                                        <Text style={styles.typeBtn}>WhatsApp</Text>
                                    </View>
                                    <View style={styles.boxBtn}>
                                        <TouchableOpacity 
                                            onPress={() => Linking.openURL(`mailto:${cliente.email}`)}
                                            style={[styles.btnBorder, !emailAtivo() ? styles.btnBorderDisabled : null]}
                                            disabled={!emailAtivo()}
                                        >
                                            {!emailAtivo() &&
                                                <EmailDisabled style={styles.icEmail} />
                                            }
                                            {emailAtivo() &&
                                                <Email style={styles.icEmail} />
                                            }
                                        </TouchableOpacity>
                                        <Text style={styles.typeBtn}>E-mail</Text>
                                    </View>
                                </View>
                            </View>
                            <View style={styles.divider}></View>
                            <View style={styles.m20}>
                                <Text style={mainStyles.labelMargin}>Sobre o cliente</Text>
                                <View style={styles.actions}>
                                    <View style={styles.boxBtn}>
                                        <TouchableOpacity style={styles.btnBorder} onPress={() => navigation.navigate('MyCustomerEdit', { id: cliente.id })}>
                                            <Data />
                                        </TouchableOpacity>
                                        <Text style={styles.typeBtn}>{`Dados\npessoais`}</Text>
                                    </View>
                                    <View style={styles.boxBtn}>
                                        <TouchableOpacity disabled style={[styles.btnBorder, styles.btnBorderDisabled]} onPress={() => Linking.openURL('https://cury.net/analise-de-credito/corretor')}>
                                            <SearchDarkBlue style={styles.icContact} />
                                            <View style={styles.tag}>
                                                <Text style={styles.textTag}>BREVE</Text>
                                            </View>
                                        </TouchableOpacity>
                                        <Text style={styles.typeBtn}>{`Análise de\ncrédito`}</Text>
                                    </View>
                                    <View style={styles.boxBtn}>
                                        <TouchableOpacity style={[styles.btnBorder, styles.btnBorderDisabled]} disabled>
                                            <Contrato style={styles.icContrato} />
                                            <View style={styles.tag}>
                                                <Text style={styles.textTag}>BREVE</Text>
                                            </View>
                                        </TouchableOpacity>
                                        <Text style={styles.typeBtn}>Contrato</Text>
                                    </View>
                                </View>
                            </View> 
                            <View style={styles.divider}></View>
                            {/* <View style={styles.m20}>
                                <Text style={mainStyles.labelMargin}>Linha do tempo</Text>
                            </View> */}
                           
                        </View>
                        {/* <ScrollView style={styles.rowLine} horizontal={true} showsHorizontalScrollIndicator={false} showsVerticalScrollIndicator={false}>
                            <View style={styles.boxLine}>
                                <Text style={styles.dateLine}>13/02/22</Text>
                                <Text style={styles.titleLine}>Lead cadastrado manualmente pelo corretor</Text>
                            </View>
                            <View style={styles.boxLine}>
                                <Text style={styles.dateLine}>13/02/22</Text>
                                <Text style={styles.titleLine}>Lead cadastrado manualmente pelo corretor</Text>
                            </View>
                            <View style={styles.boxLine}>
                                <Text style={styles.dateLine}>13/02/22</Text>
                                <Text style={styles.titleLine}>Lead cadastrado manualmente pelo corretor</Text>
                            </View>
                        </ScrollView> */}
                    </ScrollView>
                </>
            }
            {loading &&
                <View style={{ flex: 1, justifyContent: "center", alignItems: "center", marginTop: 30 }}>
                    <ActivityIndicator size="large" color="#00467F" />
                </View>
            }

        </View>
    );
}

export default MyCustomerData;