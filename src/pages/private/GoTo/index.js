import React, { useEffect, useState } from 'react';
import { Text, View, TouchableOpacity, ScrollView, ActivityIndicator, Linking } from 'react-native';

import Shifts from '../../../assets/svgs/Shifts';
import Waze from '../../../assets/svgs/Waze';
import Car from '../../../assets/svgs/Car';
import Maps from '../../../assets/svgs/Maps';
import Arrow from '../../../assets/svgs/ArrowDownGrey';
import ArrowLeft from '../../../assets/svgs/ArrowLeftSmallWhite';

import PrivateHeader from '../../../components/headers/PrivateHeader';

import styles from './styles';
import mainStyles from '../../../mainStyles';

import api from '../../../services/api';

import GetLocation from 'react-native-get-location'

const GoTo = ({route,navigation}) => {

    const [loading, setLoading] = useState(true);
    const [shifts, setShifts] = useState([]);
    const [notLocation, setNotLocation] = useState(false);

    useEffect(() => {
        getShifts();
    }, [])

    const getShifts = async () => {
        const geolocation = await GetLocation.getCurrentPosition({
                enableHighAccuracy: true,
                timeout: 15000,
            })
            .then(location => {
                return location;
            })
            .catch(error => {
                // const { code, message } = error;
                // console.warn(code, message);
                setNotLocation(true);
                setLoading(false);
            });
        
            if(geolocation){
                api.get('/plantao', {
                        params: {
                            latitude: geolocation.latitude,
                            longitude: geolocation.longitude
                        }
                    })
                    .then(res => {
                        const shifts = res.data.plantoes;
                        setShifts(shifts);
                        setLoading(false);
                    })
                    .catch(err => {
                        console.log(err.response);
                    });
            }
    }
    return (
        <View style={mainStyles.wrapper}>
            <PrivateHeader title={`Plantões`} back={() => navigation.navigate('PrivateShifts')} />
            <TouchableOpacity style={styles.buttonDate} onPress={() => navigation.goBack()}>
                <View style={styles.icArrowTop}>
                    <ArrowLeft />
                </View>
                <Text style={styles.btnTextButtonDate}>{route.params.name}</Text>
            </TouchableOpacity>
            {/* <View style={[mainStyles.privateContainer]}>
                
                <View style={styles.menu}>
                    <TouchableOpacity style={styles.menuItem} onPress={() => navigation.navigate('PrivateShifts')}>
                        <Shifts style={styles.icShifts} />
                        <Text style={styles.textShifts}>{`LISTA DE\nPLANTÕES`}</Text>
                    </TouchableOpacity>
                    <View style={styles.separator}></View>
                    <TouchableOpacity style={styles.menuItem}>
                        <Map style={styles.icMap} />
                        <Text style={styles.textMap}>{`MAPA DE\nPLANTÕES`}</Text>
                    </TouchableOpacity>
                </View>
            </View> */}
            <ScrollView style={styles.bgWhite} showsVerticalScrollIndicator={false}>
                <View style={[styles.container]}>
                    <View style={styles.titleShift}>
                        <Text style={styles.titleDrive}>Dirigir até o plantão:</Text>
                        <View style={styles.nameShift}>
                            <Text style={styles.textShift}>{route.params.name}</Text>
                        </View>
                    </View>

                    <TouchableOpacity onPress={() => {
                        const latitude = route.params.lat;
                        const longitude = route.params.long;
                        Linking.openURL(`https://waze.com/ul?ll=${latitude},${longitude}&navigate=yes`);
                    }}>
                        <View style={styles.btnOpenMap}>
                            <View style={styles.icBtn}>
                                <Waze style={{width: 35}} />
                            </View>
                            <Text style={styles.btnTextOpenMap}>Abrir Waze</Text>
                            <View style={styles.icAbs}>
                                <Arrow style={styles.icArrow} />
                            </View>
                        </View>
                    </TouchableOpacity>
                    <TouchableOpacity onPress={() => {
                        const latitude = route.params.lat;
                        const longitude = route.params.long;
                        
                        const daddr = `${latitude},${longitude}`;
                        Linking.openURL(`https://maps.google.com/maps?daddr=${daddr}`);
                    }}>
                        <View style={styles.btnOpenMap}>
                            <View style={styles.icBtn}>
                                <Maps style={styles.icMaps} />
                            </View>
                            <Text style={styles.btnTextOpenMap}>Abrir Google Maps</Text>
                            <View style={styles.icAbs}>
                                <Arrow style={styles.icArrow} />
                            </View>
                        </View>
                    </TouchableOpacity>

                    <Text style={styles.text}>{`É necessário ter o aplicativo escolhido instalado no seu celular`}</Text>
                </View>
            </ScrollView>
            <View style={styles.contentBottom}>
                <View style={[mainStyles.container, styles.contentButtons]}>
                    <TouchableOpacity style={[mainStyles.btnBlueNew, styles.buttonLarge]} onPress={() => navigation.navigate('PrivateShifts')}>
                        <Text style={mainStyles.btnTextBlueNew}>VOLTAR</Text>
                    </TouchableOpacity>
                </View>
            </View>
        </View>
    );
}

export default GoTo;