import { StyleSheet, Dimensions, Platform } from 'react-native';

const windowWidth = Dimensions.get('window').width;

const styles = StyleSheet.create({
    container: {
        width: windowWidth * 0.9,
        marginLeft: windowWidth * 0.05,
    },
    contentBottom: {
        paddingTop: 20,
        paddingBottom: 40,
        shadowRadius: Platform.OS === 'ios' ? 2 : 16,
        shadowOffset: {
            width: 0,
            height: Platform.OS === 'ios' ? -2 : 12,
        },
        shadowColor: Platform.OS === 'ios' ? "#CCC" : "#000",
        elevation: Platform.OS === 'ios' ? 2 : 24,
        shadowOpacity: 1,
        backgroundColor: "#FFF",
        position: "absolute",
        bottom: 0,
        zIndex: 10
    },
    buttonLarge: {
        width: "100%"
    },
    menu: {
        flexDirection: "row",
        backgroundColor: "#FFF",
        alignItems: 'center',
        justifyContent: 'center',
        borderRadius: 12,
        shadowColor: "#000",
        shadowOffset: {
            width: 0,
            height: 9,
        },
        shadowOpacity: 0.1,
        shadowRadius: 11.95,
        elevation: 12,
        marginTop: 15,
        paddingLeft: 30,
        paddingRight: 30,
        paddingBottom: 15,
        paddingTop: 15
    },
    menuItem: {
        flex: 1,
        flexDirection: "row",
        justifyContent: "center",
        alignItems: "center"
    },
    separator: {
        marginLeft: 25,
        marginRight: 25,
        width: 1,
        height: '70%',
        backgroundColor: '#BDBDBD'
    },
    icShifts: {
        width: 35,
        marginRight: 12
    },
    icMap: {
        width: 30,
        marginRight: 12
    },
    textShifts: {
        fontFamily: 'Ubuntu-Regular',
        fontSize: Platform.OS === 'ios' ? 16 : 12,
        letterSpacing: 1,
        color: "#4EA1CC"
    },
    textMap: {
        fontFamily: 'Ubuntu-Regular',
        fontSize: Platform.OS === 'ios' ? 16 : 12,
        letterSpacing: 1,
        color: "#828282"
    },
    bgWhite: {
        flex: 1,
        backgroundColor: "#FFF",
        marginTop: 0,
        paddingTop: 25
    },
    titleDrive: {
        fontFamily: "Ubuntu-Regular",
        fontSize: 14,
        color: "#00467F",
        letterSpacing: 1,
        marginBottom: 8
    },
    titleShift: {
        marginBottom: 25
    },
    nameShift: {
        flexDirection: "row",
        alignItems: "center"
    },
    icCar: {
        color: "#FFF",
        width: 40,
        marginRight: 15
    },
    textShift: {
        fontFamily: "Ubuntu-Medium",
        fontSize: 20,
        color: "#00467F",
        letterSpacing: 1,
    },
    btnOpenMap: {
        backgroundColor: "#F2F2F2",
        flexDirection: "row",
        alignItems: "center",
        paddingRight: 25,
        height: 65,
        marginBottom: 10,
        position: "relative"
    },
    icBtn: {
        backgroundColor: "#90B0C0",
        height: 65,
        width: 55,
        justifyContent: "center",
        alignItems: "center"
    },
    btnTextOpenMap: {
        fontSize: 15,
        fontFamily: "Ubuntu-Regular",
        color: "#00467F",
        letterSpacing: 0.46,
        marginLeft: 10
    },
    icAbs: {
        position: "absolute",
        right: 15
    },
    icMaps: {
        width: 26
    },
    icArrow: {
        transform: [{rotate: "-90deg"}]
    },
    text: {
        fontFamily: "Ubuntu-Regular",
        fontSize: 13,
        color: "#828282",
        letterSpacing: 1,
        marginBottom: 25,
        marginTop: 15
    },
    btnBack: {
        backgroundColor: 'transparent',
        borderWidth: 2,
        borderColor: '#F2F2F2',
        borderRadius: 5,
        height: 60,
        justifyContent: "center",
        alignItems: "center",
        marginBottom: 30
    },
    btnTextBack: {
        fontFamily: 'Ubuntu-Medium',
        color: '#F2F2F2',
        fontSize: 18,
        letterSpacing: 1
    },
    buttonDate: {
        backgroundColor: "#90B0C0",
        height: 58,
        width: '100%',
        alignItems: "center",
        justifyContent: "center"
    },
    icArrowTop: {
        position: "absolute",
        top: 23,
        left: 25
    },
    btnTextButtonDate: {
        fontFamily: "Ubuntu-Regular",
        color: "#FFF",
        fontSize: 15,
        letterSpacing: 0.46,
        textAlign: "center",
        width: "80%"
    },
});

export default styles;