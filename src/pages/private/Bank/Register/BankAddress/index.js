import React, { useEffect } from 'react';
import { Text, View, TouchableOpacity, TextInput, Alert } from 'react-native';

import PrivateHeader from '../../../../../components/headers/PrivateHeader';

import styles from './styles';
import mainStyles from '../../../../../mainStyles';
import { ScrollView } from 'react-native-gesture-handler';
import api from '../../../../../services/api';
import ArrowLeftSmall from '../../../../../assets/svgs/ArrowLeftSmall';
import { useBank } from '../../../../../context/bank';
import { cepMask } from '../../../../../useful/masks';
import Select from '../../../../../components/Select';

const states = [
    { label: 'AC', value: 'AC' },
    { label: 'AL', value: 'AL' },
    { label: 'AP', value: 'AP' },
    { label: 'AM', value: 'AM' },
    { label: 'BA', value: 'BA' },
    { label: 'CE', value: 'CE' },
    { label: 'DF', value: 'DF' },
    { label: 'ES', value: 'ES' },
    { label: 'GO', value: 'GO' },
    { label: 'MA', value: 'MA' },
    { label: 'MS', value: 'MS' },
    { label: 'MT', value: 'MT' },
    { label: 'MG', value: 'MG' },
    { label: 'PA', value: 'PA' },
    { label: 'PB', value: 'PB' },
    { label: 'PR', value: 'PR' },
    { label: 'PE', value: 'PE' },
    { label: 'PI', value: 'PI' },
    { label: 'RJ', value: 'RJ' },
    { label: 'RN', value: 'RN' },
    { label: 'RS', value: 'RS' },
    { label: 'RO', value: 'RO' },
    { label: 'RR', value: 'RR' },
    { label: 'SC', value: 'SC' },
    { label: 'SP', value: 'SP' },
    { label: 'SE', value: 'SE' },
    { label: 'TO', value: 'TO' }
];

const BankAddress = ({ navigation, route }) => {
    const { bankAccount, setBankAccount } = useBank();

    useEffect(() => {
        getAddress();
    }, [bankAccount.address_postal_code]);

    const getAddress = () => {
        if(bankAccount.address_postal_code.length > 8){
            api.post('/cadastro/endereco', {
                cep: bankAccount.address_postal_code
            }).then(res => {
                let neigh = res.data.bairro;
                neigh = typeof neigh === 'string' || neigh instanceof String ? neigh : '';
                
                let city = res.data.cidade;
                city = typeof city === 'string' || city instanceof String ? city : '';

                let state = res.data.uf;
                state = typeof state === 'string' || state instanceof String ? state : '';

                let addr = res.data.logradouro;
                addr = typeof addr === 'string' || addr instanceof String ? addr : '';

                setBankAccount({
                    ...bankAccount,
                    address_street: addr,
                    address_neighborhood: neigh,
                    address_city: city,
                    address_state: state,
                })
            }).catch(err => console.log(err));
        }
    }

    const next = () => {
        const { address_postal_code, address_street, address_number, address_neighborhood, address_city, address_state } = bankAccount;

        if (!address_postal_code || !address_street || !address_number || !address_neighborhood || !address_city || !address_state) {
            Alert.alert('Atenção', 'Preencha todos os campos obrigatórios do endereço.');
            return;
        }

        const cepRegex = /^\d{5}-\d{3}$/;
        if (!cepRegex.test(address_postal_code)) {
            Alert.alert('Erro', 'O CEP deve estar no formato 99999-999.');
            return;
        }

        navigation.navigate('BankSelfie');
    }

    return (
        <View style={mainStyles.wrapper}>
            <PrivateHeader title={`Endereço`} />
            <ScrollView showsVerticalScrollIndicator={false}>                
                <View style={[mainStyles.privateContainer, styles.ptop]}>
                    <Text style={[styles.textTerms, styles.textMedium]}>Confirme abaixo seu endereço</Text>
                    <View>
                        <Text style={mainStyles.label}>CEP</Text>                                
                        <TextInput  
                            underlineColorAndroid="transparent"  
                            style={mainStyles.inputText}
                            value={bankAccount.address_postal_code}
                            onChangeText={text => setBankAccount({...bankAccount, address_postal_code: cepMask(text)})}
                            keyboardType="phone-pad"
                        />
                    </View>
                    <View>
                        <Text style={mainStyles.label}>Logradouro (Rua, Avenida, etc)</Text>                                
                        <TextInput  
                            underlineColorAndroid="transparent"  
                            style={mainStyles.inputText}
                            value={bankAccount.address_street}
                            onChangeText={text => setBankAccount({...bankAccount, address_street: text})}
                        />
                    </View>
                    <View>
                        <Text style={mainStyles.label}>Número</Text>                                
                        <TextInput  
                            underlineColorAndroid="transparent"  
                            style={mainStyles.inputText}
                            value={bankAccount.address_number}
                            onChangeText={text => setBankAccount({...bankAccount, address_number: text})}
                        />
                    </View>
                    <View>
                        <Text style={mainStyles.label}>Complemento</Text>                                
                        <TextInput  
                            underlineColorAndroid="transparent"  
                            style={mainStyles.inputText}
                            value={bankAccount.address_complement}
                            onChangeText={text => setBankAccount({...bankAccount, address_complement: text})}
                        />
                    </View>
                    <View>
                        <Text style={mainStyles.label}>Bairro</Text>                                
                        <TextInput  
                            underlineColorAndroid="transparent"  
                            style={mainStyles.inputText}
                            value={bankAccount.address_neighborhood}
                            onChangeText={text => setBankAccount({...bankAccount, address_neighborhood: text})}
                        />
                    </View>
                    <View>
                        <Text style={mainStyles.label}>Cidade</Text>                                
                        <TextInput  
                            underlineColorAndroid="transparent"  
                            style={mainStyles.inputText}
                            value={bankAccount.address_city}
                            onChangeText={text => setBankAccount({...bankAccount, address_city: text})}
                        />
                    </View>
                    <View>
                        <Text style={mainStyles.label}>Estado</Text>
                        <Select 
                            placeholder="Estado" 
                            options={states} 
                            value={bankAccount.address_state} 
                            onChange={value => setBankAccount({...bankAccount, address_state: value})} 
                        />
                    </View>
                </View>
            </ScrollView>
            <View style={styles.contentBottom}>
                <View style={[mainStyles.container, styles.contentButtonsWithText]}>
                    <View style={styles.w48}>
                        <TouchableOpacity 
                            style={[mainStyles.btnCenterOutlineBlueDarkNewBorder, styles.btnWidth]} 
                            onPress={() => navigation.goBack()}                                
                        >
                            <ArrowLeftSmall />
                        </TouchableOpacity>
                        <View style={styles.boxSteps}>
                            <View style={styles.steps}></View>
                            <View style={styles.steps}></View>
                            <View style={styles.steps}></View>
                            <View style={[styles.steps, {backgroundColor: "#2D719F"}]}></View>
                            <View style={styles.steps}></View>
                        </View>
                    </View>
                    <TouchableOpacity 
                        style={[mainStyles.btnBlueNew, styles.w48]} 
                        onPress={next}
                    >
                        <Text style={mainStyles.btnTextBlueNew}>AVANÇAR</Text>
                    </TouchableOpacity>
                </View>
            </View>
        </View>
    );
}

export default BankAddress;