import React, { useEffect, useState } from 'react';
import { Text, View, TouchableOpacity, ActivityIndicator, Alert, useWindowDimensions } from 'react-native';
import { useIsFocused } from '@react-navigation/native';

import PrivateHeader from '../../../../../components/headers/PrivateHeader';

import Bank from '../../../../../assets/svgs/Bank';

import styles from './styles';
import mainStyles from '../../../../../mainStyles';
import { ScrollView } from 'react-native-gesture-handler';
import api from '../../../../../services/api';
import Checkbox from '../../../../../components/Checkbox';
import GetLocation from 'react-native-get-location';
import { useBank } from '../../../../../context/bank';
import { convertDateToBrazil } from '../../../../../useful/conversions';

const BankInitAccount = ({ navigation, route }) => {
    const isFocused = useIsFocused();
    const [loading, setLoading] = useState(true);
    const [accepted, setAccepted] = useState(false);
    const [termsUrl, setTermsUrl] = useState(null);

    const { bankAccount, setBankAccount } = useBank();

    const contentWidth = useWindowDimensions().width;

    useEffect(() => {
        getAccount();
    }, [isFocused]);

    const getAccount = () => {
        api.get('/bank-account').then(res => {
            console.log(res);
            switch (res.data.status) {
                case 'initial':
                    setTermsUrl(res.data.terms_url);
                    setLoading(false);
                    break;
                case 'terms_accepted':
                    navigation.navigate('BankDocuments');
                    const user = res.data.user;
                    const address = user.address;
                    setBankAccount({
                        ...bankAccount,
                        name: user.name ?? '',
                        document_number: user.document_number ?? '',
                        email: user.email ?? '',
                        cellphone: user.cellphone ?? '',
                        birthdate: user.birthdate ? convertDateToBrazil(user.birthdate) : '',
                        mother_name: user.mother_name ?? '',
                        nationality: user.nationality ?? '',
                        address_street: address.street,
                        address_number: address.number,
                        address_complement: address.complement,
                        address_neighborhood: address.neighborhood,
                        address_city: address.city,
                        address_state: address.state,
                        address_postal_code: address.postal_code,
                    });
                    break;
                default:
                    navigation.navigate('BankAnalysis', { data: res.data });
                    break;
            }
        }).catch(() => {
            Alert.alert('Erro ao obter a conta');
            navigation.navigate('PrivateMain');
        });
    }

    const next = async () => {
        setLoading(true);

        const geolocation = await GetLocation.getCurrentPosition({
                enableHighAccuracy: true,
                timeout: 15000,
            })
            .then(location => {
                return location;
            })
            .catch(error => {
                Alert.alert(
                    'Não foi possível obter sua localização', 
                    'Para seguirmos, precisamos da sua localização. Por favor, confira as configurações do aplicativo'
                );
                setLoading(false);
                return null;
            });

        if(!geolocation);

        api.post('/bank-account/accept-terms', {
            longitude: geolocation.longitude,
            latitude: geolocation.latitude
        }).then(() => {
            navigation.navigate('BankDocuments');
        }).catch(error => {
            console.log(error);
            setLoading(false);
            Alert.alert('Não foi possível seguir', 'Tente novamente mais tarde ou entre em contato conosco.');
        })
    }

    return (
        <View style={mainStyles.wrapper}>
            <PrivateHeader title={`Cury Bank`} />
            {loading &&
                <View style={{ flex: 1, justifyContent: "center", alignItems: "center", marginTop: 30 }}>
                    <ActivityIndicator size="large" color="#00467F" />
                </View>
            }
            {!loading && 
                <>
                    <ScrollView showsVerticalScrollIndicator={false}>                
                        <View style={[mainStyles.privateContainer, styles.ptop]}>
                            <View style={styles.boxCenter}>
                                <Bank />
                                <Text style={[styles.textTerms, styles.title]}>{`Corretor, crie sua conta no\nCury Bank!`}</Text>
                                <Text style={[styles.textTerms, styles.subtitle]}>{`Recebimentos e transferências\nintegrados, tudo em um só lugar.`}</Text>
                                <Text style={styles.textTerms}>{`Para prosseguir, é preciso ler e aceitar\n`} nossos <Text style={{fontWeight: "bold"}}>Termos e condições.</Text></Text>
                                <TouchableOpacity 
                                    style={[mainStyles.btnCenterOutlineBlueDarkNewBorder, styles.w80]}  
                                onPress={() => navigation.navigate('BankTerms', { termsUrl })}
                                >
                                    <Text style={mainStyles.btnTextOutlineBlueBorder}>Acessar termos e condições</Text>
                                </TouchableOpacity>
                                <View style={{ marginHorizontal: 15, marginVertical: 30}}>
                                    <Checkbox
                                        text={<Text>Li e aceito os Termos e condições para criação da minha conta <Text style={{ fontWeight: 'bold'}}>Cury Bank.</Text></Text>}
                                        isChecked={accepted}
                                        onPress={() => setAccepted(!accepted)}
                                    />
                                </View>
                            </View>
                        </View>
                    </ScrollView>
                    <View style={styles.contentBottom}>
                        <View style={mainStyles.container}>
                            <TouchableOpacity 
                                disabled={!accepted}
                                style={[mainStyles.btnBlueNew, accepted ? null :  mainStyles.btnDisabled]} 
                                onPress={next}
                            >
                                <Text style={mainStyles.btnTextBlueNew}>INICIAR CRIAÇÃO DA CONTA</Text>
                            </TouchableOpacity>
                        </View>
                    </View>
                </>
            }
        </View>
    );
}

export default BankInitAccount;