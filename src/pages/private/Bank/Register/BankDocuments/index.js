import React, { useEffect, useState } from 'react';
import { Text, View, TouchableOpacity, Alert, BackHandler, ActivityIndicator } from 'react-native';

import PrivateHeader from '../../../../../components/headers/PrivateHeader';
import Select from '../../../../../components/Select';

import styles from './styles';
import mainStyles from '../../../../../mainStyles';
import { ScrollView } from 'react-native-gesture-handler';
import api from '../../../../../services/api';
import ArrowLeftSmall from '../../../../../assets/svgs/ArrowLeftSmall';
import { useBank } from '../../../../../context/bank';
import BankImageInput from '../../../../../components/BankImageInput';
import BankPdfInput from '../../../../../components/BankPdfInput';

const documentsOptions = [
    { label: 'RG (imagem frente e verso)', value: 'rg_front_back' },
    { label: 'CNH (imagem frente e verso)', value: 'cnh_front_back' },
    { label: 'CNH Digital (arquivo PDF)', value: 'cnh_digital' }
]

const BankDocuments = ({ navigation, route }) => {
    const [loading, setLoading] = useState(false);
    const { bankAccount, setBankAccount } = useBank();

    useEffect(() => {
        const backAction = () => true; // true = bloqueia o back
        const backHandler = BackHandler.addEventListener(
            'hardwareBackPress',
            backAction
        );

        return () => backHandler.remove();
    }, []);

    const next = () => {
        switch (bankAccount.document_type ) {
            case 'rg_front_back':
                if(!bankAccount.bank_document_rg_front){
                    Alert.alert('Faltam documentos', 'Envie a frente do seu RG.');
                    return;
                }
                if(!bankAccount.bank_document_rg_back){
                    Alert.alert('Faltam documentos', 'Envie o verso do seu RG.');
                    return;
                }
                break;
        
            case 'cnh_front_back':
                if(!bankAccount.bank_document_cnh_front){
                    Alert.alert('Faltam documentos', 'Envie a frente da sua CNH.');
                    return;
                }
                if(!bankAccount.bank_document_rg_back){
                    Alert.alert('Faltam documentos', 'Envie o verso da sua CNH.');
                    return;
                }
                break;

            case 'cnh_digital':
                if(!bankAccount.bank_document_cnh_digital){
                    Alert.alert('Faltam documentos', 'Envie sua CNH digital.');
                    return;
                }
                break;
        }

        if(!bankAccount.bank_document_proof_of_residence){
            Alert.alert('Faltam documentos', 'Envie seu comprovante de residência.');
            return;
        }

        setLoading(true);

        api.post(`/bank-account/ocr/documents`, {
            document_type: bankAccount.document_type
        }).then(() => {
            navigation.navigate('BankMainData');
        }).catch(error => {
            console.log(error.response.data.message);
            Alert.alert('Verifique os documentos e tente novamente', error?.response?.data?.message ?? '');
        }).finally(() => {
            setTimeout(() => {
                setLoading(false);
            }, 500);
        });
    }

    return (
        <View style={mainStyles.wrapper}>
            <PrivateHeader title={`Documentos`} />
            {loading &&
                <View style={{ flex: 1, justifyContent: "center", alignItems: "center", marginTop: 30 }}>
                    <ActivityIndicator size="large" color="#00467F" />
                </View>
            }
            {!loading && 
                <>
                    <ScrollView showsVerticalScrollIndicator={false}>                
                        <View style={[mainStyles.privateContainer, styles.ptop]}>
                            <Text style={[styles.textTerms, styles.subtitle]}>Nas próximas etapas, precisaremos que nos forneça alguns documentos e informações pessoais para a criação de sua conta.</Text>
                            <View>
                                <Text style={mainStyles.label}>Tipo do documento pessoal:</Text>
                                <Select
                                    placeholder="Selecione o tipo de arquivo"
                                    borderColor="#2D719F"
                                    color="#1A374D"
                                    placeholderColor="#1A374D"
                                    fontFamily="Ubuntu-Regular"
                                    options={documentsOptions} 
                                    value={bankAccount.document_type ?? null}
                                    onChange={value => setBankAccount({...bankAccount, document_type: value})} 
                                />
                            </View>
                            <View style={styles.boxPictures}>
                                {bankAccount.document_type === 'rg_front_back' &&
                                    <View style={styles.boxPicturesFrontBack}>
                                        <View style={styles.boxW48}>
                                            <BankImageInput label={'Foto da frente'} type={`rg_front`} />
                                        </View>
                                        <View style={styles.boxW48}>
                                            <BankImageInput label={'Foto do verso'} type={`rg_back`} />
                                        </View>
                                    </View>
                                }
                                {bankAccount.document_type === 'cnh_front_back' &&
                                    <View style={styles.boxPicturesFrontBack}>
                                        <View style={styles.boxW48}>
                                            <BankImageInput label={'Foto da frente'} type={`cnh_front`} />
                                        </View>
                                        <View style={styles.boxW48}>
                                            <BankImageInput label={'Foto do verso'} type={`cnh_back`} />
                                        </View>
                                    </View>
                                }
                                {bankAccount.document_type === 'cnh_digital' &&
                                    <BankPdfInput label={'PDF do documento'} type={`cnh_digital`} />
                                }
                            </View>
                            <View>
                                <Text style={mainStyles.label}>Comprovante de Residência</Text>
                                <BankImageInput type={`proof_of_residence`} />
                            </View>
                        </View>
                    </ScrollView>
                    <View style={styles.contentBottom}>
                        <View style={[mainStyles.container, styles.contentButtonsWithText]}>
                            <View style={styles.w48}>
                                <TouchableOpacity 
                                    style={[mainStyles.btnCenterOutlineBlueDarkNewBorder, styles.btnWidth]} 
                                    onPress={() => navigation.navigate('PrivateMain')}                                
                                >
                                    <ArrowLeftSmall />
                                </TouchableOpacity>
                                <View style={styles.boxSteps}>
                                    <View style={[styles.steps, {backgroundColor: "#2D719F"}]}></View>
                                    <View style={styles.steps}></View>
                                    <View style={styles.steps}></View>
                                    <View style={styles.steps}></View>
                                    <View style={styles.steps}></View>
                                </View>
                            </View>
                            <TouchableOpacity 
                                style={[mainStyles.btnBlueNew, styles.w48]} 
                                onPress={next}
                            >
                                <Text style={mainStyles.btnTextBlueNew}>AVANÇAR</Text>
                            </TouchableOpacity>
                        </View>
                    </View>
                </>
            }
        </View>
    );
}

export default BankDocuments;