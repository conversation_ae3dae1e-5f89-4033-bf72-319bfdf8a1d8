import React from 'react';
import { Text, View, TouchableOpacity } from 'react-native';

import PrivateHeader from '../../../../../components/headers/PrivateHeader';

import styles from './styles';
import mainStyles from '../../../../../mainStyles';
import { ScrollView } from 'react-native-gesture-handler';
import ArrowLeftSmall from '../../../../../assets/svgs/ArrowLeftSmall';
import { useBank } from '../../../../../context/bank';


const BankMainData = ({ navigation, route }) => {
    const { bankAccount } = useBank();

    const next = () => navigation.navigate('BankComplementaryData');
    
    return (
        <View style={mainStyles.wrapper}>
            <PrivateHeader title={`Dados pessoais`} />
            <ScrollView showsVerticalScrollIndicator={false}>                
                <View style={[mainStyles.privateContainer, styles.ptop]}>
                    <Text style={[styles.textTerms, styles.textMedium]}>Confirme abaixo seus dados pessoais cadastrados no RH da Cury</Text>
                    <View>
                        <Text style={mainStyles.label}>Nome completo</Text>
                        <Text style={styles.textData}>{bankAccount.name}</Text>
                    </View>
                    <View style={styles.divider}></View>
                    <View>
                        <Text style={mainStyles.label}>CPF</Text>
                        <Text style={styles.textData}>{bankAccount.document_number}</Text>
                    </View>
                    <View style={styles.divider}></View>
                    <View>
                        <Text style={mainStyles.label}>E-mail</Text>
                        <Text style={styles.textData}>{bankAccount.email}</Text>
                    </View>
                    <View style={styles.divider}></View>
                    <View>
                        <Text style={mainStyles.label}>Telefone celular</Text>
                        <Text style={styles.textData}>{bankAccount.cellphone}</Text>
                    </View>
                    <View style={styles.boxCentered}>
                        <TouchableOpacity 
                            style={[mainStyles.btnCenterOutlineBlueDarkNewBorder, styles.w80]}  
                            onPress={() => navigation.navigate('RhList')}
                        >
                            <Text style={mainStyles.btnTextOutlineBlueBorder}>Solicitar atualização no RH</Text>
                        </TouchableOpacity>
                    </View>
                </View>
            </ScrollView>
            <View style={styles.contentBottom}>
                <View style={[mainStyles.container, styles.contentButtonsWithText]}>
                    <View style={styles.w48}>
                        <TouchableOpacity 
                            style={[mainStyles.btnCenterOutlineBlueDarkNewBorder, styles.btnWidth]} 
                            onPress={() => navigation.goBack()}                                
                        >
                            <ArrowLeftSmall />
                        </TouchableOpacity>
                        <View style={styles.boxSteps}>
                            <View style={styles.steps}></View>
                            <View style={[styles.steps, {backgroundColor: "#2D719F"}]}></View>
                            <View style={styles.steps}></View>
                            <View style={styles.steps}></View>
                            <View style={styles.steps}></View>
                        </View>
                    </View>
                    <TouchableOpacity 
                        style={[mainStyles.btnBlueNew, styles.w48]} 
                        onPress={next}
                    >
                        <Text style={mainStyles.btnTextBlueNew}>AVANÇAR</Text>
                    </TouchableOpacity>
                </View>
            </View>
        </View>
    );
}

export default BankMainData;