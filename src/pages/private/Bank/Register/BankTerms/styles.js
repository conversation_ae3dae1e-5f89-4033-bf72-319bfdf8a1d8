import { StyleSheet, Dimensions, Platform } from 'react-native';

const windowWidth = Dimensions.get('window').width;

const styles = StyleSheet.create({
    wrapper: {
        justifyContent: "space-between"
    },
    contentBottom: {
        paddingTop: 15,
        paddingBottom: 15,
        shadowRadius: Platform.OS === 'ios' ? 2 : 16,
        shadowOffset: {
            width: 0,
            height: Platform.OS === 'ios' ? -2 : 12,
        },
        shadowColor: Platform.OS === 'ios' ? "#CCC" : "#000",
        elevation: Platform.OS === 'ios' ? 2 : 24,
        shadowOpacity: 1,
        backgroundColor: "#FFF"
    },
    w48: {
        width: "48%",
        flexDirection: "row"
    },
    ptop: {
        paddingTop: 25,
        paddingBottom: 130
    },
    contentButtons: {
        flexDirection: "row",
        justifyContent: "space-between",
    },
    contentButtonsWithText: {
        flexDirection: "row",
        justifyContent: "space-between",
        position: "relative",
    },
    container: {
        maxWidth: 340,
        marginLeft: ((windowWidth - 340) /2),
        marginTop: 10
    },
    textTerms: {
        fontFamily: "Ubuntu-Regular",
        letterSpacing: 0.46,
        fontSize: 14,
        color: "#1A374D",
        lineHeight: 17
    },
    titleBold: {
        fontFamily: "Ubuntu-Bold",
    },
    textItalic: {
        fontStyle: "italic"
    },
    containerPdf: {
        flex: 1,
        width: Dimensions.get('window').width,
        height: Dimensions.get('window').height,
        margin: 0,
        padding: 0
    },
});

export default styles;