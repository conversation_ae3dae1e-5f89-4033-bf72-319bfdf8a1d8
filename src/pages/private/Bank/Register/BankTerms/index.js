import React from 'react';
import { Text, View, TouchableOpacity } from 'react-native';

import PrivateHeader from '../../../../../components/headers/PrivateHeader';

import styles from './styles';
import mainStyles from '../../../../../mainStyles';
import Pdf from 'react-native-pdf';

const BankTerms = ({ navigation, route }) => {
    const { termsUrl } = route.params;

    return (
        <View style={mainStyles.wrapper}>
            <PrivateHeader title={`Termos e condições`} />    
            <View style={{ flex: 1}}>
                <Pdf
                    trustAllCerts={false}
                    source={{
                        uri: termsUrl, 
                        cache: false
                    }}
                    onError={(error) => {
                        console.log(error);
                    }}
                    style={{...styles.containerPdf}} 
                />
            </View>         
            <View style={styles.contentBottom}>
                <View style={mainStyles.container}>
                    <TouchableOpacity 
                        style={mainStyles.btnBlueNew} 
                        onPress={() => navigation.goBack()}
                    >
                        <Text style={mainStyles.btnTextBlueNew}>VOLTAR</Text>
                    </TouchableOpacity>
                </View>
            </View>
        </View>
    );
}

export default BankTerms;