import React, { useEffect } from 'react';
import { Text, View, TouchableOpacity, BackHandler } from 'react-native';

import PrivateHeader from '../../../../../components/headers/PrivateHeader';

import Check from '../../../../../assets/svgs/Check4';
import Alert2 from '../../../../../assets/svgs/Alert3';

import styles from './styles';
import mainStyles from '../../../../../mainStyles';
import { ScrollView } from 'react-native-gesture-handler';

const BankAnalysis = ({ navigation, route }) => {
    const { data } = route.params;

    useEffect(() => {
        const backAction = () => true; // true = bloqueia o back
        const backHandler = BackHandler.addEventListener(
            'hardwareBackPress',
            backAction
        );

        return () => backHandler.remove();
    }, []);

    return (
        <View style={mainStyles.wrapper}>
            <PrivateHeader title={`Cury Bank`} />
            <ScrollView showsVerticalScrollIndicator={false}>                
                <View style={[mainStyles.privateContainer, styles.ptop]}>
                    <View style={styles.boxCenter}>
                        <Text style={[styles.textTerms, styles.title]}>Todas as informações para criação de sua conta Cury Bank foram fornecidas!</Text>
                        <Check style={styles.icCheck} />
                        <Text style={[styles.textTerms, styles.subtitle]}>{`Enquanto isso, estamos\nanalisando sua solicitação e te avisaremos assim que tudo\nestiver pronto.`}</Text>
                        {/* <Text style={styles.textTerms}>{`Não se esqueça de assinar digitalmente o\ncontrato enviado em seu e-mail.`}</Text> */}
                        <View style={styles.boxAlert}>
                            {data.status_info.type === 'info' &&
                                <View style={styles.icAlert}>
                                    <Alert2 style={{width: 35}} />
                                </View>
                            }
                            {data.status_info.type === 'error' &&
                                <View style={styles.icAlert}>
                                    <Alert2 style={{width: 35}} />
                                </View>
                            }
                            <Text style={styles.textBoxAlert}>{data.status_info.message}</Text>
                        </View>
                    </View>
                </View>
            </ScrollView>
            <View style={styles.contentBottom}>
                <View style={mainStyles.container}>
                    <TouchableOpacity 
                        style={mainStyles.btnBlueNew} 
                        onPress={() => navigation.navigate('PrivateMain')}
                    >
                        <Text style={mainStyles.btnTextBlueNew}>VOLTAR</Text>
                    </TouchableOpacity>
                </View>
            </View>
        </View>
    );
}

export default BankAnalysis;