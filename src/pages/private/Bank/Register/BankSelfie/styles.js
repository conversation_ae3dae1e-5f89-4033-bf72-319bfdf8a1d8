import { StyleSheet, Dimensions, Platform } from 'react-native';

const windowWidth = Dimensions.get('window').width;
const windowHeight = Dimensions.get('window').height;

const styles = StyleSheet.create({
    wrapper: {
        justifyContent: "space-between"
    },
    contentBottom: {
        paddingTop: 15,
        paddingBottom: 15,
        shadowRadius: Platform.OS === 'ios' ? 2 : 16,
        shadowOffset: {
            width: 0,
            height: Platform.OS === 'ios' ? -2 : 12,
        },
        shadowColor: Platform.OS === 'ios' ? "#CCC" : "#000",
        elevation: Platform.OS === 'ios' ? 2 : 24,
        shadowOpacity: 1,
        backgroundColor: "#FFF",
        position: "absolute",
        bottom: 0,
        zIndex: 10
    },
    w48: {
        width: "48%",
        flexDirection: "row"
    },
    ptop: {
        paddingTop: 25,
    },
    contentButtons: {
        flexDirection: "row",
        justifyContent: "space-between",
    },
    container: {
        maxWidth: 340,
        marginLeft: ((windowWidth - 340) /2),
        marginTop: 10
    },
    divider: {
        borderBottomWidth: 1,
        borderBottomColor: '#DADADA',
        marginTop: 10,
    },
    title: {
        fontSize: 22,
        lineHeight: 25,
        marginTop: 30
    },
    subtitle: {
        fontSize: 18,
        lineHeight: 22,
        marginTop: 15,
        marginBottom: 25
    },
    textMedium: {
        fontFamily: "Ubuntu-Medium",
        fontSize: 15,
        marginBottom: 20
    },
    textTerms: {
        fontFamily: "Ubuntu-Regular",
        letterSpacing: 0.46,
        fontSize: 14,
        color: "#1A374D",
        lineHeight: 17
    },
    titleBold: {
        fontFamily: "Ubuntu-Bold",
    },
    textItalic: {
        fontStyle: "italic"
    },
    w80: {
        width: "80%",
        marginTop: 20
    },
    boxCenter: {
        justifyContent: "center",
        alignItems: "center",
        textAlign: "center",
        marginTop: 50
    },
    btnUpload: {
        backgroundColor: "#F4F8FA",
        height: 78,
        justifyContent: "center",
        alignItems: "center"
    },
    boxPictures: {
        minHeight: 100,
        marginBottom: 30
    },
    labelSmaller: {
        fontSize: 13,
        marginTop: 5
    },
    boxPicturesFrontBack: {
        flexDirection: "row",
        justifyContent: "space-between"
    },
    boxW48: {
        width: "48%"
    },
    contentButtonsWithText: {
        flexDirection: "row",
        justifyContent: "space-between",
        position: "relative",
    },
    btnWidth: {
        width: 38
    },
    boxSteps: {
        flexDirection: "row",
        alignItems: "center",
        justifyContent: "space-around",
        width: "70%",
        marginLeft: 5
    },
    steps: {
        width: 8,
        height: 8,
        backgroundColor: "#D9D9D9",
        borderRadius: 50
    },
    boxPreviewImageProfile: {
        marginTop: -5,
        height: (windowHeight - 200),
        backgroundColor: '#F2F2F2',
        width: '100%',
        resizeMode: 'contain',
        position: "relative"
    },
    imgProfile: {
        width: '100%',
        height: (windowHeight - 200),
    },
    boxCentered: {
        flexDirection: "row",
        justifyContent: "center",
        position: "absolute",
        bottom: 20,
        alignSelf: "center"
    },
    w80: {
        width: "80%",
        backgroundColor: "#FFF"
    },
});

export default styles;