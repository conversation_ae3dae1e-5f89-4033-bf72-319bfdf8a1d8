import React, { useState } from 'react';
import { Text, View, TouchableOpacity, ActivityIndicator, Alert, Image } from 'react-native';
import { useIsFocused } from '@react-navigation/native';
import ImageCropPicker from 'react-native-image-crop-picker';

import PrivateHeader from '../../../../../components/headers/PrivateHeader';

import styles from './styles';
import mainStyles from '../../../../../mainStyles';
import { ScrollView } from 'react-native-gesture-handler';
import api from '../../../../../services/api';
import { useAuth } from '../../../../../context/auth';
import { requestCameraPermission } from "../../../../../useful/permissions";
import ArrowLeftSmall from '../../../../../assets/svgs/ArrowLeftSmall';
import { useBank } from '../../../../../context/bank';

const BankSelfie = ({ navigation, route }) => {
    const isFocused = useIsFocused();
    const { token, user } = useAuth();

    const { createBankAccount } = useBank();

    const [loading, setLoading] = useState(false);
    const [image, setImage] = useState(null);

    const openCamera = async() => {
        const hasPermission = await requestCameraPermission();
        if(hasPermission){
            ImageCropPicker.openCamera({
                width: 1080,
                height: 1920,
                cropping: false,
            }).then(image => {
                setImage(image);
            });
        }
    }

    const deleteImage = () => {
        openCamera();
    }

    const update = async () => {
        setLoading(true);

        let data = {
            selfie_id: imageUpdated ? await uploadImage() : imageId
        };

        api.put(`/rh/cadastro/foto`, data, {
            headers: {
                // 'content-type': 'application/x-www-form-urlencoded'
            }
        }).then(res => {
            Alert.alert(user.status_cadastro === 'reprovado' ? 'Dados salvos, caso tenha encerrado enviar para análise.' : 'Dados salvos e enviados para análise.');
            navigation.navigate('RhList')
        }).catch(err => {
            if(err?.response?.data?.errors){
                let errors = err.response.data.errors;
                let text = '';
                Object.keys(errors).map(error => {
                    text += `\n${errors[error][0]}`;
                });
                Alert.alert('Verifique os campos', text);
            } else {
                Alert.alert('Erro ao atualizar os dados.', 'Por favor, verifique os dados e tente novamente.');
            }
        }).then(() => {
            setLoading(false);
        });
    }
    const next = async () => {
        setLoading(true);

        const data = new FormData();

        data.append('selfie', {
            uri: image.path,
            type: image.mime,
            name: 'selfie.jpg'
        });

        await api.post('/bank-account/user/upload', data).catch(error =>{ 
            Alert.alert('Não foi possível enviar a foto', error?.response?.data?.message ?? 'Por favor, tente novamente ou entre em contato conosco.');
            setLoading(false);
        });

        await api.post('/bank-account/face-recon/selfie', data).catch(error =>{ 
            Alert.alert('Não foi possível validar a foto',  error?.response?.data?.message ?? 'Por favor, tente tirar outra foto.');
            setImage(null);
            setLoading(false);
        });

        const res = await createBankAccount();
        
        if(res){
            navigation.navigate('BankInitAccount');
        } else {
            setLoading(false);
        }
    }

    return (
        <View style={mainStyles.wrapper}>
            <PrivateHeader title={`Selfie`} />
            {loading &&
               <View style={{ flex: 1, justifyContent: "center", alignItems: "center", marginTop: 30 }}>
                    <ActivityIndicator size="large" color="#00467F" />
                </View>
            }
            {!loading && 
                <> 
                    <ScrollView showsVerticalScrollIndicator={false}>                
                        <View style={[mainStyles.privateContainer, styles.ptop]}>
                            {!image &&
                                <View>
                                    <Text style={[styles.textTerms, styles.subtitle]}>Para garantirmos a segurança de sua conta, precisamos que você tire uma selfie.</Text>
                                    <Text style={[styles.textTerms, styles.textMedium]}>Instruções importantes para sua Selfie</Text>
                                    <Text style={styles.textTerms}>
                                        {`• A foto deve conter apenas um rosto;\n\n• Todo o rosto deve estar visível na foto;\n\n• O rosto deve ocupar ao menos 15% da área da foto;\n\n• O rosto deve estar encarando a câmera e paralelo a ela;\n\n• O rosto deve estar com os olhos abertos;\n\n• O rosto deve estar com a boca fechada;\n\n• O rosto deve possuir expressão neutra e sem sorrisos;\n\n• O rosto não deve estar coberto por nenhum tipo de acessório (chapéus, óculos ou máscaras).`}
                                    </Text>
                                </View>
                            }
                            {image &&
                                <View style={styles.boxPreviewImageProfile}>
                                    <Image
                                        style={[mainStyles.imgProfile, styles.imgProfile]}
                                        source={{ uri: image.path }}
                                    />
                                    <View style={styles.boxCentered}>
                                        <TouchableOpacity 
                                            style={[mainStyles.btnCenterOutlineBlueDarkNewBorder, styles.w80]}  
                                            onPress={deleteImage}
                                        >
                                            <Text style={mainStyles.btnTextOutlineBlueBorder}>TIRAR NOVA SELFIE</Text>
                                        </TouchableOpacity>
                                    </View>
                                </View>
                            }
                        </View>
                    </ScrollView>
                    <View style={styles.contentBottom}>
                        <View style={[mainStyles.container, styles.contentButtonsWithText]}>
                            <View style={styles.w48}>
                                <TouchableOpacity 
                                    style={[mainStyles.btnCenterOutlineBlueDarkNewBorder, styles.btnWidth]} 
                                    onPress={() => navigation.goBack()}                                
                                >
                                    <ArrowLeftSmall />
                                </TouchableOpacity>
                                <View style={styles.boxSteps}>
                                    <View style={styles.steps}></View>
                                    <View style={styles.steps}></View>
                                    <View style={styles.steps}></View>
                                    <View style={styles.steps}></View>
                                    <View style={[styles.steps, {backgroundColor: "#2D719F"}]}></View>
                                </View>
                            </View>
                            {!image &&
                                <TouchableOpacity 
                                    style={[mainStyles.btnBlueNew, styles.w48]}
                                    onPress={openCamera}
                                >
                                    <Text style={mainStyles.btnTextBlueNew}>ATIVAR CÂMERA</Text>
                                </TouchableOpacity>
                            }
                            {image &&
                                <TouchableOpacity 
                                    style={[mainStyles.btnBlueNew, styles.w48]}
                                    onPress={next}
                                >
                                    <Text style={mainStyles.btnTextBlueNew}>FINALIZAR</Text>
                                </TouchableOpacity>
                            }
                        </View>
                    </View>
                </>
            }
        </View>
    );
}

export default BankSelfie;