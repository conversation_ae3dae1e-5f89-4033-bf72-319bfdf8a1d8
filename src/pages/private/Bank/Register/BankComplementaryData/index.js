import React from 'react';
import { TextInput, Text, View, TouchableOpacity, Alert } from 'react-native';

import PrivateHeader from '../../../../../components/headers/PrivateHeader';
import Select from '../../../../../components/Select';

import { dateMask, money } from '../../../../../useful/masks';

import styles from './styles';
import mainStyles from '../../../../../mainStyles';
import { ScrollView } from 'react-native-gesture-handler';
import api from '../../../../../services/api';
import ArrowLeftSmall from '../../../../../assets/svgs/ArrowLeftSmall';
import { useBank } from '../../../../../context/bank';

const pepOptions = [
    { label: 'Sim', value: 1 },
    { label: 'Não', value: 0 }
]

const BankComplementaryData = ({ navigation, route }) => {
    const { bankAccount, setBankAccount } = useBank();

    const next = () => {
        const { birthdate, mother_name, is_pep, nationality, monthly_income } = bankAccount;
        if (!birthdate || !mother_name || is_pep === undefined || !nationality || !monthly_income) {
            Alert.alert('Atenção', 'Preencha todos os dados os campos antes de avançar.');
            return;
        }

        const dateRegex = /^\d{2}\/\d{2}\/\d{4}$/;
        if (!dateRegex.test(birthdate)) {
            Alert.alert('Erro', 'A data de nascimento deve estar no formato DD/MM/YYYY.');
            return;
        }

        const moneyRegex = /^R\$\s\d{1,3}(\.\d{3})*,\d{2}$/;
        if (!moneyRegex.test(monthly_income)) {
            Alert.alert('Erro', 'A renda mensal deve estar no formato R$ 0,00 (ex.: R$ 12.345,67).');
            return;
        }

        navigation.navigate('BankAddress');
    }

    return (
        <View style={mainStyles.wrapper}>
            <PrivateHeader title={`Dados pessoais`} />
            <ScrollView showsVerticalScrollIndicator={false}>                
                <View style={[mainStyles.privateContainer, styles.ptop]}>
                    <Text style={[styles.textTerms, styles.textMedium]}>{`Preencha abaixo os dados\ncomplementares`}</Text>
                    <View>
                        <Text style={mainStyles.label}>Data de nascimento</Text>                                
                        <TextInput  
                            underlineColorAndroid="transparent"  
                            style={mainStyles.inputText}
                            value={bankAccount.birthdate}
                            onChangeText={text => setBankAccount({...bankAccount, birthdate: dateMask(text)})}
                            placeholder="dd/mm/aaaa"
                            keyboardType="phone-pad"
                        />
                    </View>
                    <View>
                        <Text style={mainStyles.label}>Nome da mãe</Text>                                
                        <TextInput
                            underlineColorAndroid="transparent"  
                            style={mainStyles.inputText}
                            value={bankAccount.mother_name}
                            onChangeText={text => setBankAccount({...bankAccount, mother_name: text})}
                            autoCapitalize="words"
                        />
                    </View>
                    <View>
                        <Text style={mainStyles.label}>Pessoa exposta politicamente (PEP)</Text>                                
                        <Select 
                            removeBorder={true}
                            placeholder="Selecione"
                            options={pepOptions} 
                            value={bankAccount.is_pep}
                            onChange={value => setBankAccount({...bankAccount, is_pep: value})} 
                        />
                    </View>
                    <View>
                        <Text style={mainStyles.label}>Nacionalidade:</Text>
                        <TextInput
                            underlineColorAndroid="transparent"
                            style={mainStyles.inputText}
                            value={bankAccount.nationality}
                            onChangeText={text => setBankAccount({...bankAccount, nationality: text})}
                            autoCapitalize="words"
                        />
                    </View>
                    <View>
                        <Text style={mainStyles.label}>Renda mensal</Text>
                        <TextInput
                            underlineColorAndroid="transparent"
                            style={mainStyles.inputText}
                            value={bankAccount.monthly_income}
                            onChangeText={text => setBankAccount({...bankAccount, monthly_income: money(text)})}
                            keyboardType="phone-pad"
                        />
                    </View>
                </View>
            </ScrollView>
            <View style={styles.contentBottom}>
                <View style={[mainStyles.container, styles.contentButtonsWithText]}>
                    <View style={styles.w48}>
                        <TouchableOpacity 
                            style={[mainStyles.btnCenterOutlineBlueDarkNewBorder, styles.btnWidth]} 
                            onPress={() => navigation.goBack()}                                
                        >
                            <ArrowLeftSmall />
                        </TouchableOpacity>
                        <View style={styles.boxSteps}>
                            <View style={styles.steps}></View>
                            <View style={styles.steps}></View>
                            <View style={[styles.steps, {backgroundColor: "#2D719F"}]}></View>
                            <View style={styles.steps}></View>
                            <View style={styles.steps}></View>
                        </View>
                    </View>
                    <TouchableOpacity 
                        style={[mainStyles.btnBlueNew, styles.w48]} 
                        onPress={next}
                    >
                        <Text style={mainStyles.btnTextBlueNew}>AVANÇAR</Text>
                    </TouchableOpacity>
                </View>
            </View>
        </View>
    );
}

export default BankComplementaryData;