import React, { useEffect, useState } from 'react';
import { Text, View, TouchableOpacity, useWindowDimensions } from 'react-native';
import { useIsFocused } from '@react-navigation/native';
import RenderHtml from 'react-native-render-html';

import PrivateHeader from '../../../../components/headers/PrivateHeader';

import Pix from '../../../../assets/svgs/Pix';
import Comissoes from '../../../../assets/svgs/Comissoes';

import ArrowRight from '../../../../assets/svgs/ArrowRight';

import styles from './styles';
import mainStyles from '../../../../mainStyles';
import { ScrollView } from 'react-native-gesture-handler';
import api from '../../../../services/api';

const BankHome = ({ navigation, route }) => {
    const isFocused = useIsFocused();

    const contentWidth = useWindowDimensions().width;

    const [loading, setLoading] = useState(true);

    return (
        <View style={mainStyles.wrapper}>
            <PrivateHeader title={`Cury Bank`} />
            {/* {loading &&
                <View style={{ flex: 1, justifyContent: "center", alignItems: "center", marginTop: 30 }}>
                    <ActivityIndicator size="large" color="#00467F" />
                </View>
            }
            {!loading &&  */}
            <>
                <ScrollView showsVerticalScrollIndicator={false} style={{backgroundColor: "#F2F2F2"}}>
                    <View style={styles.boxTop}>
                        <View style={[mainStyles.privateContainer, styles.containerTop]}>
                            <View style={styles.boxInitName}>
                                <Text style={styles.initName}>W</Text>
                            </View>
                            <Text style={styles.nameBroker}>Olá, [apelido do corretor]</Text>
                        </View>
                    </View>
                    <View style={[mainStyles.privateContainer, styles.ptop]}>
                        <Text style={styles.title}>Ações rápidas</Text>
                        <View style={styles.boxBtns}>
                            <TouchableOpacity 
                                style={styles.btnAction}  
                                onPress={() => {''}}
                            >
                                <Pix />
                                <Text style={styles.btnTextAction}>Transferir por PIX</Text>
                            </TouchableOpacity>
                            <TouchableOpacity 
                                style={styles.btnAction}  
                                onPress={() => {''}}
                            >
                                <Comissoes />
                                <Text style={styles.btnTextAction}>Gestão de comissões</Text>
                            </TouchableOpacity>
                        </View>
                        <View style={styles.boxExtract}>
                            <View style={styles.aboutAccount}>
                                <Text style={styles.textAccount}>Saldo em conta</Text>
                                <Text style={styles.valueAccount}>R$5.000,30</Text>
                            </View>
                            <View style={styles.divider}></View>
                            <TouchableOpacity 
                                style={styles.btnExtract}  
                                onPress={() => {''}}
                            >
                                <Text style={styles.btnTextExtract}>Acessar extrato</Text>
                                <ArrowRight style={styles.icArrow} />
                            </TouchableOpacity>
                        </View>
                    </View>
                </ScrollView>
                <View style={styles.contentBottom}>
                    <View style={mainStyles.container}>
                        <TouchableOpacity 
                            style={mainStyles.btnBlueNew} 
                            onPress={() => navigation.navigate('PrivateMain')}
                        >
                            <Text style={mainStyles.btnTextBlueNew}>VOLTAR</Text>
                        </TouchableOpacity>
                    </View>
                </View>
            </>
            {/* } */}
        </View>
    );
}

export default BankHome;