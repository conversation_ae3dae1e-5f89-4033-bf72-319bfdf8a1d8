import { StyleSheet, Dimensions, Platform } from 'react-native';

const windowWidth = Dimensions.get('window').width;

const styles = StyleSheet.create({
    wrapper: {
        justifyContent: "space-between"
    },
    contentBottom: {
        paddingTop: 15,
        paddingBottom: 15,
        shadowRadius: Platform.OS === 'ios' ? 2 : 16,
        shadowOffset: {
            width: 0,
            height: Platform.OS === 'ios' ? -2 : 12,
        },
        shadowColor: Platform.OS === 'ios' ? "#CCC" : "#000",
        elevation: Platform.OS === 'ios' ? 2 : 24,
        shadowOpacity: 1,
        backgroundColor: "#FFF",
        position: "absolute",
        bottom: 0,
        zIndex: 10
    },
    ptop: {
        paddingBottom: 130,
        backgroundColor: "#F2F2F2"
    },
    container: {
        maxWidth: 340,
        marginLeft: ((windowWidth - 340) /2),
        marginTop: 10
    },
    divider: {
        borderBottomWidth: 1,
        borderBottomColor: '#DADADA',
        marginTop: 15,
    },
    title: {
        fontFamily: "Ubuntu-Medium",
        fontSize: 16,
        letterSpacing: 0.46,
        marginTop: 20,
        color: "#1A374D"
    },
    containerTop: {
        backgroundColor: "#2D719F",
        flexDirection: "row",
        alignItems: "center"
    },
    boxTop: {
        backgroundColor: "#2D719F",
        height: 90,
        flexDirection: "row",
        alignItems: "center"
    },
    boxInitName: {
        width: 50,
        height: 50,
        borderRadius: 70,
        backgroundColor: "#90B0C0",
        justifyContent: "center",
        alignItems: "center"
    },
    initName: {
        fontFamily: "Ubuntu-Regular",
        fontSize: 25,
        color: "#FFF"
    },
    nameBroker: {
        fontFamily: "Ubuntu-Regular",
        fontSize: 18,
        color: "#FFF",
        marginLeft: 10
    },
    boxBtns: {
        flexDirection: "row",
        marginTop: 20
    },
    btnAction: {
        width: 85,
        height: 85,
        flexDirection: "column",
        justifyContent: "space-between",
        paddingTop: 8,
        paddingBottom: 8,
        paddingRight: 5,
        paddingLeft: 5,
        backgroundColor: "#2D719F",
        marginRight: 8
    },
    btnTextAction: {
        fontFamily: "Ubuntu-Regular",
        fontSize: 13,
        color: "#FFF",
        letterSpacing: 0.46
    },
    boxExtract: {
        backgroundColor: "#FFF",
        paddingLeft: 20,
        paddingRight: 20,
        minHeight: 160,
        paddingTop: 30,
        marginTop: 35
    },
    aboutAccount: {
        flexDirection: "row",
        alignItems: "center",
        justifyContent: "space-between"
    },
    textAccount: {
        fontFamily: "Ubuntu-Regular",
        fontSize: 14,
        color: "#1A374D",
        letterSpacing: 0.46
    },
    valueAccount: {
        fontFamily: "Ubuntu-Regular",
        fontSize: 21,
        color: "#1A374D",
        letterSpacing: 0.46,
        lineHeight: 25
    },
    btnExtract: {
        marginTop: 18,
        flexDirection: "row",
        alignItems: "center",
        justifyContent: "space-between",
        paddingRight: 10
    },
    btnTextExtract: {
        fontFamily: "Ubuntu-Bold",
        fontSize: 14,
        color: "#1A374D",
        letterSpacing: 0.46,
    },
    icArrow: {
        width: 7,
        color: "#1A374D"
    }
});

export default styles;