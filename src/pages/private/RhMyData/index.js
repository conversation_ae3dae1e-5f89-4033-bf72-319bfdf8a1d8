import React, { useEffect, useState } from 'react';
import { Text, View, TouchableOpacity, ScrollView, ActivityIndicator, Linking, Alert } from 'react-native';

import ArrowLeft from '../../../assets/svgs/ArrowLeftSmallWhite';
import Data from '../../../assets/svgs/Data';
import Address from '../../../assets/svgs/Address2';
import Document from '../../../assets/svgs/Document2';
import Camera from '../../../assets/svgs/CameraDark';
import Password from '../../../assets/svgs/Password';
import Arrow from '../../../assets/svgs/ArrowDownGrey';

import Dash from 'react-native-dash';

import PrivateHeader from '../../../components/headers/PrivateHeader';
import AlertFooterRed from '../../../components/AlertFooterRedRh';

import styles from './styles';
import mainStyles from '../../../mainStyles';

import api from '../../../services/api';
import { useAuth } from '../../../context/auth';
import { useIsFocused } from '@react-navigation/native';

const RhAddress = ({navigation}) => {
    const [loading, setLoading] = useState(false);
    const [pendencias, setPendencias] = useState([]);

    const isFocused = useIsFocused();

    useEffect(() => {
        if(isFocused) getData();
    }, [isFocused]);

    const getData = () => {
        api.get(`/rh`).then(res => {
            setPendencias(res.data.alteracoes_pendentes);
        });
    }

    return (
        <>
        <View style={mainStyles.wrapper}>
            <PrivateHeader title={`Meu Perfil`} back={() => navigation.navigate('PrivateMain')} />
            <TouchableOpacity style={styles.shift} onPress={() => navigation.navigate('RhList')}>
                <View style={styles.icAbs}>
                    <ArrowLeft style={styles.icArrow} />
                </View>
                <Text style={[mainStyles.textWhite]}>MEUS DADOS</Text>
            </TouchableOpacity>
            <View style={[mainStyles.privateContainer, {flex: 1}]}>
                {loading && 
                    <View style={{flex: 1, justifyContent: "center", alignItems: "center"}}>
                        <ActivityIndicator size="large" color="#00467F" />
                    </View>
                }
                {!loading &&
                <>
                    <ScrollView showsVerticalScrollIndicator={false}>
                        <TouchableOpacity style={styles.rowCenterMargin} onPress={() => navigation.navigate('RhPersonalData')}>
                            <View style={styles.icWidth}>
                                <Data style={styles.icData} />
                            </View>                                 
                            <View style={styles.marginIc}>
                                <Text style={[mainStyles.textBlue, styles.textLower]}>DADOS PESSOAIS</Text>
                                {pendencias.includes('dados-pessoais') &&
                                    <Text style={styles.textRed}>AGUARDANDO APROVAÇÃO</Text>                                       
                                }
                            </View>
                            <View style={styles.icArrowRight}>
                                <Arrow />
                            </View>
                        </TouchableOpacity>
                        <TouchableOpacity style={styles.rowCenterMargin} onPress={() => navigation.navigate('RhAddress')}>
                            <View style={styles.icWidth}>                                
                                <Address />                                    
                            </View>
                            <View style={styles.marginIc}>
                                <Text style={[mainStyles.textBlue, styles.textLower]}>ENDEREÇO</Text>
                                {pendencias.includes('endereco') &&
                                    <Text style={styles.textRed}>AGUARDANDO APROVAÇÃO</Text>                                       
                                }
                            </View>
                            <View style={styles.icArrowRight}>
                                <Arrow />
                            </View>
                        </TouchableOpacity>
                        <TouchableOpacity style={styles.rowCenterMargin} onPress={() => navigation.navigate('RhDocuments')}>
                            <View style={styles.icWidth}>                               
                                <Document />                                    
                            </View>
                            <View style={styles.marginIc}>
                                <Text style={[mainStyles.textBlue, styles.textLower]}>DOCUMENTOS</Text>
                                {pendencias.includes('documentos') &&
                                    <Text style={styles.textRed}>AGUARDANDO APROVAÇÃO</Text>                                       
                                }
                            </View>
                            <View style={styles.icArrowRight}>
                                <Arrow />
                            </View>
                        </TouchableOpacity>
                        <TouchableOpacity style={styles.rowCenterMargin} onPress={() => navigation.navigate('RhPicture')}>
                            <View style={styles.icWidth}>                                
                                <Camera style={{color: "#FFF", width: 40}} />                                    
                            </View>
                            <View style={styles.marginIc}>
                                <Text style={[mainStyles.textBlue, styles.textLower]}>MINHA FOTO</Text>
                                {pendencias.includes('foto') &&
                                    <Text style={styles.textRed}>AGUARDANDO APROVAÇÃO</Text>                                       
                                }                                    
                            </View>
                            <View style={styles.icArrowRight}>
                                <Arrow />
                            </View>
                        </TouchableOpacity>
                        <TouchableOpacity style={styles.rowCenterMargin} onPress={() => navigation.navigate('RhPassword')}>
                            <View style={styles.icWidth}>                                
                                <Password style={{color: "#FFF"}} />                                    
                            </View>
                            <View style={styles.marginIc}>
                                <Text style={[mainStyles.textBlue]}>Senha do app</Text>                                        
                            </View>
                            <View style={styles.icArrowRight}>
                                <Arrow />
                            </View>
                        </TouchableOpacity>
                    </ScrollView>
                    <View style={styles.contentBottom}>
                        <View style={[mainStyles.container, styles.contentButtons]}>
                            <TouchableOpacity style={[mainStyles.btnBlueNew, styles.buttonLarge]} onPress={() => navigation.navigate('RhList')}>
                                <Text style={mainStyles.btnTextBlueNew}>VOLTAR</Text>
                            </TouchableOpacity>
                        </View>
                    </View>
                </>
                }
            </View>
        </View>
    </>
    );
}

export default RhAddress;