import { StyleSheet, Dimensions, Platform } from 'react-native';

const windowWidth = Dimensions.get('window').width;

const styles = StyleSheet.create({
    wrapper: {
        justifyContent: "space-between"
    },
    contentBottom: {
        paddingTop: 20,
        paddingBottom: 40,
        shadowRadius: Platform.OS === 'ios' ? 2 : 16,
        shadowOffset: {
            width: 0,
            height: Platform.OS === 'ios' ? -2 : 12,
        },
        shadowColor: Platform.OS === 'ios' ? "#CCC" : "#000",
        elevation: Platform.OS === 'ios' ? 2 : 24,
        shadowOpacity: 1,
        backgroundColor: "#FFF",
        position: "absolute",
        bottom: 0,
        zIndex: 10
    },
    buttonLarge: {
        width: "100%"
    },
    btnTextOutlineBlue: {
        fontFamily: "Roboto-Regular",
        color: "#FFF",
        fontSize: 25,
        letterSpacing: 1,
        textAlign: "center"
    },
    separator: {
        marginLeft: 25,
        marginRight: 25,
        width: 1,
        height: '70%',
        backgroundColor: '#BDBDBD'
    },
    shift: {
        minHeight: 65,
        marginBottom: 20,
        backgroundColor: "#90B0C0",
        alignItems: "center",
        justifyContent: "center",
        position: "relative"
    },
    infos: {
        flexDirection: "row",
        justifyContent: "center",
        alignItems: "center"
    },
    btnBack: {
        flexDirection: "row",
        alignItems: "center"
    },
    icAbs: {
        position: "absolute",
        left: 20
    },
    icArrow: {
        width: 10,
        marginRight: 15
    },
    icArrowRight: {
        transform: [{rotate: "-90deg"}],
        position: "absolute",
        right: 15
    },
    icData: {
        color: "#FFF",
        width: 32
    },
    marginIc: {
        marginLeft: 15
    },
    rowCenterMargin: {
        flexDirection: "row",
        alignItems: "center",
        minHeight: 65,
        backgroundColor: "#F2F2F2",
        marginBottom: 13,
        position: "relative"
    },
    icWidth: {
        width: 55,
        minHeight: 65,
        backgroundColor: "#90B0C0",
        justifyContent: "center",
        alignItems: "center"
    },
    textLower: {
        fontFamily: "Ubuntu-Regular",
        textTransform: "capitalize"
    },
    textRed: {
        fontFamily: "Ubuntu-Regular",
        fontSize: 12,
        color: "#FF6542",
        letterSpacing: 1,
        marginTop: 2
    }
});

export default styles;