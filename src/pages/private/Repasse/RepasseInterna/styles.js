import { StyleSheet, Dimensions, Platform } from 'react-native';

const windowWidth = Dimensions.get('window').width;

const styles = StyleSheet.create({
    wrapper: {
        justifyContent: "space-between"
    },
    contentBottom: {
      paddingTop: 20,
      paddingBottom: 40,
      shadowRadius: Platform.OS === 'ios' ? 2 : 16,
      shadowOffset: {
        width: 0,
        height: Platform.OS === 'ios' ? -2 : 12,
      },
      shadowColor: Platform.OS === 'ios' ? '#CCC' : '#000',
      elevation: Platform.OS === 'ios' ? 2 : 24,
      shadowOpacity: 1,
      backgroundColor: '#FFF',
    },
    contentButtons: {
      flexDirection: 'row',
      justifyContent: 'space-between',
    },
    button: {
      width: '100%',
    },
    buttonLarge: {
      width: '100%',
    },
    container: {
        maxWidth: 340,
        marginLeft: ((windowWidth - 340) /2),
        marginTop: 10
    },
    contentOption: {
        flexDirection: "row",
        alignItems: "center",
        marginTop: 25,
        marginBottom: 15
    },  
    textStatusBold: {
        fontFamily: "Roboto-Bold",
        fontWeight: "bold",
        color: "#4EA1CC",
        fontSize: 25,
        letterSpacing: 1,
        marginLeft: 15
    },
    borderTop: {
        paddingTop: 10,
        paddingBottom: 10
    },
    textUpdate: {
        fontFamily: "Roboto-Bold",
        color: "#00467F",
        fontSize: 15,
        letterSpacing: 1,
        marginTop: 5,
        marginBottom: 15
    },
    boxBoxShadow: {
        backgroundColor: "#F2F2F2",
        flexDirection: "row",
        alignItems: "center",
        marginTop: 10,
        marginBottom: 10
    },
    textStatus: {
        color: "#828282",
        letterSpacing: 1,
        fontSize: 13
    },
    textResult: {
        fontFamily: "Ubuntu-Medium",
        letterSpacing: 1,
        fontSize: 17,
        marginLeft: 10,
        color: "#00467F"
    },
    textLabel: {
        fontFamily: "Ubuntu-Regular",
        letterSpacing: 1,
        fontSize: 14,
        marginLeft: 10,
        marginTop: 3,
        color: "#00467F"
    },
    datePay: {
        fontFamily: "Roboto-Bold",
        color: "#00467F",
        fontSize: 17,
        letterSpacing: 0.5,
        fontWeight: "bold"
    },
    textAboutProcess: {
        fontWeight: "normal",
        color: "#828282",
        fontSize: 13,
        letterSpacing: 0.5,
        marginTop: 10
    },
    updatePendency: {
        fontFamily: "Roboto-Regular",
        color: "#00467F",
        fontSize: 17,
        letterSpacing: 0.5
    },
    btnTextOutlineBlue: {
        fontFamily: "Roboto-Bold",
        color: "#00467F",
        fontSize: 25,
        fontWeight: "bold",
        letterSpacing: 1,
        textAlign: "center"
    },
    boxIconWidth: {
        width: 60, 
        minHeight: 65,    
        flexDirection: "row",
        justifyContent: "center",
        alignItems: "center"
    },
    buttonDate: {
        backgroundColor: "#90B0C0",
        height: 58,
        width: '100%',
        alignItems: "center",
        justifyContent: "center"
    },
    icArrow: {
        position: "absolute",
        top: 23,
        left: 25
    },
    btnTextButtonDate: {
        fontFamily: "Ubuntu-Regular",
        color: "#FFF",
        fontSize: 15,
        letterSpacing: 0.46,
        textTransform: "uppercase"
    },
});

export default styles;