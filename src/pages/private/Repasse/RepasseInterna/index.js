import React, { useEffect, useState } from 'react';
import { TextInput, Text, View, TouchableOpacity, ActivityIndicator, useWindowDimensions, Alert } from 'react-native';
import { useIsFocused, useNavigation } from '@react-navigation/native';
import moment from 'moment';
import Like from '../../../../assets/svgs/Like';
import Loading from '../../../../assets/svgs/Loading';
import Check from '../../../../assets/svgs/Check3';
import ArrowLeft from '../../../../assets/svgs/ArrowLeftSmallWhite';
import Alert5 from '../../../../assets/svgs/Alert5';
import FGTS from '../../../../assets/svgs/FGTS';

import PrivateHeader from '../../../../components/headers/PrivateHeader';

import styles from './styles';
import mainStyles from '../../../../mainStyles';
import { ScrollView } from 'react-native-gesture-handler';
import api from '../../../../services/api';
import { useAuth } from '../../../../context/auth';
import { color } from 'react-native/Libraries/Components/View/ReactNativeStyleAttributes';
import RenderHtml from 'react-native-render-html';

const RepasseInterna = ({ navigation, route }) => {
    const { pv } = route.params;
    const isFocused = useIsFocused();

    const contentWidth = useWindowDimensions().width;

    const [loading, setLoading] = useState(true);
    const [processo, setProcesso] = useState(null);
    
    useEffect(() => {
        if(isFocused){
            getProcesso();
        }

        return () => {
            setProcesso(null);
        }
    }, [isFocused]);

    const getProcesso = () => {
        setLoading(true);

        const pvArr = pv.split('-');
        const convertedPv = pvArr.length > 1 ? pvArr[1] : pv;

        api.get(`repasses-salesforce/pv/${convertedPv}`).then(res => {
            setProcesso(res.data.processo);
            console.log('processo', res.data.processo);
        }).catch(error => {
            console.log(error);
        }).then(() => setLoading(false));
    }

    return (
        <View style={mainStyles.wrapper}>
            <PrivateHeader title={`Repasse`} />
            {loading &&
                <View style={{ flex: 1, justifyContent: "center", alignItems: "center" }}>
                    <ActivityIndicator size="large" color="#00467F" />
                </View>
            }
            {!loading && processo &&
            <>
                <TouchableOpacity style={styles.buttonDate} onPress={() => navigation.navigate('RepasseHome')}>
                    <View style={styles.icArrow}>
                        <ArrowLeft />
                    </View>
                    <Text style={styles.btnTextButtonDate}>{pv}</Text>
                </TouchableOpacity>
                <ScrollView showsVerticalScrollIndicator={false}>
                    <View style={mainStyles.container}>
                        <View style={styles.borderTop}>
                            <View>
                                <View style={styles.boxBoxShadow}>
                                    <View style={[styles.boxIconWidth, { backgroundColor: processo.etapa_app.class === 'error' ? '#FF6542' : '#90B0C0'}]}>
                                        {processo.etapa_app.class !== 'error' &&
                                            <FGTS />
                                        }
                                        {processo.etapa_app.class === 'error' &&
                                            <Alert5 style={{width: 38}} />
                                        }
                                        {/* <Check style={{width: 35}} /> */}
                                    </View>
                                    <View>
                                        <Text style={styles.textResult}>{processo.etapa_app.titulo}</Text>
                                        <Text style={styles.textLabel}>{processo?.etapa_sub_app?.slug !== 'repasse-futuro' ? `Há ${processo.etapa_dias} ${processo.etapa_dias === 1 ? 'dia' : 'dias'}` : `Até ${processo.prazo_conclusao}`}</Text>
                                    </View>
                                </View>
                            </View>
                        </View>
                        {processo?.texto &&
                            <RenderHtml 
                                source={{ html: `<p style="color: #828282;">${processo.texto}</p>` }} 
                                contentWidth={contentWidth} 
                            />
                        }
                    </View>
                </ScrollView>
                <View style={styles.contentBottom}>
                    <View style={[mainStyles.container, styles.contentButtons]}>
                        <TouchableOpacity
                            style={[mainStyles.btnBlueNew, styles.buttonLarge]}
                            onPress={() => navigation.goBack()}>
                            <Text style={mainStyles.btnTextBlueNew}>VOLTAR</Text>
                        </TouchableOpacity>
                    </View>
                </View>
            </>
            }
        </View>
    );
}

export default RepasseInterna;