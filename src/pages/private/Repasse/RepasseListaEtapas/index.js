import React, { useEffect, useState } from 'react';
import { TextInput, Text, View, TouchableOpacity, ActivityIndicator, SafeAreaView, Alert, Platform, KeyboardAvoidingView } from 'react-native';
import { useIsFocused, useNavigation } from '@react-navigation/native';
import moment from 'moment';
import Alert2 from '../../../../assets/svgs/Alert2';
import Like from '../../../../assets/svgs/Like';
import Loading from '../../../../assets/svgs/Loading';
import Check2 from '../../../../assets/svgs/Check2';
import Search2 from '../../../../assets/svgs/Search2';
import Arrow from '../../../../assets/svgs/ArrowRightGray2';

import PrivateHeader from '../../../../components/headers/PrivateHeader';

import styles from './styles';
import mainStyles from '../../../../mainStyles';
import { ScrollView } from 'react-native-gesture-handler';
import api from '../../../../services/api';
import { useAuth } from '../../../../context/auth';
import ArrowLeft from '../../../../assets/svgs/ArrowLeft';
import { RepassePvSearch } from '../../../../components/RepassePvSearch';

const RepasseListaEtapas = ({ navigation, route }) => {
    const { slug, titulo } = route.params;
    const isFocused = useIsFocused();

    const [loading, setLoading] = useState(true);
    const [loadingSearch, setLoadingSearch] = useState(false);

    const [etapas, setEtapas] = useState([]);
    const [atualizadoEm, setAtualizadoEm] = useState(null);

    const [search, setSearch] = useState('');
    
    useEffect(() => {
        if(isFocused) getData();
    }, []);
    
    useEffect(() => {
        getData();
    }, [isFocused]);

    const getData = () => {
        setLoading(true);
        api.get(`repasses-salesforce/etapa/${slug}`).then(res => {
            console.log('etapas', res.data?.etapas ? Object.values(res.data.etapas) : []);
            setEtapas(res.data?.etapas ? Object.values(res.data.etapas) : []);
            setAtualizadoEm(res.data?.atualizado_em ?? '');

            setLoading(false);
        }).catch(error => {
            console.log(error)
        }).then(() => setLoading(false));
    }

    const handleSearch = () => {
        setLoadingSearch(true);
        setSearch('');
        api.get(`repasses/cvc/${search}`).then(res => {
            navigation.navigate('RepasseInterna', { cvc: `CVC-${search}` });
        }).catch(error => {
            Alert.alert('Não encontrado', 'Não encontramos o CVC informado. Por favor, tente novamente.');
            setSearch('');
        }).then(() => setLoadingSearch(false));
        
    }

    const Total = ({total}) => {
        return (
            <View>
                {loading &&
                    <View style={{ flex: 1, justifyContent: "center", alignItems: "center", }}>
                        <ActivityIndicator size="large" color="#00467F" />
                    </View>
                }
                {!loading &&
                    <Text style={styles.textBoxFlexNumber}>{total}</Text>
                }
            </View>
        );
    }

    return (
        <View style={mainStyles.wrapper}>
            <PrivateHeader title={`Repasse`} />
            <View style={mainStyles.container}>
                <RepassePvSearch />
                <View style={styles.divider}></View>
                <View style={styles.rowOptions}>
                    <View style={styles.contentOption}>
                        <Text style={styles.textStatus}>{titulo}:</Text>
                    </View>
                </View>
            </View>
            <ScrollView keyboardShouldPersistTaps={`handled`} showsVerticalScrollIndicator={false}>
                <View style={mainStyles.container}>
                    {etapas.map((etapa, index) => (
                        <TouchableOpacity 
                            key={index}
                            onPress={() => navigation.navigate('RepasseListaProcessos', {
                                titulo: etapa.titulo,
                                prevslug: slug,
                                slug: etapa.slug
                            })}
                        >
                            <View style={[styles.boxFlex, etapa.class === 'error' ? {borderLeftColor: '#FF6542'} : null]}>
                                <View style={styles.boxFlexWithText}>
                                    <Text style={styles.textBoxFlex}>{etapa.titulo}</Text>
                                </View>
                                <Total total={etapa.total} />
                                <Arrow />
                            </View>
                        </TouchableOpacity>
                    ))}
                </View>
            </ScrollView>
            <View style={styles.contentBottom}>
                <View style={[mainStyles.container, styles.contentButtons]}>
                    <TouchableOpacity
                        style={[mainStyles.btnBlueNew, styles.buttonLarge]}
                        onPress={() => navigation.goBack()}>
                        <Text style={mainStyles.btnTextBlueNew}>VOLTAR</Text>
                    </TouchableOpacity>
                </View>
            </View>
        </View>
    );
}

export default RepasseListaEtapas;