import { StyleSheet, Dimensions, Platform } from 'react-native';

const windowWidth = Dimensions.get('window').width;

const styles = StyleSheet.create({
    wrapper: {
        justifyContent: "space-between"
    },
    contentBottom: {
      paddingTop: 20,
      paddingBottom: 40,
      shadowRadius: Platform.OS === 'ios' ? 2 : 16,
      shadowOffset: {
        width: 0,
        height: Platform.OS === 'ios' ? -2 : 12,
      },
      shadowColor: Platform.OS === 'ios' ? '#CCC' : '#000',
      elevation: Platform.OS === 'ios' ? 2 : 24,
      shadowOpacity: 1,
      backgroundColor: '#FFF',
    },
    contentButtons: {
      flexDirection: 'row',
      justifyContent: 'space-between',
    },
    button: {
      width: '100%',
    },
    buttonLarge: {
      width: '100%',
    },
    container: {
        maxWidth: 340,
        marginLeft: ((windowWidth - 340) /2),
        marginTop: 10
    },
    buttonDate: {
        backgroundColor: "#90B0C0",
        height: 58,
        width: '100%',
        alignItems: "center",
        justifyContent: "center"
    },
    icArrow: {
        position: "absolute",
        top: 23,
        left: 25
    },
    btnTextButtonDate: {
        fontFamily: "Ubuntu-Regular",
        color: "#FFF",
        fontSize: 15,
        letterSpacing: 0.46
    },
    contentOption: {
        flexDirection: "row",
        alignItems: "center",
        marginTop: 25,
        marginBottom: 25
    },  
    textStatusBold: {
        fontFamily: "Roboto-Bold",
        color: "#00467F",
        fontSize: 15,
        letterSpacing: 1,
        marginLeft: 15,
        textTransform: "uppercase"
    },
    inputText: {
        backgroundColor: "#FFF",
        width: 125,
        paddingLeft: 13,
    },
    buttonCenterBlue: {
        width: 70,
        height: 50,
        marginTop: 10,
        borderRadius: 5,
        marginLeft: 15
    },
    textBtnCenterBlue: {
        fontSize: 20
    }, 
    rowCenter: {
        flexDirection: "row",
        justifyContent: "space-between",
        alignItems: 'center',
        marginBottom: 15,
        marginTop: 10
    },
    rowCenterMargin: {
        flexDirection: "row",
        alignItems: 'center',
        marginVertical: 20
    },
    optionTextBold: {
        fontFamily: "Roboto-Bold",
        fontSize: 12,
        letterSpacing: 0.7,
        color: "#4ea1cc",
        lineHeight: 18
    },
    boxFlex: {
        flexDirection: "column",
        justifyContent: "space-between",
        marginBottom: 10
    },
    boxFlexColumn: {
        flexDirection: "column"
    },
    boxIconWidth: {
        width: 40,
        flexDirection: "row",
        justifyContent: "center"
    },
    boxFlexWithText: {
        flexDirection: "row",
        alignItems: "center",
        justifyContent: "space-between",
        paddingRight: 15,
        paddingLeft: 15,
        backgroundColor: "#F2F2F2",
        borderLeftWidth: 15,
        paddingTop: 10,
        paddingBottom: 10
    },
    textBoxFlex: {
        fontFamily: "Ubuntu-Regular",
        color: "#00467F",
        fontSize: 14,
        letterSpacing: 1,
        marginTop: 3
    },
    textBoxFlexBold: {
        fontFamily: "Ubuntu-Bold",
        color: "#00467F",
        fontSize: 20,
        letterSpacing: 1,
    },
    textAboutProcessBold: {
        fontFamily: "Ubuntu-Bold",
        color: "#00467F",
        fontSize: 14,
        letterSpacing: 1,
        width: 280,
        marginTop: 5,
        marginBottom: 5
    },
    textAboutProcess: {
        fontFamily: "Ubuntu-Regular",
        color: "#828282",
        fontSize: 14,
        letterSpacing: 1,
        marginLeft: 5
    },
    btnTextOutlineBlue: {
        fontFamily: "Roboto-Bold",
        color: "#00467F",
        fontSize: 25,
        fontWeight: "bold",
        letterSpacing: 1,
        textAlign: "center"
    },
    boxFlexColumn: {
        flexDirection: "column"
    },
    textSearch: {
        fontFamily: "Ubuntu-Regular",
        color: "#00467F",
        fontSize: 17,
        letterSpacing: 1,
        marginTop: 15,
        marginBottom: 10
    },
    searchBox: {
        flexDirection: "row",
        justifyContent: "center",
        alignItems: "center",
        paddingBottom: 10
    },
    input: {
        borderWidth: 1,
        borderColor: "#E0E0E0",
        width: 235,
        height: 55,
        paddingLeft: 10,
        paddingRight: 10,
        fontSize: 16,
        color: "828282",
        marginLeft: 10
    },
    btnOk: {
        backgroundColor: "#2D719F",
        height: 55,
        width: 55,
        justifyContent: "center",
        alignItems: "center",
        marginLeft: 10
    },
    resultPV: {
        borderLeftColor: "#F2F2F2",
        borderLeftWidth: 15,
        paddingTop: 5,
        paddingBottom: 5,
        paddingRight: 5,
        paddingLeft: 8,
        marginTop: 10,
        flexDirection: "row",
        alignItems: "center",
        justifyContent: "space-between"
    },
    divider: {
        height: 1,
        backgroundColor: "#DADADA",
        marginTop: 15,
        marginBottom: 10
    },
    boxTitle: {
        flexDirection: "row",
        alignItems: "center"
    },
    label: {
        backgroundColor: "#2D719F",
        paddingLeft: 7,
        paddingRight: 7,
        paddingTop: 3,
        paddingBottom: 3,
        flexDirection: "row",
        alignItems: "center",
        justifyContent: "center",
        marginLeft: 13
    },
    textLabel: {
        fontFamily: "Ubuntu-Regular",
        color: "#FFF",
        fontSize: 15,
    }
});

export default styles;