import { StyleSheet, Dimensions, Platform } from 'react-native';

const windowWidth = Dimensions.get('window').width;

const styles = StyleSheet.create({
    wrapper: {
        justifyContent: "space-between"
    },
    contentBottom: {
      paddingTop: 20,
      paddingBottom: 40,
      shadowRadius: Platform.OS === 'ios' ? 2 : 16,
      shadowOffset: {
        width: 0,
        height: Platform.OS === 'ios' ? -2 : 12,
      },
      shadowColor: Platform.OS === 'ios' ? '#CCC' : '#000',
      elevation: Platform.OS === 'ios' ? 2 : 24,
      shadowOpacity: 1,
      backgroundColor: '#FFF',
    },
    contentButtons: {
      flexDirection: 'row',
      justifyContent: 'space-between',
    },
    button: {
      width: '100%',
    },
    buttonLarge: {
      width: '100%',
    },
    container: {
        maxWidth: 340,
        marginLeft: ((windowWidth - 340) /2),
        marginTop: 10
    },
    contentOption: {
        marginBottom: 15
    },
    textStatus: {
        fontFamily: "Ubuntu-Regular",
        color: "#00467F",
        fontSize: 15,
        letterSpacing: 1,
        marginBottom: 10
    },
    textStatusBold: {
        fontFamily: "Roboto-Bold",
        color: "#00467F",
        fontSize: 15,
        letterSpacing: 1,
        marginTop: 5,
        marginBottom: 25
    },
    inputText: {
        backgroundColor: "#FFF",
        width: 125,
        paddingLeft: 13,
    },
    buttonCenterBlue: {
        width: 70,
        height: 50,
        marginTop: 10,
        borderRadius: 5,
        marginLeft: 15
    },
    textBtnCenterBlue: {
        fontSize: 20
    }, 
    rowCenter: {
        flexDirection: "row",
        justifyContent: "space-between",
        alignItems: 'center',
        marginBottom: 15,
        marginTop: 10
    },
    rowCenterMargin: {
        flexDirection: "row",
        alignItems: 'center',
        marginVertical: 20
    },
    optionTextBold: {
        fontFamily: "Roboto-Bold",
        fontSize: 12,
        letterSpacing: 0.7,
        color: "#4ea1cc",
        lineHeight: 18
    },
    boxFlex: {
        flexDirection: "row",
        justifyContent: "space-between",
        alignItems: "center",
        backgroundColor: "#F2F2F2",
        minHeight: 65,
        marginBottom: 15,
        paddingRight: 10,
        borderLeftColor: "#90B0C0",
        borderLeftWidth: 15
    },
    boxIconWidth: {
        width: 40,
        flexDirection: "row",
        justifyContent: "center"
    },
    boxFlexWithText: {
        flexDirection: "row",
        justifyContent: "space-between",
        alignItems: "center",
        width: "70%"
    },
    textBoxFlex: {
        fontFamily: "Ubuntu-Regular",
        fontSize: 15,
        letterSpacing: 1,        
        lineHeight: 18,
        color: "#00467F",
        marginLeft: 10
    },
    textBoxFlexNumber: {
        fontFamily: "Ubuntu-Bold",
        color: "#00467F",
        fontSize: 23,
        fontWeight: "bold",
        textAlign: "right"
    },
    btnTextOutlineBlue: {
        fontFamily: "Roboto-Bold",
        color: "#00467F",
        fontSize: 25,
        fontWeight: "bold",
        letterSpacing: 1,
        textAlign: "center"
    },
    boxFlexColumn: {
        flexDirection: "column"
    },
    textSearch: {
        fontFamily: "Ubuntu-Regular",
        color: "#00467F",
        fontSize: 17,
        letterSpacing: 1,
        marginTop: 25,
        marginBottom: 10
    },
    textBold: {
        fontFamily: "Ubuntu-Bold",
        fontSize: 19,
        marginTop: 0,
        marginBottom: 0
    },
    searchBox: {
        flexDirection: "row",
        justifyContent: "center",
        alignItems: "center",
        // paddingBottom: 30
    },
    input: {
        borderWidth: 1,
        borderColor: "#E0E0E0",
        flex: 1,
        height: 55,
        paddingLeft: 10,
        paddingRight: 10,
        fontSize: 16,
        color: "828282",
        marginLeft: 10
    },
    btnOk: {
        backgroundColor: "#2D719F",
        height: 55,
        width: 55,
        justifyContent: "center",
        alignItems: "center",
        marginLeft: 10
    },
    divider: {
        height: 1,
        backgroundColor: "#DADADA",
        marginTop: 25,
        marginBottom: 15
    }
});

export default styles;