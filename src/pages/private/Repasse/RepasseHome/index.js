import React, { useEffect, useState } from 'react';
import { TextInput, Text, View, TouchableOpacity, ActivityIndicator, SafeAreaView, Alert, Platform, KeyboardAvoidingView } from 'react-native';
import { useIsFocused, useNavigation } from '@react-navigation/native';
import moment from 'moment';
import Alert2 from '../../../../assets/svgs/Alert2';
import Like from '../../../../assets/svgs/Like';
import Loading from '../../../../assets/svgs/Loading';
import Check2 from '../../../../assets/svgs/Check2';
import Search2 from '../../../../assets/svgs/Search2';
import Arrow from '../../../../assets/svgs/ArrowRightGray2';

import PrivateHeader from '../../../../components/headers/PrivateHeader';

import styles from './styles';
import mainStyles from '../../../../mainStyles';
import { ScrollView } from 'react-native-gesture-handler';
import api from '../../../../services/api';
import { useAuth } from '../../../../context/auth';
import { RepassePvSearch } from '../../../../components/RepassePvSearch';

const RepasseHome = ({ navigation }) => {
    const isFocused = useIsFocused();

    const [loading, setLoading] = useState(true);
    const [loadingSearch, setLoadingSearch] = useState(false);

    const [etapas, setEtapas] = useState([]);
    const [atualizadoEm, setAtualizadoEm] = useState(null);

    const [search, setSearch] = useState('');
    
    useEffect(() => {
        if(isFocused) getData();
    }, []);
    
    useEffect(() => {
        getData();
    }, [isFocused]);

    const getData = () => {
        setLoading(true);
        api.get(`repasses-salesforce`).then(res => {
            setEtapas(res.data?.etapas ?? []);
            setAtualizadoEm(res.data?.atualizado_em ?? '');

            setLoading(false);
        }).catch(error => {
            console.log(error)
        }).then(() => setLoading(false));
    }

    const handleSearch = () => {
        
        if(search.length < 6){
            Alert.alert('PV inválido', 'Informe os 6 dígitos da PV.');
            return;
        }

        setLoadingSearch(true);
        setSearch('');

        api.get(`repasses-salesforce/busca/${search}`).then(res => {
            if(res.data.processos.length > 0){
                navigation.navigate('RepasseInterna', { pv: `PV-${search}` });
            } else {
                Alert.alert('Não encontrado', 'Não encontramos a PV informada. Por favor, tente novamente.');
            }
            setSearch('');
        }).catch(error => {
            console.log('error', error);
        }).then(() => setLoadingSearch(false));
        
    }

    const Total = ({total}) => {
        return (
            <View>
                {loading &&
                    <View style={{ flex: 1, justifyContent: "center", alignItems: "center", }}>
                        <ActivityIndicator size="large" color="#00467F" />
                    </View>
                }
                {!loading &&
                    <Text style={styles.textBoxFlexNumber}>{total}</Text>
                }
            </View>
        );
    }

    return (
        <View style={mainStyles.wrapper}>
            <PrivateHeader title={`Repasse`} />
            <View style={mainStyles.container}>
                <RepassePvSearch />
                <View style={styles.divider}></View>
                <View style={styles.rowOptions}>
                    <View style={styles.contentOption}>
                        {atualizadoEm &&
                            <Text style={styles.textStatus}>{`Atualizado em ${atualizadoEm}`}</Text>
                        }
                        <Text style={styles.textStatus}>{`Selecione a etapa desejada:`}</Text>
                    </View>
                </View>
            </View>
            <ScrollView keyboardShouldPersistTaps={`handled`} showsVerticalScrollIndicator={false}>
                <View style={mainStyles.container}>
                    {etapas.map((etapa, index) => (
                        <TouchableOpacity 
                            key={index}
                            onPress={() => navigation.navigate(etapa.slug === 'processos-concluidos' ? 'RepasseListaProcessos' : 'RepasseListaEtapas', {
                                titulo: etapa.titulo,
                                slug: etapa.slug
                            })}
                        >
                            <View style={[styles.boxFlex, etapa.class === 'error' ? {borderLeftColor: '#FF6542'} : null]}>
                                <View style={styles.boxFlexWithText}>
                                    <Text style={styles.textBoxFlex}>{etapa.titulo}</Text>
                                </View>
                                <Total total={etapa.total} />
                                <Arrow />
                            </View>
                        </TouchableOpacity>
                    ))}
                </View>
            </ScrollView>
            <View style={styles.contentBottom}>
                <View style={[mainStyles.container, styles.contentButtons]}>
                    <TouchableOpacity
                        style={[mainStyles.btnBlueNew, styles.buttonLarge]}
                        onPress={() => navigation.goBack()}>
                        <Text style={mainStyles.btnTextBlueNew}>VOLTAR</Text>
                    </TouchableOpacity>
                </View>
            </View>
        </View>
    );
}

export default RepasseHome;