import React, { useEffect, useState } from 'react';
import { Text, View, TouchableOpacity, ScrollView, ActivityIndicator, Linking, Alert } from 'react-native';

import UserCheck from '../../../assets/svgs/UserCheck';
import UserOut from '../../../assets/svgs/UserOut';
import Celphone from '../../../assets/svgs/Celphone';
import Email from '../../../assets/svgs/Email';
import Whats from '../../../assets/svgs/Whats2';
import EmailDisabled from '../../../assets/svgs/EmailDisabled';
import WhatsDisabled from '../../../assets/svgs/WhatsDisabled';
import CelphoneDisabled from '../../../assets/svgs/CelphoneDisabled';

import Dash from 'react-native-dash';

import PrivateHeader from '../../../components/headers/PrivateHeader';

import styles from './styles';
import mainStyles from '../../../mainStyles';

import api from '../../../services/api';

import GetLocation from 'react-native-get-location';
import { clearPhone } from '../../../useful/conversions';

const PrivateContacts = ({navigation, route}) => {
    const { shift } = route.params;

    const [loading, setLoading] = useState(false);
    const [contacts, setContacts] = useState([])

    useEffect(() => {
        getContacts();
    }, [])

    const getContacts = async () => {
        api.get(`/plantoes/${shift.id}/contato`).then(res => {
            let GPsAtivos = [];
            let CoordsAtivos = [];
            let GPsInativos = [];
            let CoordsInativos = [];

            res.data.data.gps.map(gp => {
                if(gp.is_ativo){
                    GPsAtivos.push(gp);
                } else {
                    GPsInativos.push(gp);
                }
            });
            res.data.data.coordenadores.map(coord => {
                if(coord.is_ativo){
                    CoordsAtivos.push(coord);
                } else {
                    CoordsInativos.push(coord);
                }
            });

            setContacts([...GPsAtivos, ...CoordsAtivos, ...GPsInativos, ...CoordsInativos]);
        }).catch(err => {
            console.log(err.response);
        });
    }

    return (
        <View style={mainStyles.wrapper}>
            <PrivateHeader title={`Plantões`} back={() => navigation.navigate('PrivateMain')} />
            <View style={[mainStyles.privateContainer, {flex: 1}]}>
                {loading && 
                    <View style={{flex: 1, justifyContent: "center", alignItems: "center"}}>
                        <ActivityIndicator size="large" color="#00467F" />
                    </View>
                }
                {!loading &&
                <>
                    <ScrollView showsVerticalScrollIndicator={false}>
                        <View style={styles.shift}>
                            <View style={styles.infos}>
                                <View style={styles.devBox}>
                                    <Text style={styles.devTitle}>Contatos</Text>
                                    <Text style={styles.devName}>{shift.imovel_nome}</Text>
                                </View>
                                <View style={styles.divider}></View>
                            </View>
                            {/* <Dash dashThickness={1} dashLength={3}  dashColor="#DADADA" /> */}
                            {contacts.map((contact, index) => (
                                <View key={index}>
                                    <View style={styles.boxNameCenter}>
                                        <Text style={[styles.textRight, contact.is_ativo ? null : {color: "#828282"}]}>{contact.titulo_cargo}: <Text style={[styles.nameActive, contact.is_ativo ? null : {color: "#828282"}]}>{contact.apelido}</Text></Text>
                                    </View>
                                    <View style={styles.actions}>
                                        {/* {!contact.is_ativo &&
                                            <UserOut style={styles.icUser} />
                                        }
                                        {contact.is_ativo &&
                                            <UserCheck style={styles.icUser} />
                                        } */}
                                        <View style={styles.boxOption}>
                                            <TouchableOpacity 
                                                disabled={!contact.is_ativo || contact.telefone === null}
                                                style={[styles.btnBorder, contact.is_ativo ? null : styles.btnBorderDisabled]}
                                                onPress={() => Linking.openURL(`tel:+55${clearPhone(contact.telefone)}`) }>
                                                    {contact.is_ativo &&
                                                        <Celphone style={styles.icCel}/>
                                                    }
                                                    {!contact.is_ativo &&
                                                        <CelphoneDisabled style={styles.icCel}/>
                                                    }
                                            </TouchableOpacity>
                                            <Text style={styles.textOption}>Telefone</Text>
                                        </View>
                                        <View style={styles.boxOption}>
                                            <TouchableOpacity 
                                                disabled={!contact.is_ativo || contact.telefone === null}
                                                style={[styles.btnBorder, contact.is_ativo ? null : styles.btnBorderDisabled]}
                                                onPress={() => Linking.openURL(`https://wa.me/55${clearPhone(contact.telefone)}`) }>
                                                {contact.is_ativo &&
                                                    <Whats style={styles.icContact}/>
                                                }
                                                {!contact.is_ativo &&
                                                    <WhatsDisabled style={styles.icContact}/>
                                                }
                                            </TouchableOpacity>
                                            <Text style={styles.textOption}>WhatsApp</Text>
                                        </View>
                                        <View style={styles.boxOption}>
                                            <TouchableOpacity 
                                                disabled={!contact.is_ativo || contact.email === null} 
                                                style={[styles.btnBorder, contact.is_ativo ? null : styles.btnBorderDisabled]} 
                                                onPress={() => Linking.openURL(`mailto:${contact.email}`) }
                                            >
                                                {contact.is_ativo &&
                                                    <Email style={styles.icEmail}/>
                                                }
                                                {!contact.is_ativo &&
                                                    <EmailDisabled style={styles.icEmail}/>
                                                }
                                            </TouchableOpacity>
                                            <Text style={styles.textOption}>E-mail</Text>
                                        </View>
                                    </View>
                                    <View style={styles.divider}></View>
                                    {/* <Dash dashThickness={1} dashLength={3}  dashColor="#DADADA" /> */}
                                </View>
                            ))}
                        </View>
                    </ScrollView>
                </>
                }
            </View>
            <View style={styles.contentBottom}>
                <View style={[mainStyles.container, styles.contentButtons]}>
                    <TouchableOpacity style={[mainStyles.btnBlueNew, styles.buttonLarge]} onPress={() => navigation.navigate('PrivateShifts')}>
                        <Text style={mainStyles.btnTextBlueNew}>VOLTAR</Text>
                    </TouchableOpacity>
                </View>
            </View>
        </View>
    );
}

export default PrivateContacts;