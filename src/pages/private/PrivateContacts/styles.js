import { StyleSheet, Dimensions, Platform } from 'react-native';

const windowWidth = Dimensions.get('window').width;

const styles = StyleSheet.create({
    wrapper: {
        justifyContent: "space-between"
    },
    contentBottom: {
        paddingTop: 20,      
        paddingBottom: 40,
        shadowRadius: Platform.OS === 'ios' ? 2 : 16,
        shadowOffset: {
            width: 0,
            height: Platform.OS === 'ios' ? -2 : 12,
        },
        shadowColor: Platform.OS === 'ios' ? "#CCC" : "#000",
        elevation: Platform.OS === 'ios' ? 2 : 24,
        shadowOpacity: 1,
        backgroundColor: "#FFF",
        zIndex: 10
    },
    buttonLarge: {
        width: "100%"
    },
    btnTextOutlineBlue: {
        fontFamily: "Roboto-Regular",
        color: "#FFF",
        fontSize: 25,
        letterSpacing: 1,
        textAlign: "center"
    },
    menu: {
        flexDirection: "row",
        backgroundColor: "#FFF",
        alignItems: 'center',
        justifyContent: 'center',
        borderRadius: 12,
        shadowColor: "#000",
        shadowOffset: {
            width: 0,
            height: 9,
        },
        shadowOpacity: 0.1,
        shadowRadius: 11.95,
        elevation: 12,
        marginTop: 15,
        paddingLeft: 30,
        paddingRight: 30,
        paddingBottom: 15,
        paddingTop: 15
    },
    menuItem: {
        flex: 1,
        flexDirection: "row",
        justifyContent: "center",
        alignItems: "center"
    },
    separator: {
        marginLeft: 25,
        marginRight: 25,
        width: 1,
        height: '70%',
        backgroundColor: '#BDBDBD'
    },
    shift: {
        paddingTop: 30,
        paddingBottom: 0
    },
    infos: {
        marginBottom: 15
    },
    devTitle: {
        fontFamily: "Ubuntu-Regular",
        fontSize: Platform.OS === 'ios' ? 14 : 14,
        letterSpacing: 1,
        color: "#00467F"
    },
    devName: {
        fontFamily: "Ubuntu-Medium",
        fontSize: Platform.OS === 'ios' ? 21 : 19,
        letterSpacing: 1,
        color: "#00467F",
        marginBottom: 10,
        marginTop: 5
    },
    divider: {
        height: 1,
        backgroundColor: "#DADADA",
        marginTop: 10,
        marginBottom: 10
    },
    boxNameCenter: {
        marginTop: 5,
        marginBottom: 5
    },
    textRight: {
        fontFamily: "Ubuntu-Regular",
        fontSize: Platform.OS === 'ios' ? 16 : 14,
        letterSpacing: 1,
        color: "#1B9C20"
    },
    nameActive: {
        fontFamily: "Ubuntu-Regular",
        fontSize: Platform.OS === 'ios' ? 16 : 14,
        letterSpacing: 1,
        color: "#1B9C20"
    },
    actions: {
        flexDirection: "row",
        marginTop: 15,
        marginBottom: 10
    },
    boxOption: {
        width: 75,
        marginRight: 10
    },
    btnBorder: {
        alignItems: "center",
        justifyContent: "center",
        backgroundColor: "#F2F2F2",
        height: 65,
        width: 75
    },
    btnBorderDisabled: {
        // borderColor: "#979797",
        backgroundColor: "#F2F2F2"
    },
    textOption: {
        fontFamily: "Ubuntu-Regular",
        fontSize: Platform.OS === 'ios' ? 12 : 12,
        letterSpacing: 1,
        color: "#828282",
        textAlign: "center",
        marginTop: 5
    },
    icUser: {
        width: 55,
        marginTop: 5
    },
    icCel: {
        width: 30,
    },
    icContact: {
        width: 45,
    },
    notShifts: {
        fontFamily: "Roboto-Regular",
        fontSize: 16,
        textAlign: "center",
        marginTop: 45
    }
});

export default styles;