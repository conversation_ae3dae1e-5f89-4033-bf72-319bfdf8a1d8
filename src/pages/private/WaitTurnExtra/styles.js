import { StyleSheet, Dimensions, Platform } from 'react-native';

const windowWidth = Dimensions.get('window').width;

const styles = StyleSheet.create({
    wrapper: {
        justifyContent: "space-between"
    },
    container: {
        width: windowWidth * 0.9,
        marginLeft: windowWidth * 0.05,
        marginTop: 10
    },
    rowOptions: {
        flexDirection: "row",
        justifyContent: "space-between"
    },
    provisionalRow: {
        flexDirection: "row",
        flexWrap: "wrap",
        borderColor: "#e0e0e0",
        borderWidth: 1,
        borderRadius: 14,
        paddingTop: 14,
        paddingBottom: 14,
        paddingLeft: 26,
        paddingRight: 26,
        height: 120
    },
    provisionalText: {
        fontFamily: "Roboto-Regular",
        color: "#979797",
        marginTop: Platform.OS === 'ios' ? 16 : 12,
        fontSize: Platform.OS === 'ios' ? 18 : 16,
        width: 225,
        lineHeight: 15,
        letterSpacing: 1,
        fontSize: 12
    },
    spanColor: {
        color: "#FF312E",
        fontWeight: "bold",
    },
    textStatus: {
        fontFamily: "Ubuntu-Regular",
        color: "#2D719F",
        letterSpacing: 1,
        marginTop: 20,
        fontSize: Platform.OS === 'ios' ? 16 : 14
    },
    textWait: {
        color: '#2D719F',
        fontFamily: 'Ubuntu-Medium',
        fontSize: 22,
        letterSpacing: 1,
        marginTop: 5
    },
    icCalendar: {
        width: '85'
    },
    option: {
        alignItems: 'center',
        height: 160,
        backgroundColor: "#90B0C0",
        alignItems: 'center',
        justifyContent: 'center',
        borderRadius: 12,
        // shadowColor: "#000",
        // shadowOffset: {
        //     width: 0,
        //     height: 9,
        // },
        // shadowOpacity: 0.1,
        // shadowRadius: 11.95,
        // elevation: 12,
        marginTop: 25,
        position: 'relative'
    },
    optionNumber: {
        fontSize: 105,
        fontFamily: 'Ubuntu-Bold',
        color: "#00467F"
    },
    contentTop: {        
        marginTop: 30
    },
    contentButtons: {
        flexDirection: "row",
        justifyContent: "space-between"
    },
    buttonCenterBlue: {
        width: '46%',
        backgroundColor: "#2D719F",
        borderRadius: 0
    },
    buttonCenterOutlineBlue: {
        width: '46%',
        borderColor: "#000",
        borderRadius: 0
    },
    textBtnCenterBlue: {
        fontSize: 14,
        fontFamily: "Ubuntu-Regular"
    },
    textBtnCenterOutlineBlue: {
        fontSize: 14,
        color: "#000",
        fontFamily: "Ubuntu-Regular"
    },
    boxWaitingRow: {
        flexDirection: "row",
        justifyContent: "space-between",
        borderColor: "#e0e0e0",
        borderWidth: 1,
        borderRadius: 14,
        paddingTop: 20,
        paddingBottom: 14,
        paddingLeft: 26,
        paddingRight: 26,
        height: 150,
        marginTop: 40,
    },
    contentWaiting: {
        marginTop: 17
    },
    textWaiting: {
        fontFamily: "Roboto-Regular",
        color: "#979797",
        letterSpacing: 1,
    },
    textWaitingBold: {
        fontFamily: "Roboto-Bold",
        color: "#979797",
        letterSpacing: 1,
        marginTop: -4
    },
    optionWaiting: {
        alignItems: "center"
    },
    optionText: {
       fontSize: 12,
       letterSpacing: 0.7,
       color: "#4ea1cc",
       marginTop: 5 
    },
    contentBottom: {
        paddingTop: 25,      
        paddingBottom: 40,
        shadowRadius: Platform.OS === 'ios' ? 2 : 16,
        shadowOffset: {
            width: 0,
            height: Platform.OS === 'ios' ? -2 : 12,
        },
        shadowColor: Platform.OS === 'ios' ? '#CCC' : "#000",
        elevation: Platform.OS === 'ios' ? 2 : 24,
        shadowOpacity: 1,
        backgroundColor: "#FFF",
        position: "relative"
    },
    boxShadow: {
        height: 20,
        backgroundColor: "#FFF",
        elevation: 5,
        shadowColor: "#000",
        marginBottom: 0,
        position: "absolute",
        top: -20,
        right: 0,
        left: 0
    },
    contentButton: {
        flexDirection: "row",
        justifyContent: "center"
    },
    buttonLarge: {
        width: '90%',
        height: 50
    }
});

export default styles;