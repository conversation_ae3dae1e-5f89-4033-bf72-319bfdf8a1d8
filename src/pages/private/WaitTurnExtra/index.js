import React, { useState } from 'react';
import { Text, View, TouchableOpacity, ActivityIndicator } from 'react-native';

import Calendar from '../../../assets/svgs/CalendarLight';

import PrivateHeader from '../../../components/headers/PrivateHeader';
import RowCheckin from '../../../components/RowCheckin';
import AlertFooter from '../../../components/AlertFooter';

import styles from './styles';
import mainStyles from '../../../mainStyles';

import { useService } from '../../../context/service';
import { ScrollView } from 'react-native-gesture-handler';
import { useAuth } from '../../../context/auth';

const WaitTurnExtra = ({ navigation }) => {

    const [loading, setLoading] = useState(false);
    const [confirmExit, setConfirmExit] = useState(false);
    const [row, setRow] = useState(false);

    const { exit } = useService();
    const { user, hasPermission } = useAuth();

    const handleExit = async () => {
        setLoading(true);
        setConfirmExit(false);
        const result = await exit();
        if (result) {
            navigation.navigate('PrivateMain');
        } else {
            Alert.alert('Erro', 'Não foi possível sair da fila, por favor, tente novamente.');
            setLoading(false);
        }
    }

    return (
        <>
            <View style={mainStyles.wrapper}>
                {row &&
                    <RowCheckin close={() => setRow(false)} />
                }
                {confirmExit &&
                    <AlertFooter
                        text={
                            <Text>
                                {`Você deseja realmente\nsair da fila?`}
                            </Text>
                        }
                        btnText={`SIM, SAIR DA FILA`}
                        btnBorder={true}
                        close={() => setConfirmExit(false)}
                        action={() => handleExit()}
                    />
                }
                <PrivateHeader title={`Fila`} back={() => navigation.navigate('PrivateMain')} />
                {loading &&
                    <View style={{ flex: 1, justifyContent: "center", alignItems: "center" }}>
                        <ActivityIndicator size="large" color="#00467F" />
                    </View>
                }
                {!loading &&
                    <ScrollView showsVerticalScrollIndicator={false}>
                        <View style={styles.container}>
                            <View style={mainStyles.bgHourCheckin}>
                                <Text style={mainStyles.textHourCheckin}>Horário do seu check in: {user.fila.checkin_horario}</Text>
                            </View>
                            {user.fila.sorteio_horario &&
                                <View>
                                    <Text style={mainStyles.sorteioText}>Horário do sorteio: {user.fila.sorteio_horario}</Text>
                                    <View style={styles.divider}></View>
                                </View>
                            }
                            <View style={styles.rowOptions}>
                                <View style={styles.contentOption}>
                                    <Text style={styles.textStatus}>Status e posição na fila:</Text>
                                    <Text style={styles.textWait}>Somente atendimento extra</Text>
                                </View>
                            </View>
                            <View style={styles.contentOption}>
                                <View style={styles.option}>
                                    <Calendar style={styles.icCalendar} />
                                </View>
                            </View>
                            <View style={styles.contentTop}>
                                <View style={styles.contentButtons}>
                                    {hasPermission('checkin/ver-fila') &&
                                        <TouchableOpacity style={[mainStyles.btnCenterBlue, styles.buttonCenterBlue]} onPress={() => navigation.navigate('RowCheckin')}>
                                            <Text style={[mainStyles.btnTextCenterBlue, styles.textBtnCenterBlue]}>VER FILA</Text>
                                        </TouchableOpacity>
                                    }
                                    <TouchableOpacity style={[mainStyles.btnCenterOutlineBlue, styles.buttonCenterOutlineBlue, !hasPermission('checkin/ver-fila') ? { width: '100%' } : null ]} onPress={() => setConfirmExit(true)}>
                                        <Text style={[mainStyles.btnTextCenterOutlineBlue, styles.textBtnCenterOutlineBlue]}>SAIR DA FILA</Text>
                                    </TouchableOpacity>
                                </View>
                            </View>
                        </View>
                    </ScrollView>
                }
                <View style={styles.contentBottom}>
                    <View style={styles.contentButton}>
                        <TouchableOpacity style={[mainStyles.btnBlueNew, styles.buttonLarge]} onPress={() => navigation.navigate('PrivateMain')}>
                            <Text style={mainStyles.btnTextBlueNew}>VOLTAR</Text>
                        </TouchableOpacity>
                    </View>
                </View>
            </View>
        </>
    );
}

export default WaitTurnExtra;