import { StyleSheet, Dimensions, Platform } from 'react-native';

const windowWidth = Dimensions.get('window').width;

const styles = StyleSheet.create({
    container: {
        width: windowWidth * 0.9,
        marginLeft: windowWidth * 0.05,
        marginTop: 10
    }, 
    rowOptions: {
        flexDirection: "row",
        justifyContent: "space-between"
    },
    provisionalRow: {
        flexDirection: "row",
        flexWrap: "wrap",
        borderColor: "#e0e0e0",
        borderWidth: 1,
        borderRadius: 14,
        paddingTop: 14,
        paddingBottom: 14,
        paddingLeft: 26,
        paddingRight: 26,
        height: 120
    },
    provisionalText: {
        fontFamily: "Roboto-Regular",
        color: "#979797",
        marginTop: Platform.OS === 'ios' ? 16 : 12,
        fontSize: Platform.OS === 'ios' ? 18 : 16,
        width: 225,
        lineHeight: 15,
        letterSpacing: 1,
        fontSize: 12
    },
    spanColor: {
        color: "#FF312E",
        fontWeight: "bold",
    },
    textStatus: {
        fontFamily: "Ubuntu-Regular",
        color: "#2D719F",
        letterSpacing: 1,
        marginTop: 20,
        fontSize: Platform.OS === 'ios' ? 16 : 14
    },
    textWait: {
        color: '#2D719F',
        fontFamily: 'Ubuntu-Medium',
        fontSize: 22,
        letterSpacing: 1,
        marginTop: 10
    },
    textAlert: {
        fontFamily: "Ubuntu-Regular",
        color: "#60BA64",
        letterSpacing: 1,
        marginTop: 10,
        fontSize: Platform.OS === 'ios' ? 18 : 16
    },
    option: {
        flexDirection: "row",
        alignItems: 'center',
        height: 160,
        backgroundColor: "#FFF",
        justifyContent: 'center',
        borderRadius: 12,
        // shadowColor: "#000",
        // shadowOffset: {
        //     width: 0,
        //     height: 9,
        // },
        // shadowOpacity: 0.1,
        // shadowRadius: 11.95,
        // elevation: 12,
        marginTop: 40,
        position: 'relative'
    },
    optionBgColor: {
        backgroundColor: "#60BA64D3",
        height: 160,
        position: 'absolute',
        bottom: 0,
        left: 0,
        right: 0,
        borderBottomRightRadius: 12,
        borderBottomLeftRadius: 12,
        borderRadius: 12
    },
    optionNumber: {
        fontSize: 80,
        fontFamily: 'Ubuntu-Regular',
        color: "#fff",
        marginLeft: 10
    },
    alertRow: {
        fontSize: 14,
        color: "#fff",
        letterSpacing: 1,
        marginTop: 5
    },
    flexBox: {
        flexDirection: "row",
        alignItems: "center"
    },
    contentTop: {        
        marginTop: 35
    },
    contentButtons: {
        flexDirection: "row",
        justifyContent: "space-between"
    },
    buttonCenterBlue: {
        width: '48%',
        backgroundColor: "#60BA64",
        borderWidth: 1,
        borderColor: "#60BA64",
        borderRadius: 0
    },
    buttonCenterOutlineBlue: {
        width: '48%',
        borderRadius: 0,
        borderColor: "#000"
    },
    textBtnCenterBlue: {
        fontSize: 14,
        fontFamily: "Ubuntu-Regular",
        textAlign: "center",
    },
    textBtnCenterOutlineBlue: {
        fontSize: 14,
        color: "#000",
        fontFamily: "Ubuntu-Regular"
    },
    boxWaitingRow: {
        flexDirection: "row",
        justifyContent: "space-between",
        marginTop: 35,
    },
    textClient: {
        fontFamily: "Ubuntu-Regular",
        color: "#2D719F",
        letterSpacing: 1,
    },
    nameClient: {
        fontFamily: "Ubuntu-Regular",
        color: "#2D719F",
        fontSize: 24,
        letterSpacing: 1,
        marginTop: 8 
    },
    divider: {
        borderTopColor: "#DADADA",
        borderTopWidth: 1,
        marginTop: 30
    },
    optionWaiting: {
        alignItems: "center"
    },
    optionText: {
       fontSize: 12,
       letterSpacing: 0.7,
       color: "#4ea1cc",
       marginTop: 5 
    }
});

export default styles;