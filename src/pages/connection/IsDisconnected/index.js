import React from 'react';
import { Text, TouchableOpacity, View, ScrollView, Linking } from 'react-native';

import RNRestart from 'react-native-restart';

import mainStyles from '../../../mainStyles';
import styles from './styles';

import Logo from '../../../assets/svgs/Logo';
import Disapproved from '../../../assets/svgs/Disapproved';

const RegisterAlreadyRegistered = () => {
    return (
        <ScrollView style={styles.wrapper} showsVerticalScrollIndicator={false}>
            <View style={mainStyles.container}>
                <Logo style={styles.logo}/>
                {/* <Disapproved style={styles.ic}/> */}
                <Text style={styles.title}>{`Sem\nconexão`}</Text>
                <Text style={styles.text}>
                    {`Para usar o app você precisa estar conectado à internet. Por favor, conecte-se e tente novamente.`}
                </Text>
                <TouchableOpacity 
                    style={[mainStyles.btnOutlineFullWhite, styles.button]}
                    onPress={() => RNRestart.Restart()}
                >
                    <Text style={mainStyles.btnTextOutlineFullWhite}>TENTAR NOVAMENTE</Text>
                </TouchableOpacity>
            </View>
        </ScrollView>
    );
}

export default RegisterAlreadyRegistered;