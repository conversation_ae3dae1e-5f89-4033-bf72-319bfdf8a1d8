import React, { useState, useRef, useEffect } from 'react';
import { TextInput, Text, TouchableOpacity, View, Alert, KeyboardAvoidingView, ScrollView, Keyboard, ActivityIndicator, Linking } from 'react-native';

import SimpleHeader from '../../../components/headers/SimpleHeader';
import LightboxWhats from '../../../components/LightboxWhats';
import { cpfMask } from '../../../useful/masks';

import mainStyles, { selectStyles } from '../../../mainStyles';
import styles from './styles';

import api from '../../../services/api';
import { useAuth } from '../../../context/auth';

import Whats from '../../../assets/svgs/Whats';


const AuthLogin = ({navigation}) => {

    const [inCpf, setInCpf] = useState('');
    const [inPassword, setInPassword] = useState('');
    const [loading, setLoading] = useState(false);
    const [keyboardVisible, setKeyboardVisible] = useState(false);
    const scrollViewRef = useRef();
    const [lightboxWhats, setLightboxWhats] = useState(false);

    const { signed, signIn, getCredentials } = useAuth();

    useEffect(() => {
        setCredentials();

        const keyboardDidShowListener = Keyboard.addListener('keyboardDidShow', () => {
            setKeyboardVisible(true);
          }
        );
        
        const keyboardDidHideListener = Keyboard.addListener( 'keyboardDidHide', () => {
              setKeyboardVisible(false); // or some other action
            }
        );
      
        return () => {
            keyboardDidHideListener.remove();
            keyboardDidShowListener.remove();
        };

    }, []);

    useEffect(() => {
        scroll();
    }, [keyboardVisible]);

    const setCredentials = () => {
        getCredentials().then(res => { 
            setInCpf(res?.cpf ?? '');
            setInPassword(res?.password ?? '');
        });
    }


    const scroll = () => {
        scrollViewRef.current.scrollToEnd({ animated: true });
    }

    const handleSignIn = async () => {
        setLoading(true);
        const res = await signIn(inCpf, inPassword);
        if(!res){
            setLoading(false);
        }
    }

    return (
        <KeyboardAvoidingView style={{ flex: 1 }} behavior="padding" >            
            <ScrollView contentContainerStyle={{ flexGrow: 1, backgroundColor: "#FFF" }} ref={scrollViewRef} keyboardShouldPersistTaps="handled" showsVerticalScrollIndicator={false}>
                    <View style={[mainStyles.wrapperRegister, styles.wrapper]} > 
                        <SimpleHeader />
                        {loading && 
                            <View style={{flex: 1, justifyContent: "center", alignItems: "center"}}>
                                <ActivityIndicator size="large" color="#00467F" />
                            </View>
                        }
                        {!loading &&
                            <>
                                <View style={[mainStyles.container, styles.container]}>
                                    <Text style={mainStyles.label}>CPF:</Text>
                                    <TextInput  
                                        placeholderTextColor="#BDBDBD"
                                        underlineColorAndroid="transparent"  
                                        style={mainStyles.inputText}
                                        value={inCpf}
                                        onFocus={scroll}
                                        onChangeText={text => setInCpf( cpfMask(text) )}
                                        keyboardType="phone-pad"
                                    />
                                    <Text style={mainStyles.label}>Senha:</Text>
                                    <TextInput  
                                        autoCapitalize="none"
                                        placeholderTextColor="#BDBDBD"                                         
                                        underlineColorAndroid="transparent"  
                                        style={mainStyles.inputText}
                                        value={inPassword}
                                        onFocus={scroll}
                                        onChangeText={text => setInPassword(text)}
                                        secureTextEntry={true}
                                    />
                                    <TouchableOpacity style={styles.btnPassword} onPress={() => navigation.navigate('AuthPasswordRecovery')}>
                                        <Text style={styles.btnTextPassword}>Esqueci a minha senha</Text>
                                    </TouchableOpacity>
                                </View>
                            </>
                        }
                    </View>
                </ScrollView>
                <View style={[mainStyles.contentBottomRegister, styles.contentNoPadding]}>
                    <View style={[mainStyles.container, styles.contentButtons]}>
                        <TouchableOpacity style={[mainStyles.btnOutlineBlue, mainStyles.buttonW48]} onPress={() => navigation.navigate('RegisterMain')}>
                            <Text style={mainStyles.btnTextOutlineBlue}>VOLTAR</Text>
                        </TouchableOpacity>
                        <TouchableOpacity style={[mainStyles.btnBlueLight, styles.btnLogin, mainStyles.buttonW48]} onPress={handleSignIn}>
                            <Text style={mainStyles.btnTextCenterBlueLight}>ENTRAR</Text>
                        </TouchableOpacity>
                    </View>
                </View>
                {lightboxWhats &&
                    <LightboxWhats 
                        close={() => setLightboxWhats(false)}
                    />
                }
        </KeyboardAvoidingView>
    );
}

export default AuthLogin;