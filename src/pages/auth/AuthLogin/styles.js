import { Platform, StyleSheet, Dimensions } from 'react-native';

const windowWidth = Dimensions.get('window').width;

const styles = StyleSheet.create({
    wrapper: {
        flexDirection: "column",
        justifyContent: "flex-start"
    },
    contentButtons: {
        flexDirection: "row",
        alignItems: "center",
        justifyContent: "space-between",
    },
    contentNoPadding: {
        paddingTop: 0,
        paddingBottom: 0
    },
    container: {
        flex: 1,
        paddingTop: 30
    },
    btnLogin: {
        marginTop: 12,
        marginBottom: 15
    },
    btnPassword:{
        backgroundColor: 'transparent',

        justifyContent: "center"
    },
    btnTextPassword: {
        fontFamily: 'Ubuntu-Regular',
        color: '#4EA1CC',
        fontSize: Platform.OS === 'ios' ? 15 : 14,
        textDecorationLine: "underline",
        textDecorationColor: "#4EA1CC"
    },
    btnWhats: {
        flexDirection: "row",
        alignItems: "center",
        marginTop: 50
    },
    boxWhats: {
        borderColor: "#2D719F",
        borderWidth: 1,
        borderRadius: 10,
        padding: 9,
        marginRight: 10
    },
    btnTextWhats: {
        fontFamily: 'Ubuntu-Regular',
        fontSize: Platform.OS === 'ios' ? 16 : 14,
        color: '#828282'
    },
    textBlue: {
        color: "#00467F"
    }
});

export default styles;