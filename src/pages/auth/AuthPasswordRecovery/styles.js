import { StyleSheet, Dimensions } from 'react-native';

const windowWidth = Dimensions.get('window').width;

const styles = StyleSheet.create({
    // wrapper: {
    //     justifyContent: "space-between"
    // },
    container: {
        flex: 1,
        paddingTop: 30
    },
    btnRequest: {
        marginTop: 25,
        marginBottom: 15
    },
    title: {
        fontSize: 22,
        fontFamily: 'Ubuntu-Regular',
        color: '#00467F',
        marginBottom: 45
    },
    textQuestion: {
        fontFamily: 'Ubuntu-Regular',
        fontSize: 13,
        color: '#828282',
        letterSpacing: 1,
        marginBottom: 45,
        lineHeight: 18
    }
});

export default styles;