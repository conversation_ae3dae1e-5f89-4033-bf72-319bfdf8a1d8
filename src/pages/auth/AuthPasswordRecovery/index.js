import React, { useState, useRef, useEffect } from 'react';
import { TextInput, Text, TouchableOpacity, View, Alert, KeyboardAvoidingView, ScrollView, Keyboard, ActivityIndicator, Linking } from 'react-native';

import SimpleHeader from '../../../components/headers/SimpleHeader';
import { cpfMask } from '../../../useful/masks';

import mainStyles, { selectStyles } from '../../../mainStyles';
import styles from './styles';

import api from '../../../services/api';

import AlertFooter from '../../../components/AlertFooter';
import AsyncStorage from "@react-native-async-storage/async-storage";

const AuthPasswordRecovery = ({ navigation }) => {

    const [inCpf, setInCpf] = useState('');
    const [inEmail, setInEmail] = useState('');
    const [showAlertFooter, setShowAlertFooter] = useState(false);
    const [loading, setLoading] = useState(false);
    const [keyboardVisible, setKeyboardVisible] = useState(false);
    const scrollViewRef = useRef();

    const send = () => {
        setLoading(true);
        api.post('/auth/reset-senha', { cpf: inCpf })
            .then(res => {
                setShowAlertFooter(true);
                setInEmail(res.data.email);
                setLoading(false);
            })
            .catch(err => {
                setLoading(false);
                if(err.response.data?.inativo === true || err.response.data?.errors?.is_cia){
                    let regional = err.response.data.regional;
                    let numero = regional == 'rj' ? '5521991143851' : '5511992509026';
                    let numeroFormatado = regional == 'rj' ? '(21) 99114-3851' : '(11) 99250-9026';
                    let mensagemHorario = regional == 'rj' ? '' : 'Sábado das 09:00 às 13:00';

                    let mensagem = err.response.data?.errors?.is_cia ? 'E-mail não cadastrado' : 'Seu cadastro foi desativado';

                    Alert.alert('Erro ao recuperar a senha', `${mensagem}, favor contatar seu gestor ou a Gestão de Autônomos no WhatsApp:\n\n${numeroFormatado}\n\nHorário de atendimento:\n2ª a 6ª das 09:00 às 18:00\n${mensagemHorario}`, [
                        {
                            text: 'Falar com a Gestão de Autônomos',
                            onPress: () => Linking.openURL(`whatsapp://send?&phone=${numero}`),
                            style: 'cancel',
                        },
                        {
                            text: 'Fechar',
                            style: 'cancel',
                        }
                    ]);
                } else {
                    Alert.alert('Atenção', err.response.data.errors.cpf[0], [
                        {
                            text: 'Fechar',
                        },
                    ]);
                }
            }).then(() => {
                AsyncStorage.setItem('@CuryCorretor:password', '');
            });

    }

    useEffect(() => {
        const keyboardDidShowListener = Keyboard.addListener('keyboardDidShow', () => {
            setKeyboardVisible(true);
        }
        );

        const keyboardDidHideListener = Keyboard.addListener('keyboardDidHide', () => {
            setKeyboardVisible(false); // or some other action
        }
        );

        return () => {
            keyboardDidHideListener.remove();
            keyboardDidShowListener.remove();
        };

    }, []);

    useEffect(() => {
        scroll();
    }, [keyboardVisible]);


    const scroll = () => {
        scrollViewRef.current.scrollToEnd({ animated: true });
    }

    return (
        <KeyboardAvoidingView style={{ flex: 1, backgroundColor: "#FFF" }} behavior="padding" >
                {showAlertFooter &&
                    <AlertFooter
                        text={
                            <Text>
                                {`Uma senha de recuperação foi enviada para o e-mail `}<Text style={{ fontFamily: 'Ubuntu-Bold' }}>{inEmail}</Text>
                            </Text>
                        }
                        btnText={`IR PARA O LOGIN`}
                        close={() => setShowAlertFooter(false)}
                        action={() => navigation.navigate('RegisterMain')}
                    />
                }
                
                <ScrollView contentContainerStyle={{ flexGrow: 1 }} ref={scrollViewRef} keyboardShouldPersistTaps="handled" showsVerticalScrollIndicator={false}>
                    
                        <SimpleHeader />
                        {loading &&
                            <View style={{ flex: 1, justifyContent: "center", alignItems: "center" }}>
                                <ActivityIndicator size="large" color="#00467F" />
                            </View>
                        }
                        {!loading &&
                            <>
                                <View style={mainStyles.wrapperRegister}>
                                    <View style={[mainStyles.container, styles.container]}>
                                        <Text style={styles.textQuestion}>Esqueceu sua senha? Não tem problema.</Text>
                                        {/* {inCpf.length > 0 && ''} */}
                                        <Text style={mainStyles.label}>Digite seu CPF:</Text>
                                        <TextInput
                                            underlineColorAndroid="transparent"  
                                            style={mainStyles.inputText}
                                            value={inCpf}
                                            onFocus={scroll}
                                            onChangeText={text => setInCpf( cpfMask(text) )}
                                            keyboardType="phone-pad"
                                        />

                                        <TouchableOpacity style={mainStyles.btnBlueLight} onPress={send}>
                                            <Text style={mainStyles.btnTextCenterBlueLight}>SOLICITAR NOVA SENHA</Text>
                                        </TouchableOpacity>
                                    </View>
                                </View>
                                <View style={mainStyles.contentBottomRegister}>
                                    <View style={[mainStyles.container, styles.contentButtons]}> 
                                        <TouchableOpacity style={mainStyles.btnBlueLight} onPress={() => navigation.goBack()}>
                                            <Text style={mainStyles.btnTextCenterBlueLight}>VOLTAR</Text>
                                        </TouchableOpacity>
                                    </View>
                                </View>
                            </>
                        }
                    
                </ScrollView>
        </KeyboardAvoidingView>
    );
}

export default AuthPasswordRecovery;