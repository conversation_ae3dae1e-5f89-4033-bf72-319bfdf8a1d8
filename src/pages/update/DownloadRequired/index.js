import React from 'react';
import { Platform, Text, TouchableOpacity, View, ScrollView, Linking } from 'react-native';

import RNRestart from 'react-native-restart';

import mainStyles from '../../../mainStyles';
import styles from './styles';

import Logo from '../../../assets/svgs/Logo';

const DownloadRequired = () => {
    // const url = Platform.OS === 'ios' ? 'https://apps.apple.com/br/app/cury-corretor/id1547639146' : 'https://play.google.com/store/apps/details?id=com.cury';
    return (
        <ScrollView style={styles.wrapper} showsVerticalScrollIndicator={false}>
            <View style={mainStyles.container}>
                <Logo style={styles.logo}/>
                <Text style={styles.title}>{`Ops, você precisa desinstalar esse app e instalar pela loja oficial`}</Text>
                <Text style={styles.text}>
                    {`Nosso aplicativo foi atualizado e é muito importante que você baixe a nova versão na loja oficial. Antes de baixar, você precisa desinstalar essa versão atual.  `}
                </Text>
                {/* <TouchableOpacity style={[mainStyles.btnOutlineWhite, styles.btn]} onPress={() => Linking.openURL(url)} >
                    <Text style={mainStyles.btnTextOutlineWhite}>
                        IR PARA A LOJA
                    </Text>
                </TouchableOpacity> */}
            </View>
        </ScrollView>
    );
}

export default DownloadRequired;