import { Platform, StyleSheet, Dimensions } from 'react-native';

const windowWidth = Dimensions.get('window').width;
const windowHeight = Dimensions.get('window').height;

const styles = StyleSheet.create({
    wrapper: {
        width: windowWidth,
        height: windowHeight,
        backgroundColor: '#00467F'
    },
    logo: {
        marginBottom: 70,
        marginTop: Platform.OS === 'ios' ? 60 : 30
    },
    ic: {
        width: 75,
        marginBottom: 25
    },
    title: {
        fontFamily: 'Ubuntu-Medium',
        fontSize: Platform.OS === 'ios' ? 38 : 36,
        color: '#FFF',
        marginBottom: 20,
        marginTop: 20,
        letterSpacing: 1
    },
    text: {
        fontFamily: 'Ubuntu-Regular',
        fontSize: Platform.OS === 'ios' ? 22 : 18,
        color: '#FFF',
        lineHeight: 24,
        marginBottom: 20
    },
    subtext: {
        fontFamily: 'Ubuntu-Bold',
        fontSize: Platform.OS === 'ios' ? 16 : 12,
        color: '#FFF',
        lineHeight: 18,
        marginBottom: 20
    },
    btn: {
        marginTop: 20
    }
});

export default styles;