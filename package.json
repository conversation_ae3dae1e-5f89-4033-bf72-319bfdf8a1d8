{"name": "cury_corretor", "version": "0.0.1", "private": true, "scripts": {"android": "react-native run-android", "ios": "react-native run-ios", "lint": "eslint .", "start": "react-native start", "test": "jest", "postinstall": "patch-package"}, "dependencies": {"@react-native-async-storage/async-storage": "^1.19.3", "@react-native-camera-roll/camera-roll": "^5.10.0", "@react-native-clipboard/clipboard": "^1.12.1", "@react-native-community/checkbox": "^0.5.20", "@react-native-community/netinfo": "^9.4.1", "@react-native-picker/picker": "^2.5.1", "@react-navigation/native": "^6.1.8", "@react-navigation/stack": "^6.3.18", "axios": "^1.5.1", "install": "^0.13.0", "laravel-echo": "^1.15.3", "lodash": "^4.17.21", "moment": "^2.29.4", "npm": "^10.2.0", "patch-package": "^8.0.0", "pusher-js": "^8.3.0", "react": "18.2.0", "react-native": "0.72.5", "react-native-blob-util": "^0.19.1", "react-native-calendars": "^1.1301.0", "react-native-dash": "^0.0.11", "react-native-device-info": "^10.11.0", "react-native-document-picker": "^9.3.0", "react-native-dotenv": "^3.4.9", "react-native-gesture-handler": "^2.13.1", "react-native-get-location": "^4.0.0", "react-native-get-random-values": "^1.11.0", "react-native-google-places-autocomplete": "^2.5.7", "react-native-image-crop-picker": "^0.40.0", "react-native-image-picker": "^5.7.0", "react-native-image-placeholder": "^1.0.14", "react-native-onesignal": "^4.5.2", "react-native-pdf": "^6.7.1", "react-native-picker-select": "^8.1.0", "react-native-progress": "^5.0.1", "react-native-render-html": "^6.3.4", "react-native-restart": "^0.0.27", "react-native-safe-area-context": "^4.7.2", "react-native-screens": "^3.25.0", "react-native-share": "^9.4.1", "react-native-svg": "^13.14.0", "react-native-video": "^5.2.1", "react-native-webview": "^13.6.2", "react-native-youtube-iframe": "^2.3.0"}, "overrides": {"react-native-picker-select": {"@react-native-picker/picker": "$@react-native-picker/picker"}}, "devDependencies": {"@babel/core": "^7.20.0", "@babel/preset-env": "^7.20.0", "@babel/runtime": "^7.20.0", "@react-native/eslint-config": "^0.72.2", "@react-native/metro-config": "^0.72.11", "@tsconfig/react-native": "^3.0.0", "@types/react": "^18.0.24", "@types/react-test-renderer": "^18.0.0", "babel-jest": "^29.2.1", "eslint": "^8.19.0", "jest": "^29.2.1", "metro-react-native-babel-preset": "0.76.8", "prettier": "^2.4.1", "react-test-renderer": "18.2.0", "typescript": "4.8.4"}, "engines": {"node": ">=16"}}