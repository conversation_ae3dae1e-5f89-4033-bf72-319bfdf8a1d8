PODS:
  - BEM<PERSON>heckBox (1.4.1)
  - boost (1.76.0)
  - DoubleConversion (1.1.6)
  - FBLazyVector (0.72.5)
  - FBReactNativeSpec (0.72.5):
    - RCT-Folly (= 2021.07.22.00)
    - RCTRequired (= 0.72.5)
    - RCTTypeSafety (= 0.72.5)
    - React-Core (= 0.72.5)
    - React-jsi (= 0.72.5)
    - ReactCommon/turbomodule/core (= 0.72.5)
  - fmt (6.2.1)
  - glog (0.3.5)
  - OneSignalXCFramework (3.12.6):
    - OneSignalXCFramework/OneSignalCore (= 3.12.6)
    - OneSignalXCFramework/OneSignalExtension (= 3.12.6)
    - OneSignalXCFramework/OneSignalOutcomes (= 3.12.6)
  - OneSignalXCFramework/OneSignalCore (3.12.6)
  - OneSignalXCFramework/OneSignalExtension (3.12.6):
    - OneSignalXCFramework/OneSignalCore
    - OneSignalXCFramework/OneSignalOutcomes
  - OneSignalXCFramework/OneSignalOutcomes (3.12.6):
    - OneSignalXCFramework/OneSignalCore
  - RCT-Folly (2021.07.22.00):
    - boost
    - DoubleConversion
    - fmt (~> 6.2.1)
    - glog
    - RCT-Folly/Default (= 2021.07.22.00)
  - RCT-Folly/Default (2021.07.22.00):
    - boost
    - DoubleConversion
    - fmt (~> 6.2.1)
    - glog
  - RCTRequired (0.72.5)
  - RCTTypeSafety (0.72.5):
    - FBLazyVector (= 0.72.5)
    - RCTRequired (= 0.72.5)
    - React-Core (= 0.72.5)
  - React (0.72.5):
    - React-Core (= 0.72.5)
    - React-Core/DevSupport (= 0.72.5)
    - React-Core/RCTWebSocket (= 0.72.5)
    - React-RCTActionSheet (= 0.72.5)
    - React-RCTAnimation (= 0.72.5)
    - React-RCTBlob (= 0.72.5)
    - React-RCTImage (= 0.72.5)
    - React-RCTLinking (= 0.72.5)
    - React-RCTNetwork (= 0.72.5)
    - React-RCTSettings (= 0.72.5)
    - React-RCTText (= 0.72.5)
    - React-RCTVibration (= 0.72.5)
  - React-callinvoker (0.72.5)
  - React-Codegen (0.72.5):
    - DoubleConversion
    - FBReactNativeSpec
    - glog
    - RCT-Folly
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-jsc
    - React-jsi
    - React-jsiexecutor
    - React-NativeModulesApple
    - React-rncore
    - ReactCommon/turbomodule/bridging
    - ReactCommon/turbomodule/core
  - React-Core (0.72.5):
    - glog
    - RCT-Folly (= 2021.07.22.00)
    - React-Core/Default (= 0.72.5)
    - React-cxxreact
    - React-jsc
    - React-jsi
    - React-jsiexecutor
    - React-perflogger
    - React-runtimeexecutor
    - React-utils
    - SocketRocket (= 0.6.1)
    - Yoga
  - React-Core/CoreModulesHeaders (0.72.5):
    - glog
    - RCT-Folly (= 2021.07.22.00)
    - React-Core/Default
    - React-cxxreact
    - React-jsc
    - React-jsi
    - React-jsiexecutor
    - React-perflogger
    - React-runtimeexecutor
    - React-utils
    - SocketRocket (= 0.6.1)
    - Yoga
  - React-Core/Default (0.72.5):
    - glog
    - RCT-Folly (= 2021.07.22.00)
    - React-cxxreact
    - React-jsc
    - React-jsi
    - React-jsiexecutor
    - React-perflogger
    - React-runtimeexecutor
    - React-utils
    - SocketRocket (= 0.6.1)
    - Yoga
  - React-Core/DevSupport (0.72.5):
    - glog
    - RCT-Folly (= 2021.07.22.00)
    - React-Core/Default (= 0.72.5)
    - React-Core/RCTWebSocket (= 0.72.5)
    - React-cxxreact
    - React-jsc
    - React-jsi
    - React-jsiexecutor
    - React-jsinspector (= 0.72.5)
    - React-perflogger
    - React-runtimeexecutor
    - React-utils
    - SocketRocket (= 0.6.1)
    - Yoga
  - React-Core/RCTActionSheetHeaders (0.72.5):
    - glog
    - RCT-Folly (= 2021.07.22.00)
    - React-Core/Default
    - React-cxxreact
    - React-jsc
    - React-jsi
    - React-jsiexecutor
    - React-perflogger
    - React-runtimeexecutor
    - React-utils
    - SocketRocket (= 0.6.1)
    - Yoga
  - React-Core/RCTAnimationHeaders (0.72.5):
    - glog
    - RCT-Folly (= 2021.07.22.00)
    - React-Core/Default
    - React-cxxreact
    - React-jsc
    - React-jsi
    - React-jsiexecutor
    - React-perflogger
    - React-runtimeexecutor
    - React-utils
    - SocketRocket (= 0.6.1)
    - Yoga
  - React-Core/RCTBlobHeaders (0.72.5):
    - glog
    - RCT-Folly (= 2021.07.22.00)
    - React-Core/Default
    - React-cxxreact
    - React-jsc
    - React-jsi
    - React-jsiexecutor
    - React-perflogger
    - React-runtimeexecutor
    - React-utils
    - SocketRocket (= 0.6.1)
    - Yoga
  - React-Core/RCTImageHeaders (0.72.5):
    - glog
    - RCT-Folly (= 2021.07.22.00)
    - React-Core/Default
    - React-cxxreact
    - React-jsc
    - React-jsi
    - React-jsiexecutor
    - React-perflogger
    - React-runtimeexecutor
    - React-utils
    - SocketRocket (= 0.6.1)
    - Yoga
  - React-Core/RCTLinkingHeaders (0.72.5):
    - glog
    - RCT-Folly (= 2021.07.22.00)
    - React-Core/Default
    - React-cxxreact
    - React-jsc
    - React-jsi
    - React-jsiexecutor
    - React-perflogger
    - React-runtimeexecutor
    - React-utils
    - SocketRocket (= 0.6.1)
    - Yoga
  - React-Core/RCTNetworkHeaders (0.72.5):
    - glog
    - RCT-Folly (= 2021.07.22.00)
    - React-Core/Default
    - React-cxxreact
    - React-jsc
    - React-jsi
    - React-jsiexecutor
    - React-perflogger
    - React-runtimeexecutor
    - React-utils
    - SocketRocket (= 0.6.1)
    - Yoga
  - React-Core/RCTSettingsHeaders (0.72.5):
    - glog
    - RCT-Folly (= 2021.07.22.00)
    - React-Core/Default
    - React-cxxreact
    - React-jsc
    - React-jsi
    - React-jsiexecutor
    - React-perflogger
    - React-runtimeexecutor
    - React-utils
    - SocketRocket (= 0.6.1)
    - Yoga
  - React-Core/RCTTextHeaders (0.72.5):
    - glog
    - RCT-Folly (= 2021.07.22.00)
    - React-Core/Default
    - React-cxxreact
    - React-jsc
    - React-jsi
    - React-jsiexecutor
    - React-perflogger
    - React-runtimeexecutor
    - React-utils
    - SocketRocket (= 0.6.1)
    - Yoga
  - React-Core/RCTVibrationHeaders (0.72.5):
    - glog
    - RCT-Folly (= 2021.07.22.00)
    - React-Core/Default
    - React-cxxreact
    - React-jsc
    - React-jsi
    - React-jsiexecutor
    - React-perflogger
    - React-runtimeexecutor
    - React-utils
    - SocketRocket (= 0.6.1)
    - Yoga
  - React-Core/RCTWebSocket (0.72.5):
    - glog
    - RCT-Folly (= 2021.07.22.00)
    - React-Core/Default (= 0.72.5)
    - React-cxxreact
    - React-jsc
    - React-jsi
    - React-jsiexecutor
    - React-perflogger
    - React-runtimeexecutor
    - React-utils
    - SocketRocket (= 0.6.1)
    - Yoga
  - React-CoreModules (0.72.5):
    - RCT-Folly (= 2021.07.22.00)
    - RCTTypeSafety (= 0.72.5)
    - React-Codegen (= 0.72.5)
    - React-Core/CoreModulesHeaders (= 0.72.5)
    - React-jsi (= 0.72.5)
    - React-RCTBlob
    - React-RCTImage (= 0.72.5)
    - ReactCommon/turbomodule/core (= 0.72.5)
    - SocketRocket (= 0.6.1)
  - React-cxxreact (0.72.5):
    - boost (= 1.76.0)
    - DoubleConversion
    - glog
    - RCT-Folly (= 2021.07.22.00)
    - React-callinvoker (= 0.72.5)
    - React-debug (= 0.72.5)
    - React-jsi (= 0.72.5)
    - React-jsinspector (= 0.72.5)
    - React-logger (= 0.72.5)
    - React-perflogger (= 0.72.5)
    - React-runtimeexecutor (= 0.72.5)
  - React-debug (0.72.5)
  - React-jsc (0.72.5):
    - React-jsc/Fabric (= 0.72.5)
    - React-jsi (= 0.72.5)
  - React-jsc/Fabric (0.72.5):
    - React-jsi (= 0.72.5)
  - React-jsi (0.72.5):
    - boost (= 1.76.0)
    - DoubleConversion
    - glog
    - RCT-Folly (= 2021.07.22.00)
  - React-jsiexecutor (0.72.5):
    - DoubleConversion
    - glog
    - RCT-Folly (= 2021.07.22.00)
    - React-cxxreact (= 0.72.5)
    - React-jsi (= 0.72.5)
    - React-perflogger (= 0.72.5)
  - React-jsinspector (0.72.5)
  - React-logger (0.72.5):
    - glog
  - react-native-blob-util (0.19.1):
    - React-Core
  - react-native-cameraroll (5.10.0):
    - React-Core
  - react-native-document-picker (9.3.0):
    - React-Core
  - react-native-get-random-values (1.11.0):
    - React-Core
  - react-native-image-picker (7.0.0):
    - React-Core
  - react-native-netinfo (9.4.1):
    - React-Core
  - react-native-onesignal (4.5.2):
    - OneSignalXCFramework (= 3.12.6)
    - React (< 1.0.0, >= 0.13.0)
  - react-native-pdf (6.7.1):
    - React-Core
  - react-native-render-html (6.3.4):
    - React-Core
  - react-native-restart (0.0.27):
    - React-Core
  - react-native-safe-area-context (4.7.2):
    - React-Core
  - react-native-video (5.2.1):
    - React-Core
    - react-native-video/Video (= 5.2.1)
  - react-native-video/Video (5.2.1):
    - React-Core
  - react-native-webview (13.6.2):
    - React-Core
  - React-NativeModulesApple (0.72.5):
    - React-callinvoker
    - React-Core
    - React-cxxreact
    - React-jsi
    - React-runtimeexecutor
    - ReactCommon/turbomodule/bridging
    - ReactCommon/turbomodule/core
  - React-perflogger (0.72.5)
  - React-RCTActionSheet (0.72.5):
    - React-Core/RCTActionSheetHeaders (= 0.72.5)
  - React-RCTAnimation (0.72.5):
    - RCT-Folly (= 2021.07.22.00)
    - RCTTypeSafety (= 0.72.5)
    - React-Codegen (= 0.72.5)
    - React-Core/RCTAnimationHeaders (= 0.72.5)
    - React-jsi (= 0.72.5)
    - ReactCommon/turbomodule/core (= 0.72.5)
  - React-RCTAppDelegate (0.72.5):
    - RCT-Folly
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-CoreModules
    - React-jsc
    - React-NativeModulesApple
    - React-RCTImage
    - React-RCTNetwork
    - React-runtimescheduler
    - ReactCommon/turbomodule/core
  - React-RCTBlob (0.72.5):
    - RCT-Folly (= 2021.07.22.00)
    - React-Codegen (= 0.72.5)
    - React-Core/RCTBlobHeaders (= 0.72.5)
    - React-Core/RCTWebSocket (= 0.72.5)
    - React-jsi (= 0.72.5)
    - React-RCTNetwork (= 0.72.5)
    - ReactCommon/turbomodule/core (= 0.72.5)
  - React-RCTImage (0.72.5):
    - RCT-Folly (= 2021.07.22.00)
    - RCTTypeSafety (= 0.72.5)
    - React-Codegen (= 0.72.5)
    - React-Core/RCTImageHeaders (= 0.72.5)
    - React-jsi (= 0.72.5)
    - React-RCTNetwork (= 0.72.5)
    - ReactCommon/turbomodule/core (= 0.72.5)
  - React-RCTLinking (0.72.5):
    - React-Codegen (= 0.72.5)
    - React-Core/RCTLinkingHeaders (= 0.72.5)
    - React-jsi (= 0.72.5)
    - ReactCommon/turbomodule/core (= 0.72.5)
  - React-RCTNetwork (0.72.5):
    - RCT-Folly (= 2021.07.22.00)
    - RCTTypeSafety (= 0.72.5)
    - React-Codegen (= 0.72.5)
    - React-Core/RCTNetworkHeaders (= 0.72.5)
    - React-jsi (= 0.72.5)
    - ReactCommon/turbomodule/core (= 0.72.5)
  - React-RCTSettings (0.72.5):
    - RCT-Folly (= 2021.07.22.00)
    - RCTTypeSafety (= 0.72.5)
    - React-Codegen (= 0.72.5)
    - React-Core/RCTSettingsHeaders (= 0.72.5)
    - React-jsi (= 0.72.5)
    - ReactCommon/turbomodule/core (= 0.72.5)
  - React-RCTText (0.72.5):
    - React-Core/RCTTextHeaders (= 0.72.5)
  - React-RCTVibration (0.72.5):
    - RCT-Folly (= 2021.07.22.00)
    - React-Codegen (= 0.72.5)
    - React-Core/RCTVibrationHeaders (= 0.72.5)
    - React-jsi (= 0.72.5)
    - ReactCommon/turbomodule/core (= 0.72.5)
  - React-rncore (0.72.5)
  - React-runtimeexecutor (0.72.5):
    - React-jsi (= 0.72.5)
  - React-runtimescheduler (0.72.5):
    - glog
    - RCT-Folly (= 2021.07.22.00)
    - React-callinvoker
    - React-debug
    - React-jsi
    - React-runtimeexecutor
  - React-utils (0.72.5):
    - glog
    - RCT-Folly (= 2021.07.22.00)
    - React-debug
  - ReactCommon/turbomodule/bridging (0.72.5):
    - DoubleConversion
    - glog
    - RCT-Folly (= 2021.07.22.00)
    - React-callinvoker (= 0.72.5)
    - React-cxxreact (= 0.72.5)
    - React-jsi (= 0.72.5)
    - React-logger (= 0.72.5)
    - React-perflogger (= 0.72.5)
  - ReactCommon/turbomodule/core (0.72.5):
    - DoubleConversion
    - glog
    - RCT-Folly (= 2021.07.22.00)
    - React-callinvoker (= 0.72.5)
    - React-cxxreact (= 0.72.5)
    - React-jsi (= 0.72.5)
    - React-logger (= 0.72.5)
    - React-perflogger (= 0.72.5)
  - ReactNativeGetLocation (4.0.0):
    - React-Core
  - RNCAsyncStorage (1.19.3):
    - React-Core
  - RNCCheckbox (0.5.17):
    - BEMCheckBox (~> 1.4)
    - React-Core
  - RNCClipboard (1.12.1):
    - React-Core
  - RNCPicker (2.5.1):
    - React-Core
  - RNDeviceInfo (10.11.0):
    - React-Core
  - RNGestureHandler (2.13.1):
    - React-Core
  - RNImageCropPicker (0.40.0):
    - React-Core
    - React-RCTImage
    - RNImageCropPicker/QBImagePickerController (= 0.40.0)
    - TOCropViewController
  - RNImageCropPicker/QBImagePickerController (0.40.0):
    - React-Core
    - React-RCTImage
    - TOCropViewController
  - RNScreens (3.25.0):
    - React-Core
    - React-RCTImage
  - RNShare (9.4.1):
    - React-Core
  - RNSVG (13.14.0):
    - React-Core
  - SocketRocket (0.6.1)
  - TOCropViewController (2.6.1)
  - Yoga (1.14.0)

DEPENDENCIES:
  - boost (from `../node_modules/react-native/third-party-podspecs/boost.podspec`)
  - DoubleConversion (from `../node_modules/react-native/third-party-podspecs/DoubleConversion.podspec`)
  - FBLazyVector (from `../node_modules/react-native/Libraries/FBLazyVector`)
  - FBReactNativeSpec (from `../node_modules/react-native/React/FBReactNativeSpec`)
  - glog (from `../node_modules/react-native/third-party-podspecs/glog.podspec`)
  - RCT-Folly (from `../node_modules/react-native/third-party-podspecs/RCT-Folly.podspec`)
  - RCTRequired (from `../node_modules/react-native/Libraries/RCTRequired`)
  - RCTTypeSafety (from `../node_modules/react-native/Libraries/TypeSafety`)
  - React (from `../node_modules/react-native/`)
  - React-callinvoker (from `../node_modules/react-native/ReactCommon/callinvoker`)
  - React-Codegen (from `build/generated/ios`)
  - React-Core (from `../node_modules/react-native/`)
  - React-Core/RCTWebSocket (from `../node_modules/react-native/`)
  - React-CoreModules (from `../node_modules/react-native/React/CoreModules`)
  - React-cxxreact (from `../node_modules/react-native/ReactCommon/cxxreact`)
  - React-debug (from `../node_modules/react-native/ReactCommon/react/debug`)
  - React-jsc (from `../node_modules/react-native/ReactCommon/jsc`)
  - React-jsi (from `../node_modules/react-native/ReactCommon/jsi`)
  - React-jsiexecutor (from `../node_modules/react-native/ReactCommon/jsiexecutor`)
  - React-jsinspector (from `../node_modules/react-native/ReactCommon/jsinspector`)
  - React-logger (from `../node_modules/react-native/ReactCommon/logger`)
  - react-native-blob-util (from `../node_modules/react-native-blob-util`)
  - "react-native-cameraroll (from `../node_modules/@react-native-camera-roll/camera-roll`)"
  - react-native-document-picker (from `../node_modules/react-native-document-picker`)
  - react-native-get-random-values (from `../node_modules/react-native-get-random-values`)
  - react-native-image-picker (from `../node_modules/react-native-image-picker`)
  - "react-native-netinfo (from `../node_modules/@react-native-community/netinfo`)"
  - react-native-onesignal (from `../node_modules/react-native-onesignal`)
  - react-native-pdf (from `../node_modules/react-native-pdf`)
  - react-native-render-html (from `../node_modules/react-native-render-html`)
  - react-native-restart (from `../node_modules/react-native-restart`)
  - react-native-safe-area-context (from `../node_modules/react-native-safe-area-context`)
  - react-native-video (from `../node_modules/react-native-video`)
  - react-native-webview (from `../node_modules/react-native-webview`)
  - React-NativeModulesApple (from `../node_modules/react-native/ReactCommon/react/nativemodule/core/platform/ios`)
  - React-perflogger (from `../node_modules/react-native/ReactCommon/reactperflogger`)
  - React-RCTActionSheet (from `../node_modules/react-native/Libraries/ActionSheetIOS`)
  - React-RCTAnimation (from `../node_modules/react-native/Libraries/NativeAnimation`)
  - React-RCTAppDelegate (from `../node_modules/react-native/Libraries/AppDelegate`)
  - React-RCTBlob (from `../node_modules/react-native/Libraries/Blob`)
  - React-RCTImage (from `../node_modules/react-native/Libraries/Image`)
  - React-RCTLinking (from `../node_modules/react-native/Libraries/LinkingIOS`)
  - React-RCTNetwork (from `../node_modules/react-native/Libraries/Network`)
  - React-RCTSettings (from `../node_modules/react-native/Libraries/Settings`)
  - React-RCTText (from `../node_modules/react-native/Libraries/Text`)
  - React-RCTVibration (from `../node_modules/react-native/Libraries/Vibration`)
  - React-rncore (from `../node_modules/react-native/ReactCommon`)
  - React-runtimeexecutor (from `../node_modules/react-native/ReactCommon/runtimeexecutor`)
  - React-runtimescheduler (from `../node_modules/react-native/ReactCommon/react/renderer/runtimescheduler`)
  - React-utils (from `../node_modules/react-native/ReactCommon/react/utils`)
  - ReactCommon/turbomodule/core (from `../node_modules/react-native/ReactCommon`)
  - ReactNativeGetLocation (from `../node_modules/react-native-get-location`)
  - "RNCAsyncStorage (from `../node_modules/@react-native-async-storage/async-storage`)"
  - "RNCCheckbox (from `../node_modules/@react-native-community/checkbox`)"
  - "RNCClipboard (from `../node_modules/@react-native-clipboard/clipboard`)"
  - "RNCPicker (from `../node_modules/@react-native-picker/picker`)"
  - RNDeviceInfo (from `../node_modules/react-native-device-info`)
  - RNGestureHandler (from `../node_modules/react-native-gesture-handler`)
  - RNImageCropPicker (from `../node_modules/react-native-image-crop-picker`)
  - RNScreens (from `../node_modules/react-native-screens`)
  - RNShare (from `../node_modules/react-native-share`)
  - RNSVG (from `../node_modules/react-native-svg`)
  - Yoga (from `../node_modules/react-native/ReactCommon/yoga`)

SPEC REPOS:
  trunk:
    - BEMCheckBox
    - fmt
    - OneSignalXCFramework
    - SocketRocket
    - TOCropViewController

EXTERNAL SOURCES:
  boost:
    :podspec: "../node_modules/react-native/third-party-podspecs/boost.podspec"
  DoubleConversion:
    :podspec: "../node_modules/react-native/third-party-podspecs/DoubleConversion.podspec"
  FBLazyVector:
    :path: "../node_modules/react-native/Libraries/FBLazyVector"
  FBReactNativeSpec:
    :path: "../node_modules/react-native/React/FBReactNativeSpec"
  glog:
    :podspec: "../node_modules/react-native/third-party-podspecs/glog.podspec"
  RCT-Folly:
    :podspec: "../node_modules/react-native/third-party-podspecs/RCT-Folly.podspec"
  RCTRequired:
    :path: "../node_modules/react-native/Libraries/RCTRequired"
  RCTTypeSafety:
    :path: "../node_modules/react-native/Libraries/TypeSafety"
  React:
    :path: "../node_modules/react-native/"
  React-callinvoker:
    :path: "../node_modules/react-native/ReactCommon/callinvoker"
  React-Codegen:
    :path: build/generated/ios
  React-Core:
    :path: "../node_modules/react-native/"
  React-CoreModules:
    :path: "../node_modules/react-native/React/CoreModules"
  React-cxxreact:
    :path: "../node_modules/react-native/ReactCommon/cxxreact"
  React-debug:
    :path: "../node_modules/react-native/ReactCommon/react/debug"
  React-jsc:
    :path: "../node_modules/react-native/ReactCommon/jsc"
  React-jsi:
    :path: "../node_modules/react-native/ReactCommon/jsi"
  React-jsiexecutor:
    :path: "../node_modules/react-native/ReactCommon/jsiexecutor"
  React-jsinspector:
    :path: "../node_modules/react-native/ReactCommon/jsinspector"
  React-logger:
    :path: "../node_modules/react-native/ReactCommon/logger"
  react-native-blob-util:
    :path: "../node_modules/react-native-blob-util"
  react-native-cameraroll:
    :path: "../node_modules/@react-native-camera-roll/camera-roll"
  react-native-document-picker:
    :path: "../node_modules/react-native-document-picker"
  react-native-get-random-values:
    :path: "../node_modules/react-native-get-random-values"
  react-native-image-picker:
    :path: "../node_modules/react-native-image-picker"
  react-native-netinfo:
    :path: "../node_modules/@react-native-community/netinfo"
  react-native-onesignal:
    :path: "../node_modules/react-native-onesignal"
  react-native-pdf:
    :path: "../node_modules/react-native-pdf"
  react-native-render-html:
    :path: "../node_modules/react-native-render-html"
  react-native-restart:
    :path: "../node_modules/react-native-restart"
  react-native-safe-area-context:
    :path: "../node_modules/react-native-safe-area-context"
  react-native-video:
    :path: "../node_modules/react-native-video"
  react-native-webview:
    :path: "../node_modules/react-native-webview"
  React-NativeModulesApple:
    :path: "../node_modules/react-native/ReactCommon/react/nativemodule/core/platform/ios"
  React-perflogger:
    :path: "../node_modules/react-native/ReactCommon/reactperflogger"
  React-RCTActionSheet:
    :path: "../node_modules/react-native/Libraries/ActionSheetIOS"
  React-RCTAnimation:
    :path: "../node_modules/react-native/Libraries/NativeAnimation"
  React-RCTAppDelegate:
    :path: "../node_modules/react-native/Libraries/AppDelegate"
  React-RCTBlob:
    :path: "../node_modules/react-native/Libraries/Blob"
  React-RCTImage:
    :path: "../node_modules/react-native/Libraries/Image"
  React-RCTLinking:
    :path: "../node_modules/react-native/Libraries/LinkingIOS"
  React-RCTNetwork:
    :path: "../node_modules/react-native/Libraries/Network"
  React-RCTSettings:
    :path: "../node_modules/react-native/Libraries/Settings"
  React-RCTText:
    :path: "../node_modules/react-native/Libraries/Text"
  React-RCTVibration:
    :path: "../node_modules/react-native/Libraries/Vibration"
  React-rncore:
    :path: "../node_modules/react-native/ReactCommon"
  React-runtimeexecutor:
    :path: "../node_modules/react-native/ReactCommon/runtimeexecutor"
  React-runtimescheduler:
    :path: "../node_modules/react-native/ReactCommon/react/renderer/runtimescheduler"
  React-utils:
    :path: "../node_modules/react-native/ReactCommon/react/utils"
  ReactCommon:
    :path: "../node_modules/react-native/ReactCommon"
  ReactNativeGetLocation:
    :path: "../node_modules/react-native-get-location"
  RNCAsyncStorage:
    :path: "../node_modules/@react-native-async-storage/async-storage"
  RNCCheckbox:
    :path: "../node_modules/@react-native-community/checkbox"
  RNCClipboard:
    :path: "../node_modules/@react-native-clipboard/clipboard"
  RNCPicker:
    :path: "../node_modules/@react-native-picker/picker"
  RNDeviceInfo:
    :path: "../node_modules/react-native-device-info"
  RNGestureHandler:
    :path: "../node_modules/react-native-gesture-handler"
  RNImageCropPicker:
    :path: "../node_modules/react-native-image-crop-picker"
  RNScreens:
    :path: "../node_modules/react-native-screens"
  RNShare:
    :path: "../node_modules/react-native-share"
  RNSVG:
    :path: "../node_modules/react-native-svg"
  Yoga:
    :path: "../node_modules/react-native/ReactCommon/yoga"

SPEC CHECKSUMS:
  BEMCheckBox: 5ba6e37ade3d3657b36caecc35c8b75c6c2b1a4e
  boost: 57d2868c099736d80fcd648bf211b4431e51a558
  DoubleConversion: 5189b271737e1565bdce30deb4a08d647e3f5f54
  FBLazyVector: 71803c074f6325f10b5ec891c443b6bbabef0ca7
  FBReactNativeSpec: 448e08a759d29a96e15725ae532445bf4343567c
  fmt: ff9d55029c625d3757ed641535fd4a75fedc7ce9
  glog: 04b94705f318337d7ead9e6d17c019bd9b1f6b1b
  OneSignalXCFramework: ff1c970b7aeb4ac0fe48fb35393eb5d8bf378135
  RCT-Folly: 424b8c9a7a0b9ab2886ffe9c3b041ef628fd4fb1
  RCTRequired: df81ab637d35fac9e6eb94611cfd20f0feb05455
  RCTTypeSafety: 4636e4a36c7c2df332bda6d59b19b41c443d4287
  React: e0cc5197a804031a6c53fb38483c3485fcb9d6f3
  React-callinvoker: 1a635856fe0c3d8b13fccd4ed7e76283b99b0868
  React-Codegen: 2c2e23ede5a87859465582502ff61c7f63bc1ed8
  React-Core: 5bb7f0f43a6fae733201a0396521bec7c1b062c0
  React-CoreModules: f8b9e91fac7bd5d18729ce961a4978c70b5031cc
  React-cxxreact: 2fd17904f2d5ab7318e5432cf24de225cbf1768f
  React-debug: ee33d7ba43766d9b10b32561527b57ccfbcb6bd1
  React-jsc: f4a2687433840ec8c1be9d2f557fe71a7b5e66b5
  React-jsi: bd20f0acd279dee534d8b731b31131f443b00868
  React-jsiexecutor: f32f98d8ddbdfd97dea286af90381a731d68c505
  React-jsinspector: aef73cbd43b70675f572214d10fa438c89bf11ba
  React-logger: 2e4aee3e11b3ec4fa6cfd8004610bbb3b8d6cca4
  react-native-blob-util: af2dc91365bfde33f61389cbd0c692af9a4a97cb
  react-native-cameraroll: 4701ae7c3dbcd3f5e9e150ca17f250a276154b35
  react-native-document-picker: 5b97e24a7f1a1e4a50a72c540a043f32d29a70a2
  react-native-get-random-values: 21325b2244dfa6b58878f51f9aa42821e7ba3d06
  react-native-image-picker: ddbbe4d226d9c82a82360f5de66bf71a657a42e6
  react-native-netinfo: fefd4e98d75cbdd6e85fc530f7111a8afdf2b0c5
  react-native-onesignal: ab800900cffeca4d9db70a05244013fc8a36ceb8
  react-native-pdf: 7c0e91ada997bac8bac3bb5bea5b6b81f5a3caae
  react-native-render-html: 984dfe2294163d04bf5fe25d7c9f122e60e05ebe
  react-native-restart: 7595693413fe3ca15893702f2c8306c62a708162
  react-native-safe-area-context: 7aa8e6d9d0f3100a820efb1a98af68aa747f9284
  react-native-video: c26780b224543c62d5e1b2a7244a5cd1b50e8253
  react-native-webview: 8fc09f66a1a5b16bbe37c3878fda27d5982bb776
  React-NativeModulesApple: c6529c637f2e886aab44c48d66cabef2d4fd1138
  React-perflogger: cd8886513f68e1c135a1e79d20575c6489641597
  React-RCTActionSheet: 726d2615ca62a77ce3e2c13d87f65379cdc73498
  React-RCTAnimation: 8f2716b881c37c64858e4ecee0f58bfa57ff9afd
  React-RCTAppDelegate: fa1c94d4c789a37c64bf1ba9f7bc0038beb6c2be
  React-RCTBlob: 3e6120f6abbc7b8dc173da0db633d67cec7f66b7
  React-RCTImage: 747e3d7b656a67470f9c234baedb8d41bbc4e745
  React-RCTLinking: 148332b5b0396b280b05534f7d168e560a3bbd5f
  React-RCTNetwork: 1d818121a8e678f064de663a6db7aaefc099e53c
  React-RCTSettings: 4b95d26ebc88bfd3b6535b2d7904914ff88dbfc2
  React-RCTText: ce4499e4f2d8f85dc4b93ff0559313a016c4f3e2
  React-RCTVibration: 45372e61b35e96d16893540958d156675afbeb63
  React-rncore: a79d1cb3d6c01b358a8aa0b31ccc04ab5f0dbebc
  React-runtimeexecutor: 7e31e2bc6d0ecc83d4ba05eadc98401007abc10c
  React-runtimescheduler: 05bede38cb51ae152f31707022e881ee9ae970ab
  React-utils: 7a9918a1ffdd39aba67835d42386f592ea3f8e76
  ReactCommon: a77b3916d0d39113b5f0de714a0acd47c9d17c3a
  ReactNativeGetLocation: 87f83bee4dfecc88772de04afd076d9ae265cc42
  RNCAsyncStorage: c913ede1fa163a71cea118ed4670bbaaa4b511bb
  RNCCheckbox: a3ca9978cb0846b981d28da4e9914bd437403d77
  RNCClipboard: d77213bfa269013bf4b857b7a9ca37ee062d8ef1
  RNCPicker: 529d564911e93598cc399b56cc0769ce3675f8c8
  RNDeviceInfo: bf8a32acbcb875f568217285d1793b0e8588c974
  RNGestureHandler: 38aa38413896620338948fbb5c90579a7b1c3fde
  RNImageCropPicker: 486e2f7e2b0461ce24321f751410dce1b3b49e6d
  RNScreens: 85d3880b52d34db7b8eeebe2f1a0e807c05e69fa
  RNShare: 32e97adc8d8c97d4a26bcdd3c45516882184f8b6
  RNSVG: d00c8f91c3cbf6d476451313a18f04d220d4f396
  SocketRocket: f32cd54efbe0f095c4d7594881e52619cfe80b17
  TOCropViewController: edfd4f25713d56905ad1e0b9f5be3fbe0f59c863
  Yoga: 86fed2e4d425ee4c6eab3813ba1791101ee153c6

PODFILE CHECKSUM: b8530a81e1ad37c90e028e5f0c6c29876a8b6de1

COCOAPODS: 1.15.2
