<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
	<dict>
		<key>CFBundleDevelopmentRegion</key>
		<string>en</string>
		<key>CFBundleDisplayName</key>
		<string>Cury Corretor</string>
		<key>CFBundleExecutable</key>
		<string>$(EXECUTABLE_NAME)</string>
		<key>CFBundleIdentifier</key>
		<string>$(PRODUCT_BUNDLE_IDENTIFIER)</string>
		<key>CFBundleInfoDictionaryVersion</key>
		<string>6.0</string>
		<key>CFBundleName</key>
		<string>$(PRODUCT_NAME)</string>
		<key>CFBundlePackageType</key>
		<string>APPL</string>
		<key>CFBundleShortVersionString</key>
		<string>$(MARKETING_VERSION)</string>
		<key>CFBundleSignature</key>
		<string>????</string>
		<key>CFBundleVersion</key>
		<string>$(CURRENT_PROJECT_VERSION)</string>
		<key>LSApplicationCategoryType</key>
		<string/>
		<key>LSRequiresIPhoneOS</key>
		<true/>
		<key>NSAppPrivacyUsageDescription</key>
		<dict>
			<key>NSPrivacyAccessedAPITypes</key>
			<dict>
				<key>NSPrivacyAccessedAPICategoryDiskSpace</key>
				<string>Salvar arquivos de empreendimentos.</string>
			</dict>
		</dict>
		<key>NSAppTransportSecurity</key>
		<dict>
			<key>NSExceptionDomains</key>
			<dict>
				<key>localhost</key>
				<dict>
					<key>NSExceptionAllowsInsecureHTTPLoads</key>
					<true/>
				</dict>
			</dict>
		</dict>
		<key>NSCameraUsageDescription</key>
		<string>Use a câmera para enviar seus documentos para análise.</string>
		<key>NSLocationWhenInUseUsageDescription</key>
		<string>Sua localização será usada para mostrar plantões próximos de você.</string>
		<key>NSPhotoLibraryUsageDescription</key>
		<string>Use a biblioteca de fotos para enviar seus documentos para análise.</string>
		<key>UIAppFonts</key>
		<array>
			<string>Roboto-Black.ttf</string>
			<string>Roboto-Bold.ttf</string>
			<string>Roboto-Light.ttf</string>
			<string>Roboto-Medium.ttf</string>
			<string>Roboto-Regular.ttf</string>
			<string>Roboto-Thin.ttf</string>
			<string>Ubuntu-Bold.ttf</string>
			<string>Ubuntu-Light.ttf</string>
			<string>Ubuntu-Medium.ttf</string>
			<string>Ubuntu-Regular.ttf</string>
		</array>
		<key>OneSignal_app_groups_key</key>
		<string>group.com.net.cury.app.corretor.onesignal</string>
		<key>UILaunchStoryboardName</key>
		<string>LaunchScreen</string>
		<key>UIRequiredDeviceCapabilities</key>
		<array>
			<string>armv7</string>
		</array>
		<key>UISupportedInterfaceOrientations</key>
		<array>
			<string>UIInterfaceOrientationLandscapeLeft</string>
			<string>UIInterfaceOrientationLandscapeRight</string>
			<string>UIInterfaceOrientationPortrait</string>
			<string>UIInterfaceOrientationPortraitUpsideDown</string>
		</array>
		<key>UIViewControllerBasedStatusBarAppearance</key>
		<false/>
		<key>CFBundleURLTypes</key>
		<array>
			<dict>
				<key>CFBundleURLSchemes</key>
				<array>
					<string>curycorretor</string>
				</array>
			</dict>
		</array>
	</dict>
</plist>